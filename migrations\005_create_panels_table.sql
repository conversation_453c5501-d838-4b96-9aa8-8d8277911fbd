-- Migration: Create panels table
-- Version: 005
-- Description: Create panels table for managing VPN panels (Marzban, Marzneshin, X-UI)
-- Author: WeBot Team
-- Date: 2024-01-01

BEGIN;

-- Create panels table
CREATE TABLE IF NOT EXISTS `panels` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL COMMENT 'Panel display name',
    `type` ENUM('marzban', 'marzneshin', 'x-ui') NOT NULL COMMENT 'Panel type',
    `url` VARCHAR(500) NOT NULL COMMENT 'Panel URL',
    `username` VARCHAR(255) NOT NULL COMMENT 'Panel username',
    `password` VARCHAR(255) NOT NULL COMMENT 'Panel password (encrypted)',
    `token` TEXT NULL COMMENT 'Current access token',
    `refresh_token` TEXT NULL COMMENT 'Refresh token for OAuth2',
    `token_expires_at` TIMESTAMP NULL COMMENT 'Token expiration time',
    `status` ENUM('online', 'offline', 'error', 'maintenance') DEFAULT 'offline' COMMENT 'Panel status',
    `version` VARCHAR(50) NULL COMMENT 'Panel version',
    `last_sync_at` TIMESTAMP NULL COMMENT 'Last synchronization time',
    `last_health_check` TIMESTAMP NULL COMMENT 'Last health check time',
    `health_status` ENUM('healthy', 'warning', 'critical', 'unknown') DEFAULT 'unknown' COMMENT 'Health status',
    `config_data` JSON NULL COMMENT 'Panel configuration data',
    `statistics` JSON NULL COMMENT 'Panel statistics data',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT 'Is panel active',
    `priority` TINYINT(2) DEFAULT 5 COMMENT 'Panel priority (1-10, lower is higher priority)',
    `max_users` INT(11) DEFAULT 0 COMMENT 'Maximum users (0 = unlimited)',
    `current_users` INT(11) DEFAULT 0 COMMENT 'Current users count',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_panels_name` (`name`),
    UNIQUE KEY `uk_panels_url` (`url`),
    KEY `idx_panels_type` (`type`),
    KEY `idx_panels_status` (`status`),
    KEY `idx_panels_active` (`is_active`),
    KEY `idx_panels_priority` (`priority`),
    KEY `idx_panels_health` (`health_status`),
    KEY `idx_panels_last_check` (`last_health_check`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='VPN panels management';

-- Add foreign key constraint to services table if it exists
SET @sql = (
    SELECT IF(
        EXISTS(
            SELECT 1 FROM information_schema.tables 
            WHERE table_schema = DATABASE() 
            AND table_name = 'services'
        ),
        'ALTER TABLE `services` ADD COLUMN IF NOT EXISTS `panel_id` INT(11) NULL AFTER `server_id`, ADD KEY `idx_services_panel` (`panel_id`), ADD CONSTRAINT `fk_services_panel` FOREIGN KEY (`panel_id`) REFERENCES `panels`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;',
        'SELECT "Services table does not exist yet" as message;'
    )
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Insert default panels (examples)
INSERT IGNORE INTO `panels` (
    `name`, `type`, `url`, `username`, `password`, `status`, `is_active`, `priority`, `max_users`
) VALUES 
(
    'Main Marzban Panel',
    'marzban',
    'https://panel.example.com',
    'admin',
    'encrypted_password_here',
    'offline',
    TRUE,
    1,
    1000
),
(
    'Marzneshin Panel',
    'marzneshin',
    'https://marzneshin.example.com',
    'admin',
    'encrypted_password_here',
    'offline',
    TRUE,
    2,
    500
),
(
    'X-UI Panel',
    'x-ui',
    'https://xui.example.com',
    'admin',
    'encrypted_password_here',
    'offline',
    FALSE,
    3,
    200
);

-- Create view for panel statistics
CREATE OR REPLACE VIEW `panel_stats_view` AS
SELECT 
    p.id,
    p.name,
    p.type,
    p.status,
    p.health_status,
    p.is_active,
    p.current_users,
    p.max_users,
    CASE 
        WHEN p.max_users = 0 THEN 0 
        ELSE ROUND((p.current_users / p.max_users) * 100, 2) 
    END as usage_percentage,
    CASE 
        WHEN p.max_users = 0 THEN 999999 
        ELSE (p.max_users - p.current_users) 
    END as remaining_capacity,
    COUNT(s.id) as total_services,
    SUM(CASE WHEN s.status = 'active' THEN 1 ELSE 0 END) as active_services,
    p.last_health_check,
    p.last_sync_at,
    p.created_at
FROM panels p
LEFT JOIN services s ON p.id = s.panel_id
GROUP BY p.id;

-- Create procedure for panel health check
DELIMITER //
CREATE OR REPLACE PROCEDURE UpdatePanelHealth(
    IN panel_id INT,
    IN new_status VARCHAR(20),
    IN new_health_status VARCHAR(20)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;
    
    UPDATE panels 
    SET 
        status = new_status,
        health_status = new_health_status,
        last_health_check = NOW(),
        updated_at = NOW()
    WHERE id = panel_id;
    
    -- Log the health check
    INSERT INTO panel_health_log (panel_id, status, health_status, checked_at)
    VALUES (panel_id, new_status, new_health_status, NOW())
    ON DUPLICATE KEY UPDATE
        status = VALUES(status),
        health_status = VALUES(health_status),
        checked_at = VALUES(checked_at);
    
    COMMIT;
END //
DELIMITER ;

-- Create table for panel health logs
CREATE TABLE IF NOT EXISTS `panel_health_log` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `panel_id` INT(11) NOT NULL,
    `status` VARCHAR(20) NOT NULL,
    `health_status` VARCHAR(20) NOT NULL,
    `response_time_ms` INT(11) NULL,
    `error_message` TEXT NULL,
    `checked_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_panel_health_panel` (`panel_id`),
    KEY `idx_panel_health_date` (`checked_at`),
    CONSTRAINT `fk_panel_health_panel` FOREIGN KEY (`panel_id`) REFERENCES `panels`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Panel health check logs';

-- Create trigger to update panel statistics
DELIMITER //
CREATE OR REPLACE TRIGGER `update_panel_user_count` 
AFTER INSERT ON `services`
FOR EACH ROW
BEGIN
    IF NEW.panel_id IS NOT NULL THEN
        UPDATE panels 
        SET current_users = (
            SELECT COUNT(*) 
            FROM services 
            WHERE panel_id = NEW.panel_id 
            AND status = 'active'
        ),
        updated_at = NOW()
        WHERE id = NEW.panel_id;
    END IF;
END //

CREATE OR REPLACE TRIGGER `update_panel_user_count_delete` 
AFTER DELETE ON `services`
FOR EACH ROW
BEGIN
    IF OLD.panel_id IS NOT NULL THEN
        UPDATE panels 
        SET current_users = (
            SELECT COUNT(*) 
            FROM services 
            WHERE panel_id = OLD.panel_id 
            AND status = 'active'
        ),
        updated_at = NOW()
        WHERE id = OLD.panel_id;
    END IF;
END //

CREATE OR REPLACE TRIGGER `update_panel_user_count_update` 
AFTER UPDATE ON `services`
FOR EACH ROW
BEGIN
    -- Update old panel if changed
    IF OLD.panel_id IS NOT NULL AND OLD.panel_id != NEW.panel_id THEN
        UPDATE panels 
        SET current_users = (
            SELECT COUNT(*) 
            FROM services 
            WHERE panel_id = OLD.panel_id 
            AND status = 'active'
        ),
        updated_at = NOW()
        WHERE id = OLD.panel_id;
    END IF;
    
    -- Update new panel
    IF NEW.panel_id IS NOT NULL THEN
        UPDATE panels 
        SET current_users = (
            SELECT COUNT(*) 
            FROM services 
            WHERE panel_id = NEW.panel_id 
            AND status = 'active'
        ),
        updated_at = NOW()
        WHERE id = NEW.panel_id;
    END IF;
END //
DELIMITER ;

-- Record migration
INSERT INTO schema_versions (version, applied_at, description) 
VALUES (5, NOW(), 'Create panels table and related structures')
ON DUPLICATE KEY UPDATE applied_at = NOW();

COMMIT;

-- Verification
SELECT 'Panels table created successfully' as message;
SELECT COUNT(*) as panels_count FROM panels;
SELECT 'Panel health log table created' as message;
SELECT COUNT(*) as health_logs_count FROM panel_health_log;
