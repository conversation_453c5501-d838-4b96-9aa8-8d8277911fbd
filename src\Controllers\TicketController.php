<?php

declare(strict_types=1);

namespace WeBot\Controllers;

use WeBot\Exceptions\WeBotException;
use WeBot\Services\MessageService;

/**
 * Ticket Controller
 *
 * Handles support ticket system including ticket creation,
 * management, responses, and escalation.
 *
 * @package WeBot\Controllers
 * @version 2.0
 */
class TicketController extends BaseController
{
    private MessageService $messageService;

    public function __construct($container)
    {
        parent::__construct($container);
        $this->messageService = $container->get('messageService');
    }

    /**
     * Handle support command
     */
    public function handleSupport(array $message): array
    {
        try {
            $this->initialize(['message' => $message]);
            $this->logAction('support_command');

            $userInfo = $this->getUserInfo();
            if (!$userInfo) {
                return $this->sendMessage('ابتدا باید ثبت نام کنید.');
            }

            return $this->showSupportMenu();
        } catch (\Throwable $e) {
            return $this->handleError($e, 'خطا در سیستم پشتیبانی.');
        }
    }

    /**
     * Handle ticket callbacks
     */
    public function handleCallback(array $callbackQuery): array
    {
        try {
            $this->initialize(['callback_query' => $callbackQuery]);

            $userInfo = $this->getUserInfo();
            if (!$userInfo) {
                return $this->answerCallback('ابتدا باید ثبت نام کنید.');
            }

            $data = $this->data;

            return match (true) {
                $data === 'create_ticket' => $this->startTicketCreation(),
                $data === 'my_tickets' => $this->showUserTickets(),
                $data === 'faq' => $this->showFAQ(),
                str_starts_with($data, 'ticket_view_') => $this->viewTicket($data),
                str_starts_with($data, 'category_') => $this->handleCategorySelection($data),
                str_starts_with($data, 'priority_') => $this->handlePrioritySelection($data),
                default => $this->answerCallback('عملیات نامعتبر.')
            };
        } catch (\Throwable $e) {
            return $this->handleError($e, 'خطا در پردازش درخواست.');
        }
    }

    /**
     * Handle ticket messages
     */
    public function handleMessage(array $message): array
    {
        try {
            $this->initialize(['message' => $message]);

            $userInfo = $this->getUserInfo();
            if (!$userInfo) {
                return $this->handleSupport($message);
            }

            $step = $userInfo['step'] ?? '';

            return match ($step) {
                'ticket_subject' => $this->handleSubjectInput(),
                'ticket_description' => $this->handleDescriptionInput(),
                'ticket_reply' => $this->showSupportMenu(),
                default => $this->showSupportMenu()
            };
        } catch (\Throwable $e) {
            return $this->handleError($e, 'خطا در پردازش پیام.');
        }
    }

    /**
     * Show support menu
     */
    private function showSupportMenu(): array
    {
        $text = "🎧 **سیستم پشتیبانی WeBot**\n\n";
        $text .= "لطفاً یکی از گزینه‌های زیر را انتخاب کنید:\n\n";
        $text .= "• **ایجاد تیکت جدید**: برای گزارش مشکل یا درخواست\n";
        $text .= "• **تیکت‌های من**: مشاهده تیکت‌های قبلی\n";
        $text .= "• **سوالات متداول**: پاسخ سوالات رایج";

        $keyboard = $this->createInlineKeyboard([
            [
                ['text' => '🎫 ایجاد تیکت جدید', 'callback_data' => 'create_ticket']
            ],
            [
                ['text' => '📋 تیکت‌های من', 'callback_data' => 'my_tickets'],
                ['text' => '❓ سوالات متداول', 'callback_data' => 'faq']
            ],
            [
                ['text' => '🔙 بازگشت به منو اصلی', 'callback_data' => 'main_menu']
            ]
        ]);

        return $this->sendMessage($text, $keyboard);
    }

    /**
     * Start ticket creation process
     */
    private function startTicketCreation(): array
    {
        $text = "🎫 **ایجاد تیکت جدید**\n\n";
        $text .= "لطفاً دسته‌بندی مشکل خود را انتخاب کنید:";

        $keyboard = $this->createInlineKeyboard([
            [
                ['text' => '🔧 مشکل فنی', 'callback_data' => 'category_technical'],
                ['text' => '💰 مشکل پرداخت', 'callback_data' => 'category_payment']
            ],
            [
                ['text' => '📱 مشکل اتصال', 'callback_data' => 'category_connection'],
                ['text' => '⚙️ تنظیمات', 'callback_data' => 'category_settings']
            ],
            [
                ['text' => '📞 درخواست تماس', 'callback_data' => 'category_call'],
                ['text' => '❓ سایر موارد', 'callback_data' => 'category_other']
            ],
            [
                ['text' => '🔙 بازگشت', 'callback_data' => 'support_menu']
            ]
        ]);

        return $this->editMessage($text, $keyboard);
    }

    /**
     * Handle category selection
     */
    private function handleCategorySelection(string $data): array
    {
        $category = str_replace('category_', '', $data);

        // Store category in user session
        $this->setUserData('ticket_category', $category);

        $categoryNames = [
            'technical' => 'مشکل فنی',
            'payment' => 'مشکل پرداخت',
            'connection' => 'مشکل اتصال',
            'settings' => 'تنظیمات',
            'call' => 'درخواست تماس',
            'other' => 'سایر موارد'
        ];

        $categoryName = $categoryNames[$category] ?? 'نامشخص';

        $text = "🎫 **ایجاد تیکت جدید**\n\n";
        $text .= "دسته‌بندی: **{$categoryName}**\n\n";
        $text .= "لطفاً اولویت تیکت خود را انتخاب کنید:";

        $keyboard = $this->createInlineKeyboard([
            [
                ['text' => '🔴 فوری', 'callback_data' => 'priority_urgent'],
                ['text' => '🟡 متوسط', 'callback_data' => 'priority_medium']
            ],
            [
                ['text' => '🟢 کم', 'callback_data' => 'priority_low']
            ],
            [
                ['text' => '🔙 بازگشت', 'callback_data' => 'create_ticket']
            ]
        ]);

        return $this->editMessage($text, $keyboard);
    }

    /**
     * Handle priority selection
     */
    private function handlePrioritySelection(string $data): array
    {
        $priority = str_replace('priority_', '', $data);

        // Store priority in user session
        $this->setUserData('ticket_priority', $priority);

        $priorityNames = [
            'urgent' => 'فوری',
            'medium' => 'متوسط',
            'low' => 'کم'
        ];

        $priorityName = $priorityNames[$priority] ?? 'نامشخص';

        $text = "🎫 **ایجاد تیکت جدید**\n\n";
        $text .= "اولویت: **{$priorityName}**\n\n";
        $text .= "لطفاً موضوع تیکت خود را وارد کنید:\n";
        $text .= "(حداکثر 100 کاراکتر)";

        // Set user step
        $this->setUserStep('ticket_subject');

        $keyboard = $this->createInlineKeyboard([
            [
                ['text' => '❌ لغو', 'callback_data' => 'support_menu']
            ]
        ]);

        return $this->editMessage($text, $keyboard);
    }

    /**
     * Handle subject input
     */
    private function handleSubjectInput(): array
    {
        $subject = trim($this->text);

        if (empty($subject)) {
            return $this->sendMessage('⚠️ موضوع نمی‌تواند خالی باشد. لطفاً مجدداً وارد کنید:');
        }

        if (mb_strlen($subject) > 100) {
            return $this->sendMessage('⚠️ موضوع نمی‌تواند بیش از 100 کاراکتر باشد. لطفاً کوتاه‌تر وارد کنید:');
        }

        // Store subject
        $this->setUserData('ticket_subject', $subject);

        $text = "🎫 **ایجاد تیکت جدید**\n\n";
        $text .= "موضوع: **{$subject}**\n\n";
        $text .= "لطفاً توضیحات کاملی از مشکل خود ارائه دهید:\n";
        $text .= "(حداکثر 1000 کاراکتر)";

        // Set user step
        $this->setUserStep('ticket_description');

        $keyboard = $this->createInlineKeyboard([
            [
                ['text' => '❌ لغو', 'callback_data' => 'support_menu']
            ]
        ]);

        return $this->sendMessage($text, $keyboard);
    }

    /**
     * Handle description input
     */
    private function handleDescriptionInput(): array
    {
        $description = trim($this->text);

        if (empty($description)) {
            return $this->sendMessage('⚠️ توضیحات نمی‌تواند خالی باشد. لطفاً مجدداً وارد کنید:');
        }

        if (mb_strlen($description) > 1000) {
            return $this->sendMessage('⚠️ توضیحات نمی‌تواند بیش از 1000 کاراکتر باشد. لطفاً کوتاه‌تر وارد کنید:');
        }

        // Get stored data
        $category = $this->getUserData('ticket_category');
        $priority = $this->getUserData('ticket_priority');
        $subject = $this->getUserData('ticket_subject');

        // Create ticket
        $ticketId = $this->createTicket($category, $priority, $subject, $description);

        if ($ticketId) {
            // Clear user step and data
            $this->setUserStep('');
            $this->clearUserData(['ticket_category', 'ticket_priority', 'ticket_subject']);

            $text = "✅ **تیکت شما با موفقیت ایجاد شد**\n\n";
            $text .= "🎫 شماره تیکت: **#{$ticketId}**\n";
            $text .= "📝 موضوع: **{$subject}**\n\n";
            $text .= "تیم پشتیبانی در اسرع وقت به تیکت شما پاسخ خواهد داد.\n";
            $text .= "شما از طریق همین ربات از وضعیت تیکت مطلع خواهید شد.";

            $keyboard = $this->createInlineKeyboard([
                [
                    ['text' => '📋 مشاهده تیکت', 'callback_data' => "ticket_view_{$ticketId}"]
                ],
                [
                    ['text' => '🎧 پشتیبانی', 'callback_data' => 'support_menu'],
                    ['text' => '🏠 منو اصلی', 'callback_data' => 'main_menu']
                ]
            ]);

            // Notify admins
            $this->notifyAdminsNewTicket($ticketId, $subject, $priority);

            return $this->sendMessage($text, $keyboard);
        } else {
            return $this->sendMessage('❌ خطا در ایجاد تیکت. لطفاً مجدداً تلاش کنید.');
        }
    }

    /**
     * Show user tickets
     */
    private function showUserTickets(): array
    {
        $tickets = $this->getUserTickets();

        if (empty($tickets)) {
            $text = "📋 **تیکت‌های من**\n\n";
            $text .= "شما هنوز هیچ تیکتی ایجاد نکرده‌اید.\n\n";
            $text .= "برای ایجاد تیکت جدید از دکمه زیر استفاده کنید:";

            $keyboard = $this->createInlineKeyboard([
                [
                    ['text' => '🎫 ایجاد تیکت جدید', 'callback_data' => 'create_ticket']
                ],
                [
                    ['text' => '🔙 بازگشت', 'callback_data' => 'support_menu']
                ]
            ]);

            return $this->editMessage($text, $keyboard);
        }

        $text = "📋 **تیکت‌های من**\n\n";

        $keyboardButtons = [];
        foreach ($tickets as $ticket) {
            $statusEmoji = $this->getStatusEmoji($ticket['status']);
            $priorityEmoji = $this->getPriorityEmoji($ticket['priority']);

            $text .= "{$statusEmoji} **#{$ticket['id']}** - {$ticket['subject']}\n";
            $text .= "📅 {$ticket['created_at']} | {$priorityEmoji} {$ticket['priority']}\n\n";

            $keyboardButtons[] = [
                ['text' => "#{$ticket['id']} - " . mb_substr($ticket['subject'], 0, 20), 'callback_data' => "ticket_view_{$ticket['id']}"]
            ];
        }

        $keyboardButtons[] = [
            ['text' => '🎫 ایجاد تیکت جدید', 'callback_data' => 'create_ticket']
        ];
        $keyboardButtons[] = [
            ['text' => '🔙 بازگشت', 'callback_data' => 'support_menu']
        ];

        $keyboard = $this->createInlineKeyboard($keyboardButtons);

        return $this->editMessage($text, $keyboard);
    }

    /**
     * Show FAQ
     */
    private function showFAQ(): array
    {
        $text = "❓ **سوالات متداول**\n\n";

        $faqs = [
            "🔧 چگونه VPN را راه‌اندازی کنم؟" => "faq_setup",
            "💰 روش‌های پرداخت چیست؟" => "faq_payment",
            "📱 مشکل اتصال دارم" => "faq_connection",
            "⚙️ تنظیمات پیشرفته" => "faq_settings",
            "🔄 تمدید سرویس" => "faq_renewal"
        ];

        $keyboardButtons = [];
        foreach ($faqs as $question => $callback) {
            $keyboardButtons[] = [
                ['text' => $question, 'callback_data' => $callback]
            ];
        }

        $keyboardButtons[] = [
            ['text' => '🔙 بازگشت', 'callback_data' => 'support_menu']
        ];

        $keyboard = $this->createInlineKeyboard($keyboardButtons);

        return $this->editMessage($text, $keyboard);
    }

    /**
     * Create ticket in database
     */
    private function createTicket(string $category, string $priority, string $subject, string $description): ?int
    {
        try {
            $stmt = $this->database->prepare("
                INSERT INTO tickets (
                    user_id, category, priority, subject, description,
                    status, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, 'open', NOW(), NOW())
            ");

            $stmt->bind_param("issss", $this->fromId, $category, $priority, $subject, $description);
            $success = $stmt->execute();

            if ($success) {
                $ticketId = $this->database->getConnection()->insert_id;
                $stmt->close();

                $this->logAction('ticket_created', [
                    'ticket_id' => $ticketId,
                    'category' => $category,
                    'priority' => $priority
                ]);

                return $ticketId;
            }

            $stmt->close();
            return null;
        } catch (\Exception $e) {
            $this->logger->error('Failed to create ticket', [
                'user_id' => $this->fromId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get user tickets
     */
    private function getUserTickets(): array
    {
        try {
            $stmt = $this->database->prepare("
                SELECT id, subject, category, priority, status, created_at, updated_at
                FROM tickets
                WHERE user_id = ?
                ORDER BY created_at DESC
                LIMIT 10
            ");

            $stmt->bind_param("i", $this->fromId);
            $stmt->execute();
            $result = $stmt->get_result();
            $tickets = $result->fetch_all(MYSQLI_ASSOC);
            $stmt->close();

            return $tickets;
        } catch (\Exception $e) {
            $this->logger->error('Failed to get user tickets', [
                'user_id' => $this->fromId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Notify admins about new ticket
     */
    private function notifyAdminsNewTicket(int $ticketId, string $subject, string $priority): void
    {
        try {
            $adminIds = $this->config->get('telegram.admin_ids', []);

            $priorityEmoji = $this->getPriorityEmoji($priority);
            $text = "🎫 **تیکت جدید دریافت شد**\n\n";
            $text .= "🆔 شماره: **#{$ticketId}**\n";
            $text .= "📝 موضوع: **{$subject}**\n";
            $text .= "⚡ اولویت: {$priorityEmoji} **{$priority}**\n";
            $text .= "👤 کاربر: {$this->fromId}\n";
            $text .= "📅 زمان: " . date('Y-m-d H:i:s');

            $keyboard = $this->createInlineKeyboard([
                [
                    ['text' => '👁 مشاهده تیکت', 'callback_data' => "admin_ticket_{$ticketId}"],
                    ['text' => '💬 پاسخ سریع', 'callback_data' => "admin_reply_{$ticketId}"]
                ]
            ]);

            foreach ($adminIds as $adminId) {
                $this->sendMessageToUser($adminId, $text, $keyboard);
            }
        } catch (\Exception $e) {
            $this->logger->error('Failed to notify admins', [
                'ticket_id' => $ticketId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get status emoji
     */
    private function getStatusEmoji(string $status): string
    {
        return match ($status) {
            'open' => '🟢',
            'in_progress' => '🟡',
            'waiting' => '🔵',
            'closed' => '⚫',
            'resolved' => '✅',
            default => '❓'
        };
    }

    /**
     * Get priority emoji
     */
    private function getPriorityEmoji(string $priority): string
    {
        return match ($priority) {
            'urgent' => '🔴',
            'medium' => '🟡',
            'low' => '🟢',
            default => '⚪'
        };
    }

    /**
     * Set user data
     */
    private function setUserData(string $key, string $value): void
    {
        // Implementation would depend on your session/cache system
        // For now, using database
        $stmt = $this->database->prepare("
            INSERT INTO user_sessions (user_id, session_key, session_value, created_at)
            VALUES (?, ?, ?, NOW())
            ON DUPLICATE KEY UPDATE session_value = VALUES(session_value), created_at = NOW()
        ");
        $stmt->bind_param("iss", $this->fromId, $key, $value);
        $stmt->execute();
        $stmt->close();
    }

    /**
     * Get user data
     */
    private function getUserData(string $key): ?string
    {
        $stmt = $this->database->prepare("
            SELECT session_value FROM user_sessions
            WHERE user_id = ? AND session_key = ?
        ");
        $stmt->bind_param("is", $this->fromId, $key);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $stmt->close();

        return $row['session_value'] ?? null;
    }

    /**
     * Clear user data
     */
    private function clearUserData(array $keys): void
    {
        $placeholders = str_repeat('?,', count($keys) - 1) . '?';
        $stmt = $this->database->prepare("
            DELETE FROM user_sessions
            WHERE user_id = ? AND session_key IN ({$placeholders})
        ");

        $params = array_merge([$this->fromId], $keys);
        $types = 'i' . str_repeat('s', count($keys));
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $stmt->close();
    }

    /**
     * View ticket details
     */
    private function viewTicket(string $data): array
    {
        $ticketId = (int) str_replace('ticket_view_', '', $data);

        $ticket = $this->getTicketById($ticketId);
        if (!$ticket) {
            return $this->answerCallback('تیکت یافت نشد.');
        }

        $statusEmoji = $this->getStatusEmoji($ticket['status']);
        $priorityEmoji = $this->getPriorityEmoji($ticket['priority']);

        $text = "🎫 **جزئیات تیکت #{$ticket['id']}**\n\n";
        $text .= "📝 **موضوع:** {$ticket['subject']}\n";
        $text .= "📂 **دسته‌بندی:** {$ticket['category']}\n";
        $text .= "⚡ **اولویت:** {$priorityEmoji} {$ticket['priority']}\n";
        $text .= "📊 **وضعیت:** {$statusEmoji} {$ticket['status']}\n";
        $text .= "📅 **تاریخ ایجاد:** {$ticket['created_at']}\n\n";
        $text .= "💬 **توضیحات:**\n{$ticket['description']}";

        $keyboard = $this->createInlineKeyboard([
            [
                ['text' => '💬 پاسخ‌ها', 'callback_data' => "ticket_replies_{$ticketId}"],
                ['text' => '📝 پاسخ جدید', 'callback_data' => "ticket_reply_{$ticketId}"]
            ],
            [
                ['text' => '📋 تیکت‌های من', 'callback_data' => 'my_tickets'],
                ['text' => '🔙 پشتیبانی', 'callback_data' => 'support_menu']
            ]
        ]);

        return $this->editMessage($text, $keyboard);
    }

    /**
     * Get ticket by ID
     */
    private function getTicketById(int $ticketId): ?array
    {
        try {
            $stmt = $this->database->prepare("
                SELECT * FROM tickets
                WHERE id = ? AND user_id = ?
            ");

            $stmt->bind_param("ii", $ticketId, $this->fromId);
            $stmt->execute();
            $result = $stmt->get_result();
            $ticket = $result->fetch_assoc();
            $stmt->close();

            return $ticket ?: null;
        } catch (\Exception $e) {
            $this->logger->error('Failed to get ticket', [
                'ticket_id' => $ticketId,
                'user_id' => $this->fromId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Send message to user (helper method)
     */
    private function sendMessageToUser(int $userId, string $text, array $keyboard = []): void
    {
        try {
            // This would use your telegram service
            // For now, just log it
            $this->logger->info('Sending message to user', [
                'user_id' => $userId,
                'message' => $text
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Failed to send message to user', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }
}
