<?php

declare(strict_types=1);

namespace WeBot\Core;

/**
 * Error Tracker
 *
 * Advanced error tracking and analysis system with
 * pattern detection, grouping, and alerting.
 *
 * @package WeBot\Core
 * @version 2.0
 */
class ErrorTracker
{
    private AdvancedLogger $logger;
    private CacheManager $cache;
    private array $config;
    private array $errorGroups = [];

    public function __construct(
        AdvancedLogger $logger,
        CacheManager $cache,
        array $config = []
    ) {
        $this->logger = $logger;
        $this->cache = $cache;
        $this->config = array_merge([
            'enabled' => true,
            'group_similar_errors' => true,
            'max_error_groups' => 1000,
            'error_retention_days' => 30,
            'alert_thresholds' => [
                'error_rate' => 10, // errors per minute
                'critical_errors' => 5, // critical errors per hour
                'new_error_types' => 3 // new error types per hour
            ],
            'ignore_patterns' => [
                '/Notice:.*Undefined variable/',
                '/Warning:.*file_get_contents/',
            ]
        ], $config);
    }

    /**
     * Track an error
     */
    public function trackError(\Throwable $error, array $context = []): string
    {
        if (!$this->config['enabled']) {
            return '';
        }

        // Check if error should be ignored
        if ($this->shouldIgnoreError($error)) {
            return '';
        }

        $errorData = $this->extractErrorData($error, $context);
        $errorId = $this->generateErrorId($errorData);

        // Group similar errors
        if ($this->config['group_similar_errors']) {
            $groupId = $this->getOrCreateErrorGroup($errorData);
            $errorData['group_id'] = $groupId;
        }

        // Store error
        $this->storeError($errorId, $errorData);

        // Update statistics
        $this->updateErrorStatistics($errorData);

        // Check for alerts
        $this->checkAlertThresholds($errorData);

        // Log the error
        $this->logger->logException($error, 'error', array_merge($context, [
            'error_id' => $errorId,
            'group_id' => $errorData['group_id'] ?? null
        ]));

        return $errorId;
    }

    /**
     * Get error statistics
     */
    public function getErrorStatistics(int $hours = 24): array
    {
        $stats = [
            'total_errors' => 0,
            'unique_errors' => 0,
            'error_rate' => 0,
            'by_type' => [],
            'by_severity' => [],
            'by_hour' => [],
            'top_errors' => [],
            'recent_errors' => []
        ];

        $endTime = time();
        $startTime = $endTime - ($hours * 3600);

        // Get errors from cache
        for ($hour = 0; $hour < $hours; $hour++) {
            $hourKey = date('Y-m-d-H', $endTime - ($hour * 3600));
            $hourErrors = $this->cache->get("errors:hour:{$hourKey}", []);

            $stats['by_hour'][$hourKey] = count($hourErrors);
            $stats['total_errors'] += count($hourErrors);

            foreach ($hourErrors as $error) {
                // Count by type
                $type = $error['type'] ?? 'unknown';
                $stats['by_type'][$type] = ($stats['by_type'][$type] ?? 0) + 1;

                // Count by severity
                $severity = $error['severity'] ?? 'medium';
                $stats['by_severity'][$severity] = ($stats['by_severity'][$severity] ?? 0) + 1;
            }
        }

        // Calculate error rate (errors per minute)
        $stats['error_rate'] = $hours > 0 ? $stats['total_errors'] / ($hours * 60) : 0;

        // Get unique error groups
        $stats['unique_errors'] = count($this->getErrorGroups());

        // Get top errors
        $stats['top_errors'] = $this->getTopErrors(10);

        // Get recent errors
        $stats['recent_errors'] = $this->getRecentErrors(20);

        return $stats;
    }

    /**
     * Get error details
     */
    public function getErrorDetails(string $errorId): ?array
    {
        return $this->cache->get("error:{$errorId}");
    }

    /**
     * Get error group details
     */
    public function getErrorGroupDetails(string $groupId): ?array
    {
        $group = $this->cache->get("error_group:{$groupId}");

        if ($group) {
            $group['recent_occurrences'] = $this->getGroupRecentOccurrences($groupId, 10);
            $group['occurrence_timeline'] = $this->getGroupOccurrenceTimeline($groupId);
        }

        return $group;
    }

    /**
     * Mark error as resolved
     */
    public function markErrorResolved(string $errorId, string $resolvedBy = null): bool
    {
        $error = $this->getErrorDetails($errorId);

        if (!$error) {
            return false;
        }

        $error['status'] = 'resolved';
        $error['resolved_at'] = time();
        $error['resolved_by'] = $resolvedBy;

        $this->cache->set("error:{$errorId}", $error, $this->getRetentionSeconds());

        // Update group status if all errors in group are resolved
        if (isset($error['group_id'])) {
            $this->updateGroupStatus($error['group_id']);
        }

        return true;
    }

    /**
     * Get error trends
     */
    public function getErrorTrends(int $days = 7): array
    {
        $trends = [
            'daily_counts' => [],
            'error_types_trend' => [],
            'severity_trend' => [],
            'resolution_rate' => []
        ];

        for ($day = 0; $day < $days; $day++) {
            $date = date('Y-m-d', time() - ($day * 86400));
            $dayErrors = $this->getDayErrors($date);

            $trends['daily_counts'][$date] = count($dayErrors);

            // Analyze error types for this day
            $typeCount = [];
            $severityCount = [];
            $resolvedCount = 0;

            foreach ($dayErrors as $error) {
                $type = $error['type'] ?? 'unknown';
                $severity = $error['severity'] ?? 'medium';

                $typeCount[$type] = ($typeCount[$type] ?? 0) + 1;
                $severityCount[$severity] = ($severityCount[$severity] ?? 0) + 1;

                if (($error['status'] ?? 'open') === 'resolved') {
                    $resolvedCount++;
                }
            }

            $trends['error_types_trend'][$date] = $typeCount;
            $trends['severity_trend'][$date] = $severityCount;
            $trends['resolution_rate'][$date] = count($dayErrors) > 0 ?
                ($resolvedCount / count($dayErrors)) * 100 : 0;
        }

        return $trends;
    }

    /**
     * Generate error report
     */
    public function generateErrorReport(int $hours = 24): array
    {
        $stats = $this->getErrorStatistics($hours);
        $trends = $this->getErrorTrends(7);

        return [
            'report_generated_at' => time(),
            'period_hours' => $hours,
            'summary' => [
                'total_errors' => $stats['total_errors'],
                'unique_errors' => $stats['unique_errors'],
                'error_rate' => round($stats['error_rate'], 2),
                'most_common_type' => $this->getMostCommonErrorType($stats['by_type']),
                'critical_errors' => $stats['by_severity']['critical'] ?? 0
            ],
            'statistics' => $stats,
            'trends' => $trends,
            'recommendations' => $this->generateRecommendations($stats, $trends)
        ];
    }

    /**
     * Extract error data from exception
     */
    private function extractErrorData(\Throwable $error, array $context): array
    {
        return [
            'id' => uniqid('err_', true),
            'type' => get_class($error),
            'message' => $error->getMessage(),
            'code' => $error->getCode(),
            'file' => $error->getFile(),
            'line' => $error->getLine(),
            'trace' => $error->getTraceAsString(),
            'severity' => $this->determineSeverity($error),
            'fingerprint' => $this->generateFingerprint($error),
            'context' => $context,
            'environment' => [
                'php_version' => PHP_VERSION,
                'memory_usage' => memory_get_usage(true),
                'request_uri' => $_SERVER['REQUEST_URI'] ?? null,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null
            ],
            'timestamp' => time(),
            'status' => 'open'
        ];
    }

    /**
     * Generate unique error ID
     */
    private function generateErrorId(array $errorData): string
    {
        return $errorData['id'];
    }

    /**
     * Generate error fingerprint for grouping
     */
    private function generateFingerprint(\Throwable $error): string
    {
        $components = [
            get_class($error),
            $error->getFile(),
            $error->getLine(),
            // Normalize message to group similar errors
            preg_replace('/\d+/', 'N', $error->getMessage())
        ];

        return md5(implode('|', $components));
    }

    /**
     * Get or create error group
     */
    private function getOrCreateErrorGroup(array $errorData): string
    {
        $fingerprint = $errorData['fingerprint'];
        $groupId = "group_" . $fingerprint;

        $group = $this->cache->get("error_group:{$groupId}");

        if (!$group) {
            $group = [
                'id' => $groupId,
                'fingerprint' => $fingerprint,
                'type' => $errorData['type'],
                'message_template' => $this->createMessageTemplate($errorData['message']),
                'file' => $errorData['file'],
                'line' => $errorData['line'],
                'first_seen' => $errorData['timestamp'],
                'last_seen' => $errorData['timestamp'],
                'count' => 0,
                'status' => 'open'
            ];
        }

        $group['last_seen'] = $errorData['timestamp'];
        $group['count']++;

        $this->cache->set("error_group:{$groupId}", $group, $this->getRetentionSeconds());

        return $groupId;
    }

    /**
     * Store error data
     */
    private function storeError(string $errorId, array $errorData): void
    {
        // Store individual error
        $this->cache->set("error:{$errorId}", $errorData, $this->getRetentionSeconds());

        // Add to hourly index
        $hourKey = date('Y-m-d-H', $errorData['timestamp']);
        $hourErrors = $this->cache->get("errors:hour:{$hourKey}", []);
        $hourErrors[] = $errorData;
        $this->cache->set("errors:hour:{$hourKey}", $hourErrors, $this->getRetentionSeconds());

        // Add to daily index
        $dayKey = date('Y-m-d', $errorData['timestamp']);
        $dayErrors = $this->cache->get("errors:day:{$dayKey}", []);
        $dayErrors[] = $errorData;
        $this->cache->set("errors:day:{$dayKey}", $dayErrors, $this->getRetentionSeconds());
    }

    /**
     * Update error statistics
     */
    private function updateErrorStatistics(array $errorData): void
    {
        $statsKey = 'error_stats:' . date('Y-m-d-H');
        $stats = $this->cache->get($statsKey, [
            'total_count' => 0,
            'by_type' => [],
            'by_severity' => []
        ]);

        $stats['total_count']++;
        $stats['by_type'][$errorData['type']] = ($stats['by_type'][$errorData['type']] ?? 0) + 1;
        $stats['by_severity'][$errorData['severity']] = ($stats['by_severity'][$errorData['severity']] ?? 0) + 1;

        $this->cache->set($statsKey, $stats, 3600);
    }

    /**
     * Check alert thresholds
     */
    private function checkAlertThresholds(array $errorData): void
    {
        $thresholds = $this->config['alert_thresholds'];

        // Check error rate
        $currentHour = date('Y-m-d-H');
        $hourlyStats = $this->cache->get("error_stats:{$currentHour}", ['total_count' => 0]);

        if ($hourlyStats['total_count'] > $thresholds['error_rate']) {
            $this->triggerAlert('high_error_rate', [
                'current_rate' => $hourlyStats['total_count'],
                'threshold' => $thresholds['error_rate'],
                'hour' => $currentHour
            ]);
        }

        // Check critical errors
        if ($errorData['severity'] === 'critical') {
            $criticalCount = $hourlyStats['by_severity']['critical'] ?? 0;
            if ($criticalCount > $thresholds['critical_errors']) {
                $this->triggerAlert('high_critical_errors', [
                    'count' => $criticalCount,
                    'threshold' => $thresholds['critical_errors']
                ]);
            }
        }
    }

    /**
     * Determine error severity
     */
    private function determineSeverity(\Throwable $error): string
    {
        if ($error instanceof \Error) {
            return 'critical';
        }

        if ($error instanceof \RuntimeException) {
            return 'high';
        }

        if ($error instanceof \InvalidArgumentException) {
            return 'medium';
        }

        return 'low';
    }

    /**
     * Check if error should be ignored
     */
    private function shouldIgnoreError(\Throwable $error): bool
    {
        $message = $error->getMessage();

        foreach ($this->config['ignore_patterns'] as $pattern) {
            if (preg_match($pattern, $message)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Create message template for grouping
     */
    private function createMessageTemplate(string $message): string
    {
        // Replace numbers and specific values with placeholders
        $template = preg_replace('/\d+/', '{number}', $message);
        $template = preg_replace('/[a-f0-9]{32}/', '{hash}', $template);
        $template = preg_replace('/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/', '{email}', $template);

        return $template;
    }

    /**
     * Get retention period in seconds
     */
    private function getRetentionSeconds(): int
    {
        return $this->config['error_retention_days'] * 86400;
    }

    /**
     * Trigger alert
     */
    private function triggerAlert(string $type, array $data): void
    {
        $this->logger->critical("Error alert triggered: {$type}", $data);

        // Here you could integrate with external alerting systems
        // like Slack, email, PagerDuty, etc.
    }

    /**
     * Helper methods for statistics
     */
    private function getErrorGroups(): array
    {
        // Implementation would retrieve all error groups
        return [];
    }

    private function getTopErrors(int $limit): array
    {
        // Implementation would get most frequent errors
        return [];
    }

    private function getRecentErrors(int $limit): array
    {
        // Implementation would get most recent errors
        return [];
    }

    private function getDayErrors(string $date): array
    {
        return $this->cache->get("errors:day:{$date}", []);
    }

    private function getGroupRecentOccurrences(string $groupId, int $limit): array
    {
        // Implementation would get recent occurrences for a group
        return [];
    }

    private function getGroupOccurrenceTimeline(string $groupId): array
    {
        // Implementation would get occurrence timeline for a group
        return [];
    }

    private function updateGroupStatus(string $groupId): void
    {
        // Implementation would update group status based on individual error statuses
    }

    private function getMostCommonErrorType(array $byType): string
    {
        if (empty($byType)) {
            return 'none';
        }

        return array_keys($byType, max($byType))[0];
    }

    private function generateRecommendations(array $stats, array $trends): array
    {
        $recommendations = [];

        if ($stats['error_rate'] > 5) {
            $recommendations[] = 'High error rate detected. Consider reviewing recent code changes.';
        }

        if (($stats['by_severity']['critical'] ?? 0) > 0) {
            $recommendations[] = 'Critical errors detected. Immediate attention required.';
        }

        return $recommendations;
    }
}
