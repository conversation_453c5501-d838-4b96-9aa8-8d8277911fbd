<?php

declare(strict_types=1);

namespace WeBot\Utils;

use <PERSON>Bot\Contracts\HttpClientInterface;

// Create aliases for GuzzleHttp classes if not available
if (!class_exists('GuzzleHttp\Client')) {
    class_alias('WeBot\Utils\MockHttpClient', 'GuzzleHttp\Client');
}
if (!class_exists('GuzzleHttp\Exception\RequestException')) {
    class_alias('WeBot\Utils\MockRequestException', 'GuzzleHttp\Exception\RequestException');
}

/**
 * Mock HTTP Client for GuzzleHttp\Client
 * Used when GuzzleHttp is not available
 */
class MockHttpClient implements HttpClientInterface
{
    private array $config;

    public function __construct(array $config = [])
    {
        $this->config = $config;
    }

    public function request(string $method, string $uri, array $options = []): mixed
    {
        // Mock response for testing/development
        return new MockResponse([
            'status' => 200,
            'headers' => ['Content-Type' => 'application/json'],
            'body' => json_encode(['success' => true, 'data' => []])
        ]);
    }

    public function get(string $uri, array $options = []): mixed
    {
        return $this->request('GET', $uri, $options);
    }

    public function post(string $uri, array $options = []): mixed
    {
        return $this->request('POST', $uri, $options);
    }

    public function put(string $uri, array $options = []): mixed
    {
        return $this->request('PUT', $uri, $options);
    }

    public function delete(string $uri, array $options = []): mixed
    {
        return $this->request('DELETE', $uri, $options);
    }
}

/**
 * Mock Response for GuzzleHttp\Psr7\Response
 */
class MockResponse
{
    private array $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function getStatusCode(): int
    {
        return $this->data['status'] ?? 200;
    }

    public function getBody(): MockStream
    {
        return new MockStream($this->data['body'] ?? '');
    }

    public function getHeaders(): array
    {
        return $this->data['headers'] ?? [];
    }
}

/**
 * Mock Stream for response body
 */
class MockStream
{
    private string $content;

    public function __construct(string $content)
    {
        $this->content = $content;
    }

    public function getContents(): string
    {
        return $this->content;
    }

    public function __toString(): string
    {
        return $this->content;
    }
}

/**
 * Mock RequestException for GuzzleHttp compatibility
 */
class MockRequestException extends \Exception
{
    private $response;

    public function __construct(string $message = '', int $code = 0, \Throwable $previous = null, $response = null)
    {
        parent::__construct($message, $code, $previous);
        $this->response = $response;
    }

    public function getResponse()
    {
        return $this->response;
    }

    public function hasResponse(): bool
    {
        return $this->response !== null;
    }
}
