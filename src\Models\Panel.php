<?php

declare(strict_types=1);

namespace WeBot\Models;

use WeBot\Utils\Helper;

/**
 * Panel Model
 *
 * Represents a VPN panel (Marzban, Marzneshin, X-UI) in the WeBot system
 * with configuration, status tracking, and statistics.
 *
 * @package WeBot\Models
 * @version 2.0
 */
class Panel extends BaseModel
{
    protected string $table = 'panels';
    protected string $primaryKey = 'id';

    protected array $fillable = [
        'name',
        'type',
        'url',
        'username',
        'password',
        'token',
        'refresh_token',
        'token_expires_at',
        'status',
        'version',
        'last_sync_at',
        'last_health_check',
        'health_status',
        'config_data',
        'statistics',
        'is_active',
        'priority',
        'max_users',
        'current_users'
    ];

    protected array $hidden = [
        'password',
        'token',
        'refresh_token'
    ];

    protected array $casts = [
        'is_active' => 'bool',
        'priority' => 'int',
        'max_users' => 'int',
        'current_users' => 'int',
        'config_data' => 'json',
        'statistics' => 'json',
        'token_expires_at' => 'datetime',
        'last_sync_at' => 'datetime',
        'last_health_check' => 'datetime'
    ];

    // Panel types
    public const TYPE_MARZBAN = 'marzban';
    public const TYPE_MARZNESHIN = 'marzneshin';
    public const TYPE_XUI = 'x-ui';

    // Panel statuses
    public const STATUS_ONLINE = 'online';
    public const STATUS_OFFLINE = 'offline';
    public const STATUS_ERROR = 'error';
    public const STATUS_MAINTENANCE = 'maintenance';

    // Health statuses
    public const HEALTH_HEALTHY = 'healthy';
    public const HEALTH_WARNING = 'warning';
    public const HEALTH_CRITICAL = 'critical';
    public const HEALTH_UNKNOWN = 'unknown';

    /**
     * Get validation rules
     */
    protected function getValidationRules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'type' => 'required|string|in:marzban,marzneshin,x-ui',
            'url' => 'required|url|max:500',
            'username' => 'required|string|max:255',
            'password' => 'required|string|max:255',
            'status' => 'string|in:online,offline,error,maintenance',
            'health_status' => 'string|in:healthy,warning,critical,unknown',
            'is_active' => 'boolean',
            'priority' => 'integer|min:1|max:10',
            'max_users' => 'integer|min:0'
        ];
    }

    /**
     * Check if panel is online
     */
    public function isOnline(): bool
    {
        return $this->getAttribute('status') === self::STATUS_ONLINE;
    }

    /**
     * Check if panel is active
     */
    public function isActive(): bool
    {
        return (bool) $this->getAttribute('is_active');
    }

    /**
     * Check if panel is healthy
     */
    public function isHealthy(): bool
    {
        return $this->getAttribute('health_status') === self::HEALTH_HEALTHY;
    }

    /**
     * Check if panel has capacity
     */
    public function hasCapacity(): bool
    {
        $maxUsers = $this->getAttribute('max_users');
        $currentUsers = $this->getAttribute('current_users');

        if ($maxUsers === 0) {
            return true; // Unlimited
        }

        return $currentUsers < $maxUsers;
    }

    /**
     * Get capacity percentage
     */
    public function getCapacityPercentage(): float
    {
        $maxUsers = $this->getAttribute('max_users');
        $currentUsers = $this->getAttribute('current_users');

        if ($maxUsers === 0) {
            return 0; // Unlimited
        }

        return ($currentUsers / $maxUsers) * 100;
    }

    /**
     * Get remaining capacity
     */
    public function getRemainingCapacity(): int
    {
        $maxUsers = $this->getAttribute('max_users');
        $currentUsers = $this->getAttribute('current_users');

        if ($maxUsers === 0) {
            return PHP_INT_MAX; // Unlimited
        }

        return max(0, $maxUsers - $currentUsers);
    }

    /**
     * Get panel type title
     */
    public function getTypeTitle(): string
    {
        return match ($this->getAttribute('type')) {
            self::TYPE_MARZBAN => 'Marzban',
            self::TYPE_MARZNESHIN => 'Marzneshin',
            self::TYPE_XUI => 'X-UI',
            default => 'Unknown'
        };
    }

    /**
     * Get status title
     */
    public function getStatusTitle(): string
    {
        return match ($this->getAttribute('status')) {
            self::STATUS_ONLINE => 'آنلاین',
            self::STATUS_OFFLINE => 'آفلاین',
            self::STATUS_ERROR => 'خطا',
            self::STATUS_MAINTENANCE => 'تعمیرات',
            default => 'نامشخص'
        };
    }

    /**
     * Get health status title
     */
    public function getHealthStatusTitle(): string
    {
        return match ($this->getAttribute('health_status')) {
            self::HEALTH_HEALTHY => 'سالم',
            self::HEALTH_WARNING => 'هشدار',
            self::HEALTH_CRITICAL => 'بحرانی',
            self::HEALTH_UNKNOWN => 'نامشخص',
            default => 'نامشخص'
        };
    }

    /**
     * Get status emoji
     */
    public function getStatusEmoji(): string
    {
        return match ($this->getAttribute('status')) {
            self::STATUS_ONLINE => '🟢',
            self::STATUS_OFFLINE => '🔴',
            self::STATUS_ERROR => '🟠',
            self::STATUS_MAINTENANCE => '🟡',
            default => '⚪'
        };
    }

    /**
     * Get health status emoji
     */
    public function getHealthStatusEmoji(): string
    {
        return match ($this->getAttribute('health_status')) {
            self::HEALTH_HEALTHY => '💚',
            self::HEALTH_WARNING => '💛',
            self::HEALTH_CRITICAL => '❤️',
            self::HEALTH_UNKNOWN => '🤍',
            default => '🤍'
        };
    }

    /**
     * Update panel status
     */
    public function updateStatus(string $status, ?string $healthStatus = null): bool
    {
        $this->setAttribute('status', $status);
        $this->setAttribute('last_health_check', date('Y-m-d H:i:s'));

        if ($healthStatus !== null) {
            $this->setAttribute('health_status', $healthStatus);
        }

        return $this->save();
    }

    /**
     * Update panel statistics
     */
    public function updateStatistics(array $stats): bool
    {
        $currentStats = $this->getAttribute('statistics') ?? [];
        $updatedStats = array_merge($currentStats, $stats);

        $this->setAttribute('statistics', $updatedStats);
        $this->setAttribute('last_sync_at', date('Y-m-d H:i:s'));

        // Update current users if provided
        if (isset($stats['users_count'])) {
            $this->setAttribute('current_users', (int) $stats['users_count']);
        }

        return $this->save();
    }

    /**
     * Update token information
     */
    public function updateToken(string $token, ?string $refreshToken = null, ?int $expiresIn = null): bool
    {
        $this->setAttribute('token', $token);

        if ($refreshToken !== null) {
            $this->setAttribute('refresh_token', $refreshToken);
        }

        if ($expiresIn !== null) {
            $expiresAt = date('Y-m-d H:i:s', time() + $expiresIn);
            $this->setAttribute('token_expires_at', $expiresAt);
        }

        return $this->save();
    }

    /**
     * Check if token is expired
     */
    public function isTokenExpired(): bool
    {
        $expiresAt = $this->getAttribute('token_expires_at');

        if (!$expiresAt || !is_string($expiresAt)) {
            return true;
        }

        return strtotime($expiresAt) <= time();
    }

    /**
     * Get services from this panel
     */
    public function getServices(): array
    {
        return $this->database->select(
            'services',
            ['panel_id' => $this->getKey()]
        );
    }

    /**
     * Get active services count
     */
    public function getActiveServicesCount(): int
    {
        $sql = "SELECT COUNT(*) as count FROM `services` WHERE `panel_id` = ? AND `status` = ?";
        $result = $this->database->fetchRow($sql, [$this->getKey(), 'active'], 'is');

        return (int) ($result['count'] ?? 0);
    }

    /**
     * Get panel configuration
     */
    public function getConfig(): array
    {
        return $this->getAttribute('config_data') ?? [];
    }

    /**
     * Update panel configuration
     */
    public function updateConfig(array $config): bool
    {
        $currentConfig = $this->getConfig();
        $updatedConfig = array_merge($currentConfig, $config);

        $this->setAttribute('config_data', $updatedConfig);

        return $this->save();
    }

    /**
     * Get panel statistics
     */
    public function getStatistics(): array
    {
        $stats = $this->getAttribute('statistics') ?? [];

        return array_merge([
            'users_count' => 0,
            'active_users' => 0,
            'total_traffic' => 0,
            'today_traffic' => 0,
            'cpu_usage' => 0,
            'memory_usage' => 0,
            'disk_usage' => 0,
            'uptime' => 0
        ], $stats);
    }

    /**
     * Get formatted statistics
     */
    public function getFormattedStatistics(): array
    {
        $stats = $this->getStatistics();

        return [
            'users_count' => number_format($stats['users_count']),
            'active_users' => number_format($stats['active_users']),
            'total_traffic' => Helper::formatBytes($stats['total_traffic']),
            'today_traffic' => Helper::formatBytes($stats['today_traffic']),
            'cpu_usage' => $stats['cpu_usage'] . '%',
            'memory_usage' => $stats['memory_usage'] . '%',
            'disk_usage' => $stats['disk_usage'] . '%',
            'uptime' => Helper::formatDuration($stats['uptime'])
        ];
    }

    /**
     * Get panel summary
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->getKey(),
            'name' => $this->getAttribute('name'),
            'type' => $this->getTypeTitle(),
            'status' => $this->getStatusTitle(),
            'status_emoji' => $this->getStatusEmoji(),
            'health_status' => $this->getHealthStatusTitle(),
            'health_emoji' => $this->getHealthStatusEmoji(),
            'is_online' => $this->isOnline(),
            'is_active' => $this->isActive(),
            'is_healthy' => $this->isHealthy(),
            'has_capacity' => $this->hasCapacity(),
            'capacity_percentage' => round($this->getCapacityPercentage(), 2),
            'remaining_capacity' => $this->getRemainingCapacity(),
            'current_users' => $this->getAttribute('current_users'),
            'max_users' => $this->getAttribute('max_users'),
            'priority' => $this->getAttribute('priority'),
            'last_health_check' => $this->getAttribute('last_health_check'),
            'last_sync_at' => $this->getAttribute('last_sync_at'),
            'version' => $this->getAttribute('version')
        ];
    }

    /**
     * Scope: Active panels
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope: Online panels
     */
    public function scopeOnline($query)
    {
        return $query->where('status', self::STATUS_ONLINE);
    }

    /**
     * Scope: By type
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope: With capacity
     */
    public function scopeWithCapacity($query)
    {
        return $query->where(function ($q) {
            $q->where('max_users', 0)
              ->orWhereRaw('current_users < max_users');
        });
    }
}
