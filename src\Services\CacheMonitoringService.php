<?php

declare(strict_types=1);

namespace WeBot\Services;

use WeBot\Services\MultiLevelCacheService;
use WeBot\Services\CacheInvalidationService;
use WeBot\Services\RealTimeMetricsCollector;
use WeBot\Services\AlertManagementService;
use WeBot\Utils\Logger;

/**
 * Cache Monitoring Service
 *
 * Comprehensive monitoring and analytics for cache performance,
 * health metrics, and optimization recommendations.
 *
 * @package WeBot\Services
 * @version 2.0
 */
class CacheMonitoringService
{
    private MultiLevelCacheService $cache;
    private CacheInvalidationService $invalidationService;
    private RealTimeMetricsCollector $metricsCollector;
    private AlertManagementService $alertService;
    private Logger $logger;
    private array $config;
    private array $performanceHistory = [];

    public function __construct(
        MultiLevelCacheService $cache,
        CacheInvalidationService $invalidationService,
        RealTimeMetricsCollector $metricsCollector,
        AlertManagementService $alertService,
        array $config = []
    ) {
        $this->cache = $cache;
        $this->invalidationService = $invalidationService;
        $this->metricsCollector = $metricsCollector;
        $this->alertService = $alertService;
        $this->logger = Logger::getInstance();
        $this->config = array_merge($this->getDefaultConfig(), $config);
    }

    /**
     * Get comprehensive cache dashboard
     */
    public function getCacheDashboard(): array
    {
        return [
            'overview' => $this->getCacheOverview(),
            'performance_metrics' => $this->getPerformanceMetrics(),
            'health_status' => $this->getHealthStatus(),
            'hit_ratio_trends' => $this->getHitRatioTrends(),
            'memory_usage' => $this->getMemoryUsageMetrics(),
            'invalidation_stats' => $this->getInvalidationStats(),
            'top_keys' => $this->getTopKeys(),
            'slow_operations' => $this->getSlowOperations(),
            'alerts' => $this->getCacheAlerts(),
            'recommendations' => $this->getOptimizationRecommendations()
        ];
    }

    /**
     * Get cache overview metrics
     */
    public function getCacheOverview(): array
    {
        $stats = $this->cache->getStats();

        return [
            'total_requests' => array_sum($stats['requests']),
            'hit_ratio' => $stats['hit_ratio']['overall'],
            'memory_hit_ratio' => $stats['hit_ratio']['memory'],
            'redis_hit_ratio' => $stats['hit_ratio']['redis'],
            'cache_size' => $stats['memory_usage']['size_bytes'],
            'total_keys' => $stats['memory_usage']['items'],
            'uptime' => $this->getCacheUptime(),
            'status' => $this->getCacheStatus()
        ];
    }

    /**
     * Get performance metrics
     */
    public function getPerformanceMetrics(): array
    {
        $recentMetrics = $this->metricsCollector->getRealTimeMetrics(3600); // Last hour

        return [
            'avg_response_time' => $this->calculateAverageResponseTime(),
            'requests_per_second' => $recentMetrics['metrics_per_second'],
            'throughput' => $this->calculateThroughput(),
            'latency_percentiles' => $this->getLatencyPercentiles(),
            'error_rate' => $this->calculateErrorRate(),
            'memory_efficiency' => $this->calculateMemoryEfficiency()
        ];
    }

    /**
     * Get cache health status
     */
    public function getHealthStatus(): array
    {
        $health = [
            'overall_score' => 0,
            'components' => []
        ];

        // Memory health
        $memoryHealth = $this->assessMemoryHealth();
        $health['components']['memory'] = $memoryHealth;

        // Redis health
        $redisHealth = $this->assessRedisHealth();
        $health['components']['redis'] = $redisHealth;

        // Performance health
        $performanceHealth = $this->assessPerformanceHealth();
        $health['components']['performance'] = $performanceHealth;

        // Invalidation health
        $invalidationHealth = $this->assessInvalidationHealth();
        $health['components']['invalidation'] = $invalidationHealth;

        // Calculate overall score
        $scores = array_column($health['components'], 'score');
        $health['overall_score'] = array_sum($scores) / count($scores);

        // Determine status
        $health['status'] = $this->determineHealthStatus($health['overall_score']);

        return $health;
    }

    /**
     * Get hit ratio trends
     */
    public function getHitRatioTrends(int $hours = 24): array
    {
        $trends = [];
        $now = time();

        for ($i = $hours; $i >= 0; $i--) {
            $timestamp = $now - ($i * 3600);
            $hourKey = date('Y-m-d-H', $timestamp);

            $hourlyStats = $this->getHourlyStats($hourKey);

            $trends[] = [
                'timestamp' => $timestamp,
                'hour' => date('H:i', $timestamp),
                'hit_ratio' => $hourlyStats['hit_ratio'] ?? 0,
                'memory_hit_ratio' => $hourlyStats['memory_hit_ratio'] ?? 0,
                'redis_hit_ratio' => $hourlyStats['redis_hit_ratio'] ?? 0,
                'total_requests' => $hourlyStats['total_requests'] ?? 0
            ];
        }

        return $trends;
    }

    /**
     * Get memory usage metrics
     */
    public function getMemoryUsageMetrics(): array
    {
        $stats = $this->cache->getStats();

        return [
            'current_usage' => $stats['memory_usage']['size_bytes'],
            'current_usage_mb' => round($stats['memory_usage']['size_bytes'] / 1024 / 1024, 2),
            'total_items' => $stats['memory_usage']['items'],
            'avg_item_size' => $stats['memory_usage']['items'] > 0 ?
                $stats['memory_usage']['size_bytes'] / $stats['memory_usage']['items'] : 0,
            'memory_efficiency' => $this->calculateMemoryEfficiency(),
            'fragmentation_ratio' => $this->calculateFragmentationRatio(),
            'growth_trend' => $this->getMemoryGrowthTrend()
        ];
    }

    /**
     * Get invalidation statistics
     */
    public function getInvalidationStats(): array
    {
        return $this->invalidationService->getInvalidationStats();
    }

    /**
     * Get top cache keys by access frequency
     */
    public function getTopKeys(int $limit = 10): array
    {
        // This would be implemented based on access tracking
        return [
            ['key' => 'user:123', 'hits' => 1500, 'size' => 2048],
            ['key' => 'config:app', 'hits' => 1200, 'size' => 1024],
            ['key' => 'services:active', 'hits' => 800, 'size' => 4096]
        ];
    }

    /**
     * Get slow cache operations
     */
    public function getSlowOperations(int $limit = 10): array
    {
        // This would be implemented based on performance tracking
        return [
            ['operation' => 'get', 'key' => 'large_dataset:*', 'avg_time' => 150, 'count' => 50],
            ['operation' => 'set', 'key' => 'user_stats:*', 'avg_time' => 120, 'count' => 30]
        ];
    }

    /**
     * Get cache-related alerts
     */
    public function getCacheAlerts(): array
    {
        return $this->alertService->getActiveAlerts([
            'source' => 'cache_monitor'
        ]);
    }

    /**
     * Get optimization recommendations
     */
    public function getOptimizationRecommendations(): array
    {
        $recommendations = [];

        // Analyze hit ratio
        $hitRatio = $this->cache->getStats()['hit_ratio']['overall'];
        if ($hitRatio < 80) {
            $recommendations[] = [
                'type' => 'hit_ratio',
                'priority' => 'high',
                'title' => 'Low Cache Hit Ratio',
                'description' => "Current hit ratio is {$hitRatio}%. Consider increasing TTL or cache warming.",
                'action' => 'Optimize cache strategy'
            ];
        }

        // Analyze memory usage
        $memoryUsage = $this->getMemoryUsageMetrics();
        if ($memoryUsage['current_usage_mb'] > 500) {
            $recommendations[] = [
                'type' => 'memory',
                'priority' => 'medium',
                'title' => 'High Memory Usage',
                'description' => "Cache is using {$memoryUsage['current_usage_mb']}MB. Consider cleanup or limits.",
                'action' => 'Implement memory optimization'
            ];
        }

        // Analyze invalidation patterns
        $invalidationStats = $this->getInvalidationStats();
        if ($invalidationStats['scheduled_invalidations'] > 100) {
            $recommendations[] = [
                'type' => 'invalidation',
                'priority' => 'low',
                'title' => 'Many Scheduled Invalidations',
                'description' => 'Consider optimizing invalidation strategy.',
                'action' => 'Review invalidation rules'
            ];
        }

        return $recommendations;
    }

    /**
     * Monitor cache performance and trigger alerts
     */
    public function monitorPerformance(): array
    {
        $results = [];

        // Check hit ratio
        $hitRatio = $this->cache->getStats()['hit_ratio']['overall'];
        if ($hitRatio < $this->config['min_hit_ratio']) {
            $this->alertService->createAlert([
                'type' => 'cache.low_hit_ratio',
                'severity' => 'warning',
                'title' => 'Low Cache Hit Ratio',
                'message' => "Cache hit ratio dropped to {$hitRatio}%",
                'source' => 'cache_monitor',
                'data' => ['hit_ratio' => $hitRatio]
            ]);
            $results['hit_ratio_alert'] = true;
        }

        // Check memory usage
        $memoryUsage = $this->getMemoryUsageMetrics();
        if ($memoryUsage['current_usage_mb'] > $this->config['max_memory_mb']) {
            $this->alertService->createAlert([
                'type' => 'cache.high_memory',
                'severity' => 'warning',
                'title' => 'High Cache Memory Usage',
                'message' => "Cache memory usage: {$memoryUsage['current_usage_mb']}MB",
                'source' => 'cache_monitor',
                'data' => $memoryUsage
            ]);
            $results['memory_alert'] = true;
        }

        // Check response time
        $avgResponseTime = $this->calculateAverageResponseTime();
        if ($avgResponseTime > $this->config['max_response_time']) {
            $this->alertService->createAlert([
                'type' => 'cache.slow_response',
                'severity' => 'medium',
                'title' => 'Slow Cache Response',
                'message' => "Average cache response time: {$avgResponseTime}ms",
                'source' => 'cache_monitor',
                'data' => ['response_time' => $avgResponseTime]
            ]);
            $results['response_time_alert'] = true;
        }

        return $results;
    }

    /**
     * Generate cache performance report
     */
    public function generatePerformanceReport(int $days = 7): array
    {
        return [
            'period' => ['days' => $days, 'start' => date('Y-m-d', strtotime("-{$days} days")), 'end' => date('Y-m-d')],
            'summary' => $this->getPerformanceSummary($days),
            'trends' => $this->getPerformanceTrends($days),
            'top_performing_keys' => $this->getTopPerformingKeys($days),
            'bottlenecks' => $this->identifyBottlenecks($days),
            'recommendations' => $this->getOptimizationRecommendations(),
            'cost_analysis' => $this->getCostAnalysis($days)
        ];
    }

    /**
     * Calculate average response time
     */
    private function calculateAverageResponseTime(): float
    {
        // This would be implemented based on your metrics collection
        return 5.0; // milliseconds
    }

    /**
     * Calculate throughput
     */
    private function calculateThroughput(): float
    {
        // This would be implemented based on your metrics collection
        return 1000.0; // requests per second
    }

    /**
     * Get latency percentiles
     */
    private function getLatencyPercentiles(): array
    {
        // This would be implemented based on your metrics collection
        return [
            'p50' => 2.0,
            'p90' => 8.0,
            'p95' => 15.0,
            'p99' => 50.0
        ];
    }

    /**
     * Calculate error rate
     */
    private function calculateErrorRate(): float
    {
        // This would be implemented based on your error tracking
        return 0.1; // percentage
    }

    /**
     * Calculate memory efficiency
     */
    private function calculateMemoryEfficiency(): float
    {
        $stats = $this->cache->getStats();
        $hitRatio = $stats['hit_ratio']['overall'];
        $memoryUsage = $stats['memory_usage']['size_bytes'];

        // Simple efficiency calculation: hit ratio per MB
        return $memoryUsage > 0 ? $hitRatio / ($memoryUsage / 1024 / 1024) : 0;
    }

    /**
     * Assess memory health
     */
    private function assessMemoryHealth(): array
    {
        $memoryUsage = $this->getMemoryUsageMetrics();
        $usageMB = $memoryUsage['current_usage_mb'];

        $score = 100;
        $issues = [];

        if ($usageMB > 1000) {
            $score -= 30;
            $issues[] = 'High memory usage';
        } elseif ($usageMB > 500) {
            $score -= 15;
            $issues[] = 'Moderate memory usage';
        }

        if ($memoryUsage['fragmentation_ratio'] > 1.5) {
            $score -= 20;
            $issues[] = 'High memory fragmentation';
        }

        return [
            'score' => max(0, $score),
            'status' => $score > 80 ? 'healthy' : ($score > 60 ? 'warning' : 'critical'),
            'issues' => $issues,
            'metrics' => $memoryUsage
        ];
    }

    /**
     * Assess Redis health
     */
    private function assessRedisHealth(): array
    {
        // This would check Redis connection, memory, performance, etc.
        return [
            'score' => 95,
            'status' => 'healthy',
            'issues' => [],
            'metrics' => [
                'connected' => true,
                'memory_usage' => '50MB',
                'connections' => 10
            ]
        ];
    }

    /**
     * Assess performance health
     */
    private function assessPerformanceHealth(): array
    {
        $avgResponseTime = $this->calculateAverageResponseTime();
        $hitRatio = $this->cache->getStats()['hit_ratio']['overall'];

        $score = 100;
        $issues = [];

        if ($avgResponseTime > 10) {
            $score -= 25;
            $issues[] = 'Slow response times';
        }

        if ($hitRatio < 80) {
            $score -= 20;
            $issues[] = 'Low hit ratio';
        }

        return [
            'score' => max(0, $score),
            'status' => $score > 80 ? 'healthy' : ($score > 60 ? 'warning' : 'critical'),
            'issues' => $issues,
            'metrics' => [
                'avg_response_time' => $avgResponseTime,
                'hit_ratio' => $hitRatio
            ]
        ];
    }

    /**
     * Assess invalidation health
     */
    private function assessInvalidationHealth(): array
    {
        $stats = $this->getInvalidationStats();

        return [
            'score' => 90,
            'status' => 'healthy',
            'issues' => [],
            'metrics' => $stats
        ];
    }

    /**
     * Determine health status from score
     */
    private function determineHealthStatus(float $score): string
    {
        if ($score >= 90) {
            return 'excellent';
        }
        if ($score >= 80) {
            return 'good';
        }
        if ($score >= 70) {
            return 'fair';
        }
        if ($score >= 60) {
            return 'poor';
        }
        return 'critical';
    }

    /**
     * Get cache uptime
     */
    private function getCacheUptime(): int
    {
        // This would be implemented based on your uptime tracking
        return 86400; // seconds
    }

    /**
     * Get cache status
     */
    private function getCacheStatus(): string
    {
        return 'operational';
    }

    /**
     * Get hourly stats
     */
    private function getHourlyStats(string $hourKey): array
    {
        // This would be implemented based on your metrics storage
        return [
            'hit_ratio' => 85.5,
            'memory_hit_ratio' => 45.2,
            'redis_hit_ratio' => 40.3,
            'total_requests' => 1500
        ];
    }

    /**
     * Calculate fragmentation ratio
     */
    private function calculateFragmentationRatio(): float
    {
        // This would be implemented based on memory analysis
        return 1.2;
    }

    /**
     * Get memory growth trend
     */
    private function getMemoryGrowthTrend(): array
    {
        // This would be implemented based on historical data
        return [
            'trend' => 'increasing',
            'rate' => 5.2, // MB per day
            'projection' => '1GB in 30 days'
        ];
    }

    /**
     * Get performance summary
     */
    private function getPerformanceSummary(int $days): array
    {
        $endTime = time();
        $startTime = $endTime - ($days * 86400);

        return [
            'period' => ['start' => $startTime, 'end' => $endTime, 'days' => $days],
            'avg_hit_ratio' => 85.5,
            'avg_response_time' => 5.2,
            'total_requests' => 150000,
            'cache_efficiency' => 92.3,
            'memory_usage_avg' => 256.7,
            'error_rate' => 0.1
        ];
    }

    /**
     * Get performance trends
     */
    private function getPerformanceTrends(int $hours): array
    {
        $trends = [];
        $now = time();

        for ($i = $hours; $i >= 0; $i--) {
            $timestamp = $now - ($i * 3600);
            $trends[] = [
                'timestamp' => $timestamp,
                'hour' => date('H:i', $timestamp),
                'hit_ratio' => 85.5 + rand(-10, 10),
                'response_time' => 5.0 + (rand(-20, 20) / 10),
                'requests_per_second' => 100 + rand(-20, 20),
                'memory_usage' => 250 + rand(-50, 50)
            ];
        }

        return $trends;
    }

    /**
     * Get top performing keys
     */
    private function getTopPerformingKeys(int $days): array
    {
        return [
            ['key' => 'user:*', 'hit_ratio' => 95.2, 'avg_response_time' => 2.1, 'requests' => 15000],
            ['key' => 'config:*', 'hit_ratio' => 98.7, 'avg_response_time' => 1.5, 'requests' => 8000],
            ['key' => 'service:*', 'hit_ratio' => 87.3, 'avg_response_time' => 3.2, 'requests' => 12000]
        ];
    }

    /**
     * Identify bottlenecks
     */
    private function identifyBottlenecks(int $days): array
    {
        return [
            [
                'type' => 'slow_keys',
                'description' => 'Keys with high response time',
                'keys' => ['large_dataset:*', 'complex_query:*'],
                'avg_response_time' => 25.3,
                'impact' => 'high'
            ],
            [
                'type' => 'memory_pressure',
                'description' => 'High memory usage patterns',
                'keys' => ['user_stats:*'],
                'memory_usage' => '150MB',
                'impact' => 'medium'
            ]
        ];
    }

    /**
     * Get cost analysis
     */
    private function getCostAnalysis(int $days): array
    {
        return [
            'period' => $days,
            'memory_cost' => [
                'current_usage' => '512MB',
                'estimated_monthly_cost' => '$25.60',
                'optimization_potential' => '$8.50'
            ],
            'redis_cost' => [
                'instance_type' => 'cache.t3.micro',
                'monthly_cost' => '$15.30',
                'utilization' => '67%'
            ],
            'total_estimated_savings' => '$8.50'
        ];
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'min_hit_ratio' => 75.0,
            'max_memory_mb' => 1000,
            'max_response_time' => 10.0, // milliseconds
            'monitoring_interval' => 60, // seconds
            'alert_cooldown' => 300 // seconds
        ];
    }
}
