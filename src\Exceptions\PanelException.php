<?php

declare(strict_types=1);

namespace WeBot\Exceptions;

/**
 * Panel Exception
 *
 * Thrown when panel operations fail.
 *
 * @package WeBot\Exceptions
 * @version 2.0
 */
class PanelException extends WeBotException
{
    public const CONNECTION_FAILED = 2001;
    public const AUTHENTICATION_FAILED = 2002;
    public const USER_NOT_FOUND = 2003;
    public const USER_CREATION_FAILED = 2004;
    public const USER_UPDATE_FAILED = 2005;
    public const USER_DELETION_FAILED = 2006;
    public const QUOTA_EXCEEDED = 2007;
    public const PANEL_UNAVAILABLE = 2008;

    protected string $panelType = '';
    protected string $panelUrl = '';
    protected string $operation = '';

    public function __construct(
        string $message = 'Panel operation failed',
        int $code = self::CONNECTION_FAILED,
        string $panelType = '',
        string $operation = ''
    ) {
        parent::__construct($message, $code);

        $this->panelType = $panelType;
        $this->operation = $operation;

        $this->setUserMessage($this->getUserFriendlyMessage($code));
    }

    /**
     * Get panel type
     */
    public function getPanelType(): string
    {
        return $this->panelType;
    }

    /**
     * Set panel type
     */
    public function setPanelType(string $panelType): self
    {
        $this->panelType = $panelType;
        return $this;
    }

    /**
     * Get panel URL
     */
    public function getPanelUrl(): string
    {
        return $this->panelUrl;
    }

    /**
     * Set panel URL
     */
    public function setPanelUrl(string $panelUrl): self
    {
        $this->panelUrl = $panelUrl;
        return $this;
    }

    /**
     * Get operation
     */
    public function getOperation(): string
    {
        return $this->operation;
    }

    /**
     * Set operation
     */
    public function setOperation(string $operation): self
    {
        $this->operation = $operation;
        return $this;
    }

    /**
     * Get user-friendly message based on error code
     */
    private function getUserFriendlyMessage(int $code): string
    {
        return match ($code) {
            self::CONNECTION_FAILED => 'خطا در اتصال به پنل. لطفاً بعداً تلاش کنید.',
            self::AUTHENTICATION_FAILED => 'خطا در احراز هویت پنل.',
            self::USER_NOT_FOUND => 'کاربر در پنل یافت نشد.',
            self::USER_CREATION_FAILED => 'خطا در ایجاد کاربر در پنل.',
            self::USER_UPDATE_FAILED => 'خطا در به‌روزرسانی کاربر در پنل.',
            self::USER_DELETION_FAILED => 'خطا در حذف کاربر از پنل.',
            self::QUOTA_EXCEEDED => 'ظرفیت پنل تکمیل شده است.',
            self::PANEL_UNAVAILABLE => 'پنل در حال حاضر در دسترس نیست.',
            default => 'خطا در عملیات پنل.',
        };
    }

    /**
     * Create connection failed exception
     */
    public static function connectionFailed(string $panelType, string $url): self
    {
        return (new self(
            "Failed to connect to {$panelType} panel at {$url}",
            self::CONNECTION_FAILED,
            $panelType
        ))->setPanelUrl($url);
    }

    /**
     * Create authentication failed exception
     */
    public static function authenticationFailed(string $panelType): self
    {
        return new self(
            "Authentication failed for {$panelType} panel",
            self::AUTHENTICATION_FAILED,
            $panelType
        );
    }

    /**
     * Create user not found exception
     */
    public static function userNotFound(string $userId, string $panelType): self
    {
        return new self(
            "User {$userId} not found in {$panelType} panel",
            self::USER_NOT_FOUND,
            $panelType,
            'get_user'
        );
    }

    /**
     * Create user creation failed exception
     */
    public static function userCreationFailed(string $panelType, string $reason = ''): self
    {
        return new self(
            "Failed to create user in {$panelType} panel" . ($reason ? ": {$reason}" : ''),
            self::USER_CREATION_FAILED,
            $panelType,
            'create_user'
        );
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'panel_type' => $this->getPanelType(),
            'panel_url' => $this->getPanelUrl(),
            'operation' => $this->getOperation(),
        ]);
    }
}
