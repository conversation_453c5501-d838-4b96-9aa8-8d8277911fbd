<?php
/**
 * Redis Connection Test for WeBot
 * 
 * This script tests Redis connectivity and verifies
 * that Redis can be used for caching and sessions.
 */

declare(strict_types=1);

echo "=== WeBot Redis Connection Test ===\n\n";

// Load the application
try {
    require_once __DIR__ . '/autoload.php';
    echo "✅ Application loaded successfully\n\n";
} catch (Exception $e) {
    echo "❌ Failed to load application: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 1: Redis extension availability
echo "1. Redis Extension Availability Test:\n";
$redisExtensionOk = false;

if (extension_loaded('redis')) {
    echo "   ✅ Redis extension is loaded\n";
    $redisExtensionOk = true;
} else {
    echo "   ❌ Redis extension is not loaded\n";
    echo "   ℹ️  Redis functionality will be limited without the extension\n";
}

// Test 2: Redis class availability
echo "\n2. Redis Class Availability Test:\n";
$redisClassOk = false;

if (class_exists('Redis')) {
    echo "   ✅ Redis class is available\n";
    $redisClassOk = true;
} else {
    echo "   ❌ Redis class is not available\n";
}

// Test 3: Redis connection attempt
echo "\n3. Redis Connection Attempt Test:\n";
$redisConnectionOk = false;

if ($redisExtensionOk && $redisClassOk) {
    try {
        $redis = new Redis();
        
        // Try to connect to Redis
        $connected = $redis->connect('127.0.0.1', 6379, 2); // 2 second timeout
        
        if ($connected) {
            echo "   ✅ Redis connection established\n";
            $redisConnectionOk = true;
            
            // Test ping
            $pong = $redis->ping();
            if ($pong) {
                echo "   ✅ Redis ping successful\n";
            } else {
                echo "   ❌ Redis ping failed\n";
            }
            
            $redis->close();
        } else {
            echo "   ❌ Redis connection failed\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Redis connection error: " . $e->getMessage() . "\n";
    }
} else {
    echo "   ⚠️  Skipped Redis connection test (extension/class not available)\n";
}

// Test 4: Alternative Redis implementations
echo "\n4. Alternative Redis Implementations Test:\n";
$alternativeOk = false;

// Check for Predis (PHP Redis client)
if (class_exists('Predis\Client')) {
    echo "   ✅ Predis client is available\n";
    
    try {
        $predis = new Predis\Client([
            'scheme' => 'tcp',
            'host'   => '127.0.0.1',
            'port'   => 6379,
            'timeout' => 2
        ]);
        
        $predis->ping();
        echo "   ✅ Predis connection successful\n";
        $alternativeOk = true;
    } catch (Exception $e) {
        echo "   ❌ Predis connection failed: " . $e->getMessage() . "\n";
    }
} else {
    echo "   ⚠️  Predis client not available\n";
}

// Test 5: File-based cache fallback
echo "\n5. File-based Cache Fallback Test:\n";
$fileCacheOk = true;

$cacheDir = 'storage/cache';
if (is_dir($cacheDir) && is_writable($cacheDir)) {
    echo "   ✅ Cache directory is writable\n";
    
    // Test writing a cache file
    $testCacheFile = $cacheDir . '/redis_test.cache';
    $testData = json_encode(['test' => 'data', 'timestamp' => time()]);
    
    if (file_put_contents($testCacheFile, $testData)) {
        echo "   ✅ Can write to cache directory\n";
        
        // Test reading the cache file
        $readData = file_get_contents($testCacheFile);
        if ($readData === $testData) {
            echo "   ✅ Can read from cache directory\n";
        } else {
            echo "   ❌ Cache read/write mismatch\n";
            $fileCacheOk = false;
        }
        
        // Clean up
        unlink($testCacheFile);
    } else {
        echo "   ❌ Cannot write to cache directory\n";
        $fileCacheOk = false;
    }
} else {
    echo "   ❌ Cache directory not writable\n";
    $fileCacheOk = false;
}

// Test 6: Cache configuration
echo "\n6. Cache Configuration Test:\n";
$cacheConfigOk = true;

try {
    $cacheConfig = config('cache');
    if (is_array($cacheConfig)) {
        echo "   ✅ Cache configuration loaded\n";
        
        $defaultDriver = $cacheConfig['default'] ?? 'file';
        echo "   ℹ️  Default cache driver: {$defaultDriver}\n";
        
        if (isset($cacheConfig['stores'])) {
            $storeCount = count($cacheConfig['stores']);
            echo "   ✅ {$storeCount} cache store(s) configured\n";
        } else {
            echo "   ❌ Cache stores not configured\n";
            $cacheConfigOk = false;
        }
    } else {
        echo "   ❌ Cache configuration not loaded properly\n";
        $cacheConfigOk = false;
    }
} catch (Exception $e) {
    echo "   ❌ Failed to load cache configuration: " . $e->getMessage() . "\n";
    $cacheConfigOk = false;
}

// Test 7: Session storage options
echo "\n7. Session Storage Options Test:\n";
$sessionOk = true;

$sessionDir = 'storage/sessions';
if (is_dir($sessionDir) && is_writable($sessionDir)) {
    echo "   ✅ Session directory is writable\n";
} else {
    echo "   ❌ Session directory not writable\n";
    $sessionOk = false;
}

// Check session configuration
$sessionSavePath = session_save_path();
echo "   ℹ️  Current session save path: " . ($sessionSavePath ?: 'default') . "\n";

echo "\n=== Overall Status ===\n";

if ($redisConnectionOk) {
    echo "✅ Redis is fully functional!\n";
    echo "ℹ️  Redis can be used for caching and sessions\n";
} elseif ($alternativeOk) {
    echo "✅ Alternative Redis client (Predis) is working!\n";
    echo "ℹ️  Can use Predis for Redis functionality\n";
} elseif ($fileCacheOk && $sessionOk && $cacheConfigOk) {
    echo "⚠️  Redis not available, but file-based alternatives work\n";
    echo "ℹ️  System can function with file-based caching and sessions\n";
    echo "🔧 To enable Redis:\n";
    echo "   1. Install Redis server\n";
    echo "   2. Install PHP Redis extension or Predis\n";
    echo "   3. Start Redis service\n";
    echo "   4. Update cache configuration to use Redis\n";
} else {
    echo "❌ Neither Redis nor file-based alternatives are working properly\n";
    echo "\n🔧 To fix caching issues:\n";
    echo "   1. Install Redis server and PHP extension\n";
    echo "   2. Or ensure file cache directory is writable\n";
    echo "   3. Check cache configuration\n";
    echo "   4. Verify session directory permissions\n";
    exit(1);
}

exit(0);
