<?php

declare(strict_types=1);

namespace WeBot\Infrastructure\LoadBalancer;

use WeBot\Core\CacheManager;
use WeBot\Services\DatabaseService;
use WeBot\Utils\Logger;
use WeBot\Exceptions\WeBotException;

/**
 * Load Balancer Manager
 *
 * Manages load balancing across multiple server instances,
 * health checks, and traffic distribution strategies.
 *
 * @package WeBot\Infrastructure\LoadBalancer
 * @version 2.0
 */
class LoadBalancerManager
{
    private CacheManager $cache;
    private DatabaseService $database;
    private Logger $logger;
    private array $config;
    private array $servers = [];
    private array $healthChecks = [];
    private string $algorithm = 'round_robin';
    private int $currentServerIndex = 0;

    public function __construct(
        CacheManager $cache,
        DatabaseService $database,
        array $config = []
    ) {
        $this->cache = $cache;
        $this->database = $database;
        $this->logger = Logger::getInstance();
        $this->config = array_merge($this->getDefaultConfig(), $config);

        $this->initializeServers();
        $this->initializeHealthChecks();
    }

    /**
     * Get next available server
     */
    public function getNextServer(array $request = []): array
    {
        try {
            $availableServers = $this->getHealthyServers();

            if (empty($availableServers)) {
                throw new WeBotException("No healthy servers available");
            }

            $server = match ($this->algorithm) {
                'round_robin' => $this->roundRobinSelection($availableServers),
                'least_connections' => $this->leastConnectionsSelection($availableServers),
                'weighted_round_robin' => $this->weightedRoundRobinSelection($availableServers),
                'ip_hash' => $this->ipHashSelection($availableServers, $request),
                'least_response_time' => $this->leastResponseTimeSelection($availableServers),
                'resource_based' => $this->resourceBasedSelection($availableServers),
                default => $this->roundRobinSelection($availableServers)
            };

            // Update server metrics
            $this->updateServerMetrics($server['id'], 'request_assigned');

            return $server;
        } catch (\Exception $e) {
            $this->logger->error("Load balancer server selection failed", [
                'algorithm' => $this->algorithm,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Add server to pool
     */
    public function addServer(array $serverConfig): bool
    {
        try {
            $serverId = $serverConfig['id'] ?? uniqid('server_');

            $server = [
                'id' => $serverId,
                'host' => $serverConfig['host'],
                'port' => $serverConfig['port'] ?? 80,
                'weight' => $serverConfig['weight'] ?? 1,
                'max_connections' => $serverConfig['max_connections'] ?? 1000,
                'current_connections' => 0,
                'status' => 'healthy',
                'last_health_check' => time(),
                'response_time' => 0,
                'cpu_usage' => 0,
                'memory_usage' => 0,
                'added_at' => time()
            ];

            $this->servers[$serverId] = $server;

            // Store in cache
            $this->cache->set('load_balancer:servers', $this->servers, 3600);

            $this->logger->info("Server added to load balancer", [
                'server_id' => $serverId,
                'host' => $server['host'],
                'port' => $server['port']
            ]);

            return true;
        } catch (\Exception $e) {
            $this->logger->error("Failed to add server", [
                'server_config' => $serverConfig,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Remove server from pool
     */
    public function removeServer(string $serverId): bool
    {
        try {
            if (!isset($this->servers[$serverId])) {
                throw new WeBotException("Server not found: {$serverId}");
            }

            // Gracefully drain connections
            $this->drainServerConnections($serverId);

            unset($this->servers[$serverId]);

            // Update cache
            $this->cache->set('load_balancer:servers', $this->servers, 3600);

            $this->logger->info("Server removed from load balancer", [
                'server_id' => $serverId
            ]);

            return true;
        } catch (\Exception $e) {
            $this->logger->error("Failed to remove server", [
                'server_id' => $serverId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Perform health checks on all servers
     */
    public function performHealthChecks(): array
    {
        $results = [];

        foreach ($this->servers as $serverId => $server) {
            try {
                $healthResult = $this->checkServerHealth($server);

                // Update server status
                $this->servers[$serverId]['status'] = $healthResult['status'];
                $this->servers[$serverId]['last_health_check'] = time();
                $this->servers[$serverId]['response_time'] = $healthResult['response_time'];

                if (isset($healthResult['metrics'])) {
                    $this->servers[$serverId]['cpu_usage'] = $healthResult['metrics']['cpu_usage'] ?? 0;
                    $this->servers[$serverId]['memory_usage'] = $healthResult['metrics']['memory_usage'] ?? 0;
                }

                $results[$serverId] = $healthResult;
            } catch (\Exception $e) {
                $this->servers[$serverId]['status'] = 'unhealthy';
                $this->servers[$serverId]['last_health_check'] = time();

                $results[$serverId] = [
                    'status' => 'unhealthy',
                    'error' => $e->getMessage(),
                    'response_time' => 0
                ];

                $this->logger->warning("Health check failed for server", [
                    'server_id' => $serverId,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Update cache
        $this->cache->set('load_balancer:servers', $this->servers, 3600);

        return $results;
    }

    /**
     * Get load balancer statistics
     */
    public function getStatistics(): array
    {
        $totalServers = count($this->servers);
        $healthyServers = count($this->getHealthyServers());
        $totalConnections = array_sum(array_column($this->servers, 'current_connections'));

        return [
            'total_servers' => $totalServers,
            'healthy_servers' => $healthyServers,
            'unhealthy_servers' => $totalServers - $healthyServers,
            'total_connections' => $totalConnections,
            'algorithm' => $this->algorithm,
            'servers' => $this->servers,
            'load_distribution' => $this->calculateLoadDistribution(),
            'performance_metrics' => $this->getPerformanceMetrics(),
            'last_updated' => time()
        ];
    }

    /**
     * Set load balancing algorithm
     */
    public function setAlgorithm(string $algorithm): bool
    {
        $validAlgorithms = [
            'round_robin',
            'least_connections',
            'weighted_round_robin',
            'ip_hash',
            'least_response_time',
            'resource_based'
        ];

        if (!in_array($algorithm, $validAlgorithms)) {
            throw new WeBotException("Invalid algorithm: {$algorithm}");
        }

        $this->algorithm = $algorithm;

        $this->logger->info("Load balancing algorithm changed", [
            'new_algorithm' => $algorithm
        ]);

        return true;
    }

    /**
     * Round robin server selection
     */
    private function roundRobinSelection(array $servers): array
    {
        $serverList = array_values($servers);
        $server = $serverList[$this->currentServerIndex % count($serverList)];
        $this->currentServerIndex++;

        return $server;
    }

    /**
     * Least connections server selection
     */
    private function leastConnectionsSelection(array $servers): array
    {
        $minConnections = min(array_column($servers, 'current_connections'));

        foreach ($servers as $server) {
            if ($server['current_connections'] === $minConnections) {
                return $server;
            }
        }

        return array_values($servers)[0];
    }

    /**
     * Weighted round robin server selection
     */
    private function weightedRoundRobinSelection(array $servers): array
    {
        $totalWeight = array_sum(array_column($servers, 'weight'));
        $random = rand(1, $totalWeight);
        $currentWeight = 0;

        foreach ($servers as $server) {
            $currentWeight += $server['weight'];
            if ($random <= $currentWeight) {
                return $server;
            }
        }

        return array_values($servers)[0];
    }

    /**
     * IP hash server selection
     */
    private function ipHashSelection(array $servers, array $request): array
    {
        $clientIP = $request['client_ip'] ?? $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
        $hash = crc32($clientIP);
        $serverIndex = abs($hash) % count($servers);

        return array_values($servers)[$serverIndex];
    }

    /**
     * Least response time server selection
     */
    private function leastResponseTimeSelection(array $servers): array
    {
        $minResponseTime = min(array_column($servers, 'response_time'));

        foreach ($servers as $server) {
            if ($server['response_time'] === $minResponseTime) {
                return $server;
            }
        }

        return array_values($servers)[0];
    }

    /**
     * Resource-based server selection
     */
    private function resourceBasedSelection(array $servers): array
    {
        $bestScore = 0;
        $bestServer = array_values($servers)[0];

        foreach ($servers as $server) {
            // Calculate resource score (lower is better)
            $cpuScore = 100 - $server['cpu_usage'];
            $memoryScore = 100 - $server['memory_usage'];
            $connectionScore = 100 - (($server['current_connections'] / $server['max_connections']) * 100);

            $totalScore = ($cpuScore + $memoryScore + $connectionScore) / 3;

            if ($totalScore > $bestScore) {
                $bestScore = $totalScore;
                $bestServer = $server;
            }
        }

        return $bestServer;
    }

    /**
     * Get healthy servers
     */
    private function getHealthyServers(): array
    {
        return array_filter($this->servers, function ($server) {
            return $server['status'] === 'healthy';
        });
    }

    /**
     * Check server health
     */
    private function checkServerHealth(array $server): array
    {
        $startTime = microtime(true);

        try {
            // Perform HTTP health check
            $url = "http://{$server['host']}:{$server['port']}/health";
            $context = stream_context_create([
                'http' => [
                    'timeout' => $this->config['health_check_timeout'],
                    'method' => 'GET'
                ]
            ]);

            $response = file_get_contents($url, false, $context);
            $responseTime = (microtime(true) - $startTime) * 1000; // ms

            if ($response === false) {
                throw new \Exception("Health check request failed");
            }

            $healthData = json_decode($response, true);

            return [
                'status' => 'healthy',
                'response_time' => $responseTime,
                'metrics' => $healthData['metrics'] ?? []
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'response_time' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Update server metrics
     */
    private function updateServerMetrics(string $serverId, string $metric, $value = 1): void
    {
        if (!isset($this->servers[$serverId])) {
            return;
        }

        switch ($metric) {
            case 'request_assigned':
                $this->servers[$serverId]['current_connections']++;
                break;

            case 'request_completed':
                $this->servers[$serverId]['current_connections'] = max(0, $this->servers[$serverId]['current_connections'] - 1);
                break;
        }

        // Update cache
        $this->cache->set('load_balancer:servers', $this->servers, 3600);
    }

    /**
     * Drain server connections
     */
    private function drainServerConnections(string $serverId): void
    {
        // Mark server as draining
        $this->servers[$serverId]['status'] = 'draining';

        // Wait for connections to finish (simplified implementation)
        $maxWaitTime = $this->config['drain_timeout'];
        $waitTime = 0;

        while ($this->servers[$serverId]['current_connections'] > 0 && $waitTime < $maxWaitTime) {
            sleep(1);
            $waitTime++;
        }

        $this->logger->info("Server connections drained", [
            'server_id' => $serverId,
            'wait_time' => $waitTime,
            'remaining_connections' => $this->servers[$serverId]['current_connections']
        ]);
    }

    /**
     * Calculate load distribution
     */
    private function calculateLoadDistribution(): array
    {
        $distribution = [];
        $totalConnections = array_sum(array_column($this->servers, 'current_connections'));

        foreach ($this->servers as $serverId => $server) {
            $percentage = $totalConnections > 0 ?
                ($server['current_connections'] / $totalConnections) * 100 : 0;

            $distribution[$serverId] = [
                'connections' => $server['current_connections'],
                'percentage' => round($percentage, 2)
            ];
        }

        return $distribution;
    }

    /**
     * Get performance metrics
     */
    private function getPerformanceMetrics(): array
    {
        $healthyServers = $this->getHealthyServers();

        if (empty($healthyServers)) {
            return [
                'avg_response_time' => 0,
                'avg_cpu_usage' => 0,
                'avg_memory_usage' => 0
            ];
        }

        return [
            'avg_response_time' => array_sum(array_column($healthyServers, 'response_time')) / count($healthyServers),
            'avg_cpu_usage' => array_sum(array_column($healthyServers, 'cpu_usage')) / count($healthyServers),
            'avg_memory_usage' => array_sum(array_column($healthyServers, 'memory_usage')) / count($healthyServers)
        ];
    }

    /**
     * Initialize servers from configuration
     */
    private function initializeServers(): void
    {
        // Load servers from cache or config
        $cachedServers = $this->cache->get('load_balancer:servers');

        if ($cachedServers) {
            $this->servers = $cachedServers;
        } else {
            // Initialize with default servers from config
            foreach ($this->config['default_servers'] as $serverConfig) {
                $this->addServer($serverConfig);
            }
        }
    }

    /**
     * Initialize health checks
     */
    private function initializeHealthChecks(): void
    {
        // Schedule periodic health checks
        $this->healthChecks = [
            'interval' => $this->config['health_check_interval'],
            'timeout' => $this->config['health_check_timeout'],
            'last_check' => 0
        ];
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'health_check_interval' => 30,     // seconds
            'health_check_timeout' => 5,       // seconds
            'drain_timeout' => 60,             // seconds
            'default_algorithm' => 'round_robin',
            'default_servers' => [
                [
                    'id' => 'server1',
                    'host' => 'localhost',
                    'port' => 8080,
                    'weight' => 1
                ]
            ]
        ];
    }
}
