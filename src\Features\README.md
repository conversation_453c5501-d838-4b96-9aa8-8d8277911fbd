# Features Directory
## پوشه ویژگی‌های سیستم

این پوشه شامل تمام ویژگی‌های اصلی WeBot است که بر اساس معماری **Domain-Driven Design (DDD)** و **Feature-First Architecture** سازماندهی شده‌اند.

---

## 🏗️ معماری Feature-First

هر feature شامل لایه‌های زیر است:

```
Feature/
├── Controllers/     # کنترلرهای HTTP و Telegram
├── Services/        # منطق تجاری و Business Logic
├── Repositories/    # لایه دسترسی به داده
├── Models/          # مدل‌های Domain
├── DTOs/           # Data Transfer Objects
├── Events/         # رویدادهای Domain
├── Middleware/     # میدل‌ویرهای مخصوص (در صورت نیاز)
└── Adapters/       # آداپتورهای خارجی (در صورت نیاز)
```

---

## 📁 ویژگی‌های موجود

### 🔐 Auth
**مسئولیت**: احراز هویت، مجوزها و امنیت
- ورود/خروج کاربران
- مدیریت نشست‌ها
- کنترل دسترسی
- تأیید هویت دو مرحله‌ای

### 👤 User  
**مسئولیت**: مدیریت کاربران
- ثبت‌نام کاربران جدید
- مدیریت پروفایل
- تنظیمات کاربری
- تاریخچه فعالیت‌ها

### 💳 Payment
**مسئولیت**: پردازش پرداخت‌ها
- درگاه‌های پرداخت مختلف
- مدیریت کیف پول
- تراکنش‌ها و رسیدها
- بازپرداخت‌ها

### 🌐 VPNService
**مسئولیت**: مدیریت سرویس‌های VPN
- ایجاد و مدیریت سرویس‌ها
- نظارت بر مصرف
- تمدید و لغو سرویس‌ها
- تولید کانفیگ‌ها

### 🎛️ Panel
**مسئولیت**: اتصال به پنل‌های VPN
- اتصال به Marzban
- اتصال به Marzneshin  
- اتصال به X-UI
- مانیتورینگ پنل‌ها

### 🎧 Support
**مسئولیت**: سیستم پشتیبانی
- مدیریت تیکت‌ها
- چت با پشتیبانی
- پاسخ‌های خودکار
- گزارش‌گیری

---

## 🔄 نحوه استفاده

### 1. ایجاد Feature جدید
```bash
mkdir src/Features/NewFeature
mkdir src/Features/NewFeature/{Controllers,Services,Repositories,Models,DTOs,Events}
```

### 2. پیروی از الگوهای موجود
```php
// Controller Pattern
class FeatureController extends BaseController
{
    public function __construct(
        private FeatureService $service
    ) {}
}

// Service Pattern  
class FeatureService
{
    public function __construct(
        private FeatureRepository $repository
    ) {}
}

// Repository Pattern
class FeatureRepository
{
    public function __construct(
        private Database $database
    ) {}
}
```

### 3. استفاده از Events
```php
// در Service
$this->eventDispatcher->dispatch(
    new FeatureCreatedEvent($feature)
);
```

---

## 📋 قوانین توسعه

### ✅ انجام دهید
- هر feature مستقل باشد
- از Dependency Injection استفاده کنید
- Events را برای ارتباط بین features استفاده کنید
- تست‌های واحد بنویسید
- مستندات کامل ارائه دهید

### ❌ انجام ندهید
- مستقیماً از feature دیگر استفاده نکنید
- منطق تجاری را در Controller ننویسید
- Database queries را در Service ننویسید
- Hard-coded values استفاده نکنید

---

## 🧪 تست‌نویسی

هر feature باید شامل تست‌های زیر باشد:

```
tests/Feature/FeatureName/
├── Unit/
│   ├── Services/
│   ├── Repositories/
│   └── Models/
├── Integration/
│   └── Controllers/
└── E2E/
    └── FeatureWorkflow/
```

---

## 📊 مانیتورینگ

هر feature باید شامل:
- Logging مناسب
- Metrics collection
- Error tracking
- Performance monitoring

---

## 🔗 وابستگی‌ها

### Core Dependencies
- `src/Core/` - هسته سیستم
- `src/Utils/` - ابزارهای کمکی
- `src/Exceptions/` - مدیریت خطاها

### Shared Resources
- `src/Contracts/` - رابط‌های مشترک
- `src/DTOs/` - اشتراک DTOها (در صورت نیاز)
- `src/Events/` - رویدادهای سراسری

---

## 🚀 Migration از ساختار قدیمی

برای انتقال کدهای موجود:

1. شناسایی feature مربوطه
2. انتقال Controller به `Features/FeatureName/Controllers/`
3. انتقال Service به `Features/FeatureName/Services/`
4. انتقال Repository به `Features/FeatureName/Repositories/`
5. به‌روزرسانی namespace ها
6. تست عملکرد

---

> **نکته**: این ساختار به تدریج پیاده‌سازی می‌شود و کدهای موجود به آن منتقل خواهند شد.
