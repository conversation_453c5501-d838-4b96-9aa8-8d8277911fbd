<?php

declare(strict_types=1);

namespace WeBot\Core;

/**
 * SSL Manager
 *
 * Comprehensive SSL/TLS management with certificate validation,
 * automatic renewal, and security monitoring.
 *
 * @package WeBot\Core
 * @version 2.0
 */
class SSLManager
{
    private array $config;
    private string $certificatePath;
    private string $privateKeyPath;
    private string $caPath;
    private array $domains;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'certificate_path' => '/etc/nginx/ssl/cert.pem',
            'private_key_path' => '/etc/nginx/ssl/private.key',
            'ca_path' => '/etc/nginx/ssl/ca.pem',
            'domains' => ['localhost'],
            'key_size' => 2048,
            'validity_days' => 365,
            'renewal_threshold_days' => 30,
            'auto_renewal' => true,
            'hsts_max_age' => 31536000, // 1 year
            'cipher_suites' => [
                'ECDHE-ECDSA-AES256-GCM-SHA384',
                'ECDHE-RSA-AES256-GCM-SHA384',
                'ECDHE-ECDSA-CHACHA20-POLY1305',
                'ECDHE-RSA-CHACHA20-POLY1305',
                'ECDHE-ECDSA-AES128-GCM-SHA256',
                'ECDHE-RSA-AES128-GCM-SHA256'
            ]
        ], $config);

        $this->certificatePath = $this->config['certificate_path'];
        $this->privateKeyPath = $this->config['private_key_path'];
        $this->caPath = $this->config['ca_path'];
        $this->domains = $this->config['domains'];
    }

    /**
     * Generate self-signed certificate
     */
    public function generateSelfSignedCertificate(): array
    {
        $result = [
            'success' => false,
            'certificate_path' => $this->certificatePath,
            'private_key_path' => $this->privateKeyPath,
            'domains' => $this->domains,
            'errors' => []
        ];

        try {
            // Generate private key
            $privateKey = openssl_pkey_new([
                'private_key_bits' => $this->config['key_size'],
                'private_key_type' => OPENSSL_KEYTYPE_RSA,
            ]);

            if (!$privateKey) {
                throw new \Exception('Failed to generate private key: ' . openssl_error_string());
            }

            // Create certificate signing request
            $csr = openssl_csr_new($this->buildDistinguishedName(), $privateKey, [
                'digest_alg' => 'sha256',
                'x509_extensions' => 'v3_req',
                'req_extensions' => 'v3_req',
            ]);

            if (!$csr) {
                throw new \Exception('Failed to create CSR: ' . openssl_error_string());
            }

            // Generate self-signed certificate
            $certificate = openssl_csr_sign(
                $csr,
                null,
                $privateKey,
                $this->config['validity_days'],
                [
                    'digest_alg' => 'sha256',
                    'x509_extensions' => 'v3_ca',
                ]
            );

            if (!$certificate) {
                throw new \Exception('Failed to generate certificate: ' . openssl_error_string());
            }

            // Export certificate and private key
            openssl_x509_export($certificate, $certificateOut);
            openssl_pkey_export($privateKey, $privateKeyOut);

            // Ensure directory exists
            $this->ensureDirectoryExists(dirname($this->certificatePath));
            $this->ensureDirectoryExists(dirname($this->privateKeyPath));

            // Save certificate and private key
            if (!file_put_contents($this->certificatePath, $certificateOut)) {
                throw new \Exception('Failed to save certificate');
            }

            if (!file_put_contents($this->privateKeyPath, $privateKeyOut)) {
                throw new \Exception('Failed to save private key');
            }

            // Set proper permissions
            chmod($this->certificatePath, 0644);
            chmod($this->privateKeyPath, 0600);

            $result['success'] = true;
            $result['certificate_info'] = $this->getCertificateInfo();
        } catch (\Exception $e) {
            $result['errors'][] = $e->getMessage();
        }

        return $result;
    }

    /**
     * Validate certificate
     */
    public function validateCertificate(): array
    {
        $result = [
            'valid' => false,
            'certificate_exists' => false,
            'private_key_exists' => false,
            'certificate_info' => null,
            'expiry_date' => null,
            'days_until_expiry' => null,
            'needs_renewal' => false,
            'errors' => []
        ];

        try {
            // Check if files exist
            $result['certificate_exists'] = file_exists($this->certificatePath);
            $result['private_key_exists'] = file_exists($this->privateKeyPath);

            if (!$result['certificate_exists']) {
                $result['errors'][] = 'Certificate file not found';
                return $result;
            }

            if (!$result['private_key_exists']) {
                $result['errors'][] = 'Private key file not found';
                return $result;
            }

            // Load certificate
            $certificateContent = file_get_contents($this->certificatePath);
            $certificate = openssl_x509_read($certificateContent);

            if (!$certificate) {
                $result['errors'][] = 'Invalid certificate format';
                return $result;
            }

            // Get certificate info
            $certInfo = openssl_x509_parse($certificate);
            $result['certificate_info'] = $certInfo;

            // Check expiry
            $expiryTimestamp = $certInfo['validTo_time_t'];
            $result['expiry_date'] = date('Y-m-d H:i:s', $expiryTimestamp);
            $result['days_until_expiry'] = ceil(($expiryTimestamp - time()) / 86400);

            // Check if renewal is needed
            $result['needs_renewal'] = $result['days_until_expiry'] <= $this->config['renewal_threshold_days'];

            // Validate private key matches certificate
            $privateKeyContent = file_get_contents($this->privateKeyPath);
            $privateKey = openssl_pkey_get_private($privateKeyContent);

            if (!$privateKey) {
                $result['errors'][] = 'Invalid private key format';
                return $result;
            }

            if (!openssl_x509_check_private_key($certificate, $privateKey)) {
                $result['errors'][] = 'Private key does not match certificate';
                return $result;
            }

            $result['valid'] = true;
        } catch (\Exception $e) {
            $result['errors'][] = $e->getMessage();
        }

        return $result;
    }

    /**
     * Get certificate information
     */
    public function getCertificateInfo(): ?array
    {
        if (!file_exists($this->certificatePath)) {
            return null;
        }

        try {
            $certificateContent = file_get_contents($this->certificatePath);
            $certificate = openssl_x509_read($certificateContent);

            if (!$certificate) {
                return null;
            }

            $info = openssl_x509_parse($certificate);

            return [
                'subject' => $info['subject'],
                'issuer' => $info['issuer'],
                'valid_from' => date('Y-m-d H:i:s', $info['validFrom_time_t']),
                'valid_to' => date('Y-m-d H:i:s', $info['validTo_time_t']),
                'serial_number' => $info['serialNumber'],
                'signature_algorithm' => $info['signatureTypeSN'],
                'domains' => $this->extractDomainsFromCertificate($info)
            ];
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Check if certificate needs renewal
     */
    public function needsRenewal(): bool
    {
        $validation = $this->validateCertificate();
        return $validation['needs_renewal'] ?? true;
    }

    /**
     * Generate Nginx SSL configuration
     */
    public function generateNginxSSLConfig(): string
    {
        $cipherSuites = implode(':', $this->config['cipher_suites']);

        return <<<NGINX
# WeBot SSL Configuration
ssl_certificate {$this->certificatePath};
ssl_certificate_key {$this->privateKeyPath};

# SSL Protocol and Ciphers
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers {$cipherSuites};
ssl_prefer_server_ciphers on;

# SSL Session
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;
ssl_session_tickets off;

# OCSP Stapling
ssl_stapling on;
ssl_stapling_verify on;
resolver ******* ******* valid=300s;
resolver_timeout 5s;

# Security Headers
add_header Strict-Transport-Security "max-age={$this->config['hsts_max_age']}; includeSubDomains; preload" always;
add_header X-Frame-Options DENY always;
add_header X-Content-Type-Options nosniff always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;

# Perfect Forward Secrecy
ssl_dhparam /etc/nginx/ssl/dhparam.pem;
NGINX;
    }

    /**
     * Generate DH parameters
     */
    public function generateDHParams(int $keySize = 2048): bool
    {
        $dhParamPath = '/etc/nginx/ssl/dhparam.pem';

        try {
            $this->ensureDirectoryExists(dirname($dhParamPath));

            // Generate DH parameters (this can take a while)
            $command = "openssl dhparam -out {$dhParamPath} {$keySize}";
            $output = [];
            $returnCode = 0;

            exec($command, $output, $returnCode);

            if ($returnCode === 0 && file_exists($dhParamPath)) {
                chmod($dhParamPath, 0644);
                return true;
            }

            return false;
        } catch (\Exception $e) {
            error_log("Failed to generate DH parameters: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Test SSL configuration
     */
    public function testSSLConfiguration(string $domain): array
    {
        $result = [
            'domain' => $domain,
            'ssl_enabled' => false,
            'certificate_valid' => false,
            'protocol_support' => [],
            'cipher_strength' => null,
            'errors' => []
        ];

        try {
            // Test SSL connection
            $context = stream_context_create([
                'ssl' => [
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'capture_peer_cert' => true
                ]
            ]);

            $connection = @stream_socket_client(
                "ssl://{$domain}:443",
                $errno,
                $errstr,
                10,
                STREAM_CLIENT_CONNECT,
                $context
            );

            if (!$connection) {
                $result['errors'][] = "Failed to connect: {$errstr}";
                return $result;
            }

            $result['ssl_enabled'] = true;

            // Get certificate info
            $params = stream_context_get_params($connection);
            if (isset($params['options']['ssl']['peer_certificate'])) {
                $cert = $params['options']['ssl']['peer_certificate'];
                $certInfo = openssl_x509_parse($cert);

                $result['certificate_valid'] = $certInfo['validTo_time_t'] > time();
                $result['certificate_info'] = [
                    'subject' => $certInfo['subject']['CN'] ?? 'Unknown',
                    'issuer' => $certInfo['issuer']['CN'] ?? 'Unknown',
                    'valid_to' => date('Y-m-d H:i:s', $certInfo['validTo_time_t'])
                ];
            }

            fclose($connection);
        } catch (\Exception $e) {
            $result['errors'][] = $e->getMessage();
        }

        return $result;
    }

    /**
     * Monitor SSL certificate expiry
     */
    public function monitorCertificateExpiry(): array
    {
        $validation = $this->validateCertificate();
        $alerts = [];

        if (!$validation['valid']) {
            $alerts[] = [
                'level' => 'critical',
                'message' => 'SSL certificate is invalid',
                'errors' => $validation['errors']
            ];
        } elseif ($validation['needs_renewal']) {
            $daysLeft = $validation['days_until_expiry'];

            if ($daysLeft <= 7) {
                $level = 'critical';
            } elseif ($daysLeft <= 14) {
                $level = 'warning';
            } else {
                $level = 'info';
            }

            $alerts[] = [
                'level' => $level,
                'message' => "SSL certificate expires in {$daysLeft} days",
                'expiry_date' => $validation['expiry_date']
            ];
        }

        return [
            'validation' => $validation,
            'alerts' => $alerts,
            'auto_renewal_enabled' => $this->config['auto_renewal']
        ];
    }

    /**
     * Build distinguished name for certificate
     */
    private function buildDistinguishedName(): array
    {
        return [
            'countryName' => 'US',
            'stateOrProvinceName' => 'State',
            'localityName' => 'City',
            'organizationName' => 'WeBot',
            'organizationalUnitName' => 'IT Department',
            'commonName' => $this->domains[0],
            'emailAddress' => 'admin@' . $this->domains[0]
        ];
    }

    /**
     * Extract domains from certificate
     */
    private function extractDomainsFromCertificate(array $certInfo): array
    {
        $domains = [];

        // Add common name
        if (isset($certInfo['subject']['CN'])) {
            $domains[] = $certInfo['subject']['CN'];
        }

        // Add subject alternative names
        if (isset($certInfo['extensions']['subjectAltName'])) {
            $altNames = explode(', ', $certInfo['extensions']['subjectAltName']);
            foreach ($altNames as $altName) {
                if (str_starts_with($altName, 'DNS:')) {
                    $domains[] = substr($altName, 4);
                }
            }
        }

        return array_unique($domains);
    }

    /**
     * Ensure directory exists
     */
    private function ensureDirectoryExists(string $directory): void
    {
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
    }
}
