<?php

declare(strict_types=1);

namespace WeBot\Core;

/**
 * Application Monitor
 *
 * Comprehensive application monitoring with health checks,
 * performance metrics, and alerting system.
 *
 * @package WeBot\Core
 * @version 2.0
 */
class ApplicationMonitor
{
    private CacheManager $cache;
    private PerformanceMonitor $performanceMonitor;
    private array $config;
    private array $healthChecks = [];
    private array $metrics = [];

    public function __construct(
        CacheManager $cache,
        PerformanceMonitor $performanceMonitor,
        array $config = []
    ) {
        $this->cache = $cache;
        $this->performanceMonitor = $performanceMonitor;
        $this->config = array_merge([
            'enabled' => true,
            'health_check_interval' => 60, // seconds
            'metrics_retention' => 86400, // 24 hours
            'alert_thresholds' => [
                'response_time' => 2.0, // seconds
                'error_rate' => 5.0, // percentage
                'memory_usage' => 80.0, // percentage
                'disk_usage' => 90.0, // percentage
                'cpu_usage' => 80.0 // percentage
            ],
            'endpoints' => [
                'health' => '/health',
                'metrics' => '/metrics',
                'status' => '/status'
            ]
        ], $config);

        $this->initializeHealthChecks();
    }

    /**
     * Perform comprehensive health check
     */
    public function healthCheck(): array
    {
        $startTime = microtime(true);
        $results = [
            'status' => 'healthy',
            'timestamp' => time(),
            'checks' => [],
            'overall_health' => 100,
            'response_time' => 0
        ];

        $totalChecks = 0;
        $passedChecks = 0;

        foreach ($this->healthChecks as $checkName => $check) {
            $totalChecks++;

            try {
                $checkResult = $check();
                $checkResult['name'] = $checkName;

                if ($checkResult['status'] === 'healthy') {
                    $passedChecks++;
                }

                $results['checks'][] = $checkResult;
            } catch (\Exception $e) {
                $results['checks'][] = [
                    'name' => $checkName,
                    'status' => 'unhealthy',
                    'message' => $e->getMessage(),
                    'details' => []
                ];
            }
        }

        // Calculate overall health
        $results['overall_health'] = $totalChecks > 0 ?
            round(($passedChecks / $totalChecks) * 100, 2) : 100;

        // Determine overall status
        if ($results['overall_health'] >= 90) {
            $results['status'] = 'healthy';
        } elseif ($results['overall_health'] >= 70) {
            $results['status'] = 'degraded';
        } else {
            $results['status'] = 'unhealthy';
        }

        $results['response_time'] = round((microtime(true) - $startTime) * 1000, 2);

        // Store health check result
        $this->storeHealthCheckResult($results);

        return $results;
    }

    /**
     * Get application metrics
     */
    public function getMetrics(): array
    {
        $metrics = [
            'timestamp' => time(),
            'application' => $this->getApplicationMetrics(),
            'system' => $this->getSystemMetrics(),
            'performance' => $this->performanceMonitor->getStats(),
            'business' => $this->getBusinessMetrics(),
            'errors' => $this->getErrorMetrics()
        ];

        $this->storeMetrics($metrics);
        return $metrics;
    }

    /**
     * Get application status
     */
    public function getStatus(): array
    {
        $health = $this->healthCheck();
        $metrics = $this->getMetrics();

        return [
            'application' => [
                'name' => 'WeBot',
                'version' => '2.0',
                'environment' => $_ENV['APP_ENV'] ?? 'production',
                'uptime' => $this->getUptime()
            ],
            'health' => $health,
            'metrics' => $metrics,
            'alerts' => $this->getActiveAlerts(),
            'last_updated' => time()
        ];
    }

    /**
     * Check for alerts
     */
    public function checkAlerts(): array
    {
        $alerts = [];
        $metrics = $this->getMetrics();
        $thresholds = $this->config['alert_thresholds'];

        // Response time alert
        if (isset($metrics['performance']['avg_response_time'])) {
            $responseTime = $metrics['performance']['avg_response_time'];
            if ($responseTime > $thresholds['response_time']) {
                $alerts[] = [
                    'type' => 'performance',
                    'severity' => 'warning',
                    'message' => "High response time: {$responseTime}s",
                    'threshold' => $thresholds['response_time'],
                    'current_value' => $responseTime
                ];
            }
        }

        // Memory usage alert
        if (isset($metrics['system']['memory']['usage_percentage'])) {
            $memoryUsage = $metrics['system']['memory']['usage_percentage'];
            if ($memoryUsage > $thresholds['memory_usage']) {
                $alerts[] = [
                    'type' => 'system',
                    'severity' => $memoryUsage > 95 ? 'critical' : 'warning',
                    'message' => "High memory usage: {$memoryUsage}%",
                    'threshold' => $thresholds['memory_usage'],
                    'current_value' => $memoryUsage
                ];
            }
        }

        // Error rate alert
        if (isset($metrics['errors']['error_rate'])) {
            $errorRate = $metrics['errors']['error_rate'];
            if ($errorRate > $thresholds['error_rate']) {
                $alerts[] = [
                    'type' => 'errors',
                    'severity' => $errorRate > 10 ? 'critical' : 'warning',
                    'message' => "High error rate: {$errorRate}%",
                    'threshold' => $thresholds['error_rate'],
                    'current_value' => $errorRate
                ];
            }
        }

        // Store alerts
        if (!empty($alerts)) {
            $this->storeAlerts($alerts);
        }

        return $alerts;
    }

    /**
     * Get monitoring dashboard data
     */
    public function getDashboardData(): array
    {
        return [
            'overview' => [
                'status' => $this->getStatus(),
                'key_metrics' => $this->getKeyMetrics(),
                'recent_alerts' => $this->getRecentAlerts(10)
            ],
            'charts' => [
                'response_times' => $this->getResponseTimeChart(),
                'memory_usage' => $this->getMemoryUsageChart(),
                'request_volume' => $this->getRequestVolumeChart(),
                'error_rates' => $this->getErrorRateChart()
            ],
            'health_history' => $this->getHealthHistory(24), // Last 24 hours
            'performance_trends' => $this->getPerformanceTrends()
        ];
    }

    /**
     * Initialize health checks
     */
    private function initializeHealthChecks(): void
    {
        $this->healthChecks = [
            'database' => [$this, 'checkDatabase'],
            'cache' => [$this, 'checkCache'],
            'disk_space' => [$this, 'checkDiskSpace'],
            'memory' => [$this, 'checkMemory'],
            'telegram_api' => [$this, 'checkTelegramAPI'],
            'external_services' => [$this, 'checkExternalServices']
        ];
    }

    /**
     * Database health check
     */
    private function checkDatabase(): array
    {
        try {
            // Simple database connectivity test
            $pdo = new \PDO(
                $_ENV['DATABASE_URL'] ?? 'sqlite:storage/database.sqlite',
                $_ENV['DB_USERNAME'] ?? null,
                $_ENV['DB_PASSWORD'] ?? null,
                [
                    \PDO::ATTR_TIMEOUT => 5,
                    \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION
                ]
            );

            $stmt = $pdo->query('SELECT 1');
            $result = $stmt->fetch();

            return [
                'status' => 'healthy',
                'message' => 'Database connection successful',
                'details' => [
                    'connection_time' => '< 5s',
                    'query_result' => $result[0] ?? null
                ]
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Database connection failed',
                'details' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * Cache health check
     */
    private function checkCache(): array
    {
        try {
            $testKey = 'health_check_' . time();
            $testValue = 'test_value';

            // Test cache write
            $writeResult = $this->cache->set($testKey, $testValue, 60);

            // Test cache read
            $readResult = $this->cache->get($testKey);

            // Cleanup
            $this->cache->delete($testKey);

            if ($writeResult && $readResult === $testValue) {
                return [
                    'status' => 'healthy',
                    'message' => 'Cache is working properly',
                    'details' => [
                        'write_success' => $writeResult,
                        'read_success' => $readResult === $testValue
                    ]
                ];
            } else {
                return [
                    'status' => 'unhealthy',
                    'message' => 'Cache read/write failed',
                    'details' => [
                        'write_result' => $writeResult,
                        'read_result' => $readResult
                    ]
                ];
            }
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Cache error',
                'details' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * Disk space health check
     */
    private function checkDiskSpace(): array
    {
        $freeBytes = disk_free_space('.');
        $totalBytes = disk_total_space('.');
        $usedBytes = $totalBytes - $freeBytes;
        $usagePercentage = ($usedBytes / $totalBytes) * 100;

        $status = $usagePercentage > 90 ? 'unhealthy' :
                 ($usagePercentage > 80 ? 'warning' : 'healthy');

        return [
            'status' => $status,
            'message' => "Disk usage: " . round($usagePercentage, 2) . "%",
            'details' => [
                'free_space' => $this->formatBytes((int) $freeBytes),
                'total_space' => $this->formatBytes((int) $totalBytes),
                'used_space' => $this->formatBytes((int) $usedBytes),
                'usage_percentage' => round($usagePercentage, 2)
            ]
        ];
    }

    /**
     * Memory health check
     */
    private function checkMemory(): array
    {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        $usagePercentage = ($memoryUsage / $memoryLimit) * 100;

        $status = $usagePercentage > 90 ? 'unhealthy' :
                 ($usagePercentage > 80 ? 'warning' : 'healthy');

        return [
            'status' => $status,
            'message' => "Memory usage: " . round($usagePercentage, 2) . "%",
            'details' => [
                'current_usage' => $this->formatBytes($memoryUsage),
                'memory_limit' => $this->formatBytes($memoryLimit),
                'usage_percentage' => round($usagePercentage, 2),
                'peak_usage' => $this->formatBytes(memory_get_peak_usage(true))
            ]
        ];
    }

    /**
     * Telegram API health check
     */
    private function checkTelegramAPI(): array
    {
        try {
            $botToken = $_ENV['TELEGRAM_BOT_TOKEN'] ?? '';

            if (empty($botToken)) {
                return [
                    'status' => 'unhealthy',
                    'message' => 'Telegram bot token not configured',
                    'details' => []
                ];
            }

            // Simple API call to check bot status
            $url = "https://api.telegram.org/bot{$botToken}/getMe";
            $context = stream_context_create([
                'http' => [
                    'timeout' => 10,
                    'method' => 'GET'
                ]
            ]);

            $response = file_get_contents($url, false, $context);
            $data = json_decode($response, true);

            if ($data && $data['ok']) {
                return [
                    'status' => 'healthy',
                    'message' => 'Telegram API is accessible',
                    'details' => [
                        'bot_username' => $data['result']['username'] ?? 'unknown',
                        'bot_name' => $data['result']['first_name'] ?? 'unknown'
                    ]
                ];
            } else {
                return [
                    'status' => 'unhealthy',
                    'message' => 'Telegram API returned error',
                    'details' => [
                        'response' => $data
                    ]
                ];
            }
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Telegram API check failed',
                'details' => [
                    'error' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * External services health check
     */
    private function checkExternalServices(): array
    {
        $services = [
            'panel_api' => $_ENV['PANEL_URL'] ?? null,
            'payment_gateway' => $_ENV['PAYMENT_GATEWAY_URL'] ?? null
        ];

        $results = [];
        $overallStatus = 'healthy';

        foreach ($services as $serviceName => $url) {
            if (!$url) {
                $results[$serviceName] = [
                    'status' => 'not_configured',
                    'message' => 'Service URL not configured'
                ];
                continue;
            }

            try {
                $context = stream_context_create([
                    'http' => [
                        'timeout' => 5,
                        'method' => 'HEAD'
                    ]
                ]);

                $headers = get_headers($url, true, $context);
                $httpCode = substr($headers[0], 9, 3);

                if ($httpCode >= 200 && $httpCode < 400) {
                    $results[$serviceName] = [
                        'status' => 'healthy',
                        'message' => "Service accessible (HTTP {$httpCode})"
                    ];
                } else {
                    $results[$serviceName] = [
                        'status' => 'unhealthy',
                        'message' => "Service returned HTTP {$httpCode}"
                    ];
                    $overallStatus = 'degraded';
                }
            } catch (\Exception $e) {
                $results[$serviceName] = [
                    'status' => 'unhealthy',
                    'message' => 'Service unreachable'
                ];
                $overallStatus = 'degraded';
            }
        }

        return [
            'status' => $overallStatus,
            'message' => 'External services check completed',
            'details' => $results
        ];
    }

    /**
     * Get application metrics
     */
    private function getApplicationMetrics(): array
    {
        return [
            'uptime' => $this->getUptime(),
            'version' => '2.0',
            'environment' => $_ENV['APP_ENV'] ?? 'production',
            'php_version' => PHP_VERSION,
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ];
    }

    /**
     * Get system metrics
     */
    private function getSystemMetrics(): array
    {
        return [
            'memory' => [
                'current' => memory_get_usage(true),
                'peak' => memory_get_peak_usage(true),
                'limit' => $this->parseMemoryLimit(ini_get('memory_limit')),
                'usage_percentage' => (memory_get_usage(true) / $this->parseMemoryLimit(ini_get('memory_limit'))) * 100
            ],
            'disk' => [
                'free' => disk_free_space('.'),
                'total' => disk_total_space('.'),
                'usage_percentage' => ((disk_total_space('.') - disk_free_space('.')) / disk_total_space('.')) * 100
            ],
            'load_average' => function_exists('sys_getloadavg') ? sys_getloadavg() : null
        ];
    }

    /**
     * Get business metrics
     */
    private function getBusinessMetrics(): array
    {
        // These would be implemented based on your business logic
        return [
            'active_users' => $this->cache->get('metrics:active_users', 0),
            'total_requests' => $this->cache->get('metrics:total_requests', 0),
            'successful_payments' => $this->cache->get('metrics:successful_payments', 0),
            'active_services' => $this->cache->get('metrics:active_services', 0)
        ];
    }

    /**
     * Get error metrics
     */
    private function getErrorMetrics(): array
    {
        $totalRequests = $this->cache->get('metrics:total_requests', 1);
        $totalErrors = $this->cache->get('metrics:total_errors', 0);

        return [
            'total_errors' => $totalErrors,
            'error_rate' => ($totalErrors / $totalRequests) * 100,
            'last_error' => $this->cache->get('metrics:last_error'),
            'error_types' => $this->cache->get('metrics:error_types', [])
        ];
    }

    /**
     * Store health check result
     */
    private function storeHealthCheckResult(array $result): void
    {
        $key = 'health_check:' . date('Y-m-d-H-i');
        $this->cache->set($key, $result, $this->config['metrics_retention']);
    }

    /**
     * Store metrics
     */
    private function storeMetrics(array $metrics): void
    {
        $key = 'metrics:' . date('Y-m-d-H-i');
        $this->cache->set($key, $metrics, $this->config['metrics_retention']);
    }

    /**
     * Store alerts
     */
    private function storeAlerts(array $alerts): void
    {
        $key = 'alerts:' . time();
        $this->cache->set($key, $alerts, 86400); // 24 hours
    }

    /**
     * Get active alerts
     */
    private function getActiveAlerts(): array
    {
        // Implementation would retrieve recent alerts from cache
        return [];
    }

    /**
     * Get recent alerts
     */
    private function getRecentAlerts(int $limit): array
    {
        // Implementation would retrieve recent alerts
        return [];
    }

    /**
     * Get key metrics
     */
    private function getKeyMetrics(): array
    {
        return [
            'response_time' => '< 100ms',
            'uptime' => '99.9%',
            'error_rate' => '< 1%',
            'memory_usage' => '45%'
        ];
    }

    /**
     * Get uptime
     */
    private function getUptime(): int
    {
        $startTime = $this->cache->get('app_start_time');
        if (!$startTime) {
            $startTime = time();
            $this->cache->set('app_start_time', $startTime, 86400 * 30); // 30 days
        }

        return time() - $startTime;
    }

    /**
     * Parse memory limit
     */
    private function parseMemoryLimit(string $limit): int
    {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit) - 1]);
        $value = (int) $limit;

        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }

    /**
     * Format bytes
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $factor = floor((strlen((string)$bytes) - 1) / 3);

        return sprintf("%.1f %s", $bytes / pow(1024, $factor), $units[$factor]);
    }

    /**
     * Get chart data methods (simplified)
     */
    private function getResponseTimeChart(): array
    {
        return [];
    }
    private function getMemoryUsageChart(): array
    {
        return [];
    }
    private function getRequestVolumeChart(): array
    {
        return [];
    }
    private function getErrorRateChart(): array
    {
        return [];
    }
    private function getHealthHistory(int $hours): array
    {
        return [];
    }
    private function getPerformanceTrends(): array
    {
        return [];
    }
}
