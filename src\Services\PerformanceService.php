<?php

declare(strict_types=1);

namespace WeBot\Services;

use WeBot\Core\DatabaseOptimizer;
use WeBot\Core\CacheManager;
use WeBot\Core\PerformanceMonitor;
use WeBot\Utils\Logger;

/**
 * Performance Service
 *
 * Comprehensive performance optimization and monitoring service
 * that coordinates database optimization, caching, and monitoring.
 *
 * @package WeBot\Services
 * @version 2.0
 */
class PerformanceService
{
    private DatabaseOptimizer $databaseOptimizer;
    private CacheManager $cacheManager;
    private PerformanceMonitor $performanceMonitor;
    private Logger $logger;
    private array $config;

    public function __construct(
        DatabaseOptimizer $databaseOptimizer,
        CacheManager $cacheManager,
        PerformanceMonitor $performanceMonitor,
        array $config = []
    ) {
        $this->databaseOptimizer = $databaseOptimizer;
        $this->cacheManager = $cacheManager;
        $this->performanceMonitor = $performanceMonitor;
        $this->logger = Logger::getInstance();
        $this->config = array_merge($this->getDefaultConfig(), $config);
    }

    /**
     * Run comprehensive performance optimization
     */
    public function optimize(): array
    {
        $this->logger->info('Starting performance optimization');
        $results = [];

        try {
            // Database optimization
            $results['database'] = $this->optimizeDatabase();

            // Cache optimization
            $results['cache'] = $this->optimizeCache();

            // Memory optimization
            $results['memory'] = $this->optimizeMemory();

            // System optimization
            $results['system'] = $this->optimizeSystem();

            $this->logger->info('Performance optimization completed', $results);
        } catch (\Exception $e) {
            $this->logger->error('Performance optimization failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $results['error'] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Get comprehensive performance report
     */
    public function getPerformanceReport(): array
    {
        return [
            'timestamp' => date('Y-m-d H:i:s'),
            'system' => $this->getSystemMetrics(),
            'database' => $this->getDatabaseMetrics(),
            'cache' => $this->getCacheMetrics(),
            'memory' => $this->getMemoryMetrics(),
            'performance' => $this->performanceMonitor->getStats(),
            'recommendations' => $this->getOptimizationRecommendations(),
            'alerts' => $this->getActiveAlerts()
        ];
    }

    /**
     * Monitor API endpoint performance
     */
    public function monitorApiEndpoint(string $endpoint, callable $callback, ...$args)
    {
        return $this->performanceMonitor->monitorEndpoint($endpoint, $callback, ...$args);
    }

    /**
     * Optimize database performance
     */
    private function optimizeDatabase(): array
    {
        $results = [];

        try {
            // Analyze and optimize slow queries
            $results['slow_queries'] = $this->databaseOptimizer->analyzeSlowQueries();

            // Setup connection pooling
            $results['connection_pooling'] = $this->databaseOptimizer->setupConnectionPooling();

            // Get performance metrics
            $results['metrics'] = $this->databaseOptimizer->getPerformanceMetrics();

            // Suggest indexes
            $results['index_suggestions'] = $this->databaseOptimizer->suggestIndexes();
        } catch (\Exception $e) {
            $results['error'] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Optimize cache performance
     */
    private function optimizeCache(): array
    {
        $results = [];

        try {
            // Optimize cache
            $results['optimization'] = $this->cacheManager->optimize();

            // Warm up cache
            $results['warmup'] = $this->cacheManager->warmUp();

            // Get cache statistics
            $results['stats'] = $this->cacheManager->getStats();
        } catch (\Exception $e) {
            $results['error'] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Optimize memory usage
     */
    private function optimizeMemory(): array
    {
        $results = [];

        try {
            $memoryBefore = memory_get_usage(true);
            $peakBefore = memory_get_peak_usage(true);

            // Force garbage collection
            gc_collect_cycles();

            $memoryAfter = memory_get_usage(true);
            $peakAfter = memory_get_peak_usage(true);

            $results = [
                'memory_before' => $memoryBefore,
                'memory_after' => $memoryAfter,
                'memory_freed' => $memoryBefore - $memoryAfter,
                'peak_before' => $peakBefore,
                'peak_after' => $peakAfter,
                'gc_cycles' => gc_collect_cycles()
            ];
        } catch (\Exception $e) {
            $results['error'] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Optimize system performance
     */
    private function optimizeSystem(): array
    {
        $results = [];

        try {
            // Check and optimize PHP settings
            $results['php_settings'] = $this->optimizePhpSettings();

            // Check system resources
            $results['system_resources'] = $this->checkSystemResources();

            // Optimize file operations
            $results['file_operations'] = $this->optimizeFileOperations();
        } catch (\Exception $e) {
            $results['error'] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Get system metrics
     */
    private function getSystemMetrics(): array
    {
        return [
            'php_version' => PHP_VERSION,
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'opcache_enabled' => extension_loaded('opcache') && ini_get('opcache.enable'),
            'load_average' => sys_getloadavg(),
            'disk_free_space' => disk_free_space('.'),
            'disk_total_space' => disk_total_space('.')
        ];
    }

    /**
     * Get database metrics
     */
    private function getDatabaseMetrics(): array
    {
        try {
            return $this->databaseOptimizer->getPerformanceMetrics();
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Get cache metrics
     */
    private function getCacheMetrics(): array
    {
        try {
            return $this->cacheManager->getStats();
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Get memory metrics
     */
    private function getMemoryMetrics(): array
    {
        return [
            'current_usage' => memory_get_usage(true),
            'peak_usage' => memory_get_peak_usage(true),
            'limit' => ini_get('memory_limit'),
            'usage_percentage' => $this->calculateMemoryUsagePercentage()
        ];
    }

    /**
     * Get optimization recommendations
     */
    private function getOptimizationRecommendations(): array
    {
        $recommendations = [];

        // Get recommendations from performance monitor
        $recommendations = array_merge(
            $recommendations,
            $this->performanceMonitor->getPerformanceRecommendations()
        );

        // Add custom recommendations based on metrics
        $memoryUsage = $this->calculateMemoryUsagePercentage();
        if ($memoryUsage > 80) {
            $recommendations[] = [
                'type' => 'memory',
                'priority' => 'high',
                'message' => 'Memory usage is critically high. Consider increasing memory limit or optimizing code.',
                'action' => 'increase_memory_limit'
            ];
        }

        return $recommendations;
    }

    /**
     * Get active alerts
     */
    private function getActiveAlerts(): array
    {
        // This would typically fetch from a persistent storage
        return [];
    }

    /**
     * Calculate memory usage percentage
     */
    private function calculateMemoryUsagePercentage(): float
    {
        $current = memory_get_usage(true);
        $limit = $this->parseMemoryLimit(ini_get('memory_limit'));

        return $limit > 0 ? ($current / $limit) * 100 : 0;
    }

    /**
     * Parse memory limit string to bytes
     */
    private function parseMemoryLimit(string $limit): int
    {
        if ($limit === '-1') {
            return PHP_INT_MAX;
        }

        $unit = strtolower(substr($limit, -1));
        $value = (int)substr($limit, 0, -1);

        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return (int)$limit;
        }
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'optimization' => [
                'auto_optimize' => true,
                'optimization_interval' => 3600, // 1 hour
                'cache_warmup' => true,
                'gc_probability' => 1,
                'gc_divisor' => 100
            ],
            'monitoring' => [
                'enabled' => true,
                'alert_thresholds' => [
                    'memory_usage' => 80,
                    'response_time' => 2.0,
                    'error_rate' => 5.0
                ]
            ]
        ];
    }

    /**
     * Optimize PHP settings
     */
    private function optimizePhpSettings(): array
    {
        $recommendations = [];

        // Check OPcache
        if (!extension_loaded('opcache') || !ini_get('opcache.enable')) {
            $recommendations[] = 'Enable OPcache for better performance';
        }

        // Check memory limit
        $memoryLimit = ini_get('memory_limit');
        if ($this->parseMemoryLimit($memoryLimit) < 256 * 1024 * 1024) { // 256MB
            $recommendations[] = 'Consider increasing memory_limit to at least 256M';
        }

        // Check max execution time
        $maxExecutionTime = (int)ini_get('max_execution_time');
        if ($maxExecutionTime > 0 && $maxExecutionTime < 30) {
            $recommendations[] = 'Consider increasing max_execution_time for complex operations';
        }

        return [
            'current_settings' => [
                'memory_limit' => $memoryLimit,
                'max_execution_time' => $maxExecutionTime,
                'opcache_enabled' => extension_loaded('opcache') && ini_get('opcache.enable')
            ],
            'recommendations' => $recommendations
        ];
    }

    /**
     * Check system resources
     */
    private function checkSystemResources(): array
    {
        $load = sys_getloadavg();
        $diskFree = disk_free_space('.');
        $diskTotal = disk_total_space('.');

        $warnings = [];

        // Check CPU load
        if ($load[0] > 2.0) {
            $warnings[] = 'High CPU load detected';
        }

        // Check disk space
        $diskUsagePercent = (($diskTotal - $diskFree) / $diskTotal) * 100;
        if ($diskUsagePercent > 90) {
            $warnings[] = 'Low disk space (>90% used)';
        }

        return [
            'load_average' => $load,
            'disk_usage_percent' => $diskUsagePercent,
            'disk_free_gb' => round($diskFree / (1024 * 1024 * 1024), 2),
            'warnings' => $warnings
        ];
    }

    /**
     * Optimize file operations
     */
    private function optimizeFileOperations(): array
    {
        $results = [];

        try {
            // Check if realpath cache is optimized
            $realpathCacheSize = ini_get('realpath_cache_size');
            $realpathCacheTtl = ini_get('realpath_cache_ttl');

            $results['realpath_cache'] = [
                'size' => $realpathCacheSize,
                'ttl' => $realpathCacheTtl,
                'recommendation' => $realpathCacheSize === '4096k' ?
                    'Consider increasing realpath_cache_size' : 'OK'
            ];

            // Check file upload settings
            $results['file_uploads'] = [
                'upload_max_filesize' => ini_get('upload_max_filesize'),
                'post_max_size' => ini_get('post_max_size'),
                'max_file_uploads' => ini_get('max_file_uploads')
            ];
        } catch (\Exception $e) {
            $results['error'] = $e->getMessage();
        }

        return $results;
    }
}
