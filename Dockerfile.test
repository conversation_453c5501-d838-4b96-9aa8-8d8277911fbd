# WeBot Testing Environment Dockerfile
FROM php:8.2-cli

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    unzip \
    curl \
    libzip-dev \
    libicu-dev \
    libonig-dev \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) \
        pdo_mysql \
        zip \
        intl \
        mbstring \
        gd \
        bcmath

# Install Composer
COPY --from=composer:2.6 /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /app

# Copy composer files first for better caching
COPY composer.json composer.lock ./

# Install PHP dependencies
RUN composer install --no-scripts --no-autoloader --no-dev

# Copy application files
COPY . .

# Generate autoloader
RUN composer dump-autoload --optimize

# Create necessary directories
RUN mkdir -p storage/logs storage/cache storage/sessions storage/uploads \
    && chmod -R 777 storage

# Set environment for testing
ENV APP_ENV=testing
ENV APP_DEBUG=true

# Default command
CMD ["vendor/bin/phpunit", "--verbose"]
