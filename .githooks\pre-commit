#!/bin/bash

# WeBot Pre-commit Hook
# This script runs code quality checks before each commit

set -e

echo "🔍 Running WeBot pre-commit checks..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if composer is available
if ! command -v composer &> /dev/null; then
    print_error "Composer is not installed or not in PATH"
    exit 1
fi

# Check if vendor directory exists
if [ ! -d "vendor" ]; then
    print_warning "Vendor directory not found. Running composer install..."
    composer install --quiet
fi

# Get list of staged PHP files
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(php)$' || true)

if [ -z "$STAGED_FILES" ]; then
    print_status "No PHP files staged for commit"
    exit 0
fi

print_status "Found staged PHP files:"
echo "$STAGED_FILES"

# 1. PHP Syntax Check
print_status "Checking PHP syntax..."
SYNTAX_ERRORS=0
for FILE in $STAGED_FILES; do
    if [ -f "$FILE" ]; then
        php -l "$FILE" > /dev/null 2>&1
        if [ $? -ne 0 ]; then
            print_error "Syntax error in $FILE"
            SYNTAX_ERRORS=$((SYNTAX_ERRORS + 1))
        fi
    fi
done

if [ $SYNTAX_ERRORS -gt 0 ]; then
    print_error "Found $SYNTAX_ERRORS syntax error(s). Please fix them before committing."
    exit 1
fi
print_success "PHP syntax check passed"

# 2. PHP CodeSniffer (PSR-12)
print_status "Running PHP CodeSniffer (PSR-12)..."
if [ -f "vendor/bin/phpcs" ]; then
    vendor/bin/phpcs --standard=phpcs.xml $STAGED_FILES
    if [ $? -ne 0 ]; then
        print_error "Code style violations found. Run 'composer fix' to auto-fix issues."
        print_warning "Or run 'vendor/bin/phpcbf --standard=phpcs.xml $STAGED_FILES' to fix specific files."
        exit 1
    fi
    print_success "Code style check passed"
else
    print_warning "PHP CodeSniffer not found. Skipping code style check."
fi

# 3. PHPStan Static Analysis
print_status "Running PHPStan static analysis..."
if [ -f "vendor/bin/phpstan" ]; then
    # Only analyze staged files that exist in src/ directory
    SRC_FILES=$(echo "$STAGED_FILES" | grep "^src/" || true)
    if [ ! -z "$SRC_FILES" ]; then
        vendor/bin/phpstan analyse --level=8 --no-progress --quiet $SRC_FILES
        if [ $? -ne 0 ]; then
            print_error "Static analysis errors found. Please fix them before committing."
            exit 1
        fi
        print_success "Static analysis passed"
    else
        print_status "No src/ files to analyze"
    fi
else
    print_warning "PHPStan not found. Skipping static analysis."
fi

# 4. Unit Tests (only if test files are modified or core files changed)
TEST_FILES=$(echo "$STAGED_FILES" | grep -E "(test|Test)" || true)
CORE_FILES=$(echo "$STAGED_FILES" | grep -E "^(src/|tests/)" || true)

if [ ! -z "$TEST_FILES" ] || [ ! -z "$CORE_FILES" ]; then
    print_status "Running unit tests..."
    if [ -f "vendor/bin/phpunit" ]; then
        vendor/bin/phpunit --testsuite=Unit --stop-on-failure --no-coverage
        if [ $? -ne 0 ]; then
            print_error "Unit tests failed. Please fix them before committing."
            exit 1
        fi
        print_success "Unit tests passed"
    else
        print_warning "PHPUnit not found. Skipping unit tests."
    fi
fi

# 5. Security Check
print_status "Running security audit..."
composer audit --quiet
if [ $? -ne 0 ]; then
    print_warning "Security vulnerabilities found in dependencies. Consider updating."
    # Don't fail the commit for security issues, just warn
fi

# 6. Check for debugging statements
print_status "Checking for debugging statements..."
DEBUG_FOUND=0
for FILE in $STAGED_FILES; do
    if [ -f "$FILE" ]; then
        # Check for common debugging statements
        if grep -n -E "(var_dump|print_r|dd\(|dump\(|console\.log|debugger)" "$FILE"; then
            print_error "Debugging statement found in $FILE"
            DEBUG_FOUND=1
        fi
    fi
done

if [ $DEBUG_FOUND -eq 1 ]; then
    print_error "Please remove debugging statements before committing."
    exit 1
fi

# 7. Check file permissions
print_status "Checking file permissions..."
for FILE in $STAGED_FILES; do
    if [ -f "$FILE" ] && [ -x "$FILE" ]; then
        print_warning "File $FILE is executable. This might not be intended for PHP files."
    fi
done

print_success "All pre-commit checks passed! ✨"
print_status "Proceeding with commit..."

exit 0
