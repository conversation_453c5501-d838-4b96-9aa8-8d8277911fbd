<?php

declare(strict_types=1);

namespace WeBot\Controllers;

/**
 * Admin Controller
 *
 * Handles administrative operations including user management,
 * bot settings, statistics, and system configuration.
 *
 * @package WeBot\Controllers
 * @version 2.0
 */
class AdminController extends BaseController
{
    /**
     * Handle admin commands
     */
    public function handleAdmin(array $message): array
    {
        try {
            $this->initialize(['message' => $message]);

            if (!$this->isAdmin()) {
                return $this->sendMessage('⛔ شما دسترسی ادمین ندارید.');
            }

            $this->logAction('admin_command');

            return $this->showAdminPanel();
        } catch (\Throwable $e) {
            return $this->handleError($e, 'خطا در پنل مدیریت.');
        }
    }

    /**
     * Handle admin callback queries
     */
    public function handleCallback(array $callbackQuery): array
    {
        try {
            $this->initialize(['callback_query' => $callbackQuery]);

            if (!$this->isAdmin()) {
                $this->answerCallback('⛔ دسترسی غیرمجاز');
                return ['ok' => true];
            }

            $data = $this->data;

            return match (true) {
                $data === 'admin_panel' => $this->showAdminPanel(),
                $data === 'manage_users' => $this->showUserManagement(),
                $data === 'manage_servers' => $this->showServerManagement(),
                $data === 'manage_plans' => $this->showPlanManagement(),
                $data === 'bot_settings' => $this->showBotSettings(),
                $data === 'statistics' => $this->showStatistics(),
                $data === 'financial_reports' => $this->showFinancialReports(),
                $data === 'broadcast_message' => $this->showBroadcastMenu(),
                str_starts_with($data, 'user_') => $this->handleUserAction($data),
                str_starts_with($data, 'server_') => $this->handleServerAction($data),
                str_starts_with($data, 'plan_') => $this->handlePlanAction($data),
                str_starts_with($data, 'setting_') => $this->handleSettingAction($data),
                default => $this->handleUnknownCallback()
            };
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    /**
     * Show main admin panel
     */
    private function showAdminPanel(): array
    {
        $stats = $this->getQuickStats();

        $text = "⚙️ پنل مدیریت WeBot\n\n";
        $text .= "📊 آمار سریع:\n";
        $text .= "👥 کل کاربران: " . number_format($stats['total_users']) . "\n";
        $text .= "🆕 کاربران امروز: " . number_format($stats['today_users']) . "\n";
        $text .= "🔗 کل سرویس‌ها: " . number_format($stats['total_services']) . "\n";
        $text .= "💰 درآمد امروز: " . number_format($stats['today_income']) . " تومان\n\n";
        $text .= "لطفاً یکی از گزینه‌های زیر را انتخاب کنید:";

        $keyboard = $this->createInlineKeyboard([
            [
                ['text' => '👥 مدیریت کاربران', 'callback_data' => 'manage_users'],
                ['text' => '🖥 مدیریت سرورها', 'callback_data' => 'manage_servers']
            ],
            [
                ['text' => '📋 مدیریت پلن‌ها', 'callback_data' => 'manage_plans'],
                ['text' => '⚙️ تنظیمات ربات', 'callback_data' => 'bot_settings']
            ],
            [
                ['text' => '📊 آمار کامل', 'callback_data' => 'statistics'],
                ['text' => '💰 گزارش مالی', 'callback_data' => 'financial_reports']
            ],
            [
                ['text' => '📢 ارسال همگانی', 'callback_data' => 'broadcast_message'],
                ['text' => '🔧 ابزارها', 'callback_data' => 'admin_tools']
            ],
            [
                ['text' => '🔙 بازگشت به منوی اصلی', 'callback_data' => 'main_menu']
            ]
        ]);

        return $this->editMessage($text, $keyboard);
    }

    /**
     * Show user management
     */
    private function showUserManagement(): array
    {
        $text = "👥 مدیریت کاربران\n\n";
        $text .= "انتخاب کنید که چه کاری می‌خواهید انجام دهید:";

        $keyboard = $this->createInlineKeyboard([
            [
                ['text' => '🔍 جستجوی کاربر', 'callback_data' => 'user_search'],
                ['text' => '📋 لیست کاربران', 'callback_data' => 'user_list']
            ],
            [
                ['text' => '🆕 کاربران جدید', 'callback_data' => 'user_new'],
                ['text' => '🚫 کاربران مسدود', 'callback_data' => 'user_banned']
            ],
            [
                ['text' => '👑 مدیران', 'callback_data' => 'user_admins'],
                ['text' => '💰 کاربران VIP', 'callback_data' => 'user_vip']
            ],
            [
                ['text' => '🔙 بازگشت', 'callback_data' => 'admin_panel']
            ]
        ]);

        return $this->editMessage($text, $keyboard);
    }

    /**
     * Show server management
     */
    private function showServerManagement(): array
    {
        $servers = $this->getServersList();

        $text = "🖥 مدیریت سرورها\n\n";
        $text .= "تعداد سرورها: " . count($servers) . "\n\n";

        $keyboard = [];

        foreach ($servers as $server) {
            $status = $server['active'] ? '🟢' : '🔴';
            $keyboard[] = [
                ['text' => "{$status} {$server['title']}", 'callback_data' => "server_manage_{$server['id']}"]
            ];
        }

        $keyboard[] = [
            ['text' => '➕ افزودن سرور جدید', 'callback_data' => 'server_add']
        ];
        $keyboard[] = [
            ['text' => '🔙 بازگشت', 'callback_data' => 'admin_panel']
        ];

        return $this->editMessage($text, $this->createInlineKeyboard($keyboard));
    }

    /**
     * Show plan management
     */
    private function showPlanManagement(): array
    {
        $plans = $this->getPlansList();

        $text = "📋 مدیریت پلن‌ها\n\n";
        $text .= "تعداد پلن‌ها: " . count($plans) . "\n\n";

        $keyboard = [];

        foreach ($plans as $plan) {
            $status = $plan['active'] ? '🟢' : '🔴';
            $price = number_format($plan['price']);
            $keyboard[] = [
                ['text' => "{$status} {$plan['title']} - {$price}T", 'callback_data' => "plan_manage_{$plan['id']}"]
            ];
        }

        $keyboard[] = [
            ['text' => '➕ افزودن پلن جدید', 'callback_data' => 'plan_add']
        ];
        $keyboard[] = [
            ['text' => '🔙 بازگشت', 'callback_data' => 'admin_panel']
        ];

        return $this->editMessage($text, $this->createInlineKeyboard($keyboard));
    }

    /**
     * Show bot settings
     */
    private function showBotSettings(): array
    {
        $settings = $this->getBotSettings();

        $text = "⚙️ تنظیمات ربات\n\n";
        $text .= "وضعیت فعلی تنظیمات:\n\n";

        $botState = $settings['botState'] ?? 'on';
        $sellState = $settings['sellState'] ?? 'on';
        $requirePhone = $settings['requirePhone'] ?? 'off';
        $requireIranPhone = $settings['requireIranPhone'] ?? 'off';

        $text .= "🤖 وضعیت ربات: " . ($botState === 'on' ? '🟢 فعال' : '🔴 غیرفعال') . "\n";
        $text .= "🛒 وضعیت فروش: " . ($sellState === 'on' ? '🟢 فعال' : '🔴 غیرفعال') . "\n";
        $text .= "📱 الزام شماره تلفن: " . ($requirePhone === 'on' ? '🟢 فعال' : '🔴 غیرفعال') . "\n";
        $text .= "🇮🇷 الزام شماره ایرانی: " . ($requireIranPhone === 'on' ? '🟢 فعال' : '🔴 غیرفعال') . "\n";

        $keyboard = $this->createInlineKeyboard([
            [
                ['text' => '🤖 تغییر وضعیت ربات', 'callback_data' => 'setting_bot_state'],
                ['text' => '🛒 تغییر وضعیت فروش', 'callback_data' => 'setting_sell_state']
            ],
            [
                ['text' => '📱 تنظیم شماره تلفن', 'callback_data' => 'setting_phone'],
                ['text' => '💬 تنظیم متن‌ها', 'callback_data' => 'setting_texts']
            ],
            [
                ['text' => '💳 تنظیم درگاه‌ها', 'callback_data' => 'setting_gateways'],
                ['text' => '🎨 تنظیم ظاهر', 'callback_data' => 'setting_appearance']
            ],
            [
                ['text' => '🔙 بازگشت', 'callback_data' => 'admin_panel']
            ]
        ]);

        return $this->editMessage($text, $keyboard);
    }

    /**
     * Show statistics
     */
    private function showStatistics(): array
    {
        $stats = $this->getDetailedStats();

        $text = "📊 آمار کامل سیستم\n\n";

        $text .= "👥 آمار کاربران:\n";
        $text .= "• کل کاربران: " . number_format($stats['users']['total']) . "\n";
        $text .= "• کاربران فعال: " . number_format($stats['users']['active']) . "\n";
        $text .= "• کاربران امروز: " . number_format($stats['users']['today']) . "\n";
        $text .= "• کاربران این ماه: " . number_format($stats['users']['this_month']) . "\n\n";

        $text .= "🔗 آمار سرویس‌ها:\n";
        $text .= "• کل سرویس‌ها: " . number_format($stats['services']['total']) . "\n";
        $text .= "• سرویس‌های فعال: " . number_format($stats['services']['active']) . "\n";
        $text .= "• سرویس‌های امروز: " . number_format($stats['services']['today']) . "\n\n";

        $text .= "💰 آمار مالی:\n";
        $text .= "• درآمد کل: " . number_format($stats['financial']['total_income']) . " تومان\n";
        $text .= "• درآمد امروز: " . number_format($stats['financial']['today_income']) . " تومان\n";
        $text .= "• درآمد این ماه: " . number_format($stats['financial']['month_income']) . " تومان\n";

        $keyboard = $this->createInlineKeyboard([
            [
                ['text' => '📈 آمار تفصیلی', 'callback_data' => 'stats_detailed'],
                ['text' => '📊 نمودار', 'callback_data' => 'stats_chart']
            ],
            [
                ['text' => '📄 گزارش Excel', 'callback_data' => 'stats_export'],
                ['text' => '🔄 به‌روزرسانی', 'callback_data' => 'statistics']
            ],
            [
                ['text' => '🔙 بازگشت', 'callback_data' => 'admin_panel']
            ]
        ]);

        return $this->editMessage($text, $keyboard);
    }

    /**
     * Get quick statistics
     */
    private function getQuickStats(): array
    {
        $stats = [];

        // Total users
        $stmt = $this->database->prepare("SELECT COUNT(*) as count FROM `users`");
        $stmt->execute();
        $stats['total_users'] = $stmt->get_result()->fetch_assoc()['count'];
        $stmt->close();

        // Today users
        $stmt = $this->database->prepare("SELECT COUNT(*) as count FROM `users` WHERE DATE(created_at) = CURDATE()");
        $stmt->execute();
        $stats['today_users'] = $stmt->get_result()->fetch_assoc()['count'];
        $stmt->close();

        // Total services (assuming a services table exists)
        $stats['total_services'] = 0; // Placeholder

        // Today income (assuming a payments table exists)
        $stats['today_income'] = 0; // Placeholder

        return $stats;
    }

    /**
     * Get detailed statistics
     */
    private function getDetailedStats(): array
    {
        // This would contain more detailed statistics
        // For now, returning basic structure
        return [
            'users' => [
                'total' => 0,
                'active' => 0,
                'today' => 0,
                'this_month' => 0
            ],
            'services' => [
                'total' => 0,
                'active' => 0,
                'today' => 0
            ],
            'financial' => [
                'total_income' => 0,
                'today_income' => 0,
                'month_income' => 0
            ]
        ];
    }

    /**
     * Get servers list
     */
    private function getServersList(): array
    {
        $stmt = $this->database->prepare("SELECT * FROM `server_info` ORDER BY `id` ASC");
        $stmt->execute();
        $result = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
        $stmt->close();

        return $result;
    }

    /**
     * Get plans list
     */
    private function getPlansList(): array
    {
        $stmt = $this->database->prepare("SELECT * FROM `server_plans` WHERE `active` = 1 ORDER BY `id` ASC");
        $stmt->execute();
        $result = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
        $stmt->close();

        return $result;
    }

    // Placeholder methods for other admin functions
    private function showFinancialReports(): array
    {
        return $this->editMessage(
            '🚧 گزارش مالی در حال توسعه است...',
            $this->createInlineKeyboard([[['text' => '🔙 بازگشت', 'callback_data' => 'admin_panel']]])
        );
    }

    private function showBroadcastMenu(): array
    {
        return $this->editMessage(
            '🚧 ارسال همگانی در حال توسعه است...',
            $this->createInlineKeyboard([[['text' => '🔙 بازگشت', 'callback_data' => 'admin_panel']]])
        );
    }

    private function handleUserAction(string $data): array
    {
        // TODO: Implement user management based on $data
        unset($data); // Suppress unused variable warning

        return $this->editMessage(
            '🚧 عملیات کاربر در حال توسعه است...',
            $this->createInlineKeyboard([[['text' => '🔙 بازگشت', 'callback_data' => 'manage_users']]])
        );
    }

    private function handleServerAction(string $data): array
    {
        // TODO: Implement server management based on $data
        unset($data); // Suppress unused variable warning

        return $this->editMessage(
            '🚧 عملیات سرور در حال توسعه است...',
            $this->createInlineKeyboard([[['text' => '🔙 بازگشت', 'callback_data' => 'manage_servers']]])
        );
    }

    private function handlePlanAction(string $data): array
    {
        // TODO: Implement plan management based on $data
        unset($data); // Suppress unused variable warning

        return $this->editMessage(
            '🚧 عملیات پلن در حال توسعه است...',
            $this->createInlineKeyboard([[['text' => '🔙 بازگشت', 'callback_data' => 'manage_plans']]])
        );
    }

    private function handleSettingAction(string $data): array
    {
        // TODO: Implement settings management based on $data
        unset($data); // Suppress unused variable warning

        return $this->editMessage(
            '🚧 تنظیمات در حال توسعه است...',
            $this->createInlineKeyboard([[['text' => '🔙 بازگشت', 'callback_data' => 'bot_settings']]])
        );
    }

    private function handleUnknownCallback(): array
    {
        $this->answerCallback('گزینه نامعتبر');
        return ['ok' => true];
    }
}
