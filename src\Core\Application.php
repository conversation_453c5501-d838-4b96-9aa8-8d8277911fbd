<?php

declare(strict_types=1);

namespace WeBot\Core;

use WeBot\Exceptions\WeBotException;
use WeBot\Utils\Logger;
use Exception;

// Mock Container if League\Container is not available
if (!class_exists('League\Container\Container')) {
    class Container
    {
        private array $bindings = [];

        public function add(string $id, $concrete = null)
        {
            $this->bindings[$id] = $concrete;
            return $this;
        }

        public function get(string $id)
        {
            return $this->bindings[$id] ?? null;
        }

        public function has(string $id): bool
        {
            return isset($this->bindings[$id]);
        }

        public function set(string $id, $value): void
        {
            $this->bindings[$id] = $value;
        }
    }
}

/**
 * WeBot Application Core
 *
 * Main application class that handles request routing,
 * middleware processing, and response generation.
 *
 * @package WeBot\Core
 * @version 2.0
 */
class Application
{
    private Container $container;
    private Config $config;
    private Logger $logger;
    private array $middleware = [];
    private array $routes = [];

    public function __construct()
    {
        $this->container = new Container();
        $this->setupContainer();
        $this->config = $this->container->get('config');
        $this->logger = $this->container->get('logger');

        $this->registerRoutes();
        $this->registerMiddleware();
    }

    /**
     * Run the application
     */
    public function run(): void
    {
        try {
            // Get request data
            $input = $this->getInput();

            if (empty($input)) {
                $this->sendResponse(['status' => 'ok', 'message' => 'WeBot is running']);
                return;
            }

            // Log incoming request
            $this->logger->info('Incoming request', ['input' => $input]);

            // Process through middleware
            $request = $this->processMiddleware($input);

            // Route the request
            $response = $this->route($request);

            // Send response
            $this->sendResponse($response);
        } catch (WeBotException $e) {
            $this->handleWeBotException($e);
        } catch (Exception $e) {
            $this->handleException($e);
        }
    }

    /**
     * Get input from Telegram webhook
     */
    private function getInput(): array
    {
        $input = file_get_contents('php://input');

        if (empty($input)) {
            return [];
        }

        $data = json_decode($input, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new WeBotException('Invalid JSON input: ' . json_last_error_msg());
        }

        return $data ?? [];
    }

    /**
     * Process request through middleware stack
     */
    private function processMiddleware(array $input): array
    {
        $request = $input;

        foreach ($this->middleware as $middlewareClass) {
            $middleware = new $middlewareClass($this->container);
            $request = $middleware->handle($request);
        }

        return $request;
    }

    /**
     * Route the request to appropriate controller
     */
    private function route(array $request): array
    {
        // Determine request type
        if (isset($request['message'])) {
            return $this->handleMessage($request['message']);
        }

        if (isset($request['callback_query'])) {
            return $this->handleCallbackQuery($request['callback_query']);
        }

        if (isset($request['inline_query'])) {
            return $this->handleInlineQuery($request['inline_query']);
        }

        throw new WeBotException('Unknown request type');
    }

    /**
     * Handle text messages
     */
    private function handleMessage(array $message): array
    {
        $text = $message['text'] ?? '';
        $from = $message['from'] ?? [];
        unset($from); // Suppress unused variable warning

        // Route based on message content
        if (str_starts_with($text, '/start')) {
            $controller = new \WeBot\Controllers\UserController($this->container);
            return $controller->handleStart($message);
        }

        if (str_starts_with($text, '/admin')) {
            $controller = new \WeBot\Controllers\AdminController($this->container);
            return $controller->handleAdmin($message);
        }

        // Default message handling
        $controller = new \WeBot\Controllers\UserController($this->container);
        return $controller->handleMessage($message);
    }

    /**
     * Handle callback queries (inline keyboard buttons)
     */
    private function handleCallbackQuery(array $callbackQuery): array
    {
        $data = $callbackQuery['data'] ?? '';
        $from = $callbackQuery['from'] ?? [];
        unset($from); // Suppress unused variable warning

        // Route based on callback data
        if (str_starts_with($data, 'admin_')) {
            $controller = new \WeBot\Controllers\AdminController($this->container);
            return $controller->handleCallback($callbackQuery);
        }

        if (str_starts_with($data, 'payment_')) {
            $controller = new \WeBot\Controllers\PaymentController($this->container);
            return $controller->handleCallback($callbackQuery);
        }

        if (str_starts_with($data, 'service_')) {
            $controller = new \WeBot\Controllers\ServiceController($this->container);
            return $controller->handleCallback($callbackQuery);
        }

        // Default callback handling
        $controller = new \WeBot\Controllers\UserController($this->container);
        return $controller->handleCallback($callbackQuery);
    }

    /**
     * Handle inline queries
     */
    private function handleInlineQuery(array $inlineQuery): array
    {
        $controller = new \WeBot\Controllers\UserController($this->container);
        return $controller->handleInlineQuery($inlineQuery);
    }

    /**
     * Register application routes
     */
    private function registerRoutes(): void
    {
        $this->routes = [
            'message' => \WeBot\Controllers\UserController::class,
            'callback_query' => \WeBot\Controllers\UserController::class,
            'inline_query' => \WeBot\Controllers\UserController::class,
        ];
    }

    /**
     * Register middleware stack
     */
    private function registerMiddleware(): void
    {
        $this->middleware = [
            \WeBot\Middleware\AuthMiddleware::class,
            \WeBot\Middleware\RateLimitMiddleware::class,
            \WeBot\Middleware\ValidationMiddleware::class,
        ];
    }

    /**
     * Send response
     */
    private function sendResponse(array $response): void
    {
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    /**
     * Handle WeBot specific exceptions
     */
    private function handleWeBotException(WeBotException $e): void
    {
        $this->logger->error('WeBot Exception', [
            'message' => $e->getMessage(),
            'code' => $e->getCode(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);

        $this->sendResponse([
            'error' => true,
            'message' => $e->getMessage(),
            'code' => $e->getCode()
        ]);
    }

    /**
     * Handle general exceptions
     */
    private function handleException(Exception $e): void
    {
        $this->logger->critical('Unhandled Exception', [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]);

        $this->sendResponse([
            'error' => true,
            'message' => 'Internal server error'
        ]);
    }

    /**
     * Get container instance
     */
    public function getContainer(): Container
    {
        return $this->container;
    }

    /**
     * Setup container with dependencies
     */
    private function setupContainer(): void
    {
        // Setup basic dependencies
        $this->container->set('config', new Config());
        $this->container->set('logger', Logger::getInstance());
    }

    /**
     * Get config instance
     */
    public function getConfig(): Config
    {
        return $this->config;
    }

    /**
     * Process webhook data (for testing)
     */
    public function processWebhook(array $webhookData): array
    {
        try {
            // Log incoming webhook
            $this->logger->info('Processing webhook', ['data' => $webhookData]);

            // Process through middleware
            $request = $this->processMiddleware($webhookData);

            // Route the request
            $response = $this->route($request);

            return $response;
        } catch (WeBotException $e) {
            $this->logger->error('WeBot Exception in webhook', [
                'message' => $e->getMessage(),
                'code' => $e->getCode()
            ]);

            return [
                'error' => true,
                'message' => $e->getMessage(),
                'code' => $e->getCode()
            ];
        } catch (Exception $e) {
            $this->logger->critical('Unhandled Exception in webhook', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            return [
                'error' => true,
                'message' => 'Internal server error'
            ];
        }
    }
}
