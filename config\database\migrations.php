<?php

declare(strict_types=1);

/**
 * Database Migrations Configuration
 * 
 * Configuration for database migrations, schema versioning,
 * and migration execution settings.
 * 
 * @package WeBot\Config\Database
 * @version 2.0
 */

return [
    /*
    |--------------------------------------------------------------------------
    | Migrations Table
    |--------------------------------------------------------------------------
    */
    'table' => $_ENV['MIGRATIONS_TABLE'] ?? 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Migrations Directory
    |--------------------------------------------------------------------------
    */
    'path' => $_ENV['MIGRATIONS_PATH'] ?? 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Migration Execution Settings
    |--------------------------------------------------------------------------
    */
    'execution' => [
        'timeout' => (int)($_ENV['MIGRATION_TIMEOUT'] ?? 300), // 5 minutes
        'memory_limit' => $_ENV['MIGRATION_MEMORY_LIMIT'] ?? '512M',
        'batch_size' => (int)($_ENV['MIGRATION_BATCH_SIZE'] ?? 1000),
        'transaction_mode' => $_ENV['MIGRATION_TRANSACTION_MODE'] ?? 'per_migration', // 'per_migration', 'per_batch', 'none'
        'rollback_on_failure' => (bool)($_ENV['MIGRATION_ROLLBACK_ON_FAILURE'] ?? true),
        'continue_on_error' => (bool)($_ENV['MIGRATION_CONTINUE_ON_ERROR'] ?? false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Schema Validation
    |--------------------------------------------------------------------------
    */
    'validation' => [
        'enabled' => (bool)($_ENV['MIGRATION_VALIDATION_ENABLED'] ?? true),
        'check_foreign_keys' => true,
        'check_indexes' => true,
        'check_constraints' => true,
        'validate_data_types' => true,
        'strict_mode' => (bool)($_ENV['MIGRATION_STRICT_MODE'] ?? false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup Before Migration
    |--------------------------------------------------------------------------
    */
    'backup' => [
        'enabled' => (bool)($_ENV['MIGRATION_BACKUP_ENABLED'] ?? true),
        'path' => $_ENV['MIGRATION_BACKUP_PATH'] ?? 'storage/backups/migrations',
        'compression' => true,
        'retention_days' => 7,
        'include_data' => (bool)($_ENV['MIGRATION_BACKUP_INCLUDE_DATA'] ?? false),
        'exclude_tables' => [
            'sessions',
            'telegram_updates',
            'system_logs',
            'user_activities'
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Templates
    |--------------------------------------------------------------------------
    */
    'templates' => [
        'default' => 'default',
        'available' => [
            'default' => [
                'path' => 'templates/migration.sql.stub',
                'description' => 'Standard SQL migration template'
            ],
            'table_creation' => [
                'path' => 'templates/create_table.sql.stub',
                'description' => 'Template for creating new tables'
            ],
            'table_modification' => [
                'path' => 'templates/alter_table.sql.stub',
                'description' => 'Template for modifying existing tables'
            ],
            'data_migration' => [
                'path' => 'templates/data_migration.sql.stub',
                'description' => 'Template for data migrations'
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Seeding Configuration
    |--------------------------------------------------------------------------
    */
    'seeding' => [
        'enabled' => (bool)($_ENV['SEEDING_ENABLED'] ?? true),
        'path' => $_ENV['SEEDERS_PATH'] ?? 'database/seeders',
        'auto_run' => (bool)($_ENV['AUTO_RUN_SEEDERS'] ?? false),
        'environment_specific' => true,
        'order' => [
            'AdminSeeder',
            'DefaultSettingsSeeder',
            'BotCommandsSeeder',
            'TestDataSeeder' // Only in development
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Version Control
    |--------------------------------------------------------------------------
    */
    'versioning' => [
        'enabled' => true,
        'format' => 'timestamp', // 'timestamp', 'sequential', 'semantic'
        'prefix' => '',
        'suffix' => '',
        'track_rollbacks' => true,
        'max_rollback_steps' => 10,
    ],

    /*
    |--------------------------------------------------------------------------
    | Environment-Specific Settings
    |--------------------------------------------------------------------------
    */
    'environments' => [
        'development' => [
            'auto_migrate' => (bool)($_ENV['DEV_AUTO_MIGRATE'] ?? false),
            'allow_destructive' => true,
            'backup_required' => false,
            'confirmation_required' => false,
        ],
        'testing' => [
            'auto_migrate' => true,
            'allow_destructive' => true,
            'backup_required' => false,
            'confirmation_required' => false,
            'fresh_database' => true,
        ],
        'staging' => [
            'auto_migrate' => false,
            'allow_destructive' => false,
            'backup_required' => true,
            'confirmation_required' => true,
        ],
        'production' => [
            'auto_migrate' => false,
            'allow_destructive' => false,
            'backup_required' => true,
            'confirmation_required' => true,
            'maintenance_mode' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring & Logging
    |--------------------------------------------------------------------------
    */
    'monitoring' => [
        'log_migrations' => true,
        'log_level' => $_ENV['MIGRATION_LOG_LEVEL'] ?? 'info',
        'log_file' => $_ENV['MIGRATION_LOG_FILE'] ?? 'storage/logs/migrations.log',
        'notify_on_completion' => (bool)($_ENV['MIGRATION_NOTIFY_COMPLETION'] ?? false),
        'notify_on_failure' => (bool)($_ENV['MIGRATION_NOTIFY_FAILURE'] ?? true),
        'notification_channels' => [
            'email' => $_ENV['MIGRATION_NOTIFY_EMAIL'] ?? '',
            'slack' => $_ENV['MIGRATION_NOTIFY_SLACK'] ?? '',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'parallel_execution' => (bool)($_ENV['MIGRATION_PARALLEL'] ?? false),
        'max_parallel_jobs' => (int)($_ENV['MIGRATION_MAX_PARALLEL'] ?? 2),
        'chunk_size' => (int)($_ENV['MIGRATION_CHUNK_SIZE'] ?? 1000),
        'optimize_tables' => (bool)($_ENV['MIGRATION_OPTIMIZE_TABLES'] ?? true),
        'analyze_tables' => (bool)($_ENV['MIGRATION_ANALYZE_TABLES'] ?? true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    */
    'security' => [
        'require_confirmation' => [
            'drop_table' => true,
            'drop_column' => true,
            'truncate_table' => true,
            'delete_data' => true,
        ],
        'forbidden_operations' => [
            // Operations that are never allowed
            'DROP DATABASE',
            'DROP SCHEMA',
            'GRANT',
            'REVOKE',
        ],
        'allowed_users' => [
            // Users allowed to run migrations
            $_ENV['DB_USERNAME'] ?? 'root',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Rollback Configuration
    |--------------------------------------------------------------------------
    */
    'rollback' => [
        'enabled' => true,
        'automatic_rollback_scripts' => false, // Generate rollback scripts automatically
        'rollback_timeout' => (int)($_ENV['ROLLBACK_TIMEOUT'] ?? 180), // 3 minutes
        'max_rollback_depth' => (int)($_ENV['MAX_ROLLBACK_DEPTH'] ?? 5),
        'require_confirmation' => true,
        'backup_before_rollback' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Testing Configuration
    |--------------------------------------------------------------------------
    */
    'testing' => [
        'dry_run' => (bool)($_ENV['MIGRATION_DRY_RUN'] ?? false),
        'validate_syntax' => true,
        'check_dependencies' => true,
        'simulate_execution' => false,
        'generate_reports' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Migration Types
    |--------------------------------------------------------------------------
    */
    'custom_types' => [
        'data_migration' => [
            'class' => 'WeBot\\Database\\Migrations\\DataMigration',
            'template' => 'data_migration',
        ],
        'view_migration' => [
            'class' => 'WeBot\\Database\\Migrations\\ViewMigration',
            'template' => 'view_migration',
        ],
        'procedure_migration' => [
            'class' => 'WeBot\\Database\\Migrations\\ProcedureMigration',
            'template' => 'procedure_migration',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Integration Settings
    |--------------------------------------------------------------------------
    */
    'integration' => [
        'git_hooks' => [
            'enabled' => (bool)($_ENV['MIGRATION_GIT_HOOKS'] ?? false),
            'pre_commit' => true,
            'post_merge' => true,
        ],
        'ci_cd' => [
            'enabled' => (bool)($_ENV['MIGRATION_CI_CD'] ?? false),
            'pipeline_integration' => true,
            'deployment_gates' => true,
        ],
    ],
];
