<?php

declare(strict_types=1);

namespace WeBot\Core;

/**
 * Performance Monitor
 *
 * Comprehensive performance monitoring with metrics collection,
 * bottleneck detection, and optimization recommendations.
 *
 * @package WeBot\Core
 * @version 2.0
 */
class PerformanceMonitor
{
    private array $timers = [];
    private array $metrics = [];
    private array $thresholds = [];
    private array $alerts = [];
    private MemoryManager $memoryManager;
    private ResourceManager $resourceManager;
    private bool $enabled;

    public function __construct(
        MemoryManager $memoryManager = null,
        ResourceManager $resourceManager = null,
        array $config = []
    ) {
        $this->memoryManager = $memoryManager ?? new class {
            public function getUsage(): array
            {
                return ['current' => memory_get_usage(), 'peak' => memory_get_peak_usage()];
            }
            public function optimize(): void
            {
            }
        };
        $this->resourceManager = $resourceManager ?? new class {
            public function getStats(): array
            {
                return ['cpu' => 0, 'disk' => 0];
            }
            public function cleanup(): void
            {
            }
        };
        $this->enabled = $config['enabled'] ?? true;

        $this->thresholds = array_merge([
            'slow_query' => 1.0,        // seconds
            'slow_request' => 2.0,      // seconds
            'high_memory' => 80,        // percentage
            'high_cpu' => 80,           // percentage
            'max_execution_time' => 30  // seconds
        ], $config['thresholds'] ?? []);
    }

    /**
     * Start timing an operation
     */
    public function startTimer(string $name): void
    {
        if (!$this->enabled) {
            return;
        }

        $this->timers[$name] = [
            'start' => microtime(true),
            'memory_start' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ];
    }

    /**
     * Stop timing an operation
     */
    public function stopTimer(string $name): ?array
    {
        if (!$this->enabled || !isset($this->timers[$name])) {
            return null;
        }

        $timer = $this->timers[$name];
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        $peakMemory = memory_get_peak_usage(true);

        $result = [
            'name' => $name,
            'duration' => $endTime - $timer['start'],
            'memory_used' => $endMemory - $timer['memory_start'],
            'peak_memory' => $peakMemory,
            'timestamp' => $endTime
        ];

        // Store metric
        $this->recordMetric('timer', $result);

        // Check thresholds
        $this->checkThresholds($result);

        unset($this->timers[$name]);
        return $result;
    }

    /**
     * Measure execution time of a callable
     */
    public function measure(string $name, callable $callback, ...$args)
    {
        $this->startTimer($name);

        try {
            $result = $callback(...$args);
            return $result;
        } finally {
            $this->stopTimer($name);
        }
    }

    /**
     * Record a custom metric
     */
    public function recordMetric(string $type, array $data): void
    {
        if (!$this->enabled) {
            return;
        }

        $metric = [
            'type' => $type,
            'data' => $data,
            'timestamp' => microtime(true),
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ];

        $this->metrics[] = $metric;

        // Limit metrics array size
        if (count($this->metrics) > 1000) {
            $this->metrics = array_slice($this->metrics, -500);
        }
    }

    /**
     * Get performance statistics
     */
    public function getStats(): array
    {
        $stats = [
            'system' => $this->getSystemStats(),
            'memory' => $this->memoryManager->getCurrentUsage(),
            'resources' => $this->resourceManager->getStats(),
            'timers' => $this->getTimerStats(),
            'metrics' => $this->getMetricStats(),
            'bottlenecks' => $this->detectBottlenecks(),
            'recommendations' => $this->getRecommendations()
        ];

        return $stats;
    }

    /**
     * Get real-time performance metrics
     */
    public function getRealTimeMetrics(): array
    {
        return [
            'timestamp' => microtime(true),
            'memory' => [
                'current' => memory_get_usage(true),
                'peak' => memory_get_peak_usage(true),
                'limit' => ini_get('memory_limit')
            ],
            'cpu' => $this->getCpuUsage(),
            'load' => sys_getloadavg(),
            'connections' => $this->getConnectionStats(),
            'cache' => $this->getCacheMetrics(),
            'database' => $this->getDatabaseMetrics()
        ];
    }

    /**
     * Monitor API endpoint performance
     */
    public function monitorEndpoint(string $endpoint, callable $callback, ...$args)
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);

        try {
            $result = $callback(...$args);
            $success = true;
            $error = null;
        } catch (\Exception $e) {
            $result = null;
            $success = false;
            $error = $e->getMessage();
        }

        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);

        $metrics = [
            'endpoint' => $endpoint,
            'duration' => $endTime - $startTime,
            'memory_used' => $endMemory - $startMemory,
            'success' => $success,
            'error' => $error,
            'timestamp' => $endTime
        ];

        $this->recordMetric('endpoint', $metrics);

        // Check for performance issues
        if ($metrics['duration'] > 2.0) {
            $this->recordAlert('slow_endpoint', $metrics);
        }

        if ($metrics['memory_used'] > 50 * 1024 * 1024) { // 50MB
            $this->recordAlert('high_memory_usage', $metrics);
        }

        return $result;
    }

    /**
     * Record performance alert
     */
    public function recordAlert(string $type, array $data): void
    {
        $alert = [
            'type' => $type,
            'data' => $data,
            'timestamp' => microtime(true),
            'severity' => $this->getAlertSeverity($type, $data)
        ];

        $this->alerts[] = $alert;

        // Limit alerts array size
        if (count($this->alerts) > 100) {
            $this->alerts = array_slice($this->alerts, -50);
        }

        // Log critical alerts
        if ($alert['severity'] === 'critical') {
            error_log("Performance Alert: {$type} - " . json_encode($data));
        }
    }

    /**
     * Get performance recommendations
     */
    public function getPerformanceRecommendations(): array
    {
        $recommendations = [];
        $stats = $this->getStats();

        // Memory recommendations
        if (isset($stats['memory']['usage_percentage']) && $stats['memory']['usage_percentage'] > 80) {
            $recommendations[] = [
                'type' => 'memory',
                'priority' => 'high',
                'message' => 'Memory usage is high (>80%). Consider optimizing memory usage or increasing memory limit.',
                'action' => 'optimize_memory'
            ];
        }

        // Database recommendations
        if (isset($stats['database']['slow_queries']) && count($stats['database']['slow_queries']) > 10) {
            $recommendations[] = [
                'type' => 'database',
                'priority' => 'medium',
                'message' => 'Multiple slow queries detected. Consider optimizing database queries.',
                'action' => 'optimize_queries'
            ];
        }

        // Cache recommendations
        if (isset($stats['cache']['hit_rate']) && $stats['cache']['hit_rate'] < 70) {
            $recommendations[] = [
                'type' => 'cache',
                'priority' => 'medium',
                'message' => 'Cache hit rate is low (<70%). Consider reviewing cache strategy.',
                'action' => 'optimize_cache'
            ];
        }

        return $recommendations;
    }

    /**
     * Get CPU usage (approximation)
     */
    private function getCpuUsage(): array
    {
        // This is a simplified CPU usage calculation
        // In production, you might want to use more sophisticated methods
        $load = sys_getloadavg();
        return [
            'load_1min' => $load[0] ?? 0,
            'load_5min' => $load[1] ?? 0,
            'load_15min' => $load[2] ?? 0,
            'cores' => $this->getCpuCores()
        ];
    }

    /**
     * Get number of CPU cores
     */
    private function getCpuCores(): int
    {
        if (PHP_OS_FAMILY === 'Windows') {
            return (int)shell_exec('echo %NUMBER_OF_PROCESSORS%') ?: 1;
        } else {
            return (int)shell_exec('nproc') ?: 1;
        }
    }

    /**
     * Get connection statistics
     */
    private function getConnectionStats(): array
    {
        // This would typically integrate with your database/Redis connections
        return [
            'active_connections' => 0,
            'total_connections' => 0,
            'connection_pool_usage' => 0
        ];
    }

    /**
     * Get cache metrics
     */
    private function getCacheMetrics(): array
    {
        // This would typically integrate with your cache manager
        return [
            'hit_rate' => 0,
            'memory_usage' => 0,
            'total_keys' => 0
        ];
    }

    /**
     * Get database metrics
     */
    private function getDatabaseMetrics(): array
    {
        // This would typically integrate with your database service
        return [
            'query_count' => 0,
            'avg_query_time' => 0,
            'slow_queries' => []
        ];
    }

    /**
     * Get alert severity
     */
    private function getAlertSeverity(string $type, array $data): string
    {
        switch ($type) {
            case 'slow_endpoint':
                $duration = $data['duration'] ?? 0;
                if ($duration > 5.0) {
                    return 'critical';
                }
                if ($duration > 2.0) {
                    return 'high';
                }
                return 'medium';

            case 'high_memory_usage':
                $memory = $data['memory_used'] ?? 0;
                if ($memory > 100 * 1024 * 1024) {
                    return 'critical'; // 100MB
                }
                if ($memory > 50 * 1024 * 1024) {
                    return 'high'; // 50MB
                }
                return 'medium';

            case 'database_slow_query':
                $time = $data['execution_time'] ?? 0;
                if ($time > 10.0) {
                    return 'critical';
                }
                if ($time > 5.0) {
                    return 'high';
                }
                return 'medium';

            default:
                return 'low';
        }
    }

    /**
     * Get system statistics
     */
    public function getSystemStats(): array
    {
        $stats = [
            'php_version' => PHP_VERSION,
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'opcache_enabled' => function_exists('opcache_get_status') && opcache_get_status()['opcache_enabled'] ?? false,
            'uptime' => $this->getUptime()
        ];

        // CPU usage (if available)
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            $stats['cpu_load'] = [
                '1min' => $load[0],
                '5min' => $load[1],
                '15min' => $load[2]
            ];
        }

        // Disk usage
        $stats['disk_usage'] = [
            'free' => disk_free_space('.'),
            'total' => disk_total_space('.'),
            'used_percentage' => $this->getDiskUsagePercentage()
        ];

        return $stats;
    }

    /**
     * Detect performance bottlenecks
     */
    public function detectBottlenecks(): array
    {
        $bottlenecks = [];

        // Analyze timer data
        $timerStats = $this->getTimerStats();
        foreach ($timerStats as $name => $stats) {
            if ($stats['avg_duration'] > $this->thresholds['slow_request']) {
                $bottlenecks[] = [
                    'type' => 'slow_operation',
                    'operation' => $name,
                    'avg_duration' => $stats['avg_duration'],
                    'threshold' => $this->thresholds['slow_request'],
                    'severity' => $this->calculateSeverity($stats['avg_duration'], $this->thresholds['slow_request'])
                ];
            }
        }

        // Memory bottlenecks
        $memoryUsage = $this->memoryManager->getUsagePercentage();
        if ($memoryUsage > $this->thresholds['high_memory']) {
            $bottlenecks[] = [
                'type' => 'high_memory',
                'usage_percentage' => $memoryUsage,
                'threshold' => $this->thresholds['high_memory'],
                'severity' => $this->calculateSeverity($memoryUsage, $this->thresholds['high_memory'])
            ];
        }

        // Resource bottlenecks
        $resourceStats = $this->resourceManager->getStats();
        if ($resourceStats['total_resources'] > 100) {
            $bottlenecks[] = [
                'type' => 'high_resource_count',
                'count' => $resourceStats['total_resources'],
                'severity' => 'medium'
            ];
        }

        return $bottlenecks;
    }

    /**
     * Get optimization recommendations
     */
    public function getRecommendations(): array
    {
        $recommendations = [];
        $stats = $this->getSystemStats();
        $bottlenecks = $this->detectBottlenecks();

        // Memory recommendations
        $memoryRecommendations = $this->memoryManager->getRecommendations();
        $recommendations = array_merge($recommendations, $memoryRecommendations);

        // OPcache recommendation
        if (!$stats['opcache_enabled']) {
            $recommendations[] = [
                'type' => 'opcache',
                'message' => 'Enable OPcache for better performance',
                'priority' => 'high'
            ];
        }

        // Bottleneck-based recommendations
        foreach ($bottlenecks as $bottleneck) {
            switch ($bottleneck['type']) {
                case 'slow_operation':
                    $recommendations[] = [
                        'type' => 'optimization',
                        'message' => "Optimize {$bottleneck['operation']} operation",
                        'priority' => $bottleneck['severity']
                    ];
                    break;

                case 'high_memory':
                    $recommendations[] = [
                        'type' => 'memory',
                        'message' => 'Reduce memory usage or increase memory limit',
                        'priority' => $bottleneck['severity']
                    ];
                    break;
            }
        }

        return $recommendations;
    }

    /**
     * Generate performance report
     */
    public function generateReport(): array
    {
        return [
            'timestamp' => date('Y-m-d H:i:s'),
            'enabled' => $this->enabled,
            'stats' => $this->getStats(),
            'active_timers' => count($this->timers),
            'total_metrics' => count($this->metrics),
            'thresholds' => $this->thresholds
        ];
    }

    /**
     * Optimize performance
     */
    public function optimize(): array
    {
        $optimizations = [];

        // Memory optimization
        $memoryOpt = $this->memoryManager->optimize();
        if (!empty($memoryOpt)) {
            $optimizations['memory'] = $memoryOpt;
        }

        // Resource optimization
        $resourceOpt = $this->resourceManager->optimize();
        if (!empty($resourceOpt)) {
            $optimizations['resources'] = $resourceOpt;
        }

        // Clear old metrics
        if (count($this->metrics) > 500) {
            $this->metrics = array_slice($this->metrics, -250);
            $optimizations['metrics_cleaned'] = true;
        }

        return $optimizations;
    }

    /**
     * Get timer statistics
     */
    private function getTimerStats(): array
    {
        $stats = [];

        foreach ($this->metrics as $metric) {
            if ($metric['type'] === 'timer') {
                $name = $metric['data']['name'];

                if (!isset($stats[$name])) {
                    $stats[$name] = [
                        'count' => 0,
                        'total_duration' => 0,
                        'min_duration' => PHP_FLOAT_MAX,
                        'max_duration' => 0,
                        'total_memory' => 0
                    ];
                }

                $duration = $metric['data']['duration'];
                $memory = $metric['data']['memory_used'];

                $stats[$name]['count']++;
                $stats[$name]['total_duration'] += $duration;
                $stats[$name]['min_duration'] = min($stats[$name]['min_duration'], $duration);
                $stats[$name]['max_duration'] = max($stats[$name]['max_duration'], $duration);
                $stats[$name]['total_memory'] += $memory;
            }
        }

        // Calculate averages
        foreach ($stats as $name => &$stat) {
            $stat['avg_duration'] = $stat['total_duration'] / $stat['count'];
            $stat['avg_memory'] = $stat['total_memory'] / $stat['count'];
        }

        return $stats;
    }

    /**
     * Get metric statistics
     */
    private function getMetricStats(): array
    {
        $stats = [
            'total_count' => count($this->metrics),
            'by_type' => []
        ];

        foreach ($this->metrics as $metric) {
            $type = $metric['type'];

            if (!isset($stats['by_type'][$type])) {
                $stats['by_type'][$type] = 0;
            }

            $stats['by_type'][$type]++;
        }

        return $stats;
    }

    /**
     * Check performance thresholds
     */
    private function checkThresholds(array $result): void
    {
        if ($result['duration'] > $this->thresholds['slow_request']) {
            $this->recordMetric('threshold_violation', [
                'type' => 'slow_request',
                'operation' => $result['name'],
                'duration' => $result['duration'],
                'threshold' => $this->thresholds['slow_request']
            ]);
        }
    }

    /**
     * Calculate severity level
     */
    private function calculateSeverity(float $value, float $threshold): string
    {
        $ratio = $value / $threshold;

        if ($ratio >= 2.0) {
            return 'critical';
        } elseif ($ratio >= 1.5) {
            return 'high';
        } elseif ($ratio >= 1.2) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * Get system uptime
     */
    private function getUptime(): ?float
    {
        if (file_exists('/proc/uptime')) {
            $uptime = file_get_contents('/proc/uptime');
            return (float) explode(' ', $uptime)[0];
        }

        return null;
    }

    /**
     * Get disk usage percentage
     */
    private function getDiskUsagePercentage(): float
    {
        $free = disk_free_space('.');
        $total = disk_total_space('.');

        if ($total > 0) {
            return (($total - $free) / $total) * 100;
        }

        return 0;
    }
}
