<?php

declare(strict_types=1);

namespace WeBot\Repositories;

use WeBot\Core\Database;
use WeBot\Models\Payment;
use WeBot\Exceptions\DatabaseException;

/**
 * Payment Repository
 *
 * Handles all database operations related to payments
 * including CRUD operations, status tracking, and reporting.
 *
 * @package WeBot\Repositories
 * @version 2.0
 */
class PaymentRepository
{
    private Database $database;
    private string $table = 'payments';
    private string $itemsTable = 'payment_items';
    private string $logsTable = 'payment_logs';

    public function __construct(Database $database)
    {
        $this->database = $database;
    }

    /**
     * Find payment by ID
     */
    public function findById(int $id): ?Payment
    {
        $sql = "SELECT * FROM {$this->table} WHERE id = ?";
        $result = $this->database->query($sql, [$id]);

        if (!$result) {
            return null;
        }

        $payment = $this->mapToPayment($result[0]);
        $payment->items = $this->getPaymentItems($id);

        return $payment;
    }

    /**
     * Find payment by transaction ID
     */
    public function findByTransactionId(string $transactionId): ?Payment
    {
        $sql = "SELECT * FROM {$this->table} WHERE transaction_id = ?";
        $result = $this->database->query($sql, [$transactionId]);

        if (!$result) {
            return null;
        }

        $payment = $this->mapToPayment($result[0]);
        $payment->items = $this->getPaymentItems($payment->id);

        return $payment;
    }

    /**
     * Find payment by gateway transaction ID
     */
    public function findByGatewayTransactionId(string $gatewayTransactionId): ?Payment
    {
        $sql = "SELECT * FROM {$this->table} WHERE gateway_transaction_id = ?";
        $result = $this->database->query($sql, [$gatewayTransactionId]);

        if (!$result) {
            return null;
        }

        $payment = $this->mapToPayment($result[0]);
        $payment->items = $this->getPaymentItems($payment->id);

        return $payment;
    }

    /**
     * Find payments by user ID
     */
    public function findByUserId(int $userId, int $limit = 20, int $offset = 0, ?string $status = null): array
    {
        $sql = "SELECT * FROM {$this->table} WHERE user_id = ?";
        $params = [$userId];

        if ($status) {
            $sql .= " AND status = ?";
            $params[] = $status;
        }

        $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;

        $results = $this->database->query($sql, $params);

        $payments = [];
        foreach ($results as $row) {
            $payment = $this->mapToPayment($row);
            $payment->items = $this->getPaymentItems($payment->id);
            $payments[] = $payment;
        }

        return $payments;
    }

    /**
     * Create new payment
     */
    public function create(array $data): Payment
    {
        $requiredFields = ['user_id', 'amount', 'payment_method'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                throw new \InvalidArgumentException("Required field '{$field}' is missing");
            }
        }

        // Generate transaction ID if not provided
        if (!isset($data['transaction_id'])) {
            $data['transaction_id'] = $this->generateTransactionId();
        }

        // Set default expiration (24 hours)
        if (!isset($data['expires_at'])) {
            $data['expires_at'] = date('Y-m-d H:i:s', strtotime('+24 hours'));
        }

        try {
            $this->database->beginTransaction();

            // Insert payment
            $fields = array_keys($data);
            $placeholders = str_repeat('?,', count($fields) - 1) . '?';

            $sql = "INSERT INTO {$this->table} (" . implode(',', $fields) . ") VALUES ({$placeholders})";
            $this->database->execute($sql, array_values($data));
            $paymentId = $this->database->lastInsertId();

            // Log payment creation
            $this->logPaymentEvent($paymentId, 'created', 'Payment created');

            $this->database->commit();

            return $this->findById($paymentId);
        } catch (\Exception $e) {
            $this->database->rollback();
            throw new DatabaseException("Failed to create payment: " . $e->getMessage());
        }
    }

    /**
     * Update payment
     */
    public function update(int $id, array $data): bool
    {
        if (empty($data)) {
            return true;
        }

        // Remove fields that shouldn't be updated directly
        unset($data['id'], $data['user_id'], $data['created_at'], $data['updated_at']);

        $fields = array_keys($data);
        $setClause = implode(' = ?, ', $fields) . ' = ?';

        $sql = "UPDATE {$this->table} SET {$setClause} WHERE id = ?";
        $params = array_merge(array_values($data), [$id]);

        try {
            return $this->database->execute($sql, $params) > 0;
        } catch (\Exception $e) {
            throw new DatabaseException("Failed to update payment: " . $e->getMessage());
        }
    }

    /**
     * Update payment status
     */
    public function updateStatus(int $id, string $status, ?string $reason = null): bool
    {
        $data = ['status' => $status];

        if ($status === 'completed') {
            $data['completed_at'] = date('Y-m-d H:i:s');
        } elseif ($status === 'failed' && $reason) {
            $data['failure_reason'] = $reason;
        } elseif ($status === 'refunded') {
            $data['refunded_at'] = date('Y-m-d H:i:s');
        }

        return $this->update($id, $data);
    }

    /**
     * Add payment item
     */
    public function addItem(int $paymentId, array $itemData): bool
    {
        $requiredFields = ['item_type', 'item_name', 'unit_price', 'total_price'];
        foreach ($requiredFields as $field) {
            if (!isset($itemData[$field])) {
                throw new \InvalidArgumentException("Required field '{$field}' is missing");
            }
        }

        $itemData['payment_id'] = $paymentId;

        if (!isset($itemData['quantity'])) {
            $itemData['quantity'] = 1;
        }

        $fields = array_keys($itemData);
        $placeholders = str_repeat('?,', count($fields) - 1) . '?';

        $sql = "INSERT INTO {$this->itemsTable} (" . implode(',', $fields) . ") VALUES ({$placeholders})";

        try {
            return $this->database->execute($sql, array_values($itemData)) > 0;
        } catch (\Exception $e) {
            throw new DatabaseException("Failed to add payment item: " . $e->getMessage());
        }
    }

    /**
     * Get payment items
     */
    public function getPaymentItems(int $paymentId): array
    {
        $sql = "SELECT * FROM {$this->itemsTable} WHERE payment_id = ? ORDER BY created_at";
        return $this->database->query($sql, [$paymentId]);
    }

    /**
     * Get payments with filters
     */
    public function getAll(int $page = 1, int $limit = 50, array $filters = []): array
    {
        $offset = ($page - 1) * $limit;
        $whereConditions = ['1=1'];
        $params = [];

        // Apply filters
        if (!empty($filters['status'])) {
            $whereConditions[] = 'status = ?';
            $params[] = $filters['status'];
        }

        if (!empty($filters['payment_method'])) {
            $whereConditions[] = 'payment_method = ?';
            $params[] = $filters['payment_method'];
        }

        if (!empty($filters['user_id'])) {
            $whereConditions[] = 'user_id = ?';
            $params[] = $filters['user_id'];
        }

        if (!empty($filters['date_from'])) {
            $whereConditions[] = 'created_at >= ?';
            $params[] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $whereConditions[] = 'created_at <= ?';
            $params[] = $filters['date_to'];
        }

        if (!empty($filters['amount_min'])) {
            $whereConditions[] = 'amount >= ?';
            $params[] = $filters['amount_min'];
        }

        if (!empty($filters['amount_max'])) {
            $whereConditions[] = 'amount <= ?';
            $params[] = $filters['amount_max'];
        }

        $whereClause = implode(' AND ', $whereConditions);

        $sql = "SELECT * FROM {$this->table} WHERE {$whereClause} ORDER BY created_at DESC LIMIT ? OFFSET ?";
        $params = array_merge($params, [$limit, $offset]);

        $results = $this->database->query($sql, $params);

        $payments = [];
        foreach ($results as $row) {
            $payment = $this->mapToPayment($row);
            $payment->items = $this->getPaymentItems($payment->id);
            $payments[] = $payment;
        }

        return $payments;
    }

    /**
     * Count payments with filters
     */
    public function count(array $filters = []): int
    {
        $whereConditions = ['1=1'];
        $params = [];

        // Apply same filters as getAll
        if (!empty($filters['status'])) {
            $whereConditions[] = 'status = ?';
            $params[] = $filters['status'];
        }

        if (!empty($filters['payment_method'])) {
            $whereConditions[] = 'payment_method = ?';
            $params[] = $filters['payment_method'];
        }

        if (!empty($filters['user_id'])) {
            $whereConditions[] = 'user_id = ?';
            $params[] = $filters['user_id'];
        }

        if (!empty($filters['date_from'])) {
            $whereConditions[] = 'created_at >= ?';
            $params[] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $whereConditions[] = 'created_at <= ?';
            $params[] = $filters['date_to'];
        }

        $whereClause = implode(' AND ', $whereConditions);

        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE {$whereClause}";
        $result = $this->database->query($sql, $params);

        return (int) $result[0]['count'];
    }

    /**
     * Get payment statistics
     */
    public function getStatistics(array $filters = []): array
    {
        $whereConditions = ['1=1'];
        $params = [];

        if (!empty($filters['status'])) {
            $whereConditions[] = 'status = ?';
            $params[] = $filters['status'];
        }

        if (!empty($filters['payment_method'])) {
            $whereConditions[] = 'payment_method = ?';
            $params[] = $filters['payment_method'];
        }

        if (!empty($filters['user_id'])) {
            $whereConditions[] = 'user_id = ?';
            $params[] = $filters['user_id'];
        }

        if (!empty($filters['date_from'])) {
            $whereConditions[] = 'created_at >= ?';
            $params[] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $whereConditions[] = 'created_at <= ?';
            $params[] = $filters['date_to'];
        }

        if (!empty($filters['amount_min'])) {
            $whereConditions[] = 'amount >= ?';
            $params[] = $filters['amount_min'];
        }

        if (!empty($filters['amount_max'])) {
            $whereConditions[] = 'amount <= ?';
            $params[] = $filters['amount_max'];
        }

        $whereClause = implode(' AND ', $whereConditions);

        $sql = "
            SELECT 
                COUNT(*) as total_payments,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_payments,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_payments,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_payments,
                COUNT(CASE WHEN status = 'refunded' THEN 1 END) as refunded_payments,
                SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_revenue,
                SUM(CASE WHEN status = 'refunded' THEN refund_amount ELSE 0 END) as total_refunds,
                AVG(CASE WHEN status = 'completed' THEN amount END) as average_payment,
                COUNT(DISTINCT user_id) as unique_customers
            FROM {$this->table} 
            WHERE {$whereClause}
        ";

        $result = $this->database->query($sql, $params);
        return $result[0];
    }

    /**
     * Get payment statistics for a user within a date range
     */
    public function getPaymentStats(?int $userId, string $startDate, string $endDate): array
    {
        $filters = [
            'date_from' => $startDate . ' 00:00:00',
            'date_to' => $endDate . ' 23:59:59',
        ];
        if ($userId) {
            $filters['user_id'] = $userId;
        }
        return $this->getStatistics($filters);
    }

    /**
     * Get expired payments
     */
    public function getExpiredPayments(): array
    {
        $sql = "SELECT * FROM {$this->table} WHERE status = 'pending' AND expires_at < NOW()";
        $results = $this->database->query($sql);

        return array_map([$this, 'mapToPayment'], $results);
    }

    /**
     * Log payment event
     */
    public function logPaymentEvent(int $paymentId, string $eventType, string $description, array $eventData = []): bool
    {
        $sql = "INSERT INTO {$this->logsTable} (payment_id, event_type, event_description, event_data) VALUES (?, ?, ?, ?)";

        try {
            return $this->database->execute($sql, [
                $paymentId,
                $eventType,
                $description,
                json_encode($eventData)
            ]) > 0;
        } catch (\Exception $e) {
            // Log errors shouldn't break the main flow
            error_log("Failed to log payment event: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get payment logs
     */
    public function getPaymentLogs(int $paymentId): array
    {
        $sql = "SELECT * FROM {$this->logsTable} WHERE payment_id = ? ORDER BY created_at DESC";
        return $this->database->query($sql, [$paymentId]);
    }

    /**
     * Get user wallet balance
     */
    public function getWalletBalance(int $userId): float
    {
        $sql = "SELECT wallet_balance FROM users WHERE id = ?";
        $result = $this->database->query($sql, [$userId]);
        return (float)($result[0]['wallet_balance'] ?? 0.0);
    }

    /**
     * Update user wallet balance
     */
    public function updateWalletBalance(int $userId, float $newBalance): bool
    {
        $sql = "UPDATE users SET wallet_balance = ? WHERE id = ?";
        return $this->database->execute($sql, [$newBalance, $userId]) > 0;
    }

    /**
     * Record a wallet transaction
     */
    public function recordWalletTransaction(array $data): bool
    {
        $sql = "INSERT INTO wallet_transactions (user_id, amount, type, description) VALUES (?, ?, ?, ?)";
        return $this->database->execute($sql, [$data['user_id'], $data['amount'], $data['operation'], $data['description']]) > 0;
    }

    /**
     * Get user wallet transactions
     */
    public function getWalletTransactions(int $userId, int $limit, int $offset): array
    {
        $sql = "SELECT * FROM wallet_transactions WHERE user_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?";
        return $this->database->query($sql, [$userId, $limit, $offset]);
    }

    /**
     * Generate unique transaction ID
     */
    private function generateTransactionId(): string
    {
        do {
            $transactionId = 'WB' . date('Ymd') . strtoupper(substr(md5(uniqid()), 0, 8));
            $exists = $this->findByTransactionId($transactionId);
        } while ($exists);

        return $transactionId;
    }

    /**
     * Map database row to Payment model
     */
    private function mapToPayment(array $row): Payment
    {
        $payment = new Payment($this->database);
        $payment->fill([
            'id' => (int) $row['id'],
            'user_id' => (int) $row['user_id'],
            'amount' => (float) $row['amount'],
            'currency' => $row['currency'],
            'description' => $row['description'],
            'payment_method' => $row['payment_method'],
            'payment_gateway' => $row['payment_gateway'],
            'transaction_id' => $row['transaction_id'],
            'gateway_transaction_id' => $row['gateway_transaction_id'],
            'gateway_payment_id' => $row['gateway_payment_id'],
            'status' => $row['status'],
            'failure_reason' => $row['failure_reason'],
            'initiated_at' => $row['initiated_at'],
            'completed_at' => $row['completed_at'],
            'expires_at' => $row['expires_at'],
            'gateway_response' => $row['gateway_response'] ? json_decode($row['gateway_response'], true) : null,
            'gateway_webhook_data' => $row['gateway_webhook_data'] ? json_decode($row['gateway_webhook_data'], true) : null,
            'refund_amount' => (float) $row['refund_amount'],
            'refund_reason' => $row['refund_reason'],
            'refunded_at' => $row['refunded_at'],
            'metadata' => $row['metadata'] ? json_decode($row['metadata'], true) : null,
            'created_at' => $row['created_at'],
            'updated_at' => $row['updated_at']
        ]);
        $payment->setExists(true);
        return $payment;
    }
}
