<?php

declare(strict_types=1);

namespace WeBot\Core;

use WeBot\Core\Database;
use WeBot\Exceptions\MigrationException;

/**
 * Database Migration Runner
 *
 * Handles database migrations, version tracking,
 * and rollback functionality.
 *
 * @package WeBot\Core
 * @version 2.0
 */
class MigrationRunner
{
    private Database $database;
    private string $migrationsPath;
    private string $migrationsTable = 'migrations';

    public function __construct(Database $database, string $migrationsPath = 'migrations')
    {
        $this->database = $database;
        $this->migrationsPath = rtrim($migrationsPath, '/');
        $this->ensureMigrationsTable();
    }

    /**
     * Run all pending migrations
     */
    public function migrate(): array
    {
        $pendingMigrations = $this->getPendingMigrations();
        $executedMigrations = [];

        if (empty($pendingMigrations)) {
            return ['message' => 'No pending migrations found'];
        }

        foreach ($pendingMigrations as $migration) {
            try {
                $this->executeMigration($migration);
                $executedMigrations[] = $migration;
                echo "✅ Executed migration: {$migration}\n";
            } catch (\Exception $e) {
                echo "❌ Failed to execute migration {$migration}: " . $e->getMessage() . "\n";
                throw new MigrationException("Migration failed: {$migration}. Error: " . $e->getMessage());
            }
        }

        return [
            'message' => 'Migrations completed successfully',
            'executed' => $executedMigrations
        ];
    }

    /**
     * Rollback last migration
     */
    public function rollback(): array
    {
        $lastMigration = $this->getLastExecutedMigration();

        if (!$lastMigration) {
            return ['message' => 'No migrations to rollback'];
        }

        try {
            $this->rollbackMigration($lastMigration);
            echo "✅ Rolled back migration: {$lastMigration['file']}\n";

            return [
                'message' => 'Rollback completed successfully',
                'rolled_back' => $lastMigration['file']
            ];
        } catch (\Exception $e) {
            echo "❌ Failed to rollback migration {$lastMigration['file']}: " . $e->getMessage() . "\n";
            throw new MigrationException("Rollback failed: {$lastMigration['file']}. Error: " . $e->getMessage());
        }
    }

    /**
     * Get migration status
     */
    public function status(): array
    {
        $allMigrations = $this->getAllMigrationFiles();
        $executedMigrations = $this->getExecutedMigrations();
        $executedFiles = array_column($executedMigrations, 'file');

        $status = [];
        foreach ($allMigrations as $migration) {
            $status[] = [
                'file' => $migration,
                'status' => in_array($migration, $executedFiles) ? 'executed' : 'pending',
                'executed_at' => $this->getMigrationExecutionTime($migration)
            ];
        }

        return $status;
    }

    /**
     * Reset all migrations (dangerous!)
     */
    public function reset(): array
    {
        $executedMigrations = $this->getExecutedMigrations();

        if (empty($executedMigrations)) {
            return ['message' => 'No migrations to reset'];
        }

        try {
            $this->database->beginTransaction();

            // Drop all tables except migrations table
            $this->dropAllTables();

            // Clear migrations table
            $this->database->execute("DELETE FROM {$this->migrationsTable}");

            $this->database->commit();

            return [
                'message' => 'Database reset completed successfully',
                'reset_migrations' => count($executedMigrations)
            ];
        } catch (\Exception $e) {
            $this->database->rollback();
            throw new MigrationException("Reset failed: " . $e->getMessage());
        }
    }

    /**
     * Create new migration file
     */
    public function create(string $name): string
    {
        $timestamp = date('Y_m_d_His');
        $className = $this->formatClassName($name);
        $filename = "{$timestamp}_{$name}.sql";
        $filepath = "{$this->migrationsPath}/{$filename}";

        $template = $this->getMigrationTemplate($className);

        if (file_put_contents($filepath, $template) === false) {
            throw new MigrationException("Failed to create migration file: {$filepath}");
        }

        return $filepath;
    }

    /**
     * Execute single migration
     */
    private function executeMigration(string $migrationFile): void
    {
        $filepath = "{$this->migrationsPath}/{$migrationFile}";

        if (!file_exists($filepath)) {
            throw new MigrationException("Migration file not found: {$filepath}");
        }

        $sql = file_get_contents($filepath);
        if ($sql === false) {
            throw new MigrationException("Failed to read migration file: {$filepath}");
        }

        $startTime = microtime(true);

        try {
            $this->database->beginTransaction();

            // Execute migration SQL
            $this->executeSQLStatements($sql);

            // Record migration execution
            $executionTime = (microtime(true) - $startTime) * 1000; // Convert to milliseconds
            $this->recordMigration($migrationFile, $executionTime);

            $this->database->commit();
        } catch (\Exception $e) {
            $this->database->rollback();
            throw $e;
        }
    }

    /**
     * Rollback single migration
     */
    private function rollbackMigration(array $migration): void
    {
        // For SQL migrations, we can't automatically rollback
        // This would require separate rollback SQL files or embedded rollback statements

        // Remove migration record
        $this->database->execute(
            "DELETE FROM {$this->migrationsTable} WHERE file = ?",
            [$migration['file']]
        );
    }

    /**
     * Execute SQL statements from migration file
     */
    private function executeSQLStatements(string $sql): void
    {
        // Remove comments and split by semicolon
        $sql = preg_replace('/--.*$/m', '', $sql);
        $statements = array_filter(
            array_map('trim', explode(';', $sql)),
            function ($stmt) {
                return !empty($stmt);
            }
        );

        foreach ($statements as $statement) {
            if (!empty(trim($statement))) {
                $this->database->execute($statement);
            }
        }
    }

    /**
     * Get all migration files
     */
    private function getAllMigrationFiles(): array
    {
        if (!is_dir($this->migrationsPath)) {
            return [];
        }

        $files = glob("{$this->migrationsPath}/*.sql");
        $migrations = [];

        foreach ($files as $file) {
            $migrations[] = basename($file);
        }

        sort($migrations);
        return $migrations;
    }

    /**
     * Get pending migrations
     */
    private function getPendingMigrations(): array
    {
        $allMigrations = $this->getAllMigrationFiles();
        $executedMigrations = $this->getExecutedMigrations();
        $executedFiles = array_column($executedMigrations, 'file');

        return array_diff($allMigrations, $executedFiles);
    }

    /**
     * Get executed migrations
     */
    private function getExecutedMigrations(): array
    {
        return $this->database->query(
            "SELECT * FROM {$this->migrationsTable} ORDER BY executed_at"
        );
    }

    /**
     * Get last executed migration
     */
    private function getLastExecutedMigration(): ?array
    {
        $result = $this->database->query(
            "SELECT * FROM {$this->migrationsTable} ORDER BY executed_at DESC LIMIT 1"
        );

        return $result ? $result[0] : null;
    }

    /**
     * Get migration execution time
     */
    private function getMigrationExecutionTime(string $migrationFile): ?string
    {
        $result = $this->database->query(
            "SELECT executed_at FROM {$this->migrationsTable} WHERE file = ?",
            [$migrationFile]
        );

        return $result ? $result[0]['executed_at'] : null;
    }

    /**
     * Record migration execution
     */
    private function recordMigration(string $migrationFile, float $executionTime): void
    {
        $this->database->execute(
            "INSERT INTO {$this->migrationsTable} (file, executed_at, execution_time_ms) VALUES (?, NOW(), ?)",
            [$migrationFile, round($executionTime, 2)]
        );
    }

    /**
     * Ensure migrations table exists
     */
    private function ensureMigrationsTable(): void
    {
        if ($this->database->tableExists($this->migrationsTable)) {
            return;
        }

        $sql = "
            CREATE TABLE {$this->migrationsTable} (
                id INT AUTO_INCREMENT PRIMARY KEY,
                file VARCHAR(255) NOT NULL UNIQUE,
                executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                execution_time_ms DECIMAL(10,2) DEFAULT 0,
                INDEX idx_executed_at (executed_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";

        $this->database->execute($sql);
    }

    /**
     * Drop all tables except migrations table
     */
    private function dropAllTables(): void
    {
        // Get all tables
        $tables = $this->database->query("SHOW TABLES");

        // Disable foreign key checks
        $this->database->execute("SET FOREIGN_KEY_CHECKS = 0");

        foreach ($tables as $table) {
            $tableName = array_values($table)[0];

            // Don't drop migrations table
            if ($tableName !== $this->migrationsTable) {
                $this->database->execute("DROP TABLE IF EXISTS `{$tableName}`");
            }
        }

        // Re-enable foreign key checks
        $this->database->execute("SET FOREIGN_KEY_CHECKS = 1");
    }

    /**
     * Format class name for migration
     */
    private function formatClassName(string $name): string
    {
        return str_replace(' ', '', ucwords(str_replace('_', ' ', $name)));
    }

    /**
     * Get migration template
     */
    private function getMigrationTemplate(string $className): string
    {
        return "-- WeBot Database Migration: {$className}
-- Version: 1.0
-- Date: " . date('Y-m-d') . "

-- Migration SQL goes here
-- Example:
-- CREATE TABLE IF NOT EXISTS example_table (
--     id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
--     name VARCHAR(255) NOT NULL,
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
";
    }
}
