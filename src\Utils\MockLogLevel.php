<?php

declare(strict_types=1);

namespace WeBot\Utils;

/**
 * Mock LogLevel for Psr\Log\LogLevel
 * Used when PSR Log is not available
 */
class MockLogLevel
{
    public const EMERGENCY = 'emergency';
    public const ALERT = 'alert';
    public const CRITICAL = 'critical';
    public const ERROR = 'error';
    public const WARNING = 'warning';
    public const NOTICE = 'notice';
    public const INFO = 'info';
    public const DEBUG = 'debug';
}
