<?php

declare(strict_types=1);

namespace WeBot\Services;

use WeBot\Core\CacheManager;
use WeBot\Utils\Logger;
use WeBot\Exceptions\WeBotException;

/**
 * Multi-Level Cache Service
 *
 * Implements a sophisticated multi-level caching strategy with
 * memory, Redis, and database layers for optimal performance.
 *
 * @package WeBot\Services
 * @version 2.0
 */
class MultiLevelCacheService
{
    private array $memoryCache = [];
    private CacheManager $redisCache;
    private Logger $logger;
    private array $config;
    private array $stats = [
        'memory_hits' => 0,
        'redis_hits' => 0,
        'database_hits' => 0,
        'misses' => 0,
        'sets' => 0,
        'deletes' => 0
    ];

    public function __construct(CacheManager $redisCache, array $config = [])
    {
        $this->redisCache = $redisCache;
        $this->logger = Logger::getInstance();
        $this->config = array_merge($this->getDefaultConfig(), $config);
    }

    /**
     * Get value from multi-level cache
     */
    public function get(string $key, callable $fallback = null, ?int $ttl = null)
    {
        $cacheKey = $this->normalizeKey($key);

        // Level 1: Memory cache
        if ($this->config['memory_enabled'] && isset($this->memoryCache[$cacheKey])) {
            $item = $this->memoryCache[$cacheKey];
            if ($this->isValidCacheItem($item)) {
                $this->stats['memory_hits']++;
                $this->logger->debug('Cache hit: Memory', ['key' => $key]);
                return $item['value'];
            } else {
                unset($this->memoryCache[$cacheKey]);
            }
        }

        // Level 2: Redis cache
        if ($this->config['redis_enabled']) {
            $value = $this->redisCache->get($cacheKey);
            if ($value !== null) {
                $this->stats['redis_hits']++;
                $this->logger->debug('Cache hit: Redis', ['key' => $key]);

                // Store in memory cache for faster access
                if ($this->config['memory_enabled']) {
                    $this->setMemoryCache($cacheKey, $value, $ttl ?? $this->config['default_ttl']);
                }

                return $value;
            }
        }

        // Level 3: Database/Fallback
        if ($fallback !== null) {
            try {
                $value = $fallback();
                $this->stats['database_hits']++;
                $this->logger->debug('Cache miss: Using fallback', ['key' => $key]);

                // Store in all enabled cache levels
                $this->set($key, $value, $ttl);

                return $value;
            } catch (\Exception $e) {
                $this->logger->error('Cache fallback failed', [
                    'key' => $key,
                    'error' => $e->getMessage()
                ]);
                throw $e;
            }
        }

        $this->stats['misses']++;
        return null;
    }

    /**
     * Set value in multi-level cache
     */
    public function set(string $key, $value, ?int $ttl = null): bool
    {
        $cacheKey = $this->normalizeKey($key);
        $ttl = $ttl ?? $this->config['default_ttl'];
        $success = true;

        // Store in memory cache
        if ($this->config['memory_enabled']) {
            $this->setMemoryCache($cacheKey, $value, $ttl);
        }

        // Store in Redis cache
        if ($this->config['redis_enabled']) {
            $redisSuccess = $this->redisCache->set($cacheKey, $value, $ttl);
            $success = $success && $redisSuccess;
        }

        $this->stats['sets']++;

        if ($success) {
            $this->logger->debug('Cache set successful', ['key' => $key, 'ttl' => $ttl]);
        } else {
            $this->logger->warning('Cache set failed', ['key' => $key]);
        }

        return $success;
    }

    /**
     * Delete from all cache levels
     */
    public function delete(string $key): bool
    {
        $cacheKey = $this->normalizeKey($key);
        $success = true;

        // Remove from memory cache
        if (isset($this->memoryCache[$cacheKey])) {
            unset($this->memoryCache[$cacheKey]);
        }

        // Remove from Redis cache
        if ($this->config['redis_enabled']) {
            $redisSuccess = $this->redisCache->delete($cacheKey);
            $success = $success && $redisSuccess;
        }

        $this->stats['deletes']++;

        $this->logger->debug('Cache delete', ['key' => $key, 'success' => $success]);

        return $success;
    }

    /**
     * Remember pattern with multi-level caching
     */
    public function remember(string $key, callable $callback, ?int $ttl = null)
    {
        return $this->get($key, $callback, $ttl);
    }

    /**
     * Increment counter with multi-level support
     */
    public function increment(string $key, int $value = 1): int
    {
        $cacheKey = $this->normalizeKey($key);

        // Try Redis first for atomic operations
        if ($this->config['redis_enabled']) {
            $newValue = $this->redisCache->increment($cacheKey, $value);

            // Update memory cache
            if ($this->config['memory_enabled']) {
                $this->setMemoryCache($cacheKey, $newValue, $this->config['default_ttl']);
            }

            return $newValue;
        }

        // Fallback to memory cache
        if ($this->config['memory_enabled']) {
            $current = $this->memoryCache[$cacheKey]['value'] ?? 0;
            $newValue = $current + $value;
            $this->setMemoryCache($cacheKey, $newValue, $this->config['default_ttl']);
            return $newValue;
        }

        return $value;
    }

    /**
     * Bulk get operation
     */
    public function getMultiple(array $keys): array
    {
        $results = [];
        $missingKeys = [];

        foreach ($keys as $key) {
            $cacheKey = $this->normalizeKey($key);

            // Check memory cache first
            if ($this->config['memory_enabled'] && isset($this->memoryCache[$cacheKey])) {
                $item = $this->memoryCache[$cacheKey];
                if ($this->isValidCacheItem($item)) {
                    $results[$key] = $item['value'];
                    continue;
                }
            }

            $missingKeys[] = $key;
        }

        // Get missing keys from Redis
        if (!empty($missingKeys) && $this->config['redis_enabled']) {
            $redisResults = $this->redisCache->getMultiple($missingKeys);

            foreach ($redisResults as $key => $value) {
                if ($value !== null) {
                    $results[$key] = $value;

                    // Store in memory cache
                    if ($this->config['memory_enabled']) {
                        $this->setMemoryCache($this->normalizeKey($key), $value, $this->config['default_ttl']);
                    }
                }
            }
        }

        return $results;
    }

    /**
     * Bulk set operation
     */
    public function setMultiple(array $values, ?int $ttl = null): bool
    {
        $success = true;

        foreach ($values as $key => $value) {
            $setSuccess = $this->set($key, $value, $ttl);
            $success = $success && $setSuccess;
        }

        return $success;
    }

    /**
     * Clear all cache levels
     */
    public function flush(): bool
    {
        $success = true;

        // Clear memory cache
        $this->memoryCache = [];

        // Clear Redis cache
        if ($this->config['redis_enabled']) {
            $redisSuccess = $this->redisCache->flush();
            $success = $success && $redisSuccess;
        }

        // Reset stats
        $this->stats = array_fill_keys(array_keys($this->stats), 0);

        $this->logger->info('Cache flushed', ['success' => $success]);

        return $success;
    }

    /**
     * Get cache statistics
     */
    public function getStats(): array
    {
        $totalRequests = array_sum($this->stats);

        return [
            'requests' => $this->stats,
            'hit_ratio' => [
                'memory' => $totalRequests > 0 ? ($this->stats['memory_hits'] / $totalRequests) * 100 : 0,
                'redis' => $totalRequests > 0 ? ($this->stats['redis_hits'] / $totalRequests) * 100 : 0,
                'database' => $totalRequests > 0 ? ($this->stats['database_hits'] / $totalRequests) * 100 : 0,
                'overall' => $totalRequests > 0 ? (($this->stats['memory_hits'] + $this->stats['redis_hits']) / $totalRequests) * 100 : 0
            ],
            'memory_usage' => [
                'items' => count($this->memoryCache),
                'size_bytes' => $this->calculateMemoryUsage()
            ],
            'redis_info' => $this->config['redis_enabled'] ? $this->redisCache->getStats() : null
        ];
    }

    /**
     * Optimize cache performance
     */
    public function optimize(): array
    {
        $results = [];

        // Clean expired memory cache items
        $results['memory_cleanup'] = $this->cleanupMemoryCache();

        // Optimize Redis cache
        if ($this->config['redis_enabled']) {
            $results['redis_optimization'] = $this->redisCache->optimize();
        }

        // Memory usage optimization
        if ($this->isMemoryUsageHigh()) {
            $results['memory_optimization'] = $this->optimizeMemoryUsage();
        }

        return $results;
    }

    /**
     * Set memory cache item
     */
    private function setMemoryCache(string $key, $value, int $ttl): void
    {
        // Check memory limits
        if (count($this->memoryCache) >= $this->config['memory_max_items']) {
            $this->evictOldestMemoryItems();
        }

        $this->memoryCache[$key] = [
            'value' => $value,
            'expires_at' => time() + $ttl,
            'created_at' => time()
        ];
    }

    /**
     * Check if cache item is valid
     */
    private function isValidCacheItem(array $item): bool
    {
        return $item['expires_at'] > time();
    }

    /**
     * Normalize cache key
     */
    private function normalizeKey(string $key): string
    {
        return $this->config['key_prefix'] . md5($key);
    }

    /**
     * Calculate memory usage
     */
    private function calculateMemoryUsage(): int
    {
        return strlen(serialize($this->memoryCache));
    }

    /**
     * Check if memory usage is high
     */
    private function isMemoryUsageHigh(): bool
    {
        return count($this->memoryCache) > ($this->config['memory_max_items'] * 0.8);
    }

    /**
     * Cleanup expired memory cache items
     */
    private function cleanupMemoryCache(): array
    {
        $cleaned = 0;
        $now = time();

        foreach ($this->memoryCache as $key => $item) {
            if (!$this->isValidCacheItem($item)) {
                unset($this->memoryCache[$key]);
                $cleaned++;
            }
        }

        return ['cleaned_items' => $cleaned];
    }

    /**
     * Evict oldest memory items
     */
    private function evictOldestMemoryItems(): void
    {
        // Sort by creation time and remove oldest 20%
        uasort($this->memoryCache, fn($a, $b) => $a['created_at'] <=> $b['created_at']);

        $toRemove = (int)(count($this->memoryCache) * 0.2);
        $this->memoryCache = array_slice($this->memoryCache, $toRemove, null, true);
    }

    /**
     * Optimize memory usage
     */
    private function optimizeMemoryUsage(): array
    {
        $before = count($this->memoryCache);

        // Clean expired items
        $this->cleanupMemoryCache();

        // Evict if still too many items
        if (count($this->memoryCache) > $this->config['memory_max_items']) {
            $this->evictOldestMemoryItems();
        }

        $after = count($this->memoryCache);

        return [
            'items_before' => $before,
            'items_after' => $after,
            'items_removed' => $before - $after
        ];
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'memory_enabled' => true,
            'redis_enabled' => true,
            'memory_max_items' => 1000,
            'default_ttl' => 3600,
            'key_prefix' => 'mlc:',
            'cleanup_interval' => 300, // 5 minutes
            'optimization_enabled' => true
        ];
    }
}
