<?php
/**
 * Environment Variables Validation Configuration
 * 
 * This file defines validation rules for environment variables
 * to ensure proper configuration and prevent runtime errors.
 * 
 * @package WeBot
 * @version 2.0
 */

declare(strict_types=1);

return [
    
    /*
    |--------------------------------------------------------------------------
    | Required Environment Variables
    |--------------------------------------------------------------------------
    |
    | These environment variables are required for the application to function
    | properly. The application will fail to start if any of these are missing.
    |
    */
    
    'required' => [
        'APP_ENV',
        'APP_KEY',
        'TELEGRAM_BOT_TOKEN',
        'DB_HOST',
        'DB_DATABASE',
        'DB_USERNAME',
        'ADMIN_ID',
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Environment Variable Validation Rules
    |--------------------------------------------------------------------------
    |
    | Define validation rules for environment variables to ensure they
    | contain valid values and prevent configuration errors.
    |
    */
    
    'rules' => [
        'APP_ENV' => [
            'type' => 'string',
            'allowed' => ['development', 'testing', 'staging', 'production'],
            'default' => 'production'
        ],
        
        'APP_DEBUG' => [
            'type' => 'boolean',
            'default' => false
        ],
        
        'APP_KEY' => [
            'type' => 'string',
            'min_length' => 32,
            'required' => true
        ],
        
        'TELEGRAM_BOT_TOKEN' => [
            'type' => 'string',
            'pattern' => '/^\d+:[A-Za-z0-9_-]+$/',
            'required' => true
        ],
        
        'ADMIN_ID' => [
            'type' => 'integer',
            'min' => 1,
            'required' => true
        ],
        
        'DB_HOST' => [
            'type' => 'string',
            'required' => true
        ],
        
        'DB_PORT' => [
            'type' => 'integer',
            'min' => 1,
            'max' => 65535,
            'default' => 3306
        ],
        
        'DB_DATABASE' => [
            'type' => 'string',
            'required' => true
        ],
        
        'DB_USERNAME' => [
            'type' => 'string',
            'required' => true
        ],
        
        'CACHE_TTL' => [
            'type' => 'integer',
            'min' => 60,
            'max' => 86400,
            'default' => 3600
        ],
        
        'RATE_LIMIT_MAX_ATTEMPTS' => [
            'type' => 'integer',
            'min' => 1,
            'max' => 1000,
            'default' => 10
        ],
        
        'RATE_LIMIT_TIME_WINDOW' => [
            'type' => 'integer',
            'min' => 1,
            'max' => 3600,
            'default' => 60
        ],
        
        'SESSION_TIMEOUT' => [
            'type' => 'integer',
            'min' => 300,
            'max' => 7200,
            'default' => 1800
        ],
        
        'LOG_LEVEL' => [
            'type' => 'string',
            'allowed' => ['emergency', 'alert', 'critical', 'error', 'warning', 'notice', 'info', 'debug'],
            'default' => 'info'
        ],
        
        'MEMORY_LIMIT' => [
            'type' => 'string',
            'pattern' => '/^\d+[KMG]?$/',
            'default' => '256M'
        ],
        
        'MAX_UPLOAD_SIZE' => [
            'type' => 'string',
            'pattern' => '/^\d+[KMG]?$/',
            'default' => '10M'
        ],
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Environment Specific Validations
    |--------------------------------------------------------------------------
    |
    | Additional validation rules that apply only to specific environments.
    |
    */
    
    'environment_specific' => [
        'production' => [
            'required' => [
                'APP_KEY',
                'DB_PASSWORD',
                'TELEGRAM_BOT_TOKEN',
                'WEBHOOK_URL',
            ],
            'rules' => [
                'APP_DEBUG' => [
                    'type' => 'boolean',
                    'value' => false
                ],
                'LOG_LEVEL' => [
                    'type' => 'string',
                    'allowed' => ['error', 'warning', 'info']
                ],
                'SSL_ENABLED' => [
                    'type' => 'boolean',
                    'value' => true
                ],
            ]
        ],
        
        'testing' => [
            'rules' => [
                'APP_DEBUG' => [
                    'type' => 'boolean',
                    'value' => true
                ],
                'LOG_LEVEL' => [
                    'type' => 'string',
                    'value' => 'debug'
                ],
                'CACHE_DRIVER' => [
                    'type' => 'string',
                    'value' => 'array'
                ],
                'SESSION_DRIVER' => [
                    'type' => 'string',
                    'value' => 'array'
                ],
            ]
        ],
        
        'development' => [
            'rules' => [
                'APP_DEBUG' => [
                    'type' => 'boolean',
                    'value' => true
                ],
                'LOG_LEVEL' => [
                    'type' => 'string',
                    'allowed' => ['debug', 'info']
                ],
            ]
        ],
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Security Validations
    |--------------------------------------------------------------------------
    |
    | Security-related validation rules to prevent common misconfigurations.
    |
    */
    
    'security' => [
        'weak_passwords' => [
            'password',
            '123456',
            'admin',
            'root',
            'test',
            'default',
            'changeme',
        ],
        
        'insecure_tokens' => [
            'test_token',
            'your_token_here',
            'change_this',
            'example_token',
        ],
        
        'production_requirements' => [
            'APP_KEY' => 'must_be_32_characters_or_more',
            'DB_PASSWORD' => 'must_not_be_empty',
            'TELEGRAM_BOT_TOKEN' => 'must_be_valid_bot_token',
            'SSL_ENABLED' => 'must_be_true',
            'APP_DEBUG' => 'must_be_false',
        ],
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Warning Conditions
    |--------------------------------------------------------------------------
    |
    | Conditions that should trigger warnings but not prevent startup.
    |
    */
    
    'warnings' => [
        'missing_optional' => [
            'SENTRY_DSN',
            'GOOGLE_ANALYTICS_ID',
            'BACKUP_S3_BUCKET',
            'CDN_URL',
        ],
        
        'development_values_in_production' => [
            'localhost',
            'test.example.com',
            'your-domain.com',
            'example.com',
        ],
    ],
    
];
