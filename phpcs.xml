<?xml version="1.0"?>
<ruleset name="WeBot Coding Standard">
    <description>WeBot PHP Coding Standard based on PSR-12</description>

    <!-- Show progress and sniff codes -->
    <arg value="p"/>
    <arg value="s"/>
    <arg name="colors"/>
    <arg name="parallel" value="8"/>

    <!-- Paths to check -->
    <file>src</file>
    <file>tests</file>
    <file>config</file>
    <file>scripts</file>

    <!-- Paths to ignore -->
    <exclude-pattern>src/Legacy/*</exclude-pattern>
    <exclude-pattern>vendor/*</exclude-pattern>
    <exclude-pattern>storage/*</exclude-pattern>
    <exclude-pattern>public/temp/*</exclude-pattern>
    <exclude-pattern>*.min.js</exclude-pattern>
    <exclude-pattern>*.min.css</exclude-pattern>

    <!-- Use PSR-12 as base standard -->
    <rule ref="PSR12"/>

    <!-- Additional Slevomat rules for better code quality -->
    <rule ref="SlevomatCodingStandard.TypeHints.ReturnTypeHint"/>
    <rule ref="SlevomatCodingStandard.TypeHints.ParameterTypeHint"/>
    <rule ref="SlevomatCodingStandard.TypeHints.PropertyTypeHint"/>
    <rule ref="SlevomatCodingStandard.Classes.UnusedPrivateElements"/>
    <rule ref="SlevomatCodingStandard.Variables.UnusedVariable"/>
    <rule ref="SlevomatCodingStandard.Exceptions.DeadCatch"/>
    <rule ref="SlevomatCodingStandard.Arrays.TrailingArrayComma"/>
    <rule ref="SlevomatCodingStandard.Classes.ModernClassNameReference"/>
    <rule ref="SlevomatCodingStandard.Functions.UnusedInheritedVariablePassedToClosure"/>
    <rule ref="SlevomatCodingStandard.Namespaces.UnusedUses"/>
    <rule ref="SlevomatCodingStandard.Namespaces.UseFromSameNamespace"/>
    <rule ref="SlevomatCodingStandard.PHP.UselessSemicolon"/>
    <rule ref="SlevomatCodingStandard.Variables.UselessVariable"/>

    <!-- Custom rules for WeBot -->
    <rule ref="Generic.Files.LineLength">
        <properties>
            <property name="lineLimit" value="120"/>
            <property name="absoluteLineLimit" value="150"/>
        </properties>
    </rule>

    <!-- Require strict types declaration -->
    <rule ref="SlevomatCodingStandard.TypeHints.DeclareStrictTypes">
        <properties>
            <property name="newlinesCountBetweenOpenTagAndDeclare" value="2"/>
            <property name="spacesCountAroundEqualsSign" value="0"/>
        </properties>
    </rule>

    <!-- Namespace and use statements -->
    <rule ref="SlevomatCodingStandard.Namespaces.AlphabeticallySortedUses"/>
    <rule ref="SlevomatCodingStandard.Namespaces.RequireOneNamespaceInFile"/>
    <rule ref="SlevomatCodingStandard.Namespaces.NamespaceDeclaration"/>

    <!-- Class and method naming -->
    <rule ref="SlevomatCodingStandard.Classes.ClassConstantVisibility"/>
    <rule ref="SlevomatCodingStandard.Classes.MethodSpacing"/>
    <rule ref="SlevomatCodingStandard.Classes.PropertySpacing"/>

    <!-- Documentation -->
    <rule ref="SlevomatCodingStandard.Commenting.RequireOneLinePropertyDocComment"/>
    <rule ref="SlevomatCodingStandard.Commenting.RequireOneLineDocComment"/>

    <!-- Control structures -->
    <rule ref="SlevomatCodingStandard.ControlStructures.RequireNullCoalesceOperator"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.RequireShortTernaryOperator"/>
    <rule ref="SlevomatCodingStandard.ControlStructures.DisallowYodaComparison"/>

    <!-- Functions -->
    <rule ref="SlevomatCodingStandard.Functions.StrictCall"/>
    <rule ref="SlevomatCodingStandard.Functions.StaticClosure"/>

    <!-- Operators -->
    <rule ref="SlevomatCodingStandard.Operators.RequireCombinedAssignmentOperator"/>
    <rule ref="SlevomatCodingStandard.Operators.RequireOnlyStandaloneIncrementAndDecrementOperators"/>

    <!-- Exclude some rules that might be too strict -->
    <rule ref="SlevomatCodingStandard.TypeHints.ReturnTypeHint">
        <exclude name="SlevomatCodingStandard.TypeHints.ReturnTypeHint.MissingTraversableTypeHintSpecification"/>
    </rule>
    
    <rule ref="SlevomatCodingStandard.TypeHints.ParameterTypeHint">
        <exclude name="SlevomatCodingStandard.TypeHints.ParameterTypeHint.MissingTraversableTypeHintSpecification"/>
    </rule>

    <!-- Allow long lines in specific cases -->
    <rule ref="Generic.Files.LineLength">
        <exclude-pattern>*/migrations/*</exclude-pattern>
        <exclude-pattern>*/config/*</exclude-pattern>
    </rule>
</ruleset>
