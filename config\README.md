# WeBot Configuration Guide
## راهنمای تنظیمات WeBot

این پوشه شامل تمام فایل‌های تنظیمات WeBot است که به صورت ماژولار سازماندهی شده‌اند.

---

## 📁 ساختار پوشه‌ها

### 🔧 config/
**فایل‌های تنظیمات اصلی**

#### app.php ✅ NEW
- **تنظیمات اصلی application** - نام، محیط، debug mode، timezone
- **Service Providers** - لیست کلاس‌های ارائه‌دهنده سرویس
- **Class Aliases** - نام‌های مستعار برای کلاس‌ها
- **Feature Flags** - فعال/غیرفعال کردن ویژگی‌ها
- **Security Settings** - تنظیمات امنیتی
- **Performance Settings** - تنظیمات بهینه‌سازی

#### validation.php ✅ NEW
- **Environment Validation Rules** - قوانین اعتبارسنجی متغیرهای محیطی
- **Required Variables** - متغیرهای اجباری
- **Type Validation** - اعتبارسنجی نوع داده
- **Security Checks** - بررسی‌های امنیتی
- **Environment Specific Rules** - قوانین مخصوص هر محیط

### 🗄 database/
**تنظیمات دیتابیس و مایگریشن‌ها**

- **connection.php** - تنظیمات اتصال به MySQL
- **migrations.php** - تعریف مایگریشن‌ها
- **seeds.php** - داده‌های اولیه

### 📱 telegram/
**تنظیمات ربات تلگرام**

- **bot.php** - Token، webhook، تنظیمات اصلی
- **commands.php** - تعریف دستورات
- **keyboards.php** - کیبوردهای پیش‌فرض
- **messages.php** - قالب پیام‌ها

### 🖥 panels/
**تنظیمات پنل‌های VPN**

- **marzban.php** - تنظیمات Marzban
- **marzneshin.php** - تنظیمات Marzneshin  
- **x-ui.php** - تنظیمات X-UI
- **hiddify.php** - تنظیمات Hiddify

### 💳 payments/
**تنظیمات درگاه‌های پرداخت**

- **gateways.php** - درگاه‌های پرداخت
- **currencies.php** - ارزهای پشتیبانی شده
- **rates.php** - نرخ تبدیل ارز

---

## 🔧 نحوه استفاده

### بارگذاری تنظیمات:
```php
// بارگذاری تنظیمات دیتابیس
$dbConfig = require 'config/database/connection.php';

// بارگذاری تنظیمات تلگرام
$telegramConfig = require 'config/telegram/bot.php';
```

### ساختار فایل تنظیمات:
```php
<?php
return [
    'key1' => 'value1',
    'key2' => [
        'nested_key' => 'nested_value'
    ],
    'environment_dependent' => $_ENV['SOME_VAR'] ?? 'default'
];
```

---

## 🔐 امنیت

### متغیرهای محیطی:
- تمام اطلاعات حساس در `.env` قرار می‌گیرند
- فایل‌های config فقط ساختار را تعریف می‌کنند
- هیچ اطلاعات حساسی در git commit نمی‌شود

### مثال استفاده از .env:
```php
// در فایل config
'database' => [
    'host' => $_ENV['DB_HOST'] ?? 'localhost',
    'username' => $_ENV['DB_USERNAME'] ?? '',
    'password' => $_ENV['DB_PASSWORD'] ?? '',
]
```

---

## 📋 فایل‌های مورد نیاز

### database/connection.php
```php
<?php
return [
    'host' => $_ENV['DB_HOST'] ?? 'localhost',
    'port' => $_ENV['DB_PORT'] ?? 3306,
    'database' => $_ENV['DB_DATABASE'] ?? 'webot',
    'username' => $_ENV['DB_USERNAME'] ?? '',
    'password' => $_ENV['DB_PASSWORD'] ?? '',
    'charset' => 'utf8mb4',
    'options' => [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]
];
```

### telegram/bot.php
```php
<?php
return [
    'token' => $_ENV['TELEGRAM_BOT_TOKEN'] ?? '',
    'webhook_url' => $_ENV['WEBHOOK_URL'] ?? '',
    'admin_id' => $_ENV['ADMIN_ID'] ?? '',
    'channel_id' => $_ENV['CHANNEL_ID'] ?? '',
    'api_url' => 'https://api.telegram.org/bot',
    'timeout' => 30,
    'retry_attempts' => 3,
];
```

### panels/marzban.php
```php
<?php
return [
    'default_panel' => [
        'url' => $_ENV['MARZBAN_URL'] ?? '',
        'username' => $_ENV['MARZBAN_USERNAME'] ?? '',
        'password' => $_ENV['MARZBAN_PASSWORD'] ?? '',
        'timeout' => 30,
    ],
    'backup_panels' => [
        // Additional panels
    ],
    'protocols' => ['vmess', 'vless', 'trojan', 'shadowsocks'],
];
```

### payments/gateways.php
```php
<?php
return [
    'zarinpal' => [
        'merchant_id' => $_ENV['ZARINPAL_MERCHANT_ID'] ?? '',
        'sandbox' => $_ENV['APP_ENV'] === 'development',
        'callback_url' => $_ENV['ZARINPAL_CALLBACK_URL'] ?? '',
    ],
    'nowpayments' => [
        'api_key' => $_ENV['NOWPAYMENTS_API_KEY'] ?? '',
        'sandbox' => $_ENV['APP_ENV'] === 'development',
        'callback_url' => $_ENV['NOWPAYMENTS_CALLBACK_URL'] ?? '',
    ],
];
```

---

## 🔄 Environment Variables

### فایل .env نمونه:
```env
# Application
APP_ENV=production
APP_DEBUG=false

# Database
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=webot
DB_USERNAME=webot_user
DB_PASSWORD=secure_password

# Telegram
TELEGRAM_BOT_TOKEN=your_bot_token
WEBHOOK_URL=https://yourdomain.com/webhook
ADMIN_ID=123456789
CHANNEL_ID=-1001234567890

# Marzban Panel
MARZBAN_URL=https://panel.example.com
MARZBAN_USERNAME=admin
MARZBAN_PASSWORD=admin_password

# Payment Gateways
ZARINPAL_MERCHANT_ID=your_merchant_id
NOWPAYMENTS_API_KEY=your_api_key
```

---

## 🔧 Environment Files

### .env.example ✅ EXISTS
**فایل نمونه تنظیمات** - کپی کنید به `.env` و مقادیر را تنظیم کنید

### .env.production ✅ EXISTS
**تنظیمات محیط production** - برای سرور اصلی

### .env.testing ✅ EXISTS
**تنظیمات محیط test** - برای تست‌ها

### ⚙️ تنظیم اولیه

#### 1. کپی فایل Environment
```bash
# برای development
cp .env.example .env

# برای production
cp .env.production .env
```

#### 2. اعتبارسنجی تنظیمات ✅ NEW
```bash
# بررسی تنظیمات
php scripts/validate-env.php

# بررسی فایل خاص
php scripts/validate-env.php --env=.env.production

# خروجی JSON
php scripts/validate-env.php --json
```

---

## 📝 بهترین روش‌ها

### 1. جداسازی محیط‌ها:
- Development
- Staging  
- Production

### 2. Validation:
- اعتبارسنجی تمام تنظیمات در startup
- پیام‌های خطای واضح

### 3. Caching:
- کش کردن تنظیمات برای بهبود performance
- Invalidation هوشمند

### 4. Documentation:
- مستندسازی تمام تنظیمات
- مثال‌های کاربردی

---

*این ساختار برای مدیریت حرفه‌ای تنظیمات WeBot طراحی شده است.*
