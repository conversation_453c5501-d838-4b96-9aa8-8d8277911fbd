<?php
/**
 * WeBot Syntax Test
 * 
 * This file tests the syntax of all PHP files without executing them.
 * It checks for PHP syntax errors in the new class structure.
 */

echo "WeBot Syntax Test\n";
echo "=================\n\n";

// Test files to check
$testFiles = [
    'autoload.php',
    'index.php',
    'src/Core/Application.php',
    'src/Core/Config.php',
    'src/Exceptions/WeBotException.php',
    'src/Exceptions/ValidationException.php',
    'src/Exceptions/PaymentException.php',
    'src/Exceptions/PanelException.php',
    'src/Utils/Logger.php',
    'src/Utils/Helper.php',
    'src/Utils/Validator.php',
    'tests/bootstrap.php'
];

$errors = [];
$passed = 0;

foreach ($testFiles as $file) {
    echo "Testing {$file}... ";
    
    if (!file_exists($file)) {
        echo "❌ File not found\n";
        $errors[] = "{$file}: File not found";
        continue;
    }
    
    // Check PHP syntax
    $output = [];
    $returnCode = 0;
    exec("php -l \"{$file}\" 2>&1", $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "✅ OK\n";
        $passed++;
    } else {
        echo "❌ Syntax Error\n";
        $errors[] = "{$file}: " . implode("\n", $output);
    }
}

echo "\n";
echo "=================\n";
echo "Results:\n";
echo "✅ Passed: {$passed}\n";
echo "❌ Failed: " . count($errors) . "\n";

if (!empty($errors)) {
    echo "\nErrors:\n";
    foreach ($errors as $error) {
        echo "- {$error}\n";
    }
    exit(1);
} else {
    echo "\n🎉 All syntax tests passed!\n";
}

// Test class structure
echo "\nClass Structure Test:\n";
echo "====================\n";

$expectedClasses = [
    'WeBot\\Core\\Application',
    'WeBot\\Core\\Config',
    'WeBot\\Exceptions\\WeBotException',
    'WeBot\\Exceptions\\ValidationException',
    'WeBot\\Exceptions\\PaymentException',
    'WeBot\\Exceptions\\PanelException',
    'WeBot\\Utils\\Logger',
    'WeBot\\Utils\\Helper',
    'WeBot\\Utils\\Validator'
];

// Note: We can't actually test class loading without composer autoloader
// But we can check if the files follow the expected structure

foreach ($expectedClasses as $className) {
    $filePath = 'src/' . str_replace('WeBot\\', '', str_replace('\\', '/', $className)) . '.php';
    echo "Checking {$className}... ";
    
    if (file_exists($filePath)) {
        $content = file_get_contents($filePath);
        
        // Check if class is properly declared
        $classDeclaration = "class " . basename($className);
        if (strpos($content, $classDeclaration) !== false) {
            echo "✅ OK\n";
        } else {
            echo "❌ Class declaration not found\n";
        }
    } else {
        echo "❌ File not found\n";
    }
}

echo "\nDirectory Structure Test:\n";
echo "========================\n";

$expectedDirs = [
    'src',
    'src/Core',
    'src/Controllers',
    'src/Services',
    'src/Models',
    'src/Repositories',
    'src/Middleware',
    'src/Utils',
    'src/Exceptions',
    'config',
    'config/database',
    'config/telegram',
    'config/panels',
    'config/payments',
    'public',
    'public/assets',
    'public/uploads',
    'public/temp',
    'tests',
    'tests/Unit',
    'tests/Integration',
    'tests/Feature',
    'storage',
    'storage/logs',
    'storage/cache',
    'storage/sessions'
];

$missingDirs = [];

foreach ($expectedDirs as $dir) {
    if (is_dir($dir)) {
        echo "✅ {$dir}/\n";
    } else {
        echo "❌ {$dir}/\n";
        $missingDirs[] = $dir;
    }
}

if (empty($missingDirs)) {
    echo "\n🎉 All directories exist!\n";
} else {
    echo "\n❌ Missing directories: " . implode(', ', $missingDirs) . "\n";
}

echo "\nConfiguration Test:\n";
echo "==================\n";

// Test composer.json structure
if (file_exists('composer.json')) {
    $composer = json_decode(file_get_contents('composer.json'), true);
    
    echo "Testing composer.json... ";
    if (isset($composer['autoload']['psr-4']['WeBot\\'])) {
        echo "✅ PSR-4 autoloading configured\n";
    } else {
        echo "❌ PSR-4 autoloading not configured\n";
    }
}

// Test environment files
$envFiles = ['.env.example', '.env.testing'];
foreach ($envFiles as $envFile) {
    echo "Testing {$envFile}... ";
    if (file_exists($envFile)) {
        echo "✅ OK\n";
    } else {
        echo "❌ Missing\n";
    }
}

// Test PHPUnit configuration
echo "Testing phpunit.xml... ";
if (file_exists('phpunit.xml')) {
    echo "✅ OK\n";
} else {
    echo "❌ Missing\n";
}

echo "\n";
echo "=================\n";
echo "✅ Syntax test completed!\n";
echo "\nNext steps:\n";
echo "1. Install dependencies: composer install\n";
echo "2. Create .env file from .env.example\n";
echo "3. Run actual tests: ./vendor/bin/phpunit\n";
echo "4. Test application functionality\n";
