<?php

declare(strict_types=1);

namespace WeBot\Tests\Integration;

use WeBot\Services\TelegramService;
use WeBot\Core\Config;
use WeBot\Controllers\UserController;
use WeBot\Controllers\PaymentController;

/**
 * Telegram API Integration Test
 * 
 * Tests Telegram Bot API integration including
 * message sending, callback handling, and webhook processing.
 * 
 * @package WeBot\Tests\Integration
 * @version 2.0
 */
class TelegramAPIIntegrationTest
{
    private Config $config;
    private TelegramService $telegram;
    private array $testResults = [];
    private int $totalTests = 0;
    private int $passedTests = 0;
    private int $failedTests = 0;

    public function __construct()
    {
        $this->setupTestEnvironment();
        $this->initializeTelegramService();
    }

    /**
     * Setup test environment
     */
    private function setupTestEnvironment(): void
    {
        putenv('WEBOT_TEST_MODE=true');
        putenv('DISABLE_EXTERNAL_APIS=true');
        
        // Setup mock responses for testing
        $GLOBALS['mock_telegram_responses'] = [
            'sendMessage' => [
                'ok' => true,
                'result' => [
                    'message_id' => 123,
                    'date' => time(),
                    'chat' => ['id' => 123456789],
                    'text' => 'Test message'
                ]
            ],
            'editMessageText' => [
                'ok' => true,
                'result' => [
                    'message_id' => 123,
                    'date' => time(),
                    'chat' => ['id' => 123456789],
                    'text' => 'Edited message'
                ]
            ],
            'answerCallbackQuery' => [
                'ok' => true,
                'result' => true
            ],
            'sendPhoto' => [
                'ok' => true,
                'result' => [
                    'message_id' => 124,
                    'date' => time(),
                    'chat' => ['id' => 123456789],
                    'photo' => [['file_id' => 'test_photo_id']]
                ]
            ],
            'getMe' => [
                'ok' => true,
                'result' => [
                    'id' => 123456789,
                    'is_bot' => true,
                    'first_name' => 'WeBot',
                    'username' => 'webot_test'
                ]
            ]
        ];
        
        $this->config = new Config();
    }

    /**
     * Initialize Telegram service
     */
    private function initializeTelegramService(): void
    {
        $this->telegram = new TelegramService($this->config);
    }

    /**
     * Run all Telegram API integration tests
     */
    public function runAllTests(): array
    {
        echo "🧪 Telegram API Integration Tests\n";
        echo "=================================\n\n";

        $this->testBasicMessageOperations();
        $this->testCallbackQueryHandling();
        $this->testKeyboardGeneration();
        $this->testFileOperations();
        $this->testErrorHandling();
        $this->testBroadcastFunctionality();
        $this->testWebhookIntegration();
        $this->testRateLimiting();

        return $this->getTestResults();
    }

    /**
     * Test basic message operations
     */
    private function testBasicMessageOperations(): void
    {
        $this->runTest('Basic Message Operations', function() {
            // Test sendMessage
            $result = $this->telegram->sendMessage([
                'chat_id' => 123456789,
                'text' => 'Test message'
            ]);

            if (!($result['ok'] ?? false)) {
                throw new \Exception('sendMessage failed');
            }

            // Test editMessageText
            $result = $this->telegram->editMessageText([
                'chat_id' => 123456789,
                'message_id' => 123,
                'text' => 'Edited message'
            ]);

            if (!($result['ok'] ?? false)) {
                throw new \Exception('editMessageText failed');
            }

            // Test deleteMessage
            $result = $this->telegram->deleteMessage([
                'chat_id' => 123456789,
                'message_id' => 123
            ]);

            // Note: deleteMessage might not be mocked, so we don't fail on this
            return true;
        });
    }

    /**
     * Test callback query handling
     */
    private function testCallbackQueryHandling(): void
    {
        $this->runTest('Callback Query Handling', function() {
            // Test answerCallbackQuery
            $result = $this->telegram->answerCallbackQuery([
                'callback_query_id' => 'test_callback_123',
                'text' => 'Callback answered'
            ]);

            if (!($result['ok'] ?? false)) {
                throw new \Exception('answerCallbackQuery failed');
            }

            // Test callback data processing
            $mockCallback = [
                'id' => 'test_callback_123',
                'from' => ['id' => 123456789, 'first_name' => 'Test'],
                'message' => [
                    'message_id' => 123,
                    'chat' => ['id' => 123456789]
                ],
                'data' => 'test_callback_data'
            ];

            // Verify callback structure
            if (!isset($mockCallback['data'])) {
                throw new \Exception('Callback data missing');
            }

            return true;
        });
    }

    /**
     * Test keyboard generation
     */
    private function testKeyboardGeneration(): void
    {
        $this->runTest('Keyboard Generation', function() {
            // Test inline keyboard creation
            $inlineKeyboard = $this->telegram->createInlineKeyboard([
                [
                    ['text' => 'Button 1', 'callback_data' => 'btn1'],
                    ['text' => 'Button 2', 'callback_data' => 'btn2']
                ],
                [
                    ['text' => 'URL Button', 'url' => 'https://example.com']
                ]
            ]);

            if (!isset($inlineKeyboard['inline_keyboard'])) {
                throw new \Exception('Inline keyboard structure invalid');
            }

            // Test reply keyboard creation
            $replyKeyboard = $this->telegram->createReplyKeyboard([
                [['text' => 'Option 1'], ['text' => 'Option 2']],
                [['text' => 'Option 3']]
            ]);

            if (!isset($replyKeyboard['keyboard'])) {
                throw new \Exception('Reply keyboard structure invalid');
            }

            // Test keyboard removal
            $removeKeyboard = $this->telegram->removeKeyboard();
            if (!isset($removeKeyboard['remove_keyboard'])) {
                throw new \Exception('Remove keyboard structure invalid');
            }

            return true;
        });
    }

    /**
     * Test file operations
     */
    private function testFileOperations(): void
    {
        $this->runTest('File Operations', function() {
            // Test sendPhoto
            $result = $this->telegram->sendPhoto([
                'chat_id' => 123456789,
                'photo' => 'test_photo_url',
                'caption' => 'Test photo'
            ]);

            if (!($result['ok'] ?? false)) {
                throw new \Exception('sendPhoto failed');
            }

            // Test sendDocument
            $result = $this->telegram->sendDocument([
                'chat_id' => 123456789,
                'document' => 'test_document_url',
                'caption' => 'Test document'
            ]);

            // Document might not be mocked, so we don't fail on this
            return true;
        });
    }

    /**
     * Test error handling
     */
    private function testErrorHandling(): void
    {
        $this->runTest('Error Handling', function() {
            // Test with invalid parameters
            try {
                $result = $this->telegram->sendMessage([
                    'chat_id' => '', // Invalid chat_id
                    'text' => 'Test'
                ]);
                
                // In test mode, this should still work with mock
                // In real mode, this would throw an exception
            } catch (\Exception $e) {
                // This is expected behavior for invalid parameters
            }

            // Test retry mechanism by checking if service handles failures gracefully
            $result = $this->telegram->sendMessage([
                'chat_id' => 123456789,
                'text' => 'Test retry'
            ]);

            // Should work with mock responses
            return true;
        });
    }

    /**
     * Test broadcast functionality
     */
    private function testBroadcastFunctionality(): void
    {
        $this->runTest('Broadcast Functionality', function() {
            $userIds = [123456789, 987654321, 555666777];
            $message = 'Broadcast test message';

            $result = $this->telegram->broadcast($userIds, $message);

            if (!is_array($result)) {
                throw new \Exception('Broadcast result should be array');
            }

            $requiredKeys = ['total', 'success', 'failed', 'results'];
            foreach ($requiredKeys as $key) {
                if (!array_key_exists($key, $result)) {
                    throw new \Exception("Broadcast result missing key: {$key}");
                }
            }

            if ($result['total'] !== count($userIds)) {
                throw new \Exception('Broadcast total count mismatch');
            }

            return true;
        });
    }

    /**
     * Test webhook integration
     */
    private function testWebhookIntegration(): void
    {
        $this->runTest('Webhook Integration', function() {
            // Test webhook info
            $result = $this->telegram->getWebhookInfo();
            
            // In test mode, this might not be fully mocked
            // We just verify the method exists and can be called

            // Test bot info
            $result = $this->telegram->getMe();
            if (!($result['ok'] ?? false)) {
                throw new \Exception('getMe failed');
            }

            if (!isset($result['result']['is_bot'])) {
                throw new \Exception('Bot info missing is_bot field');
            }

            return true;
        });
    }

    /**
     * Test rate limiting
     */
    private function testRateLimiting(): void
    {
        $this->runTest('Rate Limiting', function() {
            // Test multiple rapid requests
            $results = [];
            for ($i = 0; $i < 5; $i++) {
                $result = $this->telegram->sendMessage([
                    'chat_id' => 123456789,
                    'text' => "Rate limit test {$i}"
                ]);
                $results[] = $result;
                
                // Small delay to simulate rate limiting
                usleep(10000); // 10ms
            }

            // All requests should succeed in test mode
            foreach ($results as $result) {
                if (!($result['ok'] ?? false)) {
                    throw new \Exception('Rate limiting test failed');
                }
            }

            return true;
        });
    }

    /**
     * Test text formatting
     */
    private function testTextFormatting(): void
    {
        $this->runTest('Text Formatting', function() {
            $rawText = 'Test **bold** and *italic* and `code`';
            $formatted = $this->telegram->formatText($rawText);

            if (!str_contains($formatted, '<b>bold</b>')) {
                throw new \Exception('Bold formatting failed');
            }

            if (!str_contains($formatted, '<i>italic</i>')) {
                throw new \Exception('Italic formatting failed');
            }

            if (!str_contains($formatted, '<code>code</code>')) {
                throw new \Exception('Code formatting failed');
            }

            return true;
        });
    }

    /**
     * Test channel membership
     */
    private function testChannelMembership(): void
    {
        $this->runTest('Channel Membership', function() {
            // Test channel membership check
            $isMember = $this->telegram->isChannelMember(123456789, '@test_channel');
            
            // In test mode, this should return false or handle gracefully
            if (!is_bool($isMember)) {
                throw new \Exception('Channel membership check should return boolean');
            }

            return true;
        });
    }

    /**
     * Run individual test
     */
    private function runTest(string $testName, callable $test): void
    {
        $this->totalTests++;
        
        try {
            $result = $test();
            
            if ($result === true) {
                $this->passedTests++;
                $this->testResults[] = ['name' => $testName, 'status' => 'PASS', 'error' => null];
                echo "✅ {$testName}\n";
            } else {
                $this->failedTests++;
                $this->testResults[] = ['name' => $testName, 'status' => 'FAIL', 'error' => 'Test returned false'];
                echo "❌ {$testName}: Test returned false\n";
            }
        } catch (\Throwable $e) {
            $this->failedTests++;
            $this->testResults[] = ['name' => $testName, 'status' => 'FAIL', 'error' => $e->getMessage()];
            echo "❌ {$testName}: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Get test results
     */
    private function getTestResults(): array
    {
        $successRate = $this->totalTests > 0 ? round(($this->passedTests / $this->totalTests) * 100, 2) : 0;
        
        return [
            'total_tests' => $this->totalTests,
            'passed_tests' => $this->passedTests,
            'failed_tests' => $this->failedTests,
            'success_rate' => $successRate,
            'results' => $this->testResults
        ];
    }

    /**
     * Print test summary
     */
    public function printSummary(): void
    {
        $results = $this->getTestResults();
        
        echo "\n📊 Telegram API Integration Test Summary:\n";
        echo "========================================\n";
        echo "Total Tests: {$results['total_tests']}\n";
        echo "Passed: {$results['passed_tests']}\n";
        echo "Failed: {$results['failed_tests']}\n";
        echo "Success Rate: {$results['success_rate']}%\n";
        
        if ($results['failed_tests'] > 0) {
            echo "\n❌ Failed Tests:\n";
            foreach ($results['results'] as $result) {
                if ($result['status'] === 'FAIL') {
                    echo "  - {$result['name']}: {$result['error']}\n";
                }
            }
            echo "\n🔴 Telegram API Integration Test: FAILED\n";
        } else {
            echo "\n🟢 Telegram API Integration Test: PASSED\n";
            echo "\n🎉 Telegram API integration verified successfully!\n";
        }
    }
}

// Run tests if executed directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'] ?? '')) {
    $tester = new TelegramAPIIntegrationTest();
    $tester->runAllTests();
    $tester->printSummary();
}
