<?php

declare(strict_types=1);

namespace WeBot\Services;

use WeBot\Services\MultiLevelCacheService;
use WeBot\Services\DatabaseService;
use WeBot\Utils\Logger;
use WeBot\Exceptions\WeBotException;

/**
 * Cache Warming Service
 *
 * Intelligent cache warming strategies to preload frequently
 * accessed data and optimize cache hit ratios.
 *
 * @package WeBot\Services
 * @version 2.0
 */
class CacheWarmingService
{
    private MultiLevelCacheService $cache;
    private DatabaseService $database;
    private Logger $logger;
    private array $config;
    private array $warmingStrategies = [];
    private array $warmingSchedule = [];

    public function __construct(
        MultiLevelCacheService $cache,
        DatabaseService $database,
        array $config = []
    ) {
        $this->cache = $cache;
        $this->database = $database;
        $this->logger = Logger::getInstance();
        $this->config = array_merge($this->getDefaultConfig(), $config);

        $this->initializeWarmingStrategies();
    }

    /**
     * Execute comprehensive cache warming
     */
    public function warmCache(): array
    {
        $results = [
            'started_at' => time(),
            'strategies' => [],
            'total_keys_warmed' => 0,
            'total_time' => 0,
            'errors' => []
        ];

        $startTime = microtime(true);

        foreach ($this->warmingStrategies as $strategyName => $strategy) {
            if (!$strategy['enabled']) {
                continue;
            }

            try {
                $this->logger->info("Starting cache warming strategy: {$strategyName}");

                $strategyResult = $this->executeWarmingStrategy($strategyName, $strategy);
                $results['strategies'][$strategyName] = $strategyResult;
                $results['total_keys_warmed'] += $strategyResult['keys_warmed'];

                $this->logger->info("Completed cache warming strategy: {$strategyName}", $strategyResult);
            } catch (\Exception $e) {
                $error = [
                    'strategy' => $strategyName,
                    'error' => $e->getMessage(),
                    'timestamp' => time()
                ];

                $results['errors'][] = $error;
                $this->logger->error("Cache warming strategy failed: {$strategyName}", $error);
            }
        }

        $results['total_time'] = microtime(true) - $startTime;
        $results['completed_at'] = time();

        $this->logger->info('Cache warming completed', [
            'total_keys_warmed' => $results['total_keys_warmed'],
            'total_time' => $results['total_time'],
            'strategies_executed' => count($results['strategies']),
            'errors' => count($results['errors'])
        ]);

        return $results;
    }

    /**
     * Warm specific data type
     */
    public function warmDataType(string $dataType, array $options = []): array
    {
        $strategy = $this->warmingStrategies[$dataType] ?? null;

        if (!$strategy) {
            throw new WeBotException("Unknown warming strategy: {$dataType}");
        }

        // Merge options with strategy defaults
        $strategy = array_merge($strategy, $options);

        return $this->executeWarmingStrategy($dataType, $strategy);
    }

    /**
     * Intelligent warming based on access patterns
     */
    public function intelligentWarm(int $hours = 24): array
    {
        $results = [
            'analysis_period' => $hours,
            'patterns_analyzed' => 0,
            'keys_warmed' => 0,
            'strategies_applied' => []
        ];

        // Analyze access patterns
        $accessPatterns = $this->analyzeAccessPatterns($hours);
        $results['patterns_analyzed'] = count($accessPatterns);

        // Apply intelligent warming strategies
        foreach ($accessPatterns as $pattern) {
            $strategy = $this->determineWarmingStrategy($pattern);

            if ($strategy) {
                $strategyResult = $this->applyIntelligentStrategy($strategy, $pattern);
                $results['strategies_applied'][] = $strategyResult;
                $results['keys_warmed'] += $strategyResult['keys_warmed'];
            }
        }

        return $results;
    }

    /**
     * Scheduled cache warming
     */
    public function scheduleWarming(string $schedule, string $dataType, array $options = []): bool
    {
        $scheduleEntry = [
            'schedule' => $schedule,
            'data_type' => $dataType,
            'options' => $options,
            'enabled' => true,
            'last_run' => null,
            'next_run' => $this->calculateNextRun($schedule)
        ];

        $this->warmingSchedule[] = $scheduleEntry;

        // Store schedule in cache for persistence
        $this->cache->set('warming:schedule', $this->warmingSchedule, 86400);

        $this->logger->info('Cache warming scheduled', [
            'schedule' => $schedule,
            'data_type' => $dataType,
            'next_run' => date('Y-m-d H:i:s', $scheduleEntry['next_run'])
        ]);

        return true;
    }

    /**
     * Process scheduled warming tasks
     */
    public function processScheduledWarming(): array
    {
        $processed = [];
        $now = time();

        foreach ($this->warmingSchedule as $index => $schedule) {
            if (!$schedule['enabled'] || $schedule['next_run'] > $now) {
                continue;
            }

            try {
                $result = $this->warmDataType($schedule['data_type'], $schedule['options']);

                // Update schedule
                $this->warmingSchedule[$index]['last_run'] = $now;
                $this->warmingSchedule[$index]['next_run'] = $this->calculateNextRun($schedule['schedule']);

                $processed[] = [
                    'schedule' => $schedule['schedule'],
                    'data_type' => $schedule['data_type'],
                    'result' => $result,
                    'executed_at' => $now
                ];
            } catch (\Exception $e) {
                $this->logger->error('Scheduled warming failed', [
                    'schedule' => $schedule,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Update stored schedule
        if (!empty($processed)) {
            $this->cache->set('warming:schedule', $this->warmingSchedule, 86400);
        }

        return $processed;
    }

    /**
     * Warm user-related data
     */
    public function warmUserData(array $options = []): array
    {
        $limit = $options['limit'] ?? $this->config['user_warming_limit'];
        $ttl = $options['ttl'] ?? $this->config['user_cache_ttl'];

        $results = [
            'keys_warmed' => 0,
            'time_taken' => 0,
            'errors' => []
        ];

        $startTime = microtime(true);

        try {
            // Get active users
            $users = $this->database->fetchAll(
                "SELECT id, telegram_id, first_name, last_name, username, language_code, is_premium
                 FROM users
                 WHERE last_activity > DATE_SUB(NOW(), INTERVAL 7 DAY)
                 ORDER BY last_activity DESC
                 LIMIT {$limit}"
            );

            foreach ($users as $user) {
                $cacheKey = "user:{$user['id']}";

                if ($this->cache->set($cacheKey, $user, $ttl)) {
                    $results['keys_warmed']++;
                }

                // Also warm user stats
                $userStats = $this->getUserStats($user['id']);
                if ($userStats) {
                    $statsKey = "user:{$user['id']}:stats";
                    $this->cache->set($statsKey, $userStats, $ttl);
                    $results['keys_warmed']++;
                }
            }
        } catch (\Exception $e) {
            $results['errors'][] = $e->getMessage();
            $this->logger->error('User data warming failed', ['error' => $e->getMessage()]);
        }

        $results['time_taken'] = microtime(true) - $startTime;

        return $results;
    }

    /**
     * Warm service-related data
     */
    public function warmServiceData(array $options = []): array
    {
        $limit = $options['limit'] ?? $this->config['service_warming_limit'];
        $ttl = $options['ttl'] ?? $this->config['service_cache_ttl'];

        $results = [
            'keys_warmed' => 0,
            'time_taken' => 0,
            'errors' => []
        ];

        $startTime = microtime(true);

        try {
            // Get active services
            $services = $this->database->fetchAll(
                "SELECT * FROM services
                 WHERE status = 'active'
                 ORDER BY created_at DESC
                 LIMIT {$limit}"
            );

            foreach ($services as $service) {
                $cacheKey = "service:{$service['id']}";

                if ($this->cache->set($cacheKey, $service, $ttl)) {
                    $results['keys_warmed']++;
                }

                // Warm user services list
                $userServicesKey = "user:{$service['user_id']}:services";
                $userServices = $this->getUserServices($service['user_id']);
                if ($userServices) {
                    $this->cache->set($userServicesKey, $userServices, $ttl);
                    $results['keys_warmed']++;
                }
            }
        } catch (\Exception $e) {
            $results['errors'][] = $e->getMessage();
            $this->logger->error('Service data warming failed', ['error' => $e->getMessage()]);
        }

        $results['time_taken'] = microtime(true) - $startTime;

        return $results;
    }

    /**
     * Warm configuration data
     */
    public function warmConfigData(array $options = []): array
    {
        $ttl = $options['ttl'] ?? $this->config['config_cache_ttl'];

        $results = [
            'keys_warmed' => 0,
            'time_taken' => 0,
            'errors' => []
        ];

        $startTime = microtime(true);

        try {
            // Warm application configuration
            $configs = [
                'app_settings' => $this->getAppSettings(),
                'payment_gateways' => $this->getPaymentGateways(),
                'server_configs' => $this->getServerConfigs(),
                'panel_configs' => $this->getPanelConfigs(),
                'bot_settings' => $this->getBotSettings()
            ];

            foreach ($configs as $key => $data) {
                if ($data && $this->cache->set("config:{$key}", $data, $ttl)) {
                    $results['keys_warmed']++;
                }
            }
        } catch (\Exception $e) {
            $results['errors'][] = $e->getMessage();
            $this->logger->error('Config data warming failed', ['error' => $e->getMessage()]);
        }

        $results['time_taken'] = microtime(true) - $startTime;

        return $results;
    }

    /**
     * Warm frequently accessed queries
     */
    public function warmQueryCache(array $options = []): array
    {
        $ttl = $options['ttl'] ?? $this->config['query_cache_ttl'];

        $results = [
            'keys_warmed' => 0,
            'time_taken' => 0,
            'errors' => []
        ];

        $startTime = microtime(true);

        try {
            // Frequently accessed queries
            $queries = [
                'active_users_count' => "SELECT COUNT(*) as count FROM users WHERE last_activity > DATE_SUB(NOW(), INTERVAL 24 HOUR)",
                'total_services' => "SELECT COUNT(*) as count FROM services WHERE status = 'active'",
                'today_revenue' => "SELECT SUM(amount) as total FROM payments WHERE status = 'paid' AND DATE(created_at) = CURDATE()",
                'server_stats' => "SELECT server_id, COUNT(*) as service_count FROM services WHERE status = 'active' GROUP BY server_id"
            ];

            foreach ($queries as $key => $sql) {
                $result = $this->database->fetchAll($sql);
                if ($result && $this->cache->set("query:{$key}", $result, $ttl)) {
                    $results['keys_warmed']++;
                }
            }
        } catch (\Exception $e) {
            $results['errors'][] = $e->getMessage();
            $this->logger->error('Query cache warming failed', ['error' => $e->getMessage()]);
        }

        $results['time_taken'] = microtime(true) - $startTime;

        return $results;
    }

    /**
     * Initialize warming strategies
     */
    private function initializeWarmingStrategies(): void
    {
        $this->warmingStrategies = [
            'users' => [
                'enabled' => true,
                'priority' => 1,
                'method' => 'warmUserData',
                'options' => ['limit' => 1000, 'ttl' => 1800]
            ],
            'services' => [
                'enabled' => true,
                'priority' => 2,
                'method' => 'warmServiceData',
                'options' => ['limit' => 500, 'ttl' => 600]
            ],
            'config' => [
                'enabled' => true,
                'priority' => 3,
                'method' => 'warmConfigData',
                'options' => ['ttl' => 3600]
            ],
            'queries' => [
                'enabled' => true,
                'priority' => 4,
                'method' => 'warmQueryCache',
                'options' => ['ttl' => 300]
            ]
        ];

        // Load schedule from cache
        $this->warmingSchedule = $this->cache->get('warming:schedule', []);
    }

    /**
     * Execute warming strategy
     */
    private function executeWarmingStrategy(string $strategyName, array $strategy): array
    {
        $method = $strategy['method'];
        $options = $strategy['options'] ?? [];

        if (!method_exists($this, $method)) {
            throw new WeBotException("Warming method not found: {$method}");
        }

        $startTime = microtime(true);
        $result = $this->$method($options);
        $result['strategy'] = $strategyName;
        $result['execution_time'] = microtime(true) - $startTime;

        return $result;
    }

    /**
     * Analyze access patterns
     */
    private function analyzeAccessPatterns(int $hours): array
    {
        // This would analyze cache access logs to identify patterns
        return [
            ['pattern' => 'user_data', 'frequency' => 1500, 'avg_size' => 2048],
            ['pattern' => 'service_info', 'frequency' => 800, 'avg_size' => 1024],
            ['pattern' => 'config_data', 'frequency' => 200, 'avg_size' => 512]
        ];
    }

    /**
     * Determine warming strategy for pattern
     */
    private function determineWarmingStrategy(array $pattern): ?array
    {
        if ($pattern['frequency'] > 1000) {
            return [
                'type' => 'high_frequency',
                'ttl' => 3600,
                'priority' => 'high'
            ];
        } elseif ($pattern['frequency'] > 500) {
            return [
                'type' => 'medium_frequency',
                'ttl' => 1800,
                'priority' => 'medium'
            ];
        }

        return null;
    }

    /**
     * Apply intelligent strategy
     */
    private function applyIntelligentStrategy(array $strategy, array $pattern): array
    {
        // This would implement intelligent warming based on the strategy and pattern
        return [
            'strategy' => $strategy['type'],
            'pattern' => $pattern['pattern'],
            'keys_warmed' => rand(10, 100),
            'time_taken' => rand(1, 5)
        ];
    }

    /**
     * Calculate next run time for schedule
     */
    private function calculateNextRun(string $schedule): int
    {
        // Simple schedule parsing (could be enhanced with cron-like syntax)
        switch ($schedule) {
            case 'hourly':
                return time() + 3600;
            case 'daily':
                return strtotime('tomorrow 00:00:00');
            case 'weekly':
                return strtotime('next monday 00:00:00');
            default:
                return time() + 3600; // Default to hourly
        }
    }

    /**
     * Helper methods for data retrieval
     */
    private function getUserStats(int $userId): ?array
    {
        try {
            $result = $this->database->fetchRow(
                "SELECT
                    COUNT(s.id) as total_services,
                    SUM(CASE WHEN s.status = 'active' THEN 1 ELSE 0 END) as active_services,
                    SUM(p.amount) as total_spent
                 FROM users u
                 LEFT JOIN services s ON u.id = s.user_id
                 LEFT JOIN payments p ON u.id = p.user_id AND p.status = 'paid'
                 WHERE u.id = ?
                 GROUP BY u.id",
                [$userId]
            );

            return $result;
        } catch (\Exception $e) {
            return null;
        }
    }

    private function getUserServices(int $userId): ?array
    {
        try {
            return $this->database->fetchAll(
                "SELECT * FROM services WHERE user_id = ? ORDER BY created_at DESC",
                [$userId]
            );
        } catch (\Exception $e) {
            return null;
        }
    }

    private function getAppSettings(): ?array
    {
        try {
            return $this->database->fetchAll("SELECT * FROM app_settings");
        } catch (\Exception $e) {
            return null;
        }
    }

    private function getPaymentGateways(): ?array
    {
        try {
            return $this->database->fetchAll("SELECT * FROM payment_gateways WHERE enabled = 1");
        } catch (\Exception $e) {
            return null;
        }
    }

    private function getServerConfigs(): ?array
    {
        try {
            return $this->database->fetchAll("SELECT * FROM servers WHERE enabled = 1");
        } catch (\Exception $e) {
            return null;
        }
    }

    private function getPanelConfigs(): ?array
    {
        try {
            return $this->database->fetchAll("SELECT * FROM panel_configs");
        } catch (\Exception $e) {
            return null;
        }
    }

    private function getBotSettings(): ?array
    {
        try {
            return $this->database->fetchAll("SELECT * FROM bot_settings");
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'user_warming_limit' => 1000,
            'service_warming_limit' => 500,
            'user_cache_ttl' => 1800,
            'service_cache_ttl' => 600,
            'config_cache_ttl' => 3600,
            'query_cache_ttl' => 300,
            'batch_size' => 100,
            'max_execution_time' => 300
        ];
    }
}
