<?php

declare(strict_types=1);

/**
 * WeBot Error Manager CLI Tool
 * 
 * Command-line tool for error handling, monitoring, and recovery.
 * 
 * Usage:
 *   php scripts/error-manager.php [command] [options]
 * 
 * Commands:
 *   monitor      - Show error monitoring dashboard
 *   report       - Generate error report
 *   recovery     - Show recovery statistics
 *   alerts       - Show active alerts
 *   clear        - Clear error data
 *   test         - Test error handling system
 * 
 * @package WeBot\Scripts
 * @version 2.0
 */

// Include bootstrap
require_once __DIR__ . '/../src/Core/bootstrap.php';

use WeBot\Services\ErrorRecoveryService;
use WeBot\Services\ErrorMonitoringService;
use WeBot\Core\EnhancedErrorHandler;
use WeBot\Core\CacheManager;
use WeBot\Services\DatabaseService;
use WeBot\Core\Config;

/**
 * Error Manager CLI
 */
class ErrorManagerCLI
{
    private ErrorRecoveryService $recoveryService;
    private ErrorMonitoringService $monitoringService;
    private EnhancedErrorHandler $errorHandler;
    private array $commands;
    
    public function __construct()
    {
        $this->initializeServices();
        $this->initializeCommands();
    }
    
    /**
     * Run CLI application
     */
    public function run(array $argv): void
    {
        $command = $argv[1] ?? 'help';
        $options = array_slice($argv, 2);
        
        echo "🛡️ WeBot Error Manager\n";
        echo "=" . str_repeat("=", 30) . "\n\n";
        
        if (!isset($this->commands[$command])) {
            $this->showHelp();
            return;
        }
        
        try {
            $this->commands[$command]($options);
        } catch (Exception $e) {
            echo "❌ Error: " . $e->getMessage() . "\n";
            exit(1);
        }
    }
    
    /**
     * Initialize services
     */
    private function initializeServices(): void
    {
        $config = new Config();
        $cache = new CacheManager([
            'enabled' => true,
            'host' => $_ENV['REDIS_HOST'] ?? 'localhost',
            'port' => (int)($_ENV['REDIS_PORT'] ?? 6379)
        ]);
        $database = new DatabaseService($config);
        
        $this->recoveryService = new ErrorRecoveryService($cache, $database);
        $this->monitoringService = new ErrorMonitoringService($cache);
        $this->errorHandler = new EnhancedErrorHandler([
            'display_errors' => false,
            'log_errors' => true
        ]);
    }
    
    /**
     * Initialize commands
     */
    private function initializeCommands(): void
    {
        $this->commands = [
            'help' => [$this, 'showHelp'],
            'monitor' => [$this, 'showMonitoringDashboard'],
            'report' => [$this, 'generateErrorReport'],
            'recovery' => [$this, 'showRecoveryStats'],
            'alerts' => [$this, 'showActiveAlerts'],
            'clear' => [$this, 'clearErrorData'],
            'test' => [$this, 'testErrorHandling']
        ];
    }
    
    /**
     * Show help information
     */
    private function showHelp(array $options = []): void
    {
        echo "📖 Available Commands:\n\n";
        
        $commands = [
            'monitor' => 'Show real-time error monitoring dashboard',
            'report' => 'Generate comprehensive error report',
            'recovery' => 'Show error recovery statistics',
            'alerts' => 'Show active error alerts',
            'clear' => 'Clear error data and history',
            'test' => 'Test error handling system',
            'help' => 'Show this help message'
        ];
        
        foreach ($commands as $command => $description) {
            echo sprintf("  %-12s %s\n", $command, $description);
        }
        
        echo "\n📋 Examples:\n";
        echo "  php scripts/error-manager.php monitor\n";
        echo "  php scripts/error-manager.php report --timeframe=24\n";
        echo "  php scripts/error-manager.php clear --older-than=7\n";
        echo "  php scripts/error-manager.php test --type=database\n";
        echo "\n";
    }
    
    /**
     * Show monitoring dashboard
     */
    private function showMonitoringDashboard(array $options): void
    {
        echo "📊 Error Monitoring Dashboard\n";
        echo "=" . str_repeat("=", 40) . "\n\n";
        
        $timeframe = (int)$this->getOption($options, '--timeframe', '1') * 3600; // hours to seconds
        $stats = $this->monitoringService->getErrorStats($timeframe);
        
        // Summary
        echo "📈 Summary (Last " . ($timeframe / 3600) . " hours):\n";
        echo "  Total Errors: " . $stats['total_errors'] . "\n";
        echo "  Error Rate: " . round($stats['error_rate'], 2) . " errors/min\n";
        echo "  Affected Users: " . $stats['affected_users'] . "\n";
        echo "  Peak Memory: " . round($stats['memory_usage']['peak'] / 1024 / 1024, 2) . " MB\n\n";
        
        // By severity
        if (!empty($stats['by_severity'])) {
            echo "🚨 Errors by Severity:\n";
            foreach ($stats['by_severity'] as $severity => $count) {
                $icon = match ($severity) {
                    'critical' => '🔴',
                    'high' => '🟡',
                    'medium' => '🟠',
                    default => '🟢'
                };
                echo "  {$icon} {$severity}: {$count}\n";
            }
            echo "\n";
        }
        
        // Top errors
        if (!empty($stats['top_errors'])) {
            echo "🔝 Top Errors:\n";
            $i = 1;
            foreach (array_slice($stats['top_errors'], 0, 5) as $error) {
                echo "  {$i}. {$error['type']}: {$error['count']} occurrences\n";
                echo "     " . substr($error['message'], 0, 60) . "...\n";
                $i++;
            }
            echo "\n";
        }
        
        // Error patterns
        if (!empty($stats['error_patterns']['error_hotspots'])) {
            echo "🔥 Error Hotspots:\n";
            foreach (array_slice($stats['error_patterns']['error_hotspots'], 0, 3) as $file => $count) {
                echo "  📁 " . basename($file) . ": {$count} errors\n";
            }
            echo "\n";
        }
        
        echo "✅ Dashboard updated: " . date('Y-m-d H:i:s') . "\n";
    }
    
    /**
     * Generate error report
     */
    private function generateErrorReport(array $options): void
    {
        echo "📋 Generating Error Report...\n\n";
        
        $timeframe = (int)$this->getOption($options, '--timeframe', '24') * 3600; // hours to seconds
        $format = $this->getOption($options, '--format', 'text');
        
        $report = $this->monitoringService->generateErrorReport($timeframe);
        
        if ($format === 'json') {
            echo json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            return;
        }
        
        // Text format
        echo "📊 Error Report\n";
        echo "Generated: {$report['generated_at']}\n";
        echo "Timeframe: {$report['timeframe_hours']} hours\n";
        echo "=" . str_repeat("=", 50) . "\n\n";
        
        // Summary
        echo "📈 Summary:\n";
        foreach ($report['summary'] as $key => $value) {
            echo "  " . ucfirst(str_replace('_', ' ', $key)) . ": {$value}\n";
        }
        echo "\n";
        
        // Trends
        if (!empty($report['trends'])) {
            echo "📈 7-Day Trends:\n";
            foreach ($report['trends'] as $date => $data) {
                echo "  {$date}: {$data['total_errors']} errors ({$data['critical_errors']} critical)\n";
            }
            echo "\n";
        }
        
        // Active alerts
        if (!empty($report['active_alerts'])) {
            echo "🚨 Active Alerts:\n";
            foreach ($report['active_alerts'] as $alert) {
                $icon = match ($alert['severity']) {
                    'critical' => '🔴',
                    'high' => '🟡',
                    default => '🟠'
                };
                echo "  {$icon} {$alert['type']}: {$alert['message']}\n";
            }
            echo "\n";
        }
        
        // Recommendations
        if (!empty($report['recommendations'])) {
            echo "💡 Recommendations:\n";
            foreach ($report['recommendations'] as $i => $rec) {
                $icon = match ($rec['priority']) {
                    'high' => '🔴',
                    'medium' => '🟡',
                    default => '🟢'
                };
                echo "  " . ($i + 1) . ". {$icon} [{$rec['priority']}] {$rec['message']}\n";
            }
            echo "\n";
        }
        
        echo "✅ Report generated successfully!\n";
    }
    
    /**
     * Show recovery statistics
     */
    private function showRecoveryStats(array $options): void
    {
        echo "🔄 Error Recovery Statistics\n";
        echo "=" . str_repeat("=", 35) . "\n\n";
        
        $stats = $this->recoveryService->getRecoveryStats();
        
        echo "📊 Recovery Summary:\n";
        echo "  Total Attempts: " . $stats['total_attempts'] . "\n";
        echo "  Recent Attempts (1h): " . $stats['recent_attempts'] . "\n\n";
        
        if (!empty($stats['by_type'])) {
            echo "📋 Recovery Attempts by Exception Type:\n";
            arsort($stats['by_type']);
            foreach ($stats['by_type'] as $type => $count) {
                echo "  📁 " . basename($type) . ": {$count} attempts\n";
            }
            echo "\n";
        }
        
        if ($stats['total_attempts'] === 0) {
            echo "ℹ️ No recovery attempts recorded.\n";
        }
        
        echo "✅ Recovery statistics updated: " . date('Y-m-d H:i:s') . "\n";
    }
    
    /**
     * Show active alerts
     */
    private function showActiveAlerts(array $options): void
    {
        echo "🚨 Active Error Alerts\n";
        echo "=" . str_repeat("=", 25) . "\n\n";
        
        $alerts = $this->monitoringService->getActiveAlerts();
        
        if (empty($alerts)) {
            echo "✅ No active alerts.\n";
            return;
        }
        
        foreach ($alerts as $alert) {
            $icon = match ($alert['severity']) {
                'critical' => '🔴',
                'high' => '🟡',
                default => '🟠'
            };
            
            echo "{$icon} Alert: {$alert['type']}\n";
            echo "  Severity: {$alert['severity']}\n";
            echo "  Message: {$alert['message']}\n";
            echo "  Triggered: " . date('Y-m-d H:i:s', $alert['triggered_at']) . "\n";
            echo "  Expires: " . date('Y-m-d H:i:s', $alert['expires_at']) . "\n";
            echo "  Acknowledged: " . ($alert['acknowledged'] ? 'Yes' : 'No') . "\n";
            echo "\n";
        }
        
        echo "📊 Total active alerts: " . count($alerts) . "\n";
    }
    
    /**
     * Clear error data
     */
    private function clearErrorData(array $options): void
    {
        echo "🗑️ Clearing Error Data...\n\n";
        
        $olderThan = (int)$this->getOption($options, '--older-than', '7') * 86400; // days to seconds
        $clearAll = in_array('--all', $options);
        
        if ($clearAll) {
            echo "⚠️ Clearing ALL error data...\n";
            $this->monitoringService->clearErrorData(0);
            $this->recoveryService->clearRecoveryHistory();
        } else {
            echo "🗑️ Clearing error data older than " . ($olderThan / 86400) . " days...\n";
            $this->monitoringService->clearErrorData($olderThan);
        }
        
        echo "✅ Error data cleared successfully!\n";
    }
    
    /**
     * Test error handling system
     */
    private function testErrorHandling(array $options): void
    {
        echo "🧪 Testing Error Handling System...\n\n";
        
        $testType = $this->getOption($options, '--type', 'all');
        
        if ($testType === 'all' || $testType === 'exception') {
            echo "🔍 Testing exception handling...\n";
            try {
                throw new \Exception('Test exception for error handling');
            } catch (\Exception $e) {
                $this->monitoringService->recordError($e, [
                    'request_id' => 'test_' . uniqid(),
                    'test' => true
                ]);
                echo "  ✅ Exception recorded successfully\n";
            }
        }
        
        if ($testType === 'all' || $testType === 'recovery') {
            echo "🔄 Testing error recovery...\n";
            $testException = new \Exception('Test recovery exception');
            $recovered = $this->recoveryService->attemptRecovery($testException);
            echo "  ✅ Recovery attempt completed (result: " . ($recovered ? 'success' : 'failed') . ")\n";
        }
        
        if ($testType === 'all' || $testType === 'monitoring') {
            echo "📊 Testing error monitoring...\n";
            $stats = $this->monitoringService->getErrorStats(3600);
            echo "  ✅ Monitoring stats retrieved (total errors: {$stats['total_errors']})\n";
        }
        
        echo "\n🎉 Error handling system test completed!\n";
    }
    
    /**
     * Get option value from command line arguments
     */
    private function getOption(array $options, string $name, string $default = ''): string
    {
        foreach ($options as $option) {
            if (strpos($option, $name . '=') === 0) {
                return substr($option, strlen($name) + 1);
            }
        }
        return $default;
    }
}

// Run CLI application
if (php_sapi_name() === 'cli') {
    $cli = new ErrorManagerCLI();
    $cli->run($argv);
}
