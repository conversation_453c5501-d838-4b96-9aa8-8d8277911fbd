<?php
// tests/phpstan-bootstrap.php

/**
 * PHPStan Bootstrap File
 *
 * This file helps PHPStan understand the application's environment,
 * especially for autoloading and dependency injection setup.
 */

// Autoload all Composer dependencies
require_once __DIR__ . '/../vendor/autoload.php';

// We can define constants or global functions here if needed by the application
// For now, autoloading is sufficient since we fixed the core dependency issues.

// Example of setting up the container if needed for deeper analysis:
/*
use WeBot\Core\DIContainer;

$container = new DIContainer();
$container->setupDefaults();

// You could return the container to be used in PHPStan's config
return $container;
*/ 
