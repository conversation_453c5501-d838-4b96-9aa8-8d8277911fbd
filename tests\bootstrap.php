<?php

/**
 * PHPUnit Bootstrap File
 *
 * This file is executed before the test suite runs.
 */

declare(strict_types=1);

error_reporting(E_ALL);
ini_set('display_errors', '1');
ini_set('display_startup_errors', '1');

// Set timezone to UTC for consistency
date_default_timezone_set('UTC');

// Autoload all Composer dependencies
require_once dirname(__DIR__) . '/vendor/autoload.php';

// Define a root constant for the project if not already defined
if (!defined('WEBOT_ROOT')) {
    define('WEBOT_ROOT', dirname(__DIR__));
}

echo "WeBot test environment initialized.\n";
echo "PHP Version: " . phpversion() . "\n";
echo "---
";
