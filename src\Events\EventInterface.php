<?php

declare(strict_types=1);

namespace WeBot\Events;

/**
 * Event Interface
 *
 * Base interface for all events in the WeBot system.
 * Provides common structure for event handling.
 *
 * @package WeBot\Events
 * @version 2.0
 */
interface EventInterface
{
    /**
     * Get event name
     */
    public function getName(): string;

    /**
     * Get event data
     */
    public function getData(): array;

    /**
     * Get event timestamp
     */
    public function getTimestamp(): float;

    /**
     * Check if event is stoppable
     */
    public function isStoppable(): bool;

    /**
     * Stop event propagation
     */
    public function stopPropagation(): void;

    /**
     * Check if propagation is stopped
     */
    public function isPropagationStopped(): bool;
}
