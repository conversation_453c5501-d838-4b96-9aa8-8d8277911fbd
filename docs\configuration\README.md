# ⚙️ WeBot 2.0 Configuration Guide

Complete configuration guide for WeBot 2.0 including environment variables, panel settings, and advanced configurations.

## 📋 Configuration Overview

WeBot uses a hierarchical configuration system:
- **Environment Variables** (`.env`) - Core settings
- **Configuration Files** (`config/`) - Detailed settings
- **Database Settings** - Runtime configurations
- **Panel Configurations** - VPN panel integrations

## 🔧 Environment Configuration

### Core Application Settings
```env
# Application
APP_NAME="WeBot"
APP_ENV=production              # production, staging, development
APP_DEBUG=false                 # Enable debug mode
APP_URL=https://yourdomain.com  # Your domain URL
APP_KEY=your_32_character_secret_key
APP_TIMEZONE=Asia/Tehran        # Your timezone

# Localization
APP_LOCALE=fa                   # Default language (fa, en)
APP_FALLBACK_LOCALE=en         # Fallback language
```

### Database Configuration
```env
# Primary Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=webot
DB_USERNAME=webot
DB_PASSWORD=secure_password
DB_CHARSET=utf8mb4
DB_COLLATION=utf8mb4_unicode_ci

# Connection Pool
DB_POOL_MIN=5                   # Minimum connections
DB_POOL_MAX=20                  # Maximum connections
DB_TIMEOUT=30                   # Connection timeout (seconds)
```

### Cache Configuration
```env
# Redis Cache
CACHE_DRIVER=redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_DB=0

# Cache Settings
CACHE_PREFIX=webot_
CACHE_TTL=3600                  # Default TTL (seconds)
SESSION_LIFETIME=120            # Session lifetime (minutes)
```

### Telegram Bot Configuration
```env
# Bot Settings
TELEGRAM_BOT_TOKEN=your_bot_token_from_botfather
TELEGRAM_BOT_USERNAME=your_bot_username
TELEGRAM_WEBHOOK_URL=https://yourdomain.com/webhook.php
TELEGRAM_WEBHOOK_SECRET=your_webhook_secret

# Bot Behavior
TELEGRAM_PARSE_MODE=HTML        # HTML, Markdown, MarkdownV2
TELEGRAM_DISABLE_WEB_PAGE_PREVIEW=true
TELEGRAM_TIMEOUT=30             # Request timeout
TELEGRAM_MAX_RETRIES=3          # Max retry attempts
```

## 🔌 Panel Configurations

### Marzban Panel
```env
# Marzban Configuration
MARZBAN_ENABLED=true
MARZBAN_URL=https://your-marzban-panel.com
MARZBAN_USERNAME=admin
MARZBAN_PASSWORD=admin_password
MARZBAN_TIMEOUT=30
MARZBAN_VERIFY_SSL=true
MARZBAN_API_VERSION=v1
```

### X-UI Panel
```env
# X-UI Configuration
XUI_ENABLED=false
XUI_URL=https://your-xui-panel.com
XUI_USERNAME=admin
XUI_PASSWORD=admin_password
XUI_TIMEOUT=30
XUI_VERIFY_SSL=true
```

### Marzneshin Panel
```env
# Marzneshin Configuration
MARZNESHIN_ENABLED=false
MARZNESHIN_URL=https://your-marzneshin-panel.com
MARZNESHIN_USERNAME=admin
MARZNESHIN_PASSWORD=admin_password
MARZNESHIN_TIMEOUT=30
```

## 💳 Payment Gateway Configuration

### ZarinPal (Iranian Gateway)
```env
# ZarinPal Configuration
ZARINPAL_ENABLED=true
ZARINPAL_MERCHANT_ID=your_merchant_id
ZARINPAL_SANDBOX=false          # Use sandbox for testing
ZARINPAL_CALLBACK_URL=https://yourdomain.com/payment/zarinpal/callback
ZARINPAL_CURRENCY=IRR
ZARINPAL_TIMEOUT=30
```

### PayPal (International)
```env
# PayPal Configuration
PAYPAL_ENABLED=false
PAYPAL_CLIENT_ID=your_client_id
PAYPAL_CLIENT_SECRET=your_client_secret
PAYPAL_SANDBOX=false            # Use sandbox for testing
PAYPAL_CALLBACK_URL=https://yourdomain.com/payment/paypal/callback
PAYPAL_CURRENCY=USD
```

### Crypto Payments
```env
# Cryptocurrency Configuration
CRYPTO_ENABLED=false
CRYPTO_BITCOIN_ADDRESS=your_bitcoin_address
CRYPTO_ETHEREUM_ADDRESS=your_ethereum_address
CRYPTO_TETHER_ADDRESS=your_tether_address
CRYPTO_CONFIRMATION_BLOCKS=3    # Required confirmations
```

## 🔒 Security Configuration

### Authentication & Authorization
```env
# JWT Configuration
JWT_SECRET=your_jwt_secret_key
JWT_TTL=60                      # Token lifetime (minutes)
JWT_REFRESH_TTL=20160          # Refresh token lifetime (minutes)
JWT_ALGO=HS256                 # Algorithm

# API Security
API_RATE_LIMIT=60              # Requests per minute
API_RATE_LIMIT_WINDOW=1        # Window in minutes
API_KEY_LENGTH=32              # API key length
```

### Rate Limiting
```env
# Rate Limiting Configuration
RATE_LIMIT_ENABLED=true
RATE_LIMIT_DEFAULT=60          # Default requests per minute
RATE_LIMIT_BURST=10            # Burst limit
RATE_LIMIT_WINDOW=60           # Window in seconds

# Specific Limits
RATE_LIMIT_WEBHOOK=120         # Webhook requests per minute
RATE_LIMIT_API=60              # API requests per minute
RATE_LIMIT_ADMIN=300           # Admin requests per minute
```

### Security Headers
```env
# Security Headers
SECURITY_HEADERS_ENABLED=true
SECURITY_HSTS_ENABLED=true
SECURITY_CSP_ENABLED=true
SECURITY_XSS_PROTECTION=true
SECURITY_CONTENT_TYPE_NOSNIFF=true
SECURITY_FRAME_OPTIONS=DENY
```

## 📊 Logging & Monitoring

### Logging Configuration
```env
# Logging
LOG_CHANNEL=daily              # single, daily, slack, syslog
LOG_LEVEL=info                 # debug, info, notice, warning, error
LOG_MAX_FILES=14               # Keep logs for 14 days
LOG_PERMISSION=0644            # Log file permissions

# Database Query Logging
DB_LOG_QUERIES=false           # Log all database queries
DB_LOG_SLOW_QUERIES=true       # Log slow queries only
DB_SLOW_QUERY_TIME=1000        # Slow query threshold (ms)
```

### Performance Monitoring
```env
# Performance Monitoring
MONITORING_ENABLED=true
MONITORING_MEMORY_LIMIT=128M   # Memory limit per request
MONITORING_EXECUTION_TIME=30   # Max execution time (seconds)
MONITORING_QUERY_LIMIT=50      # Max queries per request

# Error Tracking
ERROR_TRACKING_ENABLED=true
ERROR_REPORTING_EMAIL=<EMAIL>
ERROR_SLACK_WEBHOOK=your_slack_webhook_url
```

## 🌐 Advanced Configuration

### Multi-Language Support
```env
# Internationalization
I18N_ENABLED=true
I18N_DEFAULT_LOCALE=fa
I18N_SUPPORTED_LOCALES=fa,en,ar
I18N_FALLBACK_LOCALE=en
I18N_CACHE_ENABLED=true
```

### File Upload Configuration
```env
# File Uploads
UPLOAD_MAX_SIZE=10M            # Max file size
UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,pdf,txt
UPLOAD_PATH=public/uploads
UPLOAD_VIRUS_SCAN=false        # Enable virus scanning
```

### Backup Configuration
```env
# Backup Settings
BACKUP_ENABLED=true
BACKUP_SCHEDULE=daily          # daily, weekly, monthly
BACKUP_RETENTION=30            # Keep backups for 30 days
BACKUP_STORAGE=local           # local, s3, ftp
BACKUP_ENCRYPTION=true         # Encrypt backups
```

## 📁 Configuration Files

### Database Configuration (`config/database.php`)
```php
<?php
return [
    'default' => env('DB_CONNECTION', 'mysql'),
    'connections' => [
        'mysql' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '3306'),
            'database' => env('DB_DATABASE', 'webot'),
            'username' => env('DB_USERNAME', 'webot'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ],
        ],
    ],
];
```

### Cache Configuration (`config/cache.php`)
```php
<?php
return [
    'default' => env('CACHE_DRIVER', 'redis'),
    'stores' => [
        'redis' => [
            'driver' => 'redis',
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', 6379),
            'database' => env('REDIS_DB', 0),
            'prefix' => env('CACHE_PREFIX', 'webot_'),
        ],
    ],
    'ttl' => env('CACHE_TTL', 3600),
];
```

## 🔧 Environment-Specific Configurations

### Development Environment
```env
APP_ENV=development
APP_DEBUG=true
LOG_LEVEL=debug
DB_LOG_QUERIES=true
CACHE_TTL=60
RATE_LIMIT_ENABLED=false
```

### Staging Environment
```env
APP_ENV=staging
APP_DEBUG=false
LOG_LEVEL=info
MONITORING_ENABLED=true
RATE_LIMIT_ENABLED=true
```

### Production Environment
```env
APP_ENV=production
APP_DEBUG=false
LOG_LEVEL=warning
MONITORING_ENABLED=true
SECURITY_HEADERS_ENABLED=true
BACKUP_ENABLED=true
```

## ✅ Configuration Validation

### Validate Configuration
```bash
# Validate all configurations
php scripts/validate-env.php

# Validate specific configuration
php scripts/validate-env.php --section=database
php scripts/validate-env.php --section=telegram
php scripts/validate-env.php --section=payments
```

### Configuration Testing
```bash
# Test database connection
php scripts/test-database.php

# Test Redis connection
php scripts/test-redis.php

# Test Telegram bot
php scripts/test-telegram.php

# Test payment gateways
php scripts/test-payments.php
```

## 🚨 Troubleshooting

### Common Configuration Issues

**Database Connection Failed**
```bash
# Check database credentials
mysql -h DB_HOST -u DB_USERNAME -p DB_DATABASE

# Verify PHP MySQL extension
php -m | grep mysql
```

**Redis Connection Failed**
```bash
# Test Redis connection
redis-cli -h REDIS_HOST -p REDIS_PORT ping

# Check Redis configuration
redis-cli config get "*"
```

**Telegram Webhook Issues**
```bash
# Check webhook status
curl "https://api.telegram.org/bot{BOT_TOKEN}/getWebhookInfo"

# Test webhook endpoint
curl -X POST "https://yourdomain.com/webhook.php"
```

## 📞 Support

- **Configuration Issues**: Check logs in `storage/logs/`
- **Environment Problems**: Use validation scripts
- **Performance Issues**: Review monitoring settings

---

**Next**: [Development Guide](../development/README.md)
