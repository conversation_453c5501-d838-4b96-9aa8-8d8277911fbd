<?php
/**
 * WeBot CLI Bootstrap
 *
 * Initializes the environment for command-line scripts.
 */

declare(strict_types=1);

// Define root path
define('WEBOT_ROOT', dirname(__DIR__));

// Load Composer autoloader
$autoloaderPath = WEBOT_ROOT . '/vendor/autoload.php';
if (!file_exists($autoloaderPath)) {
    echo "Error: Composer dependencies not installed.\n";
    echo "Please run 'composer install' in the project root.\n";
    exit(1);
}
require_once $autoloaderPath;

// Load environment variables from .env file
try {
    if (class_exists('Dotenv\Dotenv')) {
        $dotenv = Dotenv\Dotenv::createImmutable(WEBOT_ROOT);
        $dotenv->load();
    }
} catch (Exception $e) {
    echo "Warning: Could not load .env file. " . $e->getMessage() . "\n";
}

// Set default timezone
date_default_timezone_set($_ENV['APP_TIMEZONE'] ?? 'Asia/Tehran'); 