<?php

declare(strict_types=1);

namespace WeBot\Core;

use PDO;
use PDOException;
use WeBot\Exceptions\DatabaseException;

/**
 * Database Connection Manager
 *
 * Handles database connections, query execution,
 * connection pooling, and transaction management.
 *
 * @package WeBot\Core
 * @version 2.0
 */
class Database
{
    private array $config;
    private ?PDO $connection = null;
    private array $connectionPool = [];
    private int $maxConnections = 10;
    private bool $inTransaction = false;
    private array $queryLog = [];
    private bool $logQueries = false;

    public function __construct(array $config)
    {
        $this->config = $config;
        $this->maxConnections = $config['pool']['max_connections'] ?? 10;
        $this->logQueries = $config['log_queries'] ?? false;
    }

    /**
     * Get database connection
     */
    public function getConnection(): PDO
    {
        if ($this->connection === null) {
            $this->connection = $this->createConnection();
        }

        return $this->connection;
    }

    /**
     * Create new database connection
     */
    private function createConnection(): PDO
    {
        $driver = $this->config['default'] ?? 'mysql';
        $connectionConfig = $this->config['connections'][$driver] ?? [];

        if (empty($connectionConfig)) {
            throw new DatabaseException("Database configuration for driver '{$driver}' not found");
        }

        try {
            $dsn = $this->buildDSN($driver, $connectionConfig);
            $username = $connectionConfig['username'] ?? '';
            $password = $connectionConfig['password'] ?? '';
            $options = $connectionConfig['options'] ?? [];

            // Set default options
            $defaultOptions = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ];

            $options = array_merge($defaultOptions, $options);

            $pdo = new PDO($dsn, $username, $password, $options);

            // Set timezone
            if ($driver === 'mysql') {
                $pdo->exec("SET time_zone = '+00:00'");
            }

            return $pdo;
        } catch (PDOException $e) {
            throw new DatabaseException("Database connection failed: " . $e->getMessage());
        }
    }

    /**
     * Build DSN string
     */
    private function buildDSN(string $driver, array $config): string
    {
        switch ($driver) {
            case 'mysql':
                return sprintf(
                    'mysql:host=%s;port=%d;dbname=%s;charset=%s',
                    $config['host'],
                    $config['port'] ?? 3306,
                    $config['database'],
                    $config['charset'] ?? 'utf8mb4'
                );

            case 'pgsql':
                return sprintf(
                    'pgsql:host=%s;port=%d;dbname=%s',
                    $config['host'],
                    $config['port'] ?? 5432,
                    $config['database']
                );

            case 'sqlite':
                return 'sqlite:' . $config['database'];

            default:
                throw new DatabaseException("Unsupported database driver: {$driver}");
        }
    }

    /**
     * Execute query and return results
     */
    public function query(string $sql, array $params = []): array
    {
        $startTime = microtime(true);

        try {
            $connection = $this->getConnection();
            $statement = $connection->prepare($sql);
            $statement->execute($params);

            $results = $statement->fetchAll();

            if ($this->logQueries) {
                $this->logQuery($sql, $params, microtime(true) - $startTime);
            }

            return $results;
        } catch (PDOException $e) {
            throw new DatabaseException("Query execution failed: " . $e->getMessage() . " SQL: {$sql}");
        }
    }

    /**
     * Execute statement and return affected rows
     */
    public function execute(string $sql, array $params = []): int
    {
        $startTime = microtime(true);

        try {
            $connection = $this->getConnection();
            $statement = $connection->prepare($sql);
            $statement->execute($params);

            $affectedRows = $statement->rowCount();

            if ($this->logQueries) {
                $this->logQuery($sql, $params, microtime(true) - $startTime, $affectedRows);
            }

            return $affectedRows;
        } catch (PDOException $e) {
            throw new DatabaseException("Statement execution failed: " . $e->getMessage() . " SQL: {$sql}");
        }
    }

    /**
     * Get last insert ID
     */
    public function lastInsertId(): int
    {
        return (int) $this->getConnection()->lastInsertId();
    }

    /**
     * Begin transaction
     */
    public function beginTransaction(): bool
    {
        if ($this->inTransaction) {
            throw new DatabaseException("Transaction already in progress");
        }

        $result = $this->getConnection()->beginTransaction();
        $this->inTransaction = true;

        return $result;
    }

    /**
     * Commit transaction
     */
    public function commit(): bool
    {
        if (!$this->inTransaction) {
            throw new DatabaseException("No transaction in progress");
        }

        $result = $this->getConnection()->commit();
        $this->inTransaction = false;

        return $result;
    }

    /**
     * Rollback transaction
     */
    public function rollback(): bool
    {
        if (!$this->inTransaction) {
            throw new DatabaseException("No transaction in progress");
        }

        $result = $this->getConnection()->rollBack();
        $this->inTransaction = false;

        return $result;
    }

    /**
     * Create savepoint
     */
    public function savepoint(string $name): bool
    {
        $sql = "SAVEPOINT {$name}";
        return $this->execute($sql) >= 0;
    }

    /**
     * Rollback to savepoint
     */
    public function rollbackToSavepoint(string $name): bool
    {
        $sql = "ROLLBACK TO SAVEPOINT {$name}";
        return $this->execute($sql) >= 0;
    }

    /**
     * Release savepoint
     */
    public function releaseSavepoint(string $name): bool
    {
        $sql = "RELEASE SAVEPOINT {$name}";
        return $this->execute($sql) >= 0;
    }

    /**
     * Check if table exists
     */
    public function tableExists(string $tableName): bool
    {
        $driver = $this->config['default'] ?? 'mysql';

        if ($driver === 'mysql') {
            $sql = "SHOW TABLES LIKE ?";
        } elseif ($driver === 'pgsql') {
            $sql = "SELECT tablename FROM pg_tables WHERE tablename = ?";
        } else {
            $sql = "SELECT name FROM sqlite_master WHERE type='table' AND name = ?";
        }

        $result = $this->query($sql, [$tableName]);
        return !empty($result);
    }

    /**
     * Check if index exists
     */
    public function indexExists(string $tableName, string $indexName): bool
    {
        $driver = $this->config['default'] ?? 'mysql';

        if ($driver === 'mysql') {
            $sql = "SHOW INDEX FROM {$tableName} WHERE Key_name = ?";
        } elseif ($driver === 'pgsql') {
            $sql = "SELECT indexname FROM pg_indexes WHERE tablename = ? AND indexname = ?";
            return !empty($this->query($sql, [$tableName, $indexName]));
        } else {
            $sql = "SELECT name FROM sqlite_master WHERE type='index' AND name = ?";
        }

        $result = $this->query($sql, [$indexName]);
        return !empty($result);
    }

    /**
     * Get foreign key constraints
     */
    public function getForeignKeyConstraints(): array
    {
        $driver = $this->config['default'] ?? 'mysql';

        if ($driver === 'mysql') {
            $sql = "
                SELECT 
                    TABLE_NAME,
                    COLUMN_NAME,
                    CONSTRAINT_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                WHERE REFERENCED_TABLE_SCHEMA = DATABASE() 
                AND REFERENCED_TABLE_NAME IS NOT NULL
            ";
        } elseif ($driver === 'pgsql') {
            $sql = "
                SELECT 
                    tc.table_name,
                    kcu.column_name,
                    tc.constraint_name,
                    ccu.table_name AS referenced_table_name,
                    ccu.column_name AS referenced_column_name
                FROM information_schema.table_constraints AS tc
                JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                JOIN information_schema.constraint_column_usage AS ccu
                    ON ccu.constraint_name = tc.constraint_name
                WHERE tc.constraint_type = 'FOREIGN KEY'
            ";
        } else {
            return []; // SQLite doesn't have easy FK introspection
        }

        return $this->query($sql);
    }

    /**
     * Check if connected
     */
    public function isConnected(): bool
    {
        try {
            $this->getConnection()->query('SELECT 1');
            return true;
        } catch (PDOException $e) {
            return false;
        }
    }

    /**
     * Get connection from pool
     */
    public function getPooledConnection(): PDO
    {
        if (count($this->connectionPool) < $this->maxConnections) {
            $connection = $this->createConnection();
            $this->connectionPool[] = $connection;
            return $connection;
        }

        // Return existing connection from pool
        return $this->connectionPool[array_rand($this->connectionPool)];
    }

    /**
     * Release connection back to pool
     */
    public function releaseConnection(PDO $connection): void
    {
        // TODO: Implement connection release logic
        unset($connection); // Suppress unused variable warning

        // In a real implementation, you might want to validate the connection
        // and potentially close it if it's been idle too long
    }

    /**
     * Log query for debugging
     */
    private function logQuery(string $sql, array $params, float $executionTime, ?int $affectedRows = null): void
    {
        $this->queryLog[] = [
            'sql' => $sql,
            'params' => $params,
            'execution_time' => $executionTime,
            'affected_rows' => $affectedRows,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Get query log
     */
    public function getQueryLog(): array
    {
        return $this->queryLog;
    }

    /**
     * Clear query log
     */
    public function clearQueryLog(): void
    {
        $this->queryLog = [];
    }

    /**
     * Get performance metrics
     */
    public function getPerformanceMetrics(): array
    {
        $totalQueries = count($this->queryLog);
        $totalTime = array_sum(array_column($this->queryLog, 'execution_time'));
        $avgTime = $totalQueries > 0 ? $totalTime / $totalQueries : 0;

        $slowQueries = array_filter($this->queryLog, function ($query) {
            return $query['execution_time'] > 0.1; // Queries slower than 100ms
        });

        return [
            'query_count' => $totalQueries,
            'total_execution_time' => $totalTime,
            'avg_query_time' => $avgTime,
            'slow_queries' => count($slowQueries),
            'connection_pool_size' => count($this->connectionPool),
            'max_connections' => $this->maxConnections
        ];
    }

    /**
     * Close all connections
     */
    public function closeConnections(): void
    {
        $this->connection = null;
        $this->connectionPool = [];
        $this->inTransaction = false;
    }

    /**
     * Destructor
     */
    public function __destruct()
    {
        if ($this->inTransaction) {
            try {
                $this->rollback();
            } catch (\Exception $e) {
                // Ignore errors during cleanup
            }
        }

        $this->closeConnections();
    }

    /**
     * Select single record from table
     */
    public function selectOne(string $table, array $where = []): ?array
    {
        $whereParts = [];
        $params = [];

        foreach ($where as $column => $value) {
            $whereParts[] = "`{$column}` = ?";
            $params[] = $value;
        }

        $sql = "SELECT * FROM `{$table}`";
        if (!empty($whereParts)) {
            $sql .= " WHERE " . implode(' AND ', $whereParts);
        }
        $sql .= " LIMIT 1";

        $result = $this->query($sql, $params);
        return $result[0] ?? null;
    }

    /**
     * Insert record and return ID
     */
    public function insert(string $table, array $data): int
    {
        $columns = array_keys($data);
        $placeholders = array_fill(0, count($data), '?');

        $sql = "INSERT INTO `{$table}` (`" . implode('`, `', $columns) . "`) VALUES (" . implode(', ', $placeholders) . ")";

        $this->execute($sql, array_values($data));

        return $this->lastInsertId();
    }

    /**
     * Update records
     */
    public function update(string $table, array $data, array $where): int
    {
        $setParts = [];
        $whereParts = [];
        $params = [];

        // Build SET clause
        foreach ($data as $column => $value) {
            $setParts[] = "`{$column}` = ?";
            $params[] = $value;
        }

        // Build WHERE clause
        foreach ($where as $column => $value) {
            $whereParts[] = "`{$column}` = ?";
            $params[] = $value;
        }

        $sql = "UPDATE `{$table}` SET " . implode(', ', $setParts);
        if (!empty($whereParts)) {
            $sql .= " WHERE " . implode(' AND ', $whereParts);
        }

        return $this->execute($sql, $params);
    }

    /**
     * Delete records
     */
    public function delete(string $table, array $where): int
    {
        $whereParts = [];
        $params = [];

        foreach ($where as $column => $value) {
            $whereParts[] = "`{$column}` = ?";
            $params[] = $value;
        }

        $sql = "DELETE FROM `{$table}`";
        if (!empty($whereParts)) {
            $sql .= " WHERE " . implode(' AND ', $whereParts);
        }

        return $this->execute($sql, $params);
    }

    /**
     * Select multiple records from table
     */
    public function select(string $table, array $where = [], array $columns = ['*'], array $options = []): array
    {
        $whereParts = [];
        $params = [];

        foreach ($where as $column => $value) {
            $whereParts[] = "`{$column}` = ?";
            $params[] = $value;
        }

        // Build columns part
        $columnsStr = is_array($columns) && !empty($columns) ? implode(', ', $columns) : '*';

        $sql = "SELECT {$columnsStr} FROM `{$table}`";
        if (!empty($whereParts)) {
            $sql .= " WHERE " . implode(' AND ', $whereParts);
        }

        // Handle different option formats
        if (is_array($options)) {
            // New format: ['order_by' => 'column ASC', 'limit' => 10]
            if (!empty($options['order_by'])) {
                $sql .= " ORDER BY " . $options['order_by'];
            }
            if (!empty($options['limit'])) {
                $sql .= " LIMIT " . (int) $options['limit'];
            }
        } else {
            // Legacy format: limit as separate parameter
            if (is_numeric($options)) {
                $sql .= " LIMIT " . (int) $options;
            }
        }

        return $this->query($sql, $params);
    }

    /**
     * Prepare statement (PDO compatibility)
     */
    public function prepare(string $sql): \PDOStatement
    {
        $connection = $this->getConnection();
        return $connection->prepare($sql);
    }
}
