<?php

declare(strict_types=1);

namespace WeBot\Services;

use WeBot\Core\Config;
use WeBot\Utils\Logger;
use WeBot\Exceptions\WeBotException;
use Monolog\Logger as MonologLogger;
use mysqli;
use mysqli_stmt;
use mysqli_result;

/**
 * Database Service
 *
 * Handles all database operations with connection management,
 * query execution, transactions, and error handling.
 *
 * @package WeBot\Services
 * @version 2.0
 */
class DatabaseService
{
    private Config $config;
    private MonologLogger $logger;
    private ?mysqli $connection = null;
    private array $connectionConfig;
    private bool $inTransaction = false;
    private int $queryCount = 0;
    private float $totalQueryTime = 0;

    public function __construct(Config $config)
    {
        $this->config = $config;
        $this->logger = Logger::getInstance();

        $this->connectionConfig = [
            'host' => $config->get('database.host'),
            'port' => $config->get('database.port', 3306),
            'username' => $config->get('database.username'),
            'password' => $config->get('database.password'),
            'database' => $config->get('database.database'),
            'charset' => $config->get('database.charset', 'utf8mb4')
        ];

        $this->validateConfig();
    }

    /**
     * Get database connection
     */
    public function getConnection(): mysqli
    {
        if ($this->connection === null || !$this->connection->ping()) {
            $this->connect();
        }

        return $this->connection;
    }

    /**
     * Establish database connection
     */
    private function connect(): void
    {
        try {
            $this->connection = new mysqli(
                $this->connectionConfig['host'],
                $this->connectionConfig['username'],
                $this->connectionConfig['password'],
                $this->connectionConfig['database'],
                (int)$this->connectionConfig['port']
            );

            if ($this->connection->connect_error) {
                throw new WeBotException(
                    "Database connection failed: " . $this->connection->connect_error
                );
            }

            // Set charset
            $this->connection->set_charset($this->connectionConfig['charset']);

            // Set SQL mode for better compatibility
            $this->connection->query("SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'");

            // Set timezone
            $this->connection->query("SET time_zone = '+00:00'");

            $this->logger->debug('Database connected successfully', [
                'host' => $this->connectionConfig['host'],
                'database' => $this->connectionConfig['database']
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Database connection failed', [
                'host' => $this->connectionConfig['host'],
                'database' => $this->connectionConfig['database'],
                'error' => $e->getMessage()
            ]);

            throw new WeBotException("Database connection failed: " . $e->getMessage());
        }
    }

    /**
     * Prepare SQL statement
     */
    public function prepare(string $sql): mysqli_stmt
    {
        $connection = $this->getConnection();
        $stmt = $connection->prepare($sql);

        if (!$stmt) {
            $this->logger->error('SQL prepare failed', [
                'sql' => $sql,
                'error' => $connection->error
            ]);

            throw new WeBotException("SQL prepare failed: {$connection->error}");
        }

        return $stmt;
    }

    /**
     * Execute SQL query
     */
    public function query(string $sql): mysqli_result|bool
    {
        $startTime = microtime(true);
        $connection = $this->getConnection();

        $result = $connection->query($sql);

        $queryTime = microtime(true) - $startTime;
        $this->queryCount++;
        $this->totalQueryTime += $queryTime;

        if (!$result) {
            $this->logger->error('SQL query failed', [
                'sql' => $sql,
                'error' => $connection->error,
                'errno' => $connection->errno
            ]);

            throw new WeBotException("SQL query failed: {$connection->error}");
        }

        $this->logger->debug('SQL query executed', [
            'sql' => $this->sanitizeSql($sql),
            'execution_time' => round($queryTime * 1000, 2) . 'ms',
            'affected_rows' => $connection->affected_rows
        ]);

        return $result;
    }

    /**
     * Execute prepared statement with parameters
     */
    public function execute(string $sql, array $params = [], string $types = ''): mysqli_result|bool
    {
        $stmt = $this->prepare($sql);

        if (!empty($params)) {
            if (empty($types)) {
                $types = $this->inferTypes($params);
            }

            $stmt->bind_param($types, ...$params);
        }

        $startTime = microtime(true);
        $success = $stmt->execute();
        $queryTime = microtime(true) - $startTime;

        $this->queryCount++;
        $this->totalQueryTime += $queryTime;

        if (!$success) {
            $this->logger->error('Prepared statement execution failed', [
                'sql' => $sql,
                'params' => $params,
                'error' => $stmt->error
            ]);

            $stmt->close();
            throw new WeBotException("Prepared statement execution failed: {$stmt->error}");
        }

        $result = $stmt->get_result();

        $this->logger->debug('Prepared statement executed', [
            'sql' => $this->sanitizeSql($sql),
            'param_count' => count($params),
            'execution_time' => round($queryTime * 1000, 2) . 'ms'
        ]);

        $stmt->close();

        return $result ?: true;
    }

    /**
     * Fetch single row
     */
    public function fetchRow(string $sql, array $params = [], string $types = ''): ?array
    {
        $result = $this->execute($sql, $params, $types);

        if ($result instanceof mysqli_result) {
            return $result->fetch_assoc();
        }

        return null;
    }

    /**
     * Fetch all rows
     */
    public function fetchAll(string $sql, array $params = [], string $types = ''): array
    {
        $result = $this->execute($sql, $params, $types);

        if ($result instanceof mysqli_result) {
            return $result->fetch_all(MYSQLI_ASSOC);
        }

        return [];
    }

    /**
     * Fetch single value
     */
    public function fetchValue(string $sql, array $params = [], string $types = '')
    {
        $row = $this->fetchRow($sql, $params, $types);

        if ($row) {
            return array_values($row)[0];
        }

        return null;
    }

    /**
     * Select single record from table
     */
    public function selectOne(string $table, array $where = []): ?array
    {
        $whereParts = [];
        $params = [];
        $types = '';

        foreach ($where as $column => $value) {
            $whereParts[] = "`{$column}` = ?";
            $params[] = $value;
            $types .= $this->getType($value);
        }

        $sql = "SELECT * FROM `{$table}`";
        if (!empty($whereParts)) {
            $sql .= " WHERE " . implode(' AND ', $whereParts);
        }
        $sql .= " LIMIT 1";

        return $this->fetchRow($sql, $params, $types);
    }

    /**
     * Select multiple records from table
     */
    public function select(string $table, array $where = [], array $options = []): array
    {
        $whereParts = [];
        $params = [];
        $types = '';

        foreach ($where as $column => $value) {
            $whereParts[] = "`{$column}` = ?";
            $params[] = $value;
            $types .= $this->getType($value);
        }

        $sql = "SELECT * FROM `{$table}`";
        if (!empty($whereParts)) {
            $sql .= " WHERE " . implode(' AND ', $whereParts);
        }

        // Add ORDER BY if specified
        if (!empty($options['order_by'])) {
            $sql .= " ORDER BY " . $options['order_by'];
        }

        // Add LIMIT if specified
        if (!empty($options['limit'])) {
            $sql .= " LIMIT " . (int) $options['limit'];
        }

        return $this->fetchAll($sql, $params, $types);
    }

    /**
     * Insert record and return ID
     */
    public function insert(string $table, array $data): int
    {
        $columns = array_keys($data);
        $placeholders = array_fill(0, count($data), '?');
        $types = $this->inferTypes(array_values($data));

        $sql = "INSERT INTO `{$table}` (`" . implode('`, `', $columns) . "`) VALUES (" . implode(', ', $placeholders) . ")";

        $this->execute($sql, array_values($data), $types);

        return $this->getConnection()->insert_id;
    }

    /**
     * Update records
     */
    public function update(string $table, array $data, array $where): int
    {
        $setParts = [];
        $params = [];
        $types = '';

        foreach ($data as $column => $value) {
            $setParts[] = "`{$column}` = ?";
            $params[] = $value;
            $types .= $this->getType($value);
        }

        $whereParts = [];
        foreach ($where as $column => $value) {
            $whereParts[] = "`{$column}` = ?";
            $params[] = $value;
            $types .= $this->getType($value);
        }

        $sql = "UPDATE `{$table}` SET " . implode(', ', $setParts) . " WHERE " . implode(' AND ', $whereParts);

        $this->execute($sql, $params, $types);

        return $this->getConnection()->affected_rows;
    }

    /**
     * Delete records
     */
    public function delete(string $table, array $where): int
    {
        $whereParts = [];
        $params = [];
        $types = '';

        foreach ($where as $column => $value) {
            $whereParts[] = "`{$column}` = ?";
            $params[] = $value;
            $types .= $this->getType($value);
        }

        $sql = "DELETE FROM `{$table}` WHERE " . implode(' AND ', $whereParts);

        $this->execute($sql, $params, $types);

        return $this->getConnection()->affected_rows;
    }

    /**
     * Begin transaction
     */
    public function beginTransaction(): bool
    {
        if ($this->inTransaction) {
            $this->logger->warning('Transaction already in progress');
            return false;
        }

        $success = $this->getConnection()->begin_transaction();

        if ($success) {
            $this->inTransaction = true;
            $this->logger->debug('Transaction started');
        }

        return $success;
    }

    /**
     * Commit transaction
     */
    public function commit(): bool
    {
        if (!$this->inTransaction) {
            $this->logger->warning('No transaction to commit');
            return false;
        }

        $success = $this->getConnection()->commit();

        if ($success) {
            $this->inTransaction = false;
            $this->logger->debug('Transaction committed');
        }

        return $success;
    }

    /**
     * Rollback transaction
     */
    public function rollback(): bool
    {
        if (!$this->inTransaction) {
            $this->logger->warning('No transaction to rollback');
            return false;
        }

        $success = $this->getConnection()->rollback();

        if ($success) {
            $this->inTransaction = false;
            $this->logger->debug('Transaction rolled back');
        }

        return $success;
    }

    /**
     * Set autocommit mode
     */
    public function autocommit(bool $mode): bool
    {
        $success = $this->getConnection()->autocommit($mode);

        if ($success) {
            $this->logger->debug('Autocommit set to: ' . ($mode ? 'true' : 'false'));
        } else {
            $this->logger->error('Failed to set autocommit: ' . $this->getConnection()->error);
        }

        return $success;
    }

    /**
     * Execute in transaction
     */
    public function transaction(callable $callback)
    {
        $this->beginTransaction();

        try {
            $result = $callback($this);
            $this->commit();
            return $result;
        } catch (\Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * Get last insert ID
     */
    public function getLastInsertId(): int
    {
        return $this->getConnection()->insert_id;
    }

    /**
     * Get affected rows
     */
    public function getAffectedRows(): int
    {
        return $this->getConnection()->affected_rows;
    }

    /**
     * Escape string
     */
    public function escape(string $string): string
    {
        return $this->getConnection()->real_escape_string($string);
    }

    /**
     * Check if table exists
     */
    public function tableExists(string $table): bool
    {
        $sql = "SHOW TABLES LIKE ?";
        $result = $this->fetchValue($sql, [$table], 's');

        return $result !== null;
    }

    /**
     * Get table columns
     */
    public function getTableColumns(string $table): array
    {
        $sql = "DESCRIBE `{$table}`";
        return $this->fetchAll($sql);
    }

    /**
     * Get database statistics
     */
    public function getStats(): array
    {
        return [
            'query_count' => $this->queryCount,
            'total_query_time' => round($this->totalQueryTime * 1000, 2) . 'ms',
            'average_query_time' => $this->queryCount > 0 ? round(($this->totalQueryTime / $this->queryCount) * 1000, 2) . 'ms' : '0ms',
            'in_transaction' => $this->inTransaction,
            'connection_active' => $this->connection !== null && $this->connection->ping()
        ];
    }

    /**
     * Close connection
     */
    public function close(): void
    {
        if ($this->connection) {
            if ($this->inTransaction) {
                $this->rollback();
            }

            $this->connection->close();
            $this->connection = null;

            $this->logger->debug('Database connection closed');
        }
    }

    /**
     * Validate configuration
     */
    private function validateConfig(): void
    {
        $required = ['host', 'username', 'database'];

        foreach ($required as $key) {
            if (empty($this->connectionConfig[$key])) {
                throw new WeBotException("Database configuration missing: {$key}");
            }
        }
    }

    /**
     * Infer parameter types
     */
    private function inferTypes(array $params): string
    {
        $types = '';

        foreach ($params as $param) {
            $types .= $this->getType($param);
        }

        return $types;
    }

    /**
     * Get parameter type
     */
    private function getType($value): string
    {
        if (is_int($value)) {
            return 'i';
        } elseif (is_float($value)) {
            return 'd';
        } elseif (is_string($value)) {
            return 's';
        } else {
            return 'b'; // blob
        }
    }

    /**
     * Sanitize SQL for logging
     */
    private function sanitizeSql(string $sql): string
    {
        // Remove extra whitespace and newlines
        $sql = preg_replace('/\s+/', ' ', $sql);

        // Truncate if too long
        if (strlen($sql) > 200) {
            $sql = substr($sql, 0, 200) . '...';
        }

        return trim($sql);
    }

    /**
     * Reconnect to database
     */
    public function reconnect(): bool
    {
        try {
            $this->close();
            $this->connect();

            $this->logger->info('Database reconnection successful');
            return true;
        } catch (\Exception $e) {
            $this->logger->error('Database reconnection failed', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Enable read-only mode
     */
    public function enableReadOnlyMode(): bool
    {
        try {
            if ($this->connection) {
                // Set session to read-only
                $this->connection->query("SET SESSION TRANSACTION READ ONLY");
                $this->logger->info('Database switched to read-only mode');
                return true;
            }
            return false;
        } catch (\Exception $e) {
            $this->logger->error('Failed to enable read-only mode', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Disable read-only mode
     */
    public function disableReadOnlyMode(): bool
    {
        try {
            if ($this->connection) {
                // Set session to read-write
                $this->connection->query("SET SESSION TRANSACTION READ WRITE");
                $this->logger->info('Database switched to read-write mode');
                return true;
            }
            return false;
        } catch (\Exception $e) {
            $this->logger->error('Failed to disable read-only mode', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Check if database is in read-only mode
     */
    public function isReadOnlyMode(): bool
    {
        try {
            if ($this->connection) {
                $result = $this->connection->query("SELECT @@SESSION.transaction_read_only as readonly");
                $row = $result->fetch_assoc();
                return (bool)($row['readonly'] ?? false);
            }
            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Destructor - ensure connection is closed
     */
    public function __destruct()
    {
        $this->close();
    }
}
