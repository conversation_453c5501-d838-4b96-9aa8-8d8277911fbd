@font-face {
    font-family: 'iransans';
    src: url('IRANSans.ttf');
}
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-font-smoothing: antialiased;
    -moz-font-smoothing: antialiased;
    -o-font-smoothing: antialiased;
    font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
}

body {
    font-family: 'iransans';
    line-height: 30px;
    color: #777;
    background: #345987;
    direction: rtl;
}

.container {
    width: 80%;
    margin: 0 auto;
    position: relative;
}

h1{
    text-align: center;
    margin: 60px auto;
    color: #FFFFFF;
}
h3 , h4 {
    text-align: right !important;
}
.btninstall {
    border-radius: 10px;
}
#contact input[type="text"],
#contact input[type="tel"],
#contact textarea,
#contact button[type="submit"] {
    font: 500 16px/25px 'iransans';
}

#contact {
    background: #F9F9F9;
    padding: 25px;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.2), 0 5px 5px 0 rgba(0, 0, 0, 0.24);
    border-radius: 15px !important;
}

#contact h3 {
    display: block;
    font-size: 30px;
    font-weight: 300;
    margin-bottom: 10px;
    color: #414040;
}

#contact h4 {
    margin: 5px 0 15px;
    display: block;
    font-size: 13px;
    font-weight: 400;
}
a {
    text-decoration: none !important;
}
fieldset {
    border: medium none !important;
    margin: 0 0 10px;
    min-width: 100%;
    padding: 0;
    width: 100%;
}

#contact input[type="text"],
#contact input[type="tel"]
{
    width: 100%;
    border: 1px solid #ccc;
    border-radius: 10px;
    background: #FFF;
    margin: 0 0 2px;
    padding: 6px;
    font-size: 14px;
}

#contact input[type="text"]:hover,
#contact input[type="tel"]:hover
{
    -webkit-transition: border-color 0.3s ease-in-out;
    -moz-transition: border-color 0.3s ease-in-out;
    transition: border-color 0.3s ease-in-out;
    border: 1px solid #aaa;
}

#contact button[type="submit"] {
    cursor: pointer;
    width: 100%;
    border: none;
    background: #345987;
    color: #FFF;
    margin: 0 0 5px;
    padding: 10px;
    font-size: 15px;
}

#contact button[type="submit"]:hover {
    background: #345987;
    -webkit-transition: background 0.3s ease-in-out;
    -moz-transition: background 0.3s ease-in-out;
    transition: background-color 0.3s ease-in-out;
}

#contact button[type="submit"]:active {
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.5);
}

.copyright {
    text-align: center;
}

#contact input:focus {
    outline: 0;
    border: 1px solid #aaa;
}

::-webkit-input-placeholder {
    color: #888;
}

:-moz-placeholder {
    color: #888;
}

::-moz-placeholder {
    color: #888;
}

:-ms-input-placeholder {
    color: #888;
}


