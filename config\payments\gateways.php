<?php

declare(strict_types=1);

/**
 * Payment Gateways Configuration
 * 
 * Configuration for various payment gateways including
 * Stripe, PayPal, cryptocurrency, and local payment methods.
 * 
 * @package WeBot\Config\Payments
 * @version 2.0
 */

return [
    /*
    |--------------------------------------------------------------------------
    | Default Payment Gateway
    |--------------------------------------------------------------------------
    */
    'default' => $_ENV['DEFAULT_PAYMENT_GATEWAY'] ?? 'stripe',

    /*
    |--------------------------------------------------------------------------
    | Payment Gateways
    |--------------------------------------------------------------------------
    */
    'gateways' => [
        'stripe' => [
            'enabled' => (bool)($_ENV['STRIPE_ENABLED'] ?? false),
            'name' => 'Stripe',
            'description' => 'Credit/Debit Card payments via Stripe',
            'driver' => 'stripe',
            'config' => [
                'public_key' => $_ENV['STRIPE_PUBLIC_KEY'] ?? '',
                'secret_key' => $_ENV['STRIPE_SECRET_KEY'] ?? '',
                'webhook_secret' => $_ENV['STRIPE_WEBHOOK_SECRET'] ?? '',
                'api_version' => $_ENV['STRIPE_API_VERSION'] ?? '2023-10-16',
                'currency' => $_ENV['STRIPE_CURRENCY'] ?? 'USD',
                'capture_method' => $_ENV['STRIPE_CAPTURE_METHOD'] ?? 'automatic',
                'confirmation_method' => $_ENV['STRIPE_CONFIRMATION_METHOD'] ?? 'automatic',
            ],
            'supported_currencies' => ['USD', 'EUR', 'GBP', 'CAD', 'AUD'],
            'min_amount' => 1.00,
            'max_amount' => 10000.00,
            'fees' => [
                'percentage' => 2.9,
                'fixed' => 0.30,
                'currency' => 'USD',
            ],
            'features' => [
                'refunds' => true,
                'partial_refunds' => true,
                'webhooks' => true,
                'recurring' => true,
                'saved_cards' => true,
            ],
        ],

        'paypal' => [
            'enabled' => (bool)($_ENV['PAYPAL_ENABLED'] ?? false),
            'name' => 'PayPal',
            'description' => 'PayPal payments',
            'driver' => 'paypal',
            'config' => [
                'client_id' => $_ENV['PAYPAL_CLIENT_ID'] ?? '',
                'client_secret' => $_ENV['PAYPAL_CLIENT_SECRET'] ?? '',
                'mode' => $_ENV['PAYPAL_MODE'] ?? 'sandbox', // 'sandbox' or 'live'
                'webhook_id' => $_ENV['PAYPAL_WEBHOOK_ID'] ?? '',
                'currency' => $_ENV['PAYPAL_CURRENCY'] ?? 'USD',
                'intent' => $_ENV['PAYPAL_INTENT'] ?? 'CAPTURE',
            ],
            'supported_currencies' => ['USD', 'EUR', 'GBP', 'CAD', 'AUD'],
            'min_amount' => 1.00,
            'max_amount' => 10000.00,
            'fees' => [
                'percentage' => 3.49,
                'fixed' => 0.49,
                'currency' => 'USD',
            ],
            'features' => [
                'refunds' => true,
                'partial_refunds' => true,
                'webhooks' => true,
                'recurring' => false,
                'saved_cards' => false,
            ],
        ],

        'crypto' => [
            'enabled' => (bool)($_ENV['CRYPTO_ENABLED'] ?? false),
            'name' => 'Cryptocurrency',
            'description' => 'Bitcoin, Ethereum, and other cryptocurrencies',
            'driver' => 'crypto',
            'config' => [
                'provider' => $_ENV['CRYPTO_PROVIDER'] ?? 'coinbase', // 'coinbase', 'blockchain', 'custom'
                'api_key' => $_ENV['CRYPTO_API_KEY'] ?? '',
                'api_secret' => $_ENV['CRYPTO_API_SECRET'] ?? '',
                'webhook_secret' => $_ENV['CRYPTO_WEBHOOK_SECRET'] ?? '',
                'network' => $_ENV['CRYPTO_NETWORK'] ?? 'mainnet',
                'confirmation_blocks' => (int)($_ENV['CRYPTO_CONFIRMATIONS'] ?? 3),
            ],
            'supported_currencies' => ['BTC', 'ETH', 'USDT', 'USDC', 'LTC'],
            'min_amount' => 0.001,
            'max_amount' => 100.0,
            'fees' => [
                'percentage' => 1.0,
                'fixed' => 0.0,
                'currency' => 'USD',
            ],
            'features' => [
                'refunds' => false,
                'partial_refunds' => false,
                'webhooks' => true,
                'recurring' => false,
                'saved_cards' => false,
            ],
        ],

        'zarinpal' => [
            'enabled' => (bool)($_ENV['ZARINPAL_ENABLED'] ?? false),
            'name' => 'ZarinPal',
            'description' => 'Iranian payment gateway',
            'driver' => 'zarinpal',
            'config' => [
                'merchant_id' => $_ENV['ZARINPAL_MERCHANT_ID'] ?? '',
                'sandbox' => (bool)($_ENV['ZARINPAL_SANDBOX'] ?? true),
                'currency' => 'IRR',
                'callback_url' => $_ENV['ZARINPAL_CALLBACK_URL'] ?? '',
            ],
            'supported_currencies' => ['IRR'],
            'min_amount' => 1000, // IRR
            'max_amount' => 50000000, // IRR
            'fees' => [
                'percentage' => 1.5,
                'fixed' => 0,
                'currency' => 'IRR',
            ],
            'features' => [
                'refunds' => true,
                'partial_refunds' => false,
                'webhooks' => false,
                'recurring' => false,
                'saved_cards' => false,
            ],
        ],

        'idpay' => [
            'enabled' => (bool)($_ENV['IDPAY_ENABLED'] ?? false),
            'name' => 'IDPay',
            'description' => 'Iranian payment gateway',
            'driver' => 'idpay',
            'config' => [
                'api_key' => $_ENV['IDPAY_API_KEY'] ?? '',
                'sandbox' => (bool)($_ENV['IDPAY_SANDBOX'] ?? true),
                'currency' => 'IRR',
                'callback_url' => $_ENV['IDPAY_CALLBACK_URL'] ?? '',
            ],
            'supported_currencies' => ['IRR'],
            'min_amount' => 1000, // IRR
            'max_amount' => 50000000, // IRR
            'fees' => [
                'percentage' => 1.8,
                'fixed' => 0,
                'currency' => 'IRR',
            ],
            'features' => [
                'refunds' => true,
                'partial_refunds' => true,
                'webhooks' => true,
                'recurring' => false,
                'saved_cards' => false,
            ],
        ],

        'perfectmoney' => [
            'enabled' => (bool)($_ENV['PERFECTMONEY_ENABLED'] ?? false),
            'name' => 'Perfect Money',
            'description' => 'Perfect Money e-currency',
            'driver' => 'perfectmoney',
            'config' => [
                'account_id' => $_ENV['PERFECTMONEY_ACCOUNT_ID'] ?? '',
                'passphrase' => $_ENV['PERFECTMONEY_PASSPHRASE'] ?? '',
                'alternate_passphrase' => $_ENV['PERFECTMONEY_ALT_PASSPHRASE'] ?? '',
                'currency' => $_ENV['PERFECTMONEY_CURRENCY'] ?? 'USD',
            ],
            'supported_currencies' => ['USD', 'EUR'],
            'min_amount' => 1.00,
            'max_amount' => 5000.00,
            'fees' => [
                'percentage' => 0.5,
                'fixed' => 0,
                'currency' => 'USD',
            ],
            'features' => [
                'refunds' => false,
                'partial_refunds' => false,
                'webhooks' => true,
                'recurring' => false,
                'saved_cards' => false,
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Global Payment Settings
    |--------------------------------------------------------------------------
    */
    'settings' => [
        'default_currency' => $_ENV['DEFAULT_CURRENCY'] ?? 'USD',
        'auto_capture' => (bool)($_ENV['AUTO_CAPTURE_PAYMENTS'] ?? true),
        'payment_timeout' => (int)($_ENV['PAYMENT_TIMEOUT'] ?? 1800), // 30 minutes
        'retry_failed_payments' => (bool)($_ENV['RETRY_FAILED_PAYMENTS'] ?? true),
        'max_retry_attempts' => (int)($_ENV['MAX_PAYMENT_RETRIES'] ?? 3),
        'save_payment_methods' => (bool)($_ENV['SAVE_PAYMENT_METHODS'] ?? true),
        'require_cvv' => (bool)($_ENV['REQUIRE_CVV'] ?? true),
        'enable_3ds' => (bool)($_ENV['ENABLE_3DS'] ?? true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    */
    'security' => [
        'encrypt_payment_data' => (bool)($_ENV['ENCRYPT_PAYMENT_DATA'] ?? true),
        'pci_compliance' => (bool)($_ENV['PCI_COMPLIANCE'] ?? true),
        'fraud_detection' => (bool)($_ENV['FRAUD_DETECTION'] ?? true),
        'ip_whitelist' => explode(',', $_ENV['PAYMENT_IP_WHITELIST'] ?? ''),
        'webhook_verification' => (bool)($_ENV['WEBHOOK_VERIFICATION'] ?? true),
        'rate_limiting' => [
            'enabled' => true,
            'max_attempts' => 5,
            'window_minutes' => 15,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Webhook Configuration
    |--------------------------------------------------------------------------
    */
    'webhooks' => [
        'enabled' => (bool)($_ENV['PAYMENT_WEBHOOKS_ENABLED'] ?? true),
        'verify_signatures' => (bool)($_ENV['VERIFY_WEBHOOK_SIGNATURES'] ?? true),
        'retry_failed_webhooks' => (bool)($_ENV['RETRY_FAILED_WEBHOOKS'] ?? true),
        'max_retry_attempts' => (int)($_ENV['MAX_WEBHOOK_RETRIES'] ?? 3),
        'retry_delay' => (int)($_ENV['WEBHOOK_RETRY_DELAY'] ?? 300), // 5 minutes
        'timeout' => (int)($_ENV['WEBHOOK_TIMEOUT'] ?? 30),
        'events' => [
            'payment.created',
            'payment.succeeded',
            'payment.failed',
            'payment.refunded',
            'payment.disputed',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging & Monitoring
    |--------------------------------------------------------------------------
    */
    'logging' => [
        'enabled' => (bool)($_ENV['PAYMENT_LOGGING'] ?? true),
        'log_level' => $_ENV['PAYMENT_LOG_LEVEL'] ?? 'info',
        'log_file' => $_ENV['PAYMENT_LOG_FILE'] ?? 'storage/logs/payments.log',
        'log_sensitive_data' => (bool)($_ENV['LOG_SENSITIVE_PAYMENT_DATA'] ?? false),
        'retention_days' => (int)($_ENV['PAYMENT_LOG_RETENTION'] ?? 90),
    ],

    /*
    |--------------------------------------------------------------------------
    | Notifications
    |--------------------------------------------------------------------------
    */
    'notifications' => [
        'enabled' => (bool)($_ENV['PAYMENT_NOTIFICATIONS'] ?? true),
        'channels' => [
            'email' => (bool)($_ENV['EMAIL_PAYMENT_NOTIFICATIONS'] ?? true),
            'telegram' => (bool)($_ENV['TELEGRAM_PAYMENT_NOTIFICATIONS'] ?? true),
            'webhook' => (bool)($_ENV['WEBHOOK_PAYMENT_NOTIFICATIONS'] ?? false),
        ],
        'events' => [
            'payment_successful' => true,
            'payment_failed' => true,
            'refund_processed' => true,
            'chargeback_received' => true,
        ],
        'admin_notifications' => [
            'large_payments' => (float)($_ENV['LARGE_PAYMENT_THRESHOLD'] ?? 1000.00),
            'failed_payments' => true,
            'suspicious_activity' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Testing & Development
    |--------------------------------------------------------------------------
    */
    'testing' => [
        'test_mode' => (bool)($_ENV['PAYMENT_TEST_MODE'] ?? false),
        'mock_payments' => (bool)($_ENV['MOCK_PAYMENTS'] ?? false),
        'test_cards' => [
            'visa' => '****************',
            'visa_debit' => '****************',
            'mastercard' => '****************',
            'amex' => '***************',
            'declined' => '****************',
        ],
        'test_amounts' => [
            'success' => [1.00, 10.00, 100.00],
            'decline' => [2.00, 20.00],
            'error' => [3.00, 30.00],
        ],
    ],
];
