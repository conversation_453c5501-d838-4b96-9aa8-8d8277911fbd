<?php

declare(strict_types=1);

namespace WeBot\Middleware;

use WeBot\Core\SessionManager;
use WeBot\Repositories\UserRepository;
use WeBot\Exceptions\AuthenticationException;

// Mock Firebase JWT classes if not available
if (!class_exists('Firebase\JWT\JWT')) {
    class_alias('WeBot\Utils\MockJWT', 'Firebase\JWT\JWT');
}
if (!class_exists('Firebase\JWT\Key')) {
    class_alias('WeBot\Utils\MockKey', 'Firebase\JWT\Key');
}

use Firebase\JWT\JWT;
use Firebase\JWT\Key;

/**
 * Authentication Middleware
 *
 * Handles JWT authentication, session management,
 * and user verification for protected routes.
 *
 * @package WeBot\Middleware
 * @version 2.0
 */
class AuthenticationMiddleware
{
    private SessionManager $sessionManager;
    private UserRepository $userRepository;
    private array $config;
    private string $jwtSecret;
    private string $jwtAlgorithm = 'HS256';

    public function __construct(
        SessionManager $sessionManager,
        UserRepository $userRepository,
        array $config
    ) {
        $this->sessionManager = $sessionManager;
        $this->userRepository = $userRepository;
        $this->config = $config;
        $this->jwtSecret = $config['jwt_secret'] ?? $_ENV['JWT_SECRET'] ?? '';

        if (empty($this->jwtSecret)) {
            throw new \InvalidArgumentException('JWT secret is required');
        }
    }

    /**
     * Handle authentication middleware
     */
    public function handle(array $request, callable $next): array
    {
        try {
            $user = $this->authenticateRequest($request);

            // Add user to request context
            $request['user'] = $user;
            $request['authenticated'] = true;

            // Update last activity
            $this->updateUserActivity($user['id']);

            return $next($request);
        } catch (AuthenticationException $e) {
            return [
                'success' => false,
                'error' => 'Authentication failed',
                'message' => $e->getMessage(),
                'status_code' => 401
            ];
        }
    }

    /**
     * Authenticate request using multiple methods
     */
    private function authenticateRequest(array $request): array
    {
        // Try JWT authentication first
        if (isset($request['headers']['Authorization'])) {
            return $this->authenticateWithJWT($request['headers']['Authorization']);
        }

        // Try session authentication
        if (isset($request['session_id'])) {
            return $this->authenticateWithSession($request['session_id']);
        }

        // Try Telegram authentication
        if (isset($request['telegram_id'])) {
            return $this->authenticateWithTelegram($request['telegram_id']);
        }

        throw new AuthenticationException('No authentication method provided');
    }

    /**
     * Authenticate using JWT token
     */
    private function authenticateWithJWT(string $authHeader): array
    {
        if (!str_starts_with($authHeader, 'Bearer ')) {
            throw new AuthenticationException('Invalid authorization header format');
        }

        $token = substr($authHeader, 7);

        try {
            $decoded = JWT::decode($token, new Key($this->jwtSecret, $this->jwtAlgorithm));
            $payload = (array) $decoded;

            // Validate token claims
            $this->validateJWTClaims($payload);

            // Get user from database
            $user = $this->userRepository->findById($payload['user_id']);

            if (!$user) {
                throw new AuthenticationException('User not found');
            }

            if ($user->status !== 'active') {
                throw new AuthenticationException('User account is not active');
            }

            return $user->toArray();
        } catch (\Exception $e) {
            throw new AuthenticationException('Invalid JWT token: ' . $e->getMessage());
        }
    }

    /**
     * Authenticate using session
     */
    private function authenticateWithSession(string $sessionId): array
    {
        $session = $this->sessionManager->getSession($sessionId);

        if (!$session) {
            throw new AuthenticationException('Invalid session');
        }

        if ($this->sessionManager->isExpired($session)) {
            $this->sessionManager->destroySession($sessionId);
            throw new AuthenticationException('Session expired');
        }

        $userId = $session['user_id'] ?? null;

        if (!$userId) {
            throw new AuthenticationException('Session does not contain user information');
        }

        $user = $this->userRepository->findById($userId);

        if (!$user) {
            throw new AuthenticationException('User not found');
        }

        if ($user->status !== 'active') {
            throw new AuthenticationException('User account is not active');
        }

        // Refresh session
        $this->sessionManager->refreshSession($sessionId);

        return $user->toArray();
    }

    /**
     * Authenticate using Telegram ID (for bot interactions)
     */
    private function authenticateWithTelegram(int $telegramId): array
    {
        $user = $this->userRepository->findByTelegramId($telegramId);

        if (!$user) {
            throw new AuthenticationException('Telegram user not found');
        }

        if ($user->status !== 'active') {
            throw new AuthenticationException('User account is not active');
        }

        return $user->toArray();
    }

    /**
     * Generate JWT token for user
     */
    public function generateJWT(array $user, int $expiresIn = 3600): string
    {
        $now = time();
        $payload = [
            'iss' => $this->config['app_url'] ?? 'webot',
            'aud' => $this->config['app_url'] ?? 'webot',
            'iat' => $now,
            'exp' => $now + $expiresIn,
            'user_id' => $user['id'],
            'telegram_id' => $user['telegram_id'],
            'role' => $user['role'],
            'jti' => uniqid('jwt_', true)
        ];

        return JWT::encode($payload, $this->jwtSecret, $this->jwtAlgorithm);
    }

    /**
     * Create authenticated session
     */
    public function createSession(array $user, array $sessionData = []): string
    {
        $sessionData = array_merge($sessionData, [
            'user_id' => $user['id'],
            'telegram_id' => $user['telegram_id'],
            'role' => $user['role'],
            'authenticated_at' => time(),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);

        return $this->sessionManager->createSession($sessionData);
    }

    /**
     * Validate JWT claims
     */
    private function validateJWTClaims(array $payload): void
    {
        $now = time();

        // Check expiration
        if (isset($payload['exp']) && $payload['exp'] < $now) {
            throw new AuthenticationException('Token expired');
        }

        // Check not before
        if (isset($payload['nbf']) && $payload['nbf'] > $now) {
            throw new AuthenticationException('Token not yet valid');
        }

        // Check required claims
        $requiredClaims = ['user_id', 'iat'];
        foreach ($requiredClaims as $claim) {
            if (!isset($payload[$claim])) {
                throw new AuthenticationException("Missing required claim: {$claim}");
            }
        }
    }

    /**
     * Update user last activity
     */
    private function updateUserActivity(int $userId): void
    {
        try {
            $this->userRepository->update($userId, [
                'last_login_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            // Log error but don't fail authentication
            error_log("Failed to update user activity: " . $e->getMessage());
        }
    }

    /**
     * Logout user (destroy session/invalidate token)
     */
    public function logout(array $request): array
    {
        try {
            // Destroy session if exists
            if (isset($request['session_id'])) {
                $this->sessionManager->destroySession($request['session_id']);
            }

            // For JWT, we would typically add to blacklist
            // but for simplicity, we'll just return success
            // In production, implement JWT blacklisting

            return [
                'success' => true,
                'message' => 'Logged out successfully'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Logout failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Refresh JWT token
     */
    public function refreshJWT(string $token): array
    {
        try {
            $decoded = JWT::decode($token, new Key($this->jwtSecret, $this->jwtAlgorithm));
            $payload = (array) $decoded;

            // Check if token is close to expiry (within 5 minutes)
            $now = time();
            $exp = $payload['exp'] ?? 0;

            if ($exp - $now > 300) {
                return [
                    'success' => false,
                    'error' => 'Token does not need refresh yet'
                ];
            }

            // Get user and generate new token
            $user = $this->userRepository->findById($payload['user_id']);

            if (!$user) {
                throw new AuthenticationException('User not found');
            }

            $newToken = $this->generateJWT($user->toArray());

            return [
                'success' => true,
                'token' => $newToken,
                'expires_in' => 3600
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Token refresh failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Check if user has required role
     */
    public function hasRole(array $user, string $requiredRole): bool
    {
        $roleHierarchy = [
            'user' => 1,
            'moderator' => 2,
            'admin' => 3
        ];

        $userRoleLevel = $roleHierarchy[$user['role']] ?? 0;
        $requiredRoleLevel = $roleHierarchy[$requiredRole] ?? 999;

        return $userRoleLevel >= $requiredRoleLevel;
    }

    /**
     * Role-based middleware
     */
    public function requireRole(string $requiredRole): callable
    {
        return function (array $request, callable $next) use ($requiredRole) {
            if (!isset($request['user'])) {
                return [
                    'success' => false,
                    'error' => 'Authentication required',
                    'status_code' => 401
                ];
            }

            if (!$this->hasRole($request['user'], $requiredRole)) {
                return [
                    'success' => false,
                    'error' => 'Insufficient permissions',
                    'status_code' => 403
                ];
            }

            return $next($request);
        };
    }

    /**
     * Optional authentication middleware
     */
    public function optional(array $request, callable $next): array
    {
        try {
            $user = $this->authenticateRequest($request);
            $request['user'] = $user;
            $request['authenticated'] = true;
        } catch (AuthenticationException $e) {
            $request['authenticated'] = false;
        }

        return $next($request);
    }
}
