<?php

declare(strict_types=1);

namespace WeBot\Microservices\Core;

use WeBot\Core\CacheManager;
use WeBot\Utils\Logger;

/**
 * Circuit Breaker
 *
 * Implements circuit breaker pattern to prevent cascading failures
 * in microservices architecture by monitoring service health.
 *
 * @package WeBot\Microservices\Core
 * @version 2.0
 */
class CircuitBreaker
{
    private CacheManager $cache;
    private Logger $logger;
    private array $config;

    // Circuit states
    const STATE_CLOSED = 'closed';
    const STATE_OPEN = 'open';
    const STATE_HALF_OPEN = 'half_open';

    public function __construct(CacheManager $cache, array $config = [])
    {
        $this->cache = $cache;
        $this->logger = Logger::getInstance();
        $this->config = array_merge($this->getDefaultConfig(), $config);
    }

    /**
     * Check if circuit allows execution
     */
    public function canExecute(string $circuitKey): bool
    {
        $circuit = $this->getCircuitState($circuitKey);

        switch ($circuit['state']) {
            case self::STATE_CLOSED:
                return true;

            case self::STATE_OPEN:
                // Check if timeout period has passed
                if (time() - $circuit['last_failure_time'] >= $this->config['timeout']) {
                    $this->transitionToHalfOpen($circuitKey);
                    return true;
                }
                return false;

            case self::STATE_HALF_OPEN:
                // Allow limited requests to test service
                return $circuit['half_open_requests'] < $this->config['half_open_max_requests'];

            default:
                return true;
        }
    }

    /**
     * Record successful execution
     */
    public function recordSuccess(string $circuitKey): void
    {
        $circuit = $this->getCircuitState($circuitKey);

        if ($circuit['state'] === self::STATE_HALF_OPEN) {
            $circuit['half_open_successes']++;
            $circuit['half_open_requests']++;

            // If enough successes, close the circuit
            if ($circuit['half_open_successes'] >= $this->config['half_open_success_threshold']) {
                $this->transitionToClosed($circuitKey);
                return;
            }
        } else {
            // Reset failure count on success
            $circuit['failure_count'] = 0;
            $circuit['consecutive_failures'] = 0;
        }

        $circuit['last_success_time'] = time();
        $circuit['total_requests']++;
        $circuit['successful_requests']++;

        $this->saveCircuitState($circuitKey, $circuit);
    }

    /**
     * Record failed execution
     */
    public function recordFailure(string $circuitKey): void
    {
        $circuit = $this->getCircuitState($circuitKey);

        $circuit['failure_count']++;
        $circuit['consecutive_failures']++;
        $circuit['last_failure_time'] = time();
        $circuit['total_requests']++;
        $circuit['failed_requests']++;

        if ($circuit['state'] === self::STATE_HALF_OPEN) {
            $circuit['half_open_requests']++;
            // Any failure in half-open state opens the circuit
            $this->transitionToOpen($circuitKey);
            return;
        }

        // Check if we should open the circuit
        if ($this->shouldOpenCircuit($circuit)) {
            $this->transitionToOpen($circuitKey);
        } else {
            $this->saveCircuitState($circuitKey, $circuit);
        }
    }

    /**
     * Get circuit state
     */
    public function getCircuitState(string $circuitKey): array
    {
        $cacheKey = "circuit_breaker:{$circuitKey}";
        $circuit = $this->cache->get($cacheKey);

        if ($circuit === null) {
            $circuit = $this->createNewCircuit();
            $this->saveCircuitState($circuitKey, $circuit);
        }

        return $circuit;
    }

    /**
     * Get circuit statistics
     */
    public function getCircuitStatistics(string $circuitKey): array
    {
        $circuit = $this->getCircuitState($circuitKey);

        $successRate = $circuit['total_requests'] > 0
            ? ($circuit['successful_requests'] / $circuit['total_requests']) * 100
            : 0;

        $failureRate = $circuit['total_requests'] > 0
            ? ($circuit['failed_requests'] / $circuit['total_requests']) * 100
            : 0;

        return [
            'circuit_key' => $circuitKey,
            'state' => $circuit['state'],
            'total_requests' => $circuit['total_requests'],
            'successful_requests' => $circuit['successful_requests'],
            'failed_requests' => $circuit['failed_requests'],
            'success_rate' => round($successRate, 2),
            'failure_rate' => round($failureRate, 2),
            'consecutive_failures' => $circuit['consecutive_failures'],
            'last_failure_time' => $circuit['last_failure_time'],
            'last_success_time' => $circuit['last_success_time'],
            'state_changed_at' => $circuit['state_changed_at']
        ];
    }

    /**
     * Reset circuit to closed state
     */
    public function resetCircuit(string $circuitKey): void
    {
        $this->transitionToClosed($circuitKey);

        $this->logger->info("Circuit breaker reset", [
            'circuit_key' => $circuitKey
        ]);
    }

    /**
     * Get all circuit statistics
     */
    public function getAllCircuitStatistics(): array
    {
        // This would require storing a list of all circuit keys
        // For now, return empty array - implement based on your needs
        return [];
    }

    /**
     * Check if circuit should be opened
     */
    private function shouldOpenCircuit(array $circuit): bool
    {
        // Check failure threshold
        if ($circuit['consecutive_failures'] >= $this->config['failure_threshold']) {
            return true;
        }

        // Check failure rate over time window
        if ($circuit['total_requests'] >= $this->config['minimum_requests']) {
            $failureRate = $circuit['failed_requests'] / $circuit['total_requests'];
            if ($failureRate >= $this->config['failure_rate_threshold']) {
                return true;
            }
        }

        return false;
    }

    /**
     * Transition circuit to open state
     */
    private function transitionToOpen(string $circuitKey): void
    {
        $circuit = $this->getCircuitState($circuitKey);
        $circuit['state'] = self::STATE_OPEN;
        $circuit['state_changed_at'] = time();

        $this->saveCircuitState($circuitKey, $circuit);

        $this->logger->warning("Circuit breaker opened", [
            'circuit_key' => $circuitKey,
            'failure_count' => $circuit['failure_count'],
            'consecutive_failures' => $circuit['consecutive_failures']
        ]);
    }

    /**
     * Transition circuit to half-open state
     */
    private function transitionToHalfOpen(string $circuitKey): void
    {
        $circuit = $this->getCircuitState($circuitKey);
        $circuit['state'] = self::STATE_HALF_OPEN;
        $circuit['state_changed_at'] = time();
        $circuit['half_open_requests'] = 0;
        $circuit['half_open_successes'] = 0;

        $this->saveCircuitState($circuitKey, $circuit);

        $this->logger->info("Circuit breaker half-opened", [
            'circuit_key' => $circuitKey
        ]);
    }

    /**
     * Transition circuit to closed state
     */
    private function transitionToClosed(string $circuitKey): void
    {
        $circuit = $this->createNewCircuit();
        $circuit['state'] = self::STATE_CLOSED;
        $circuit['state_changed_at'] = time();

        $this->saveCircuitState($circuitKey, $circuit);

        $this->logger->info("Circuit breaker closed", [
            'circuit_key' => $circuitKey
        ]);
    }

    /**
     * Create new circuit state
     */
    private function createNewCircuit(): array
    {
        return [
            'state' => self::STATE_CLOSED,
            'failure_count' => 0,
            'consecutive_failures' => 0,
            'total_requests' => 0,
            'successful_requests' => 0,
            'failed_requests' => 0,
            'last_failure_time' => 0,
            'last_success_time' => 0,
            'state_changed_at' => time(),
            'half_open_requests' => 0,
            'half_open_successes' => 0
        ];
    }

    /**
     * Save circuit state to cache
     */
    private function saveCircuitState(string $circuitKey, array $circuit): void
    {
        $cacheKey = "circuit_breaker:{$circuitKey}";
        $this->cache->set($cacheKey, $circuit, $this->config['state_ttl']);
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'failure_threshold' => 5,           // Number of consecutive failures to open circuit
            'failure_rate_threshold' => 0.5,   // Failure rate (0.5 = 50%) to open circuit
            'minimum_requests' => 10,           // Minimum requests before considering failure rate
            'timeout' => 60,                    // Seconds to wait before trying half-open
            'half_open_max_requests' => 3,      // Max requests to allow in half-open state
            'half_open_success_threshold' => 2, // Successes needed to close circuit
            'state_ttl' => 3600                 // TTL for circuit state in cache
        ];
    }
}
