# Legacy Code Directory
## پوشه کدهای قدیمی

این پوشه شامل فایل‌ها و کدهای قدیمی WeBot است که در نسخه 2.0 جایگزین شده‌اند اما برای سازگاری با نسخه‌های قبلی نگهداری می‌شوند.

---

## 📁 محتویات

### فایل‌های PHP قدیمی
- **apipanel.php** - API قدیمی پنل‌ها
- **botapi.php** - توابع قدیمی Telegram Bot API
- **marzneshin.php** - اتصال قدیمی به پنل Marzneshin
- **panels.php** - مدیریت قدیمی پنل‌ها
- **search.php** - جستجوی قدیمی

### فایل‌های تست و اعتبارسنجی قدیمی
- **test_autoload.php** - تست قدیمی autoloader
- **test_syntax.php** - بر<PERSON><PERSON><PERSON> قدیمی syntax
- **run-tests.php** - اجرای قدیمی تست‌ها
- **docs-validation-final.php** - اعتبارسنجی قدیمی مستندات
- **validate-api-docs.php** - اعتبارسنجی قدیمی API docs
- **updateShareConfig.php** - به‌روزرسانی قدیمی تنظیمات

### پوشه‌های قدیمی
- **phpqrcode/** - کتابخانه قدیمی تولید QR Code
- **pay/** - سیستم قدیمی پرداخت

---

## ⚠️ هشدار

**این فایل‌ها منسوخ شده‌اند و نباید در کد جدید استفاده شوند.**

- برای API پنل‌ها از `src/Adapters/` استفاده کنید
- برای Telegram Bot API از `src/Services/TelegramService.php` استفاده کنید
- برای QR Code از `endroid/qr-code` package استفاده کنید
- برای تست‌ها از `tests/` directory استفاده کنید

---

## 🔄 Migration Guide

اگر نیاز به استفاده از این کدها دارید:

1. **LegacyBridge.php** را برای اتصال به کدهای قدیمی استفاده کنید
2. **LegacyMigrator.php** را برای مهاجرت داده‌ها استفاده کنید
3. کدهای جدید را در `src/` directory پیاده‌سازی کنید

---

## 📅 تاریخچه

- **نسخه 1.x**: کدهای اصلی
- **نسخه 2.0**: منتقل شده به Legacy برای سازگاری
- **آینده**: حذف کامل در نسخه‌های بعدی

---

> **نکته**: این پوشه در نسخه‌های آتی WeBot حذف خواهد شد. لطفاً کدهای خود را به نسخه جدید مهاجرت دهید.
