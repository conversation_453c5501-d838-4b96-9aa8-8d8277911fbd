-- Migration: Create message system tables
-- Version: 007
-- Description: Create message queue and templates tables for MessageService
-- Author: WeBot Team
-- Date: 2024-01-01

BEGIN;

-- Create message queue table
CREATE TABLE IF NOT EXISTS `message_queue` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT(20) NOT NULL COMMENT 'Target user Telegram ID',
    `message` TEXT NOT NULL COMMENT 'Message content',
    `options` JSON NULL COMMENT 'Message options (keyboard, parse_mode, etc.)',
    `status` ENUM('pending', 'sent', 'failed', 'cancelled') DEFAULT 'pending' COMMENT 'Message status',
    `priority` TINYINT(2) DEFAULT 5 COMMENT 'Message priority (1-10, lower is higher priority)',
    `attempts` TINYINT(2) DEFAULT 0 COMMENT 'Send attempts count',
    `max_attempts` TINYINT(2) DEFAULT 3 COMMENT 'Maximum send attempts',
    `scheduled_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When to send the message',
    `sent_at` TIMESTAMP NULL COMMENT 'When message was sent',
    `failed_at` TIMESTAMP NULL COMMENT 'When message failed',
    `error` TEXT NULL COMMENT 'Error message if failed',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_queue_user` (`user_id`),
    KEY `idx_queue_status` (`status`),
    KEY `idx_queue_priority` (`priority`),
    KEY `idx_queue_scheduled` (`scheduled_at`),
    KEY `idx_queue_composite` (`status`, `scheduled_at`, `priority`),
    CONSTRAINT `fk_queue_user` FOREIGN KEY (`user_id`) REFERENCES `users`(`userid`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Message queue for bot';

-- Create message templates table
CREATE TABLE IF NOT EXISTS `message_templates` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `language` VARCHAR(5) NOT NULL DEFAULT 'fa' COMMENT 'Language code (fa, en, ar)',
    `message_key` VARCHAR(255) NOT NULL COMMENT 'Template key',
    `message_value` TEXT NOT NULL COMMENT 'Template content',
    `description` VARCHAR(500) NULL COMMENT 'Template description',
    `category` VARCHAR(100) NULL COMMENT 'Template category',
    `variables` JSON NULL COMMENT 'Available variables',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT 'Is template active',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_templates_lang_key` (`language`, `message_key`),
    KEY `idx_templates_language` (`language`),
    KEY `idx_templates_key` (`message_key`),
    KEY `idx_templates_category` (`category`),
    KEY `idx_templates_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Message templates for localization';

-- Create user sessions table for AuthService
CREATE TABLE IF NOT EXISTS `user_sessions` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT(20) NOT NULL COMMENT 'User Telegram ID',
    `session_id` VARCHAR(64) NOT NULL COMMENT 'Session identifier',
    `session_key` VARCHAR(255) NULL COMMENT 'Session data key',
    `session_value` TEXT NULL COMMENT 'Session data value',
    `expires_at` TIMESTAMP NOT NULL COMMENT 'Session expiration time',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_sessions_id` (`session_id`),
    UNIQUE KEY `uk_sessions_user_key` (`user_id`, `session_key`),
    KEY `idx_sessions_user` (`user_id`),
    KEY `idx_sessions_expires` (`expires_at`),
    CONSTRAINT `fk_sessions_user` FOREIGN KEY (`user_id`) REFERENCES `users`(`userid`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User sessions and temporary data';

-- Create notification logs table
CREATE TABLE IF NOT EXISTS `notification_logs` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT(20) NOT NULL COMMENT 'Target user ID',
    `type` VARCHAR(100) NOT NULL COMMENT 'Notification type',
    `title` VARCHAR(255) NOT NULL COMMENT 'Notification title',
    `message` TEXT NOT NULL COMMENT 'Notification message',
    `data` JSON NULL COMMENT 'Additional notification data',
    `status` ENUM('pending', 'sent', 'failed', 'read') DEFAULT 'pending' COMMENT 'Notification status',
    `sent_at` TIMESTAMP NULL COMMENT 'When notification was sent',
    `read_at` TIMESTAMP NULL COMMENT 'When notification was read',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_notifications_user` (`user_id`),
    KEY `idx_notifications_type` (`type`),
    KEY `idx_notifications_status` (`status`),
    KEY `idx_notifications_created` (`created_at`),
    CONSTRAINT `fk_notifications_user` FOREIGN KEY (`user_id`) REFERENCES `users`(`userid`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Notification logs';

-- Insert default message templates
INSERT IGNORE INTO `message_templates` (`language`, `message_key`, `message_value`, `description`, `category`) VALUES
-- Persian templates
('fa', 'welcome', '🎉 سلام {first_name}!\nبه WeBot خوش آمدید.\n\n🤖 من ربات مدیریت VPN هستم و آماده کمک به شما می‌باشم.\n\n📱 برای شروع از دکمه‌های زیر استفاده کنید:', 'Welcome message', 'auth'),
('fa', 'help', '📚 **راهنمای استفاده از ربات**\n\n🏠 /start - شروع و منو اصلی\n❓ /help - نمایش این راهنما\n👤 /profile - مشاهده پروفایل\n🛒 /buy - خرید سرویس جدید\n📊 /services - مشاهده سرویس‌ها\n💰 /wallet - موجودی کیف پول\n🎧 /support - پشتیبانی\n\n💡 **نکته:** از منو اصلی می‌توانید به تمام امکانات دسترسی داشته باشید.', 'Help message', 'general'),
('fa', 'error.general', '❌ خطایی رخ داده است. لطفاً مجدداً تلاش کنید.\n\nاگر مشکل ادامه داشت، با پشتیبانی تماس بگیرید.', 'General error message', 'error'),
('fa', 'error.not_found', '❌ موردی یافت نشد.\n\nلطفاً از صحت اطلاعات وارد شده اطمینان حاصل کنید.', 'Not found error', 'error'),
('fa', 'error.permission_denied', '🚫 شما دسترسی لازم برای این عملیات را ندارید.', 'Permission denied error', 'error'),
('fa', 'success.general', '✅ عملیات با موفقیت انجام شد.', 'General success message', 'success'),
('fa', 'success.payment_completed', '✅ پرداخت شما با موفقیت انجام شد.\n\n💳 مبلغ: {amount} {currency}\n🆔 شناسه تراکنش: {transaction_id}\n📅 تاریخ: {date}', 'Payment success message', 'payment'),
('fa', 'service_info', '📊 **اطلاعات سرویس {service_name}**\n\n📝 نوع: {service_type}\n📊 وضعیت: {status}\n📈 ترافیک مصرفی: {used_traffic}\n📊 کل ترافیک: {data_limit}\n📉 ترافیک باقی‌مانده: {remaining_traffic}\n📅 تاریخ انقضا: {expire_date}\n⏰ روزهای باقی‌مانده: {days_remaining}', 'Service information template', 'service'),
('fa', 'payment_info', '💳 **اطلاعات پرداخت**\n\n🆔 شناسه: {payment_id}\n💰 مبلغ: {amount} {currency}\n📊 وضعیت: {status}\n🏦 درگاه: {gateway}\n📅 تاریخ: {created_at}\n📝 توضیحات: {description}', 'Payment information template', 'payment'),
('fa', 'user_stats', '📊 **آمار کاربری شما**\n\n📱 تعداد سرویس‌ها: {total_services}\n✅ سرویس‌های فعال: {active_services}\n❌ سرویس‌های منقضی: {expired_services}\n📈 کل ترافیک مصرفی: {total_traffic}\n💰 تعداد پرداخت‌ها: {total_payments}\n💵 کل مبلغ پرداختی: {total_spent}\n📅 عضویت از: {member_since}', 'User statistics template', 'user'),

-- Notification templates
('fa', 'service_expiring_title', '⚠️ هشدار انقضای سرویس', 'Service expiring notification title', 'notification'),
('fa', 'service_expiring_message', '⚠️ سرویس {service_name} شما در {days_remaining} روز آینده منقضی خواهد شد.\n\nبرای تمدید سرویس از منو اصلی اقدام کنید.', 'Service expiring notification', 'notification'),
('fa', 'service_expired_title', '❌ سرویس منقضی شد', 'Service expired notification title', 'notification'),
('fa', 'service_expired_message', '❌ سرویس {service_name} شما منقضی شده است.\n\nبرای تمدید یا خرید سرویس جدید اقدام کنید.', 'Service expired notification', 'notification'),
('fa', 'payment_successful_title', '✅ پرداخت موفق', 'Payment successful notification title', 'notification'),
('fa', 'payment_successful_message', '✅ پرداخت شما به مبلغ {amount} {currency} با موفقیت انجام شد.\n\nسرویس شما فعال گردید.', 'Payment successful notification', 'notification'),
('fa', 'payment_failed_title', '❌ پرداخت ناموفق', 'Payment failed notification title', 'notification'),
('fa', 'payment_failed_message', '❌ پرداخت شما به مبلغ {amount} {currency} ناموفق بود.\n\nلطفاً مجدداً تلاش کنید.', 'Payment failed notification', 'notification'),
('fa', 'service_created_title', '🎉 سرویس جدید', 'Service created notification title', 'notification'),
('fa', 'service_created_message', '🎉 سرویس جدید {service_name} برای شما ایجاد شد.\n\nاز منو اصلی می‌توانید کانفیگ را دریافت کنید.', 'Service created notification', 'notification'),

-- English templates
('en', 'welcome', '🎉 Hello {first_name}!\nWelcome to WeBot.\n\n🤖 I am a VPN management bot and ready to help you.\n\n📱 Use the buttons below to get started:', 'Welcome message', 'auth'),
('en', 'help', '📚 **Bot Usage Guide**\n\n🏠 /start - Start and main menu\n❓ /help - Show this help\n👤 /profile - View profile\n🛒 /buy - Buy new service\n📊 /services - View services\n💰 /wallet - Wallet balance\n🎧 /support - Support\n\n💡 **Tip:** You can access all features from the main menu.', 'Help message', 'general'),
('en', 'error.general', '❌ An error occurred. Please try again.\n\nIf the problem persists, contact support.', 'General error message', 'error'),
('en', 'success.general', '✅ Operation completed successfully.', 'General success message', 'success'),

-- Arabic templates
('ar', 'welcome', '🎉 مرحباً {first_name}!\nأهلاً بك في WeBot.\n\n🤖 أنا بوت إدارة VPN وجاهز لمساعدتك.\n\n📱 استخدم الأزرار أدناه للبدء:', 'Welcome message', 'auth'),
('ar', 'help', '📚 **دليل استخدام البوت**\n\n🏠 /start - البدء والقائمة الرئيسية\n❓ /help - عرض هذا الدليل\n👤 /profile - عرض الملف الشخصي\n🛒 /buy - شراء خدمة جديدة\n📊 /services - عرض الخدمات\n💰 /wallet - رصيد المحفظة\n🎧 /support - الدعم\n\n💡 **نصيحة:** يمكنك الوصول إلى جميع الميزات من القائمة الرئيسية.', 'Help message', 'general'),
('ar', 'error.general', '❌ حدث خطأ. يرجى المحاولة مرة أخرى.\n\nإذا استمرت المشكلة، اتصل بالدعم.', 'General error message', 'error'),
('ar', 'success.general', '✅ تمت العملية بنجاح.', 'General success message', 'success');

-- Create procedure for message queue processing
DELIMITER //
CREATE OR REPLACE PROCEDURE ProcessMessageQueue(
    IN batch_size INT DEFAULT 100
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE msg_id INT;
    DECLARE msg_user_id BIGINT(20);
    DECLARE msg_content TEXT;
    DECLARE msg_options JSON;
    DECLARE msg_attempts TINYINT(2);
    DECLARE msg_max_attempts TINYINT(2);
    
    DECLARE cur CURSOR FOR 
        SELECT id, user_id, message, options, attempts, max_attempts
        FROM message_queue 
        WHERE status = 'pending' 
        AND scheduled_at <= NOW()
        ORDER BY priority ASC, created_at ASC
        LIMIT batch_size;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    
    read_loop: LOOP
        FETCH cur INTO msg_id, msg_user_id, msg_content, msg_options, msg_attempts, msg_max_attempts;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- Update attempts count
        UPDATE message_queue 
        SET attempts = attempts + 1, updated_at = NOW()
        WHERE id = msg_id;
        
        -- Here you would call your actual message sending logic
        -- For now, we'll just mark as sent (this should be handled by PHP)
        
    END LOOP;
    
    CLOSE cur;
END //
DELIMITER ;

-- Create procedure for cleaning old data
DELIMITER //
CREATE OR REPLACE PROCEDURE CleanOldMessageData()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;
    
    -- Clean old sent messages (older than 7 days)
    DELETE FROM message_queue 
    WHERE status = 'sent' 
    AND sent_at < DATE_SUB(NOW(), INTERVAL 7 DAY);
    
    -- Clean old failed messages (older than 30 days)
    DELETE FROM message_queue 
    WHERE status = 'failed' 
    AND failed_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- Clean expired sessions
    DELETE FROM user_sessions 
    WHERE expires_at < NOW();
    
    -- Clean old notification logs (older than 30 days)
    DELETE FROM notification_logs 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    COMMIT;
END //
DELIMITER ;

-- Create indexes for performance
CREATE INDEX `idx_queue_processing` ON `message_queue` (`status`, `scheduled_at`, `priority`);
CREATE INDEX `idx_templates_lookup` ON `message_templates` (`language`, `message_key`, `is_active`);

-- Record migration
INSERT INTO schema_versions (version, applied_at, description) 
VALUES (7, NOW(), 'Create message system tables')
ON DUPLICATE KEY UPDATE applied_at = NOW();

COMMIT;

-- Verification
SELECT 'Message queue table created successfully' as message;
SELECT COUNT(*) as queue_count FROM message_queue;
SELECT 'Message templates table created' as message;
SELECT COUNT(*) as templates_count FROM message_templates;
SELECT 'User sessions table created' as message;
SELECT COUNT(*) as sessions_count FROM user_sessions;
SELECT 'Notification logs table created' as message;
SELECT COUNT(*) as notifications_count FROM notification_logs;
