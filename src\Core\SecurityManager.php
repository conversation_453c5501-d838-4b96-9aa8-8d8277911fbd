<?php

declare(strict_types=1);

namespace WeBot\Core;

/**
 * Security Manager
 *
 * Comprehensive security management with headers,
 * input validation, XSS protection, and CSRF protection.
 *
 * @package WeBot\Core
 * @version 2.0
 */
class SecurityManager
{
    private array $config;
    private array $securityHeaders;
    private array $validationRules;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'csrf_protection' => true,
            'xss_protection' => true,
            'sql_injection_protection' => true,
            'file_upload_protection' => true,
            'rate_limiting' => true,
            'security_headers' => true,
            'input_validation' => true,
            'output_encoding' => true,
            'session_security' => true
        ], $config);

        $this->initializeSecurityHeaders();
        $this->initializeValidationRules();
    }

    /**
     * Apply security headers
     */
    public function applySecurityHeaders(): void
    {
        if (!$this->config['security_headers']) {
            return;
        }

        foreach ($this->securityHeaders as $header => $value) {
            if (!headers_sent()) {
                header("{$header}: {$value}");
            }
        }
    }

    /**
     * Validate input data
     */
    public function validateInput(array $data, array $rules = []): array
    {
        $result = [
            'valid' => true,
            'errors' => [],
            'sanitized_data' => []
        ];

        foreach ($data as $field => $value) {
            $fieldRules = $rules[$field] ?? $this->validationRules['default'] ?? [];
            $validation = $this->validateField($field, $value, $fieldRules);

            if (!$validation['valid']) {
                $result['valid'] = false;
                $result['errors'][$field] = $validation['errors'];
            }

            $result['sanitized_data'][$field] = $validation['sanitized_value'];
        }

        return $result;
    }

    /**
     * Protect against XSS
     */
    public function protectXSS(string $input, bool $allowHtml = false): string
    {
        if (!$this->config['xss_protection']) {
            return $input;
        }

        if ($allowHtml) {
            // Allow specific HTML tags but sanitize
            return $this->sanitizeHtml($input);
        } else {
            // Strip all HTML tags
            return htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        }
    }

    /**
     * Protect against SQL injection
     */
    public function protectSQLInjection(string $input): string
    {
        if (!$this->config['sql_injection_protection']) {
            return $input;
        }

        // Remove dangerous SQL keywords and characters
        $dangerous = [
            'union', 'select', 'insert', 'update', 'delete', 'drop', 'create',
            'alter', 'exec', 'execute', 'script', '--', ';', '/*', '*/',
            'xp_', 'sp_', 'char(', 'nchar(', 'varchar(', 'nvarchar(',
            'waitfor', 'delay'
        ];

        $input = str_ireplace($dangerous, '', $input);

        // Escape special characters
        return addslashes($input);
    }

    /**
     * Generate CSRF token
     */
    public function generateCSRFToken(): string
    {
        if (!$this->config['csrf_protection']) {
            return '';
        }

        $token = bin2hex(random_bytes(32));
        $_SESSION['csrf_token'] = $token;
        $_SESSION['csrf_token_time'] = time();

        return $token;
    }

    /**
     * Verify CSRF token
     */
    public function verifyCSRFToken(string $token): bool
    {
        if (!$this->config['csrf_protection']) {
            return true;
        }

        if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
            return false;
        }

        // Check token expiry (1 hour)
        if (time() - $_SESSION['csrf_token_time'] > 3600) {
            unset($_SESSION['csrf_token'], $_SESSION['csrf_token_time']);
            return false;
        }

        return hash_equals($_SESSION['csrf_token'], $token);
    }

    /**
     * Sanitize input data
     */
    public function sanitizeInput(string $input): string
    {
        // Remove null bytes
        $input = str_replace("\0", '', $input);

        // Trim whitespace
        $input = trim($input);

        // Remove control characters except newlines and tabs
        $input = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $input);

        // Basic XSS prevention
        $input = htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        return $input;
    }

    /**
     * Validate file upload
     */
    public function validateFileUpload(array $file, array $options = []): array
    {
        $result = [
            'valid' => false,
            'errors' => [],
            'file_info' => []
        ];

        if (!$this->config['file_upload_protection']) {
            $result['valid'] = true;
            return $result;
        }

        $maxSize = $options['max_size'] ?? 5 * 1024 * 1024; // 5MB
        $allowedTypes = $options['allowed_types'] ?? ['jpg', 'jpeg', 'png', 'gif', 'pdf'];
        $allowedMimes = $options['allowed_mimes'] ?? [
            'image/jpeg', 'image/png', 'image/gif', 'application/pdf'
        ];

        // Check upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $result['errors'][] = $this->getUploadErrorMessage($file['error']);
            return $result;
        }

        // Check file size
        if ($file['size'] > $maxSize) {
            $result['errors'][] = "File size exceeds maximum allowed size of " .
                                  $this->formatBytes($maxSize);
            return $result;
        }

        // Check file extension
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $allowedTypes)) {
            $result['errors'][] = "File type '{$extension}' is not allowed";
            return $result;
        }

        // Check MIME type
        $mimeType = mime_content_type($file['tmp_name']);
        if (!in_array($mimeType, $allowedMimes)) {
            $result['errors'][] = "MIME type '{$mimeType}' is not allowed";
            return $result;
        }

        // Additional security checks
        if ($this->isExecutableFile($file['tmp_name'])) {
            $result['errors'][] = "Executable files are not allowed";
            return $result;
        }

        $result['valid'] = true;
        $result['file_info'] = [
            'original_name' => $file['name'],
            'size' => $file['size'],
            'type' => $mimeType,
            'extension' => $extension,
            'safe_name' => $this->generateSafeFileName($file['name'])
        ];

        return $result;
    }

    /**
     * Secure session configuration
     */
    public function configureSecureSession(): void
    {
        if (!$this->config['session_security']) {
            return;
        }

        // Set secure session parameters
        ini_set('session.cookie_httponly', '1');
        ini_set('session.cookie_secure', '1');
        ini_set('session.cookie_samesite', 'Strict');
        ini_set('session.use_strict_mode', '1');
        ini_set('session.use_only_cookies', '1');
        ini_set('session.cookie_lifetime', '0');

        // Regenerate session ID periodically
        if (isset($_SESSION['last_regeneration'])) {
            if (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
                session_regenerate_id(true);
                $_SESSION['last_regeneration'] = time();
            }
        } else {
            $_SESSION['last_regeneration'] = time();
        }
    }

    /**
     * Get security audit report
     */
    public function getSecurityAudit(): array
    {
        return [
            'timestamp' => time(),
            'security_features' => [
                'csrf_protection' => $this->config['csrf_protection'],
                'xss_protection' => $this->config['xss_protection'],
                'sql_injection_protection' => $this->config['sql_injection_protection'],
                'file_upload_protection' => $this->config['file_upload_protection'],
                'security_headers' => $this->config['security_headers'],
                'session_security' => $this->config['session_security']
            ],
            'headers_status' => $this->checkHeadersStatus(),
            'session_security_status' => $this->checkSessionSecurity(),
            'recommendations' => $this->getSecurityRecommendations()
        ];
    }

    /**
     * Initialize security headers
     */
    private function initializeSecurityHeaders(): void
    {
        $this->securityHeaders = [
            'X-Content-Type-Options' => 'nosniff',
            'X-Frame-Options' => 'DENY',
            'X-XSS-Protection' => '1; mode=block',
            'Referrer-Policy' => 'strict-origin-when-cross-origin',
            'Content-Security-Policy' => $this->buildCSP(),
            'Strict-Transport-Security' => 'max-age=31536000; includeSubDomains; preload',
            'Permissions-Policy' => 'geolocation=(), microphone=(), camera=()',
            'X-Permitted-Cross-Domain-Policies' => 'none',
            'X-Download-Options' => 'noopen',
            'Cross-Origin-Embedder-Policy' => 'require-corp',
            'Cross-Origin-Opener-Policy' => 'same-origin',
            'Cross-Origin-Resource-Policy' => 'cross-origin'
        ];
    }

    /**
     * Build Content Security Policy
     */
    private function buildCSP(): string
    {
        $directives = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline'",
            "style-src 'self' 'unsafe-inline'",
            "img-src 'self' data: https:",
            "font-src 'self'",
            "connect-src 'self'",
            "frame-ancestors 'none'",
            "base-uri 'self'",
            "form-action 'self'"
        ];

        return implode('; ', $directives);
    }

    /**
     * Initialize validation rules
     */
    private function initializeValidationRules(): void
    {
        $this->validationRules = [
            'default' => [
                'max_length' => 1000,
                'min_length' => 0,
                'allow_html' => false,
                'required' => false
            ],
            'email' => [
                'pattern' => '/^[^\s@]+@[^\s@]+\.[^\s@]+$/',
                'max_length' => 255
            ],
            'phone' => [
                'pattern' => '/^[\+]?[0-9\-\(\)\s]+$/',
                'max_length' => 20
            ],
            'username' => [
                'pattern' => '/^[a-zA-Z0-9_]{3,30}$/',
                'max_length' => 30,
                'min_length' => 3
            ],
            'password' => [
                'min_length' => 8,
                'max_length' => 128,
                'require_special' => true
            ]
        ];
    }

    /**
     * Validate individual field
     */
    private function validateField(string $field, $value, array $rules): array
    {
        $result = [
            'valid' => true,
            'errors' => [],
            'sanitized_value' => $value
        ];

        // Required check
        if (($rules['required'] ?? false) && empty($value)) {
            $result['valid'] = false;
            $result['errors'][] = "Field '{$field}' is required";
            return $result;
        }

        if (empty($value)) {
            return $result; // Skip other validations for empty optional fields
        }

        // Length checks
        $length = strlen((string)$value);
        if (isset($rules['max_length']) && $length > $rules['max_length']) {
            $result['valid'] = false;
            $result['errors'][] = "Field '{$field}' exceeds maximum length";
        }

        if (isset($rules['min_length']) && $length < $rules['min_length']) {
            $result['valid'] = false;
            $result['errors'][] = "Field '{$field}' is below minimum length";
        }

        // Pattern validation
        if (isset($rules['pattern']) && !preg_match($rules['pattern'], (string)$value)) {
            $result['valid'] = false;
            $result['errors'][] = "Field '{$field}' format is invalid";
        }

        // Sanitize value
        $allowHtml = $rules['allow_html'] ?? false;
        $result['sanitized_value'] = $this->protectXSS((string)$value, $allowHtml);

        return $result;
    }

    /**
     * Sanitize HTML content
     */
    private function sanitizeHtml(string $input): string
    {
        $allowedTags = '<p><br><strong><em><u><a><ul><ol><li>';
        $cleaned = strip_tags($input, $allowedTags);

        // Remove dangerous attributes
        $cleaned = preg_replace('/on\w+="[^"]*"/i', '', $cleaned);
        $cleaned = preg_replace('/javascript:/i', '', $cleaned);

        return $cleaned;
    }

    /**
     * Check if file is executable
     */
    private function isExecutableFile(string $filePath): bool
    {
        $content = file_get_contents($filePath, false, null, 0, 1024);

        // Check for executable signatures
        $signatures = [
            "\x7fELF",     // Linux executable
            "MZ",          // Windows executable
            "\xca\xfe\xba\xbe", // Java class
            "#!/",         // Script with shebang
            "<?php"       // PHP script
        ];

        foreach ($signatures as $signature) {
            if (str_starts_with($content, $signature)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Generate safe file name
     */
    private function generateSafeFileName(string $originalName): string
    {
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $baseName = pathinfo($originalName, PATHINFO_FILENAME);

        // Sanitize base name
        $safeName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $baseName);
        $safeName = substr($safeName, 0, 50); // Limit length

        // Add timestamp for uniqueness
        $timestamp = time();

        return "{$safeName}_{$timestamp}.{$extension}";
    }

    /**
     * Get upload error message
     */
    private function getUploadErrorMessage(int $errorCode): string
    {
        $messages = [
            UPLOAD_ERR_INI_SIZE => 'File exceeds upload_max_filesize directive',
            UPLOAD_ERR_FORM_SIZE => 'File exceeds MAX_FILE_SIZE directive',
            UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
            UPLOAD_ERR_NO_FILE => 'No file was uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'File upload stopped by extension'
        ];

        return $messages[$errorCode] ?? 'Unknown upload error';
    }

    /**
     * Format bytes to human readable
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $factor = floor((strlen((string)$bytes) - 1) / 3);

        return sprintf("%.1f %s", $bytes / pow(1024, $factor), $units[$factor]);
    }

    /**
     * Check headers status
     */
    private function checkHeadersStatus(): array
    {
        $status = [];

        foreach ($this->securityHeaders as $header => $value) {
            $status[$header] = [
                'configured' => true,
                'value' => $value,
                'sent' => headers_sent()
            ];
        }

        return $status;
    }

    /**
     * Check session security
     */
    private function checkSessionSecurity(): array
    {
        return [
            'cookie_httponly' => (bool)ini_get('session.cookie_httponly'),
            'cookie_secure' => (bool)ini_get('session.cookie_secure'),
            'use_strict_mode' => (bool)ini_get('session.use_strict_mode'),
            'use_only_cookies' => (bool)ini_get('session.use_only_cookies'),
            'session_started' => session_status() === PHP_SESSION_ACTIVE
        ];
    }

    /**
     * Get security recommendations
     */
    private function getSecurityRecommendations(): array
    {
        $recommendations = [];

        if (!$this->config['csrf_protection']) {
            $recommendations[] = 'Enable CSRF protection for forms';
        }

        if (!$this->config['security_headers']) {
            $recommendations[] = 'Enable security headers';
        }

        if (!ini_get('session.cookie_secure')) {
            $recommendations[] = 'Enable secure session cookies';
        }

        return $recommendations;
    }
}
