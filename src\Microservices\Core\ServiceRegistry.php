<?php

declare(strict_types=1);

namespace WeBot\Microservices\Core;

use WeBot\Core\CacheManager;
use WeBot\Utils\Logger;
use WeBot\Exceptions\WeBotException;

/**
 * Service Registry
 *
 * Central registry for microservices discovery, health checking,
 * and load balancing across distributed services.
 *
 * @package WeBot\Microservices\Core
 * @version 2.0
 */
class ServiceRegistry
{
    private CacheManager $cache;
    private Logger $logger;
    private array $config;
    private array $services = [];
    private array $healthChecks = [];

    public function __construct(CacheManager $cache, array $config = [])
    {
        $this->cache = $cache;
        $this->logger = Logger::getInstance();
        $this->config = array_merge($this->getDefaultConfig(), $config);

        $this->loadRegisteredServices();
    }

    /**
     * Register a service
     */
    public function registerService(string $serviceName, array $serviceConfig): bool
    {
        $service = [
            'name' => $serviceName,
            'id' => $serviceConfig['id'] ?? uniqid($serviceName . '_'),
            'host' => $serviceConfig['host'],
            'port' => $serviceConfig['port'],
            'protocol' => $serviceConfig['protocol'] ?? 'http',
            'version' => $serviceConfig['version'] ?? '1.0.0',
            'health_check_url' => $serviceConfig['health_check_url'] ?? '/health',
            'metadata' => $serviceConfig['metadata'] ?? [],
            'tags' => $serviceConfig['tags'] ?? [],
            'weight' => $serviceConfig['weight'] ?? 100,
            'status' => 'healthy',
            'registered_at' => time(),
            'last_heartbeat' => time(),
            'failure_count' => 0
        ];

        $this->services[$serviceName][$service['id']] = $service;
        $this->persistServices();

        $this->logger->info("Service registered", [
            'service' => $serviceName,
            'id' => $service['id'],
            'endpoint' => $this->buildServiceUrl($service)
        ]);

        return true;
    }

    /**
     * Deregister a service
     */
    public function deregisterService(string $serviceName, string $serviceId): bool
    {
        if (isset($this->services[$serviceName][$serviceId])) {
            unset($this->services[$serviceName][$serviceId]);

            if (empty($this->services[$serviceName])) {
                unset($this->services[$serviceName]);
            }

            $this->persistServices();

            $this->logger->info("Service deregistered", [
                'service' => $serviceName,
                'id' => $serviceId
            ]);

            return true;
        }

        return false;
    }

    /**
     * Discover services by name
     */
    public function discoverService(string $serviceName): array
    {
        $services = $this->services[$serviceName] ?? [];

        // Filter healthy services
        $healthyServices = array_filter($services, function ($service) {
            return $service['status'] === 'healthy';
        });

        if (empty($healthyServices)) {
            throw new WeBotException("No healthy instances found for service: {$serviceName}");
        }

        return array_values($healthyServices);
    }

    /**
     * Get service instance with load balancing
     */
    public function getServiceInstance(string $serviceName, string $strategy = 'round_robin'): array
    {
        $services = $this->discoverService($serviceName);

        return match ($strategy) {
            'round_robin' => $this->roundRobinSelection($serviceName, $services),
            'weighted' => $this->weightedSelection($services),
            'random' => $this->randomSelection($services),
            'least_connections' => $this->leastConnectionsSelection($services),
            default => $this->roundRobinSelection($serviceName, $services)
        };
    }

    /**
     * Update service heartbeat
     */
    public function heartbeat(string $serviceName, string $serviceId, array $metadata = []): bool
    {
        if (!isset($this->services[$serviceName][$serviceId])) {
            return false;
        }

        $this->services[$serviceName][$serviceId]['last_heartbeat'] = time();
        $this->services[$serviceName][$serviceId]['status'] = 'healthy';
        $this->services[$serviceName][$serviceId]['failure_count'] = 0;

        if (!empty($metadata)) {
            $this->services[$serviceName][$serviceId]['metadata'] = array_merge(
                $this->services[$serviceName][$serviceId]['metadata'],
                $metadata
            );
        }

        $this->persistServices();

        return true;
    }

    /**
     * Perform health checks on all services
     */
    public function performHealthChecks(): array
    {
        $results = [];

        foreach ($this->services as $serviceName => $instances) {
            $results[$serviceName] = [];

            foreach ($instances as $serviceId => $service) {
                $healthStatus = $this->checkServiceHealth($service);
                $results[$serviceName][$serviceId] = $healthStatus;

                // Update service status
                $this->updateServiceHealth($serviceName, $serviceId, $healthStatus);
            }
        }

        return $results;
    }

    /**
     * Get all registered services
     */
    public function getAllServices(): array
    {
        return $this->services;
    }

    /**
     * Get service statistics
     */
    public function getServiceStatistics(): array
    {
        $stats = [
            'total_services' => count($this->services),
            'total_instances' => 0,
            'healthy_instances' => 0,
            'unhealthy_instances' => 0,
            'services' => []
        ];

        foreach ($this->services as $serviceName => $instances) {
            $serviceStats = [
                'name' => $serviceName,
                'total_instances' => count($instances),
                'healthy_instances' => 0,
                'unhealthy_instances' => 0,
                'instances' => []
            ];

            foreach ($instances as $serviceId => $service) {
                $stats['total_instances']++;
                $serviceStats['instances'][$serviceId] = [
                    'id' => $serviceId,
                    'status' => $service['status'],
                    'last_heartbeat' => $service['last_heartbeat'],
                    'failure_count' => $service['failure_count']
                ];

                if ($service['status'] === 'healthy') {
                    $stats['healthy_instances']++;
                    $serviceStats['healthy_instances']++;
                } else {
                    $stats['unhealthy_instances']++;
                    $serviceStats['unhealthy_instances']++;
                }
            }

            $stats['services'][$serviceName] = $serviceStats;
        }

        return $stats;
    }

    /**
     * Round robin service selection
     */
    private function roundRobinSelection(string $serviceName, array $services): array
    {
        $cacheKey = "service_registry:round_robin:{$serviceName}";
        $currentIndex = $this->cache->get($cacheKey, 0);

        $service = $services[$currentIndex % count($services)];

        $this->cache->set($cacheKey, $currentIndex + 1, 3600);

        return $service;
    }

    /**
     * Weighted service selection
     */
    private function weightedSelection(array $services): array
    {
        $totalWeight = array_sum(array_column($services, 'weight'));
        $random = rand(1, $totalWeight);

        $currentWeight = 0;
        foreach ($services as $service) {
            $currentWeight += $service['weight'];
            if ($random <= $currentWeight) {
                return $service;
            }
        }

        return $services[0]; // Fallback
    }

    /**
     * Random service selection
     */
    private function randomSelection(array $services): array
    {
        return $services[array_rand($services)];
    }

    /**
     * Least connections service selection
     */
    private function leastConnectionsSelection(array $services): array
    {
        // Sort by connection count (stored in metadata)
        usort($services, function ($a, $b) {
            $connectionsA = $a['metadata']['active_connections'] ?? 0;
            $connectionsB = $b['metadata']['active_connections'] ?? 0;
            return $connectionsA <=> $connectionsB;
        });

        return $services[0];
    }

    /**
     * Check service health
     */
    private function checkServiceHealth(array $service): array
    {
        $url = $this->buildServiceUrl($service) . $service['health_check_url'];

        $startTime = microtime(true);

        try {
            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'timeout' => $this->config['health_check_timeout'],
                    'header' => [
                        'User-Agent: WeBot-ServiceRegistry/1.0',
                        'Accept: application/json'
                    ]
                ]
            ]);

            $response = file_get_contents($url, false, $context);
            $responseTime = (microtime(true) - $startTime) * 1000;

            if ($response !== false) {
                $data = json_decode($response, true);

                return [
                    'status' => 'healthy',
                    'response_time' => $responseTime,
                    'timestamp' => time(),
                    'data' => $data
                ];
            }
        } catch (\Exception $e) {
            $this->logger->warning("Health check failed", [
                'service' => $service['name'],
                'url' => $url,
                'error' => $e->getMessage()
            ]);
        }

        return [
            'status' => 'unhealthy',
            'response_time' => (microtime(true) - $startTime) * 1000,
            'timestamp' => time(),
            'error' => 'Health check failed'
        ];
    }

    /**
     * Update service health status
     */
    private function updateServiceHealth(string $serviceName, string $serviceId, array $healthStatus): void
    {
        if (!isset($this->services[$serviceName][$serviceId])) {
            return;
        }

        $service = &$this->services[$serviceName][$serviceId];

        if ($healthStatus['status'] === 'healthy') {
            $service['status'] = 'healthy';
            $service['failure_count'] = 0;
        } else {
            $service['failure_count']++;

            if ($service['failure_count'] >= $this->config['max_failures']) {
                $service['status'] = 'unhealthy';
            }
        }

        $service['last_health_check'] = $healthStatus['timestamp'];
        $service['last_response_time'] = $healthStatus['response_time'];

        $this->persistServices();
    }

    /**
     * Build service URL
     */
    private function buildServiceUrl(array $service): string
    {
        return "{$service['protocol']}://{$service['host']}:{$service['port']}";
    }

    /**
     * Load registered services from cache
     */
    private function loadRegisteredServices(): void
    {
        $this->services = $this->cache->get('service_registry:services', []);
    }

    /**
     * Persist services to cache
     */
    private function persistServices(): void
    {
        $this->cache->set('service_registry:services', $this->services, 0); // No expiration
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'health_check_timeout' => 5,
            'health_check_interval' => 30,
            'max_failures' => 3,
            'cleanup_interval' => 300,
            'service_ttl' => 3600
        ];
    }
}
