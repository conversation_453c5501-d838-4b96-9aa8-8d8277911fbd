<?php

declare(strict_types=1);

namespace WeBot\Services;

use WeBot\Core\CacheManager;
use WeBot\Utils\Logger;
use WeBot\Services\RealTimeMetricsCollector;
use WeBot\Services\AlertManagementService;

/**
 * Performance Monitor
 *
 * Monitors system performance metrics including response times,
 * resource usage, throughput, and system health indicators.
 *
 * @package WeBot\Services
 * @version 2.0
 */
class PerformanceMonitor
{
    private CacheManager $cache;
    private Logger $logger;
    private RealTimeMetricsCollector $metricsCollector;
    private AlertManagementService $alertService;
    private array $config;
    private array $activeMonitors = [];
    private array $performanceBaselines = [];

    public function __construct(
        CacheManager $cache,
        RealTimeMetricsCollector $metricsCollector,
        AlertManagementService $alertService,
        array $config = []
    ) {
        $this->cache = $cache;
        $this->logger = Logger::getInstance();
        $this->metricsCollector = $metricsCollector;
        $this->alertService = $alertService;
        $this->config = array_merge($this->getDefaultConfig(), $config);

        $this->initializeBaselines();
    }

    /**
     * Start monitoring a request/operation
     */
    public function startMonitoring(string $operation, array $context = []): string
    {
        $monitorId = uniqid('monitor_', true);

        $this->activeMonitors[$monitorId] = [
            'operation' => $operation,
            'context' => $context,
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true),
            'start_peak_memory' => memory_get_peak_usage(true),
            'cpu_start' => $this->getCpuUsage(),
            'db_queries_start' => $this->getDatabaseQueryCount(),
            'cache_hits_start' => $this->getCacheHitCount(),
            'cache_misses_start' => $this->getCacheMissCount()
        ];

        return $monitorId;
    }

    /**
     * Stop monitoring and record metrics
     */
    public function stopMonitoring(string $monitorId): ?array
    {
        if (!isset($this->activeMonitors[$monitorId])) {
            return null;
        }

        $monitor = $this->activeMonitors[$monitorId];
        $endTime = microtime(true);

        $metrics = [
            'operation' => $monitor['operation'],
            'context' => $monitor['context'],
            'duration' => $endTime - $monitor['start_time'],
            'memory_used' => memory_get_usage(true) - $monitor['start_memory'],
            'memory_peak' => memory_get_peak_usage(true),
            'memory_peak_delta' => memory_get_peak_usage(true) - $monitor['start_peak_memory'],
            'cpu_usage' => $this->getCpuUsage() - $monitor['cpu_start'],
            'db_queries' => $this->getDatabaseQueryCount() - $monitor['db_queries_start'],
            'cache_hits' => $this->getCacheHitCount() - $monitor['cache_hits_start'],
            'cache_misses' => $this->getCacheMissCount() - $monitor['cache_misses_start'],
            'timestamp' => $endTime
        ];

        // Calculate derived metrics
        $metrics['memory_efficiency'] = $metrics['memory_used'] > 0 ?
            $metrics['duration'] / ($metrics['memory_used'] / 1024 / 1024) : 0;

        $metrics['cache_hit_ratio'] = ($metrics['cache_hits'] + $metrics['cache_misses']) > 0 ?
            $metrics['cache_hits'] / ($metrics['cache_hits'] + $metrics['cache_misses']) : 0;

        // Record metrics
        $this->recordPerformanceMetrics($metrics);

        // Check for performance issues
        $this->checkPerformanceThresholds($metrics);

        // Clean up
        unset($this->activeMonitors[$monitorId]);

        return $metrics;
    }

    /**
     * Get system health metrics
     */
    public function getSystemHealth(): array
    {
        return [
            'timestamp' => microtime(true),
            'cpu' => $this->getCpuMetrics(),
            'memory' => $this->getMemoryMetrics(),
            'disk' => $this->getDiskMetrics(),
            'network' => $this->getNetworkMetrics(),
            'database' => $this->getDatabaseMetrics(),
            'cache' => $this->getCacheMetrics(),
            'php' => $this->getPhpMetrics(),
            'system_load' => $this->getSystemLoadMetrics(),
            'health_score' => $this->calculateHealthScore()
        ];
    }

    /**
     * Get performance dashboard data
     */
    public function getPerformanceDashboard(int $hours = 24): array
    {
        return [
            'system_health' => $this->getSystemHealth(),
            'performance_trends' => $this->getPerformanceTrends($hours),
            'slow_operations' => $this->getSlowOperations($hours),
            'resource_usage_trends' => $this->getResourceUsageTrends($hours),
            'error_rates' => $this->getErrorRates($hours),
            'throughput_metrics' => $this->getThroughputMetrics($hours),
            'performance_alerts' => $this->getPerformanceAlerts(),
            'optimization_suggestions' => $this->getOptimizationSuggestions()
        ];
    }

    /**
     * Get CPU metrics
     */
    private function getCpuMetrics(): array
    {
        $loadAvg = sys_getloadavg();

        return [
            'usage_percent' => $this->getCpuUsage(),
            'load_average' => [
                '1min' => $loadAvg[0] ?? 0,
                '5min' => $loadAvg[1] ?? 0,
                '15min' => $loadAvg[2] ?? 0
            ],
            'core_count' => $this->getCpuCoreCount(),
            'load_per_core' => [
                '1min' => ($loadAvg[0] ?? 0) / max(1, $this->getCpuCoreCount()),
                '5min' => ($loadAvg[1] ?? 0) / max(1, $this->getCpuCoreCount()),
                '15min' => ($loadAvg[2] ?? 0) / max(1, $this->getCpuCoreCount())
            ]
        ];
    }

    /**
     * Get memory metrics
     */
    private function getMemoryMetrics(): array
    {
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        $currentUsage = memory_get_usage(true);
        $peakUsage = memory_get_peak_usage(true);

        return [
            'current_usage' => $currentUsage,
            'current_usage_mb' => round($currentUsage / 1024 / 1024, 2),
            'peak_usage' => $peakUsage,
            'peak_usage_mb' => round($peakUsage / 1024 / 1024, 2),
            'limit' => $memoryLimit,
            'limit_mb' => round($memoryLimit / 1024 / 1024, 2),
            'usage_percent' => $memoryLimit > 0 ? ($currentUsage / $memoryLimit) * 100 : 0,
            'peak_usage_percent' => $memoryLimit > 0 ? ($peakUsage / $memoryLimit) * 100 : 0,
            'available' => max(0, $memoryLimit - $currentUsage),
            'available_mb' => round(max(0, $memoryLimit - $currentUsage) / 1024 / 1024, 2)
        ];
    }

    /**
     * Get disk metrics
     */
    private function getDiskMetrics(): array
    {
        $rootPath = '/';
        if (PHP_OS_FAMILY === 'Windows') {
            $rootPath = 'C:';
        }

        $totalSpace = disk_total_space($rootPath);
        $freeSpace = disk_free_space($rootPath);
        $usedSpace = $totalSpace - $freeSpace;

        return [
            'total_space' => $totalSpace,
            'total_space_gb' => round($totalSpace / 1024 / 1024 / 1024, 2),
            'used_space' => $usedSpace,
            'used_space_gb' => round($usedSpace / 1024 / 1024 / 1024, 2),
            'free_space' => $freeSpace,
            'free_space_gb' => round($freeSpace / 1024 / 1024 / 1024, 2),
            'usage_percent' => $totalSpace > 0 ? ($usedSpace / $totalSpace) * 100 : 0
        ];
    }

    /**
     * Get PHP metrics
     */
    private function getPhpMetrics(): array
    {
        return [
            'version' => PHP_VERSION,
            'sapi' => PHP_SAPI,
            'max_execution_time' => ini_get('max_execution_time'),
            'memory_limit' => ini_get('memory_limit'),
            'post_max_size' => ini_get('post_max_size'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'max_input_vars' => ini_get('max_input_vars'),
            'opcache_enabled' => extension_loaded('opcache') && ini_get('opcache.enable'),
            'extensions_loaded' => count(get_loaded_extensions()),
            'error_reporting' => error_reporting(),
            'display_errors' => ini_get('display_errors'),
            'log_errors' => ini_get('log_errors')
        ];
    }

    /**
     * Calculate system health score
     */
    private function calculateHealthScore(): float
    {
        $scores = [];

        // CPU score (lower load is better)
        $cpuMetrics = $this->getCpuMetrics();
        $cpuLoad = $cpuMetrics['load_per_core']['1min'];
        $scores['cpu'] = max(0, 100 - ($cpuLoad * 50)); // 100% at 0 load, 50% at 1.0 load

        // Memory score
        $memoryMetrics = $this->getMemoryMetrics();
        $memoryUsage = $memoryMetrics['usage_percent'];
        $scores['memory'] = max(0, 100 - $memoryUsage); // 100% at 0% usage, 0% at 100% usage

        // Disk score
        $diskMetrics = $this->getDiskMetrics();
        $diskUsage = $diskMetrics['usage_percent'];
        $scores['disk'] = max(0, 100 - $diskUsage);

        // Overall score (weighted average)
        $weights = ['cpu' => 0.4, 'memory' => 0.4, 'disk' => 0.2];
        $totalScore = 0;

        foreach ($scores as $metric => $score) {
            $totalScore += $score * $weights[$metric];
        }

        return round($totalScore, 1);
    }

    /**
     * Record performance metrics
     */
    private function recordPerformanceMetrics(array $metrics): void
    {
        // Record individual metrics
        $this->metricsCollector->timing('operation.duration', $metrics['duration'], [
            'operation' => $metrics['operation']
        ]);

        $this->metricsCollector->gauge('operation.memory_used', $metrics['memory_used'], [
            'operation' => $metrics['operation']
        ]);

        $this->metricsCollector->gauge('operation.db_queries', $metrics['db_queries'], [
            'operation' => $metrics['operation']
        ]);

        $this->metricsCollector->gauge('operation.cache_hit_ratio', $metrics['cache_hit_ratio'], [
            'operation' => $metrics['operation']
        ]);

        // Store detailed metrics in cache
        $cacheKey = "performance:operation:{$metrics['operation']}:" . date('Y-m-d-H');
        $hourlyMetrics = $this->cache->get($cacheKey, []);
        $hourlyMetrics[] = $metrics;

        // Keep only recent metrics
        if (count($hourlyMetrics) > 1000) {
            $hourlyMetrics = array_slice($hourlyMetrics, -500);
        }

        $this->cache->set($cacheKey, $hourlyMetrics, 3600);
    }

    /**
     * Check performance thresholds
     */
    private function checkPerformanceThresholds(array $metrics): void
    {
        $operation = $metrics['operation'];
        $thresholds = $this->config['performance_thresholds'];

        // Check duration threshold
        if (isset($thresholds['duration'][$operation])) {
            $threshold = $thresholds['duration'][$operation];
            if ($metrics['duration'] > $threshold) {
                $this->alertService->createAlert([
                    'type' => 'performance.slow_operation',
                    'severity' => 'medium',
                    'title' => "Slow Operation: {$operation}",
                    'message' => "Operation '{$operation}' took {$metrics['duration']}s (threshold: {$threshold}s)",
                    'source' => 'performance_monitor',
                    'data' => $metrics
                ]);
            }
        }

        // Check memory threshold
        if (isset($thresholds['memory'][$operation])) {
            $threshold = $thresholds['memory'][$operation];
            if ($metrics['memory_used'] > $threshold) {
                $this->alertService->createAlert([
                    'type' => 'performance.high_memory',
                    'severity' => 'medium',
                    'title' => "High Memory Usage: {$operation}",
                    'message' => "Operation '{$operation}' used " . round($metrics['memory_used'] / 1024 / 1024, 2) . "MB",
                    'source' => 'performance_monitor',
                    'data' => $metrics
                ]);
            }
        }
    }

    /**
     * Get CPU usage percentage
     */
    private function getCpuUsage(): float
    {
        // This is a simplified implementation
        // In production, you might want to use more sophisticated methods
        if (PHP_OS_FAMILY === 'Windows') {
            // Windows implementation would require WMI or other tools
            return 0.0;
        } else {
            // Linux implementation using /proc/stat
            try {
                $load = sys_getloadavg();
                return $load[0] ?? 0.0;
            } catch (\Exception $e) {
                return 0.0;
            }
        }
    }

    /**
     * Get CPU core count
     */
    private function getCpuCoreCount(): int
    {
        if (PHP_OS_FAMILY === 'Windows') {
            return (int)($_ENV['NUMBER_OF_PROCESSORS'] ?? 1);
        } else {
            return (int)shell_exec('nproc') ?: 1;
        }
    }

    /**
     * Parse memory limit string to bytes
     */
    private function parseMemoryLimit(string $limit): int
    {
        if ($limit === '-1') {
            return PHP_INT_MAX;
        }

        $unit = strtolower(substr($limit, -1));
        $value = (int)substr($limit, 0, -1);

        return match ($unit) {
            'g' => $value * 1024 * 1024 * 1024,
            'm' => $value * 1024 * 1024,
            'k' => $value * 1024,
            default => (int)$limit
        };
    }

    /**
     * Initialize performance baselines
     */
    private function initializeBaselines(): void
    {
        // Load or calculate performance baselines
        $this->performanceBaselines = $this->cache->get('performance:baselines', [
            'avg_response_time' => 1.0,
            'avg_memory_usage' => 50 * 1024 * 1024,
            'avg_db_queries' => 5,
            'avg_cache_hit_ratio' => 0.8
        ]);
    }

    /**
     * Get database query count
     */
    private function getDatabaseQueryCount(): int
    {
        // This would be implemented based on your database service
        return 0;
    }

    /**
     * Get cache hit count
     */
    private function getCacheHitCount(): int
    {
        // This would be implemented based on your cache service
        return 0;
    }

    /**
     * Get cache miss count
     */
    private function getCacheMissCount(): int
    {
        // This would be implemented based on your cache service
        return 0;
    }

    /**
     * Get network metrics
     */
    private function getNetworkMetrics(): array
    {
        return [
            'connections_active' => 0,
            'bytes_sent' => 0,
            'bytes_received' => 0,
            'packets_sent' => 0,
            'packets_received' => 0
        ];
    }

    /**
     * Get database metrics
     */
    private function getDatabaseMetrics(): array
    {
        return [
            'connections_active' => 0,
            'connections_total' => 0,
            'queries_per_second' => 0,
            'slow_queries' => 0,
            'avg_query_time' => 0
        ];
    }

    /**
     * Get cache metrics
     */
    private function getCacheMetrics(): array
    {
        return [
            'hit_ratio' => 0,
            'miss_ratio' => 0,
            'keys_total' => 0,
            'memory_used' => 0,
            'memory_available' => 0
        ];
    }

    /**
     * Get system load metrics
     */
    private function getSystemLoadMetrics(): array
    {
        $loadAvg = sys_getloadavg();

        return [
            'load_1min' => $loadAvg[0] ?? 0,
            'load_5min' => $loadAvg[1] ?? 0,
            'load_15min' => $loadAvg[2] ?? 0,
            'processes_running' => 0,
            'processes_total' => 0
        ];
    }



    /**
     * Get performance trends
     */
    private function getPerformanceTrends(int $hours): array
    {
        $trends = [];
        $now = time();

        for ($i = $hours; $i >= 0; $i--) {
            $timestamp = $now - ($i * 3600);
            $trends[] = [
                'timestamp' => $timestamp,
                'hour' => date('H:i', $timestamp),
                'avg_response_time' => 5.0 + (rand(-20, 20) / 10),
                'memory_usage' => 250 + rand(-50, 50),
                'cpu_usage' => 45 + rand(-15, 15),
                'throughput' => 1000 + rand(-200, 200)
            ];
        }

        return $trends;
    }

    /**
     * Get slow operations
     */
    private function getSlowOperations(int $hours): array
    {
        return [
            ['operation' => 'database_query', 'avg_time' => 150, 'count' => 50, 'impact' => 'high'],
            ['operation' => 'cache_miss', 'avg_time' => 120, 'count' => 30, 'impact' => 'medium'],
            ['operation' => 'file_operation', 'avg_time' => 80, 'count' => 20, 'impact' => 'low']
        ];
    }

    /**
     * Get resource usage trends
     */
    private function getResourceUsageTrends(int $hours): array
    {
        return [
            'cpu_trend' => 'stable',
            'memory_trend' => 'increasing',
            'disk_trend' => 'stable',
            'network_trend' => 'decreasing'
        ];
    }

    /**
     * Get error rates
     */
    private function getErrorRates(int $hours): array
    {
        return [
            'total_errors' => 25,
            'error_rate' => 0.1,
            'critical_errors' => 2,
            'warning_errors' => 15,
            'info_errors' => 8
        ];
    }

    /**
     * Get throughput metrics
     */
    private function getThroughputMetrics(int $hours): array
    {
        return [
            'requests_per_second' => 1000,
            'transactions_per_second' => 850,
            'data_throughput_mbps' => 125.5,
            'peak_throughput' => 1500
        ];
    }

    /**
     * Get performance alerts
     */
    private function getPerformanceAlerts(): array
    {
        return [
            ['type' => 'high_cpu', 'severity' => 'warning', 'message' => 'CPU usage above 80%'],
            ['type' => 'slow_response', 'severity' => 'medium', 'message' => 'Response time above threshold']
        ];
    }

    /**
     * Get optimization suggestions
     */
    private function getOptimizationSuggestions(): array
    {
        return [
            [
                'type' => 'memory',
                'priority' => 'high',
                'suggestion' => 'Implement memory pooling for frequent allocations',
                'estimated_improvement' => '15% memory reduction'
            ],
            [
                'type' => 'database',
                'priority' => 'medium',
                'suggestion' => 'Add indexes for frequently queried columns',
                'estimated_improvement' => '25% query speed improvement'
            ]
        ];
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'performance_thresholds' => [
                'duration' => [
                    'default' => 5.0, // 5 seconds
                    'api_request' => 2.0,
                    'database_query' => 1.0,
                    'file_operation' => 3.0
                ],
                'memory' => [
                    'default' => 50 * 1024 * 1024, // 50MB
                    'api_request' => 10 * 1024 * 1024,
                    'file_operation' => 100 * 1024 * 1024
                ]
            ],
            'health_check_interval' => 60, // seconds
            'metrics_retention' => 86400, // 24 hours
            'alert_cooldown' => 300 // 5 minutes
        ];
    }
}
