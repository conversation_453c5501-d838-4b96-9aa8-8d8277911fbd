<?php
/**
 * WeBot Test Runner
 * اجرای تست‌ها بدون نیاز به PHPUnit
 */

// تنظیم محیط تست
define('APP_ENV', 'testing');
define('APP_DEBUG', true);

// تنظیم error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// تنظیم timezone
date_default_timezone_set('Asia/Tehran');

echo "=== WeBot Test Runner ===\n";
echo "شروع تست‌های پروژه...\n\n";

// بررسی PHP version
echo "1. بررسی PHP Version:\n";
echo "PHP Version: " . PHP_VERSION . "\n";
if (version_compare(PHP_VERSION, '8.1.0', '<')) {
    echo "❌ خطا: PHP 8.1+ مورد نیاز است\n";
    exit(1);
}
echo "✅ PHP Version مناسب است\n\n";

// بررسی extensions مورد نیاز
echo "2. بررسی PHP Extensions:\n";
$required_extensions = [
    'json', 'mbstring', 'curl', 'openssl', 'pdo', 'pdo_mysql'
];

$missing_extensions = [];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✅ $ext\n";
    } else {
        echo "❌ $ext (مفقود)\n";
        $missing_extensions[] = $ext;
    }
}

if (!empty($missing_extensions)) {
    echo "\nخطا: Extensions زیر مفقود هستند: " . implode(', ', $missing_extensions) . "\n";
    exit(1);
}
echo "\n";

// بررسی فایل‌های پروژه
echo "3. بررسی ساختار پروژه:\n";
$required_files = [
    'composer.json',
    'src/',
    'tests/',
    'config/',
    'public/'
];

foreach ($required_files as $file) {
    if (file_exists(__DIR__ . '/../' . $file)) {
        echo "✅ $file\n";
    } else {
        echo "❌ $file (مفقود)\n";
    }
}
echo "\n";

// تست autoloader
echo "4. تست Autoloader:\n";
$autoloader_path = __DIR__ . '/../vendor/autoload.php';
if (file_exists($autoloader_path)) {
    require_once $autoloader_path;
    echo "✅ Autoloader بارگذاری شد\n";
} else {
    echo "❌ Autoloader مفقود است. لطفاً 'composer install' اجرا کنید\n";
    
    // تلاش برای ایجاد autoloader ساده
    echo "ایجاد autoloader ساده...\n";
    
    spl_autoload_register(function ($class) {
        $prefix = 'WeBot\\';
        $base_dir = __DIR__ . '/../src/';
        
        $len = strlen($prefix);
        if (strncmp($prefix, $class, $len) !== 0) {
            return;
        }
        
        $relative_class = substr($class, $len);
        $file = $base_dir . str_replace('\\', '/', $relative_class) . '.php';
        
        if (file_exists($file)) {
            require $file;
        }
    });
    
    echo "✅ Autoloader ساده ایجاد شد\n";
}
echo "\n";

// تست کلاس‌های اصلی
echo "5. تست کلاس‌های اصلی:\n";

// تست Config
try {
    if (class_exists('WeBot\\Core\\Config')) {
        echo "✅ Config class موجود است\n";
    } else {
        echo "❌ Config class مفقود است\n";
    }
} catch (Exception $e) {
    echo "❌ خطا در بارگذاری Config: " . $e->getMessage() . "\n";
}

// تست Database
try {
    if (class_exists('WeBot\\Core\\Database')) {
        echo "✅ Database class موجود است\n";
    } else {
        echo "❌ Database class مفقود است\n";
    }
} catch (Exception $e) {
    echo "❌ خطا در بارگذاری Database: " . $e->getMessage() . "\n";
}

// تست TelegramBot
try {
    if (class_exists('WeBot\\Core\\TelegramBot')) {
        echo "✅ TelegramBot class موجود است\n";
    } else {
        echo "❌ TelegramBot class مفقود است\n";
    }
} catch (Exception $e) {
    echo "❌ خطا در بارگذاری TelegramBot: " . $e->getMessage() . "\n";
}

echo "\n";

// تست تنظیمات
echo "6. تست تنظیمات:\n";
$env_file = __DIR__ . '/../.env';
if (file_exists($env_file)) {
    echo "✅ فایل .env موجود است\n";
    
    // خواندن تنظیمات
    $env_content = file_get_contents($env_file);
    $required_configs = [
        'TELEGRAM_BOT_TOKEN',
        'DB_HOST',
        'DB_DATABASE',
        'DB_USERNAME'
    ];
    
    foreach ($required_configs as $config) {
        if (strpos($env_content, $config) !== false) {
            echo "✅ $config تنظیم شده\n";
        } else {
            echo "❌ $config مفقود است\n";
        }
    }
} else {
    echo "❌ فایل .env مفقود است\n";
    echo "لطفاً .env.example را به .env کپی کنید\n";
}

echo "\n";

// تست‌های ساده عملکردی
echo "7. تست‌های عملکردی ساده:\n";

// تست JSON encoding/decoding
$test_data = ['test' => 'data', 'number' => 123];
$json = json_encode($test_data);
$decoded = json_decode($json, true);
if ($decoded === $test_data) {
    echo "✅ JSON encoding/decoding\n";
} else {
    echo "❌ JSON encoding/decoding\n";
}

// تست cURL
if (function_exists('curl_init')) {
    echo "✅ cURL موجود است\n";
} else {
    echo "❌ cURL مفقود است\n";
}

// تست OpenSSL
if (extension_loaded('openssl')) {
    echo "✅ OpenSSL موجود است\n";
} else {
    echo "❌ OpenSSL مفقود است\n";
}

echo "\n";

// خلاصه نتایج
echo "=== خلاصه نتایج ===\n";
echo "تست‌های اولیه پروژه WeBot تکمیل شد.\n";
echo "برای اجرای تست‌های کامل، لطفاً dependencies را نصب کنید:\n";
echo "composer install\n";
echo "سپس تست‌های PHPUnit را اجرا کنید:\n";
echo "vendor/bin/phpunit\n\n";

echo "تاریخ و زمان تست: " . date('Y-m-d H:i:s') . "\n";
echo "=========================\n";
?>
