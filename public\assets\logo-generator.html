<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WeBot Logo Generator</title>
    <style>
        @font-face {
            font-family: 'IRANSans';
            src: url('IRANSans.ttf');
        }
        body {
            font-family: 'IRANSans', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
        }
        h1 {
            color: #4A90E2;
            margin-bottom: 20px;
        }
        canvas {
            border: 2px solid #ddd;
            border-radius: 10px;
            margin: 20px 0;
        }
        button {
            background: linear-gradient(135deg, #4A90E2, #357ABD);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            font-family: 'IRANSans', Arial, sans-serif;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 WeBot Logo Generator</h1>
        <p>لوگوی جدید WeBot را تولید و دانلود کنید</p>
        
        <canvas id="logoCanvas" width="400" height="400"></canvas>
        
        <div>
            <button onclick="generateLogo()">تولید لوگو</button>
            <button onclick="downloadLogo()">دانلود PNG</button>
        </div>
        
        <div style="margin-top: 20px;">
            <label>رنگ اصلی: </label>
            <input type="color" id="primaryColor" value="#4A90E2" onchange="generateLogo()">
            <label>رنگ ثانویه: </label>
            <input type="color" id="secondaryColor" value="#50C878" onchange="generateLogo()">
        </div>
    </div>

    <script>
        const canvas = document.getElementById('logoCanvas');
        const ctx = canvas.getContext('2d');

        function generateLogo() {
            const primaryColor = document.getElementById('primaryColor').value;
            const secondaryColor = document.getElementById('secondaryColor').value;
            
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, primaryColor);
            gradient.addColorStop(1, '#357ABD');
            
            // Background circle
            ctx.beginPath();
            ctx.arc(200, 200, 180, 0, 2 * Math.PI);
            ctx.fillStyle = gradient;
            ctx.fill();
            ctx.strokeStyle = '#2C5282';
            ctx.lineWidth = 6;
            ctx.stroke();
            
            // Robot head
            ctx.fillStyle = '#FFFFFF';
            ctx.fillRect(120, 100, 160, 120);
            ctx.strokeStyle = '#2C5282';
            ctx.lineWidth = 4;
            ctx.strokeRect(120, 100, 160, 120);
            
            // Robot eyes
            ctx.fillStyle = secondaryColor;
            ctx.beginPath();
            ctx.arc(160, 150, 16, 0, 2 * Math.PI);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(240, 150, 16, 0, 2 * Math.PI);
            ctx.fill();
            
            // Eye highlights
            ctx.fillStyle = '#FFFFFF';
            ctx.beginPath();
            ctx.arc(160, 150, 6, 0, 2 * Math.PI);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(240, 150, 6, 0, 2 * Math.PI);
            ctx.fill();
            
            // Robot mouth
            ctx.fillStyle = '#2C5282';
            ctx.fillRect(180, 180, 40, 16);
            
            // Robot antennas
            ctx.strokeStyle = '#2C5282';
            ctx.lineWidth = 6;
            ctx.beginPath();
            ctx.moveTo(150, 100);
            ctx.lineTo(150, 70);
            ctx.stroke();
            ctx.beginPath();
            ctx.moveTo(250, 100);
            ctx.lineTo(250, 70);
            ctx.stroke();
            
            // Antenna tips
            ctx.fillStyle = secondaryColor;
            ctx.beginPath();
            ctx.arc(150, 64, 8, 0, 2 * Math.PI);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(250, 64, 8, 0, 2 * Math.PI);
            ctx.fill();
            
            // Robot body
            ctx.fillStyle = '#FFFFFF';
            ctx.fillRect(140, 220, 120, 100);
            ctx.strokeStyle = '#2C5282';
            ctx.lineWidth = 4;
            ctx.strokeRect(140, 220, 120, 100);
            
            // Robot arms
            ctx.fillRect(90, 230, 40, 70);
            ctx.strokeRect(90, 230, 40, 70);
            ctx.fillRect(270, 230, 40, 70);
            ctx.strokeRect(270, 230, 40, 70);
            
            // Robot hands
            ctx.fillStyle = secondaryColor;
            ctx.beginPath();
            ctx.arc(110, 310, 16, 0, 2 * Math.PI);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(290, 310, 16, 0, 2 * Math.PI);
            ctx.fill();
            
            // WeBot text
            ctx.fillStyle = '#FFFFFF';
            ctx.font = 'bold 32px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('WeBot', 200, 50);
            
            // VPN text
            ctx.fillStyle = '#2C5282';
            ctx.font = '24px Arial';
            ctx.fillText('VPN Bot', 200, 380);
        }

        function downloadLogo() {
            const link = document.createElement('a');
            link.download = 'webot-logo.png';
            link.href = canvas.toDataURL();
            link.click();
        }

        // Generate initial logo
        generateLogo();
    </script>
</body>
</html>
