<?php
/**
 * WeBot Autoloader
 * 
 * This file initializes the WeBot application with proper autoloading,
 * environment configuration, and dependency injection container.
 * 
 * @package WeBot
 * @version 2.0
 * <AUTHOR> Team
 */

declare(strict_types=1);

// Define application constants
define('WEBOT_ACCESS', true);
define('WEBOT_VERSION', '2.0.0');
define('WEBOT_ROOT', __DIR__);
define('WEBOT_SRC', WEBOT_ROOT . '/src');
define('WEBOT_CONFIG', WEBOT_ROOT . '/config');
define('WEBOT_PUBLIC', WEBOT_ROOT . '/public');
define('WEBOT_STORAGE', WEBOT_ROOT . '/storage');

// Check PHP version
if (version_compare(PHP_VERSION, '8.1.0', '<')) {
    die('WeBot requires PHP 8.1 or higher. Current version: ' . PHP_VERSION);
}

// Check required extensions
$requiredExtensions = ['curl', 'json', 'pdo', 'mbstring', 'gd'];
foreach ($requiredExtensions as $extension) {
    if (!extension_loaded($extension)) {
        die("Required PHP extension '{$extension}' is not loaded.");
    }
}

// Load Composer autoloader with fallback
$autoloaderPath = __DIR__ . '/vendor/autoload.php';
if (file_exists($autoloaderPath)) {
    require_once $autoloaderPath;
    define('COMPOSER_LOADED', true);
} else {
    define('COMPOSER_LOADED', false);
    // Manual autoloader for development without composer
    spl_autoload_register(function ($class) {
        $file = WEBOT_SRC . '/' . str_replace('\\', '/', $class) . '.php';
        if (file_exists($file)) {
            require_once $file;
        }
    });

    // Create mock classes for missing dependencies
    if (!class_exists('Dotenv\Dotenv')) {
        // Create a simple mock Dotenv class
        if (!class_exists('MockDotenv')) {
            class MockDotenv {
                public static function createImmutable(string $path): self {
                    // Mock implementation - path parameter is intentionally ignored
                    unset($path);
                    return new self();
                }
                public function load(): array {
                    // Mock implementation - do nothing
                    return [];
                }
            }
        }
        class_alias('MockDotenv', 'Dotenv\Dotenv');
    }

    if (!class_exists('League\Container\Container')) {
        // League Container will use SimpleContainer fallback
    }
}

// Load environment variables
try {
    if (COMPOSER_LOADED && class_exists('Dotenv\Dotenv') && method_exists('Dotenv\Dotenv', 'createImmutable')) {
        $dotenv = \Dotenv\Dotenv::createImmutable(__DIR__);
        $dotenv->load();
    } else {
        // Manual .env loading fallback
        $envFile = __DIR__ . '/.env';
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                $line = trim($line);
                if (empty($line) || $line[0] === '#') {
                    continue;
                }
                if (strpos($line, '=') !== false) {
                    [$key, $value] = explode('=', $line, 2);
                    $key = trim($key);
                    $value = trim($value, '"\'');
                    if (!empty($key)) {
                        $_ENV[$key] = $value;
                        putenv("{$key}={$value}");
                    }
                }
            }
        }
    }
} catch (Exception $e) {
    // .env file is optional in production
    if (($_ENV['APP_ENV'] ?? 'production') !== 'production') {
        error_log('Environment file (.env) error: ' . $e->getMessage());
    }
}

// Set error reporting based on environment
$appEnv = $_ENV['APP_ENV'] ?? 'production';
$appDebug = filter_var($_ENV['APP_DEBUG'] ?? false, FILTER_VALIDATE_BOOLEAN);

if ($appEnv === 'development' || $appDebug) {
    error_reporting(E_ALL);
    ini_set('display_errors', '1');
    ini_set('display_startup_errors', '1');
} else {
    error_reporting(E_ERROR | E_WARNING | E_PARSE);
    ini_set('display_errors', '0');
    ini_set('display_startup_errors', '0');
}

// Set timezone
$timezone = $_ENV['APP_TIMEZONE'] ?? 'Asia/Tehran';
date_default_timezone_set($timezone);

// Set memory limit
$memoryLimit = $_ENV['MEMORY_LIMIT'] ?? '256M';
ini_set('memory_limit', $memoryLimit);

// Initialize error and exception handlers
set_error_handler(function ($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return false;
    }
    
    $logger = WeBot\Utils\Logger::getInstance();
    $logger->error("PHP Error: {$message}", [
        'file' => $file,
        'line' => $line,
        'severity' => $severity
    ]);
    
    if ($severity === E_ERROR || $severity === E_PARSE) {
        die('Fatal error occurred. Please check logs.');
    }
    
    return true;
});

set_exception_handler(function ($exception) {
    $logger = WeBot\Utils\Logger::getInstance();
    $logger->error("Uncaught Exception: " . $exception->getMessage(), [
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString()
    ]);
    
    if (($_ENV['APP_ENV'] ?? 'production') === 'development') {
        echo "<pre>";
        echo "Uncaught Exception: " . $exception->getMessage() . "\n";
        echo "File: " . $exception->getFile() . "\n";
        echo "Line: " . $exception->getLine() . "\n";
        echo "Trace:\n" . $exception->getTraceAsString();
        echo "</pre>";
    } else {
        echo "An error occurred. Please try again later.";
    }
});

// Register shutdown function for fatal errors
register_shutdown_function(function () {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        $logger = WeBot\Utils\Logger::getInstance();
        $logger->critical("Fatal Error: {$error['message']}", [
            'file' => $error['file'],
            'line' => $error['line']
        ]);
    }
});

// Initialize dependency injection container
try {
    // Create a simple container fallback if not already defined
    if (!class_exists('SimpleContainer')) {
        class SimpleContainer {
            private $services = [];

            public function add($name, $factory) {
                $this->services[$name] = $factory;
                return $this;
            }

            public function get($name) {
                if (!isset($this->services[$name])) {
                    throw new Exception("Service '$name' not found");
                }

                $factory = $this->services[$name];
                return is_callable($factory) ? $factory() : $factory;
            }

            public function has($name) {
                return isset($this->services[$name]);
            }
        }
    }

    // Use League Container if available, otherwise use SimpleContainer
    $container = (COMPOSER_LOADED && class_exists('League\Container\Container'))
        ? new \League\Container\Container()
        : new SimpleContainer();
    
    // Register core services
    $container->add('config', function () {
        return new WeBot\Core\Config();
    });
    
    $container->add('database', function () use ($container) {
        return new WeBot\Services\DatabaseService($container->get('config'));
    });
    
    $container->add('telegram', function () use ($container) {
        return new WeBot\Services\TelegramService($container->get('config'));
    });
    
    $container->add('logger', function () {
        return WeBot\Utils\Logger::getInstance();
    });
    
    // Store container globally for access
    $GLOBALS['container'] = $container;
    
} catch (Exception $e) {
    die('Failed to initialize dependency container: ' . $e->getMessage());
}

// Helper function to get container
function container() {
    return $GLOBALS['container'];
}

// Helper function to get service from container
function service(string $name) {
    return container()->get($name);
}

// Helper function to get config value
function config(string $key, $default = null) {
    return service('config')->get($key, $default);
}

// Helper function to get environment variable
function env(string $key, $default = null) {
    return $_ENV[$key] ?? $default;
}

// Helper function for logging
function logger(): WeBot\Utils\Logger {
    return service('logger');
}

// Helper function for database
function db(): WeBot\Services\DatabaseService {
    return service('database');
}

// Helper function for telegram
function telegram(): WeBot\Services\TelegramService {
    return service('telegram');
}

// Load helper functions
if (file_exists(__DIR__ . '/src/Utils/helpers.php')) {
    require_once __DIR__ . '/src/Utils/helpers.php';
}

// Legacy functions removed - functionality moved to src/Utils/ and src/Services/

// Validate environment variables in non-production environments
if ($appEnv !== 'production' && class_exists('WeBot\Core\EnvironmentValidator')) {
    try {
        $validator = new WeBot\Core\EnvironmentValidator();
        $result = $validator->validate();

        if (!$result['valid']) {
            $errors = implode("\n", $result['errors']);
            error_log("Environment validation failed:\n{$errors}");

            if ($appEnv === 'development') {
                echo "⚠️ Environment validation warnings:\n{$errors}\n";
            }
        }

        if (!empty($result['warnings']) && $appEnv === 'development') {
            $warnings = implode("\n", $result['warnings']);
            echo "⚠️ Environment warnings:\n{$warnings}\n";
        }
    } catch (Exception $e) {
        error_log("Environment validation error: " . $e->getMessage());
    }
}

// Application is now ready
if (defined('WEBOT_DEBUG') && WEBOT_DEBUG) {
    logger()->info('WeBot application initialized successfully', [
        'version' => WEBOT_VERSION,
        'php_version' => PHP_VERSION,
        'environment' => $appEnv,
        'memory_limit' => $memoryLimit,
        'timezone' => $timezone
    ]);
}
