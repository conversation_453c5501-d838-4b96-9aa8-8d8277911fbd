-- WeBot Database Migration: Create Services Table
-- Version: 1.0
-- Date: 2025-01-07

-- Create services table
CREATE TABLE IF NOT EXISTS services (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    
    -- Service identification
    service_name VA<PERSON>HAR(255) NOT NULL,
    service_type ENU<PERSON>('vpn', 'proxy', 'ssh', 'v2ray', 'trojan', 'shadowsocks') NOT NULL,
    panel_type ENUM('marzban', 'marzneshin', 'x-ui', 'hiddify', 'other') NOT NULL,
    
    -- Panel connection info
    panel_id BIGINT UNSIGNED NULL,
    panel_user_id VARCHAR(255) NULL,
    panel_username VARCHAR(255) NULL,
    
    -- Service configuration
    config_data JSON NULL,
    subscription_url TEXT NULL,
    qr_code_data TEXT NULL,
    
    -- Service limits
    data_limit BIGINT UNSIGNED NULL, -- in bytes
    data_used BIGINT UNSIGNED DEFAULT 0,
    connection_limit INT UNSIGNED DEFAULT 1,
    active_connections INT UNSIGNED DEFAULT 0,
    
    -- Service duration
    duration_days INT UNSIGNED NULL,
    expires_at TIMESTAMP NULL,
    auto_renew BOOLEAN DEFAULT FALSE,
    
    -- Service status
    status ENUM('active', 'inactive', 'suspended', 'expired', 'cancelled') DEFAULT 'active',
    last_connected_at TIMESTAMP NULL,
    last_ip VARCHAR(45) NULL,
    
    -- Traffic statistics
    upload_traffic BIGINT UNSIGNED DEFAULT 0,
    download_traffic BIGINT UNSIGNED DEFAULT 0,
    total_traffic BIGINT UNSIGNED DEFAULT 0,
    
    -- Renewal info
    renewal_price DECIMAL(10,2) NULL,
    next_renewal_at TIMESTAMP NULL,
    renewal_attempts INT UNSIGNED DEFAULT 0,
    
    -- Notes and metadata
    notes TEXT NULL,
    metadata JSON NULL,
    
    -- Timestamps
    activated_at TIMESTAMP NULL,
    suspended_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    -- Indexes
    INDEX idx_user_id (user_id),
    INDEX idx_service_type (service_type),
    INDEX idx_panel_type (panel_type),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at),
    INDEX idx_panel_user_id (panel_user_id),
    INDEX idx_created_at (created_at),
    
    -- Foreign key constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create service_usage_logs table for detailed usage tracking
CREATE TABLE IF NOT EXISTS service_usage_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    service_id BIGINT UNSIGNED NOT NULL,
    
    -- Usage details
    session_start TIMESTAMP NOT NULL,
    session_end TIMESTAMP NULL,
    duration_seconds INT UNSIGNED NULL,
    
    -- Traffic data
    upload_bytes BIGINT UNSIGNED DEFAULT 0,
    download_bytes BIGINT UNSIGNED DEFAULT 0,
    total_bytes BIGINT UNSIGNED DEFAULT 0,
    
    -- Connection info
    client_ip VARCHAR(45) NULL,
    server_ip VARCHAR(45) NULL,
    protocol VARCHAR(20) NULL,
    user_agent TEXT NULL,
    
    -- Location data
    country_code VARCHAR(2) NULL,
    city VARCHAR(100) NULL,
    
    -- Metadata
    metadata JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_service_id (service_id),
    INDEX idx_session_start (session_start),
    INDEX idx_client_ip (client_ip),
    INDEX idx_created_at (created_at),
    
    -- Foreign key constraints
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create service_renewals table
CREATE TABLE IF NOT EXISTS service_renewals (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    service_id BIGINT UNSIGNED NOT NULL,
    payment_id BIGINT UNSIGNED NULL,
    
    -- Renewal details
    renewal_type ENUM('manual', 'auto', 'admin') NOT NULL,
    old_expires_at TIMESTAMP NULL,
    new_expires_at TIMESTAMP NULL,
    extended_days INT UNSIGNED NOT NULL,
    
    -- Pricing
    renewal_price DECIMAL(10,2) NOT NULL,
    discount_applied DECIMAL(10,2) DEFAULT 0.00,
    
    -- Status
    status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    failure_reason TEXT NULL,
    
    -- Metadata
    notes TEXT NULL,
    processed_by BIGINT UNSIGNED NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_service_id (service_id),
    INDEX idx_payment_id (payment_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    
    -- Foreign key constraints
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
    FOREIGN KEY (payment_id) REFERENCES payments(id) ON DELETE SET NULL,
    FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create triggers for service management
DELIMITER $$
CREATE TRIGGER services_update_traffic 
    BEFORE UPDATE ON services 
    FOR EACH ROW 
BEGIN
    -- Update total traffic when upload or download changes
    IF NEW.upload_traffic != OLD.upload_traffic OR NEW.download_traffic != OLD.download_traffic THEN
        SET NEW.total_traffic = NEW.upload_traffic + NEW.download_traffic;
        SET NEW.data_used = NEW.total_traffic;
    END IF;
    
    -- Auto-suspend if data limit exceeded
    IF NEW.data_limit IS NOT NULL AND NEW.data_used >= NEW.data_limit AND NEW.status = 'active' THEN
        SET NEW.status = 'suspended';
        SET NEW.suspended_at = CURRENT_TIMESTAMP;
    END IF;
    
    -- Auto-expire if expiration date passed
    IF NEW.expires_at IS NOT NULL AND NEW.expires_at <= CURRENT_TIMESTAMP AND NEW.status = 'active' THEN
        SET NEW.status = 'expired';
    END IF;
END$$
DELIMITER ;

-- Create trigger for service activation
DELIMITER $$
CREATE TRIGGER services_activation 
    BEFORE UPDATE ON services 
    FOR EACH ROW 
BEGIN
    -- Set activation timestamp when status changes to active
    IF OLD.status != 'active' AND NEW.status = 'active' THEN
        SET NEW.activated_at = CURRENT_TIMESTAMP;
        SET NEW.suspended_at = NULL;
    END IF;
    
    -- Set suspension timestamp when status changes to suspended
    IF OLD.status != 'suspended' AND NEW.status = 'suspended' THEN
        SET NEW.suspended_at = CURRENT_TIMESTAMP;
    END IF;
END$$
DELIMITER ;
