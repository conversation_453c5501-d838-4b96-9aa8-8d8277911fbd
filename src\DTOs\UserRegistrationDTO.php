<?php

declare(strict_types=1);

namespace WeBot\DTOs;

/**
 * User Registration DTO
 *
 * Data transfer object for user registration process.
 * Handles validation and data transformation for new user creation.
 *
 * @package WeBot\DTOs
 * @version 2.0
 */
class UserRegistrationDTO extends BaseDTO
{
    /**
     * Validate registration data
     */
    protected function validate(): void
    {
        $this->validateRequired(['telegram_id', 'first_name']);
        $this->validateTelegramId('telegram_id');
        $this->validateLength('first_name', 1, 100);
        $this->validateLength('last_name', 0, 100);
        $this->validateLength('username', 0, 50);

        if ($this->has('phone')) {
            $this->validatePhone('phone');
        }

        if ($this->has('email')) {
            $this->validateEmail('email');
        }
    }

    /**
     * Get validation rules
     */
    protected function rules(): array
    {
        return [
            'telegram_id' => 'required|telegram_id',
            'first_name' => 'required|string|max:100',
            'last_name' => 'string|max:100',
            'username' => 'string|max:50',
            'phone' => 'phone',
            'email' => 'email',
            'language_code' => 'string|max:10',
            'is_premium' => 'boolean'
        ];
    }

    /**
     * Get field labels
     */
    protected function labels(): array
    {
        return [
            'telegram_id' => 'شناسه تلگرام',
            'first_name' => 'نام',
            'last_name' => 'نام خانوادگی',
            'username' => 'نام کاربری',
            'phone' => 'شماره تلفن',
            'email' => 'ایمیل',
            'language_code' => 'کد زبان',
            'is_premium' => 'پریمیوم'
        ];
    }

    /**
     * Get telegram ID
     */
    public function getTelegramId(): int
    {
        return (int)$this->get('telegram_id');
    }

    /**
     * Get first name
     */
    public function getFirstName(): string
    {
        return (string)$this->get('first_name');
    }

    /**
     * Get last name
     */
    public function getLastName(): ?string
    {
        return $this->get('last_name');
    }

    /**
     * Get username
     */
    public function getUsername(): ?string
    {
        return $this->get('username');
    }

    /**
     * Get phone number
     */
    public function getPhone(): ?string
    {
        return $this->get('phone');
    }

    /**
     * Get email
     */
    public function getEmail(): ?string
    {
        return $this->get('email');
    }

    /**
     * Get language code
     */
    public function getLanguageCode(): string
    {
        return $this->get('language_code', 'fa');
    }

    /**
     * Check if user is premium
     */
    public function isPremium(): bool
    {
        return (bool)$this->get('is_premium', false);
    }

    /**
     * Get full name
     */
    public function getFullName(): string
    {
        $firstName = $this->getFirstName();
        $lastName = $this->getLastName();

        return $lastName ? "{$firstName} {$lastName}" : $firstName;
    }

    /**
     * Get display name
     */
    public function getDisplayName(): string
    {
        $username = $this->getUsername();
        return $username ? "@{$username}" : $this->getFullName();
    }

    /**
     * Create from Telegram user data
     */
    public static function fromTelegramUser(array $telegramUser): self
    {
        return new self([
            'telegram_id' => $telegramUser['id'] ?? null,
            'first_name' => $telegramUser['first_name'] ?? null,
            'last_name' => $telegramUser['last_name'] ?? null,
            'username' => $telegramUser['username'] ?? null,
            'language_code' => $telegramUser['language_code'] ?? 'fa',
            'is_premium' => $telegramUser['is_premium'] ?? false
        ]);
    }

    /**
     * Convert to database array
     */
    public function toDatabaseArray(): array
    {
        return [
            'telegram_id' => $this->getTelegramId(),
            'first_name' => $this->getFirstName(),
            'last_name' => $this->getLastName(),
            'username' => $this->getUsername(),
            'phone' => $this->getPhone(),
            'email' => $this->getEmail(),
            'language_code' => $this->getLanguageCode(),
            'is_premium' => $this->isPremium(),
            'status' => 'active',
            'step' => 'completed',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
    }
}
