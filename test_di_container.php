<?php
/**
 * Dependency Injection Container Test for WeBot
 * 
 * This script tests the DI container functionality and verifies
 * that all services are properly registered and can be resolved.
 */

declare(strict_types=1);

echo "=== WeBot DI Container Test ===\n\n";

// Load the application
try {
    require_once __DIR__ . '/autoload.php';
    echo "✅ Application loaded successfully\n\n";
} catch (Exception $e) {
    echo "❌ Failed to load application: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 1: Container availability
echo "1. Container Availability Test:\n";
try {
    $container = container();
    echo "   ✅ Container is available via helper function\n";
    
    if (method_exists($container, 'get') && method_exists($container, 'has')) {
        echo "   ✅ Container has required methods (get, has)\n";
    } else {
        echo "   ❌ Container missing required methods\n";
        exit(1);
    }
} catch (Exception $e) {
    echo "   ❌ Container not available: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 2: Core services registration
echo "\n2. Core Services Registration Test:\n";
$coreServices = [
    'config' => 'Configuration service',
    'database' => 'Database service',
    'telegram' => 'Telegram service',
    'logger' => 'Logger service'
];

$servicesOk = true;
foreach ($coreServices as $serviceName => $description) {
    try {
        if ($container->has($serviceName)) {
            echo "   ✅ {$serviceName}: Registered - {$description}\n";
        } else {
            echo "   ❌ {$serviceName}: Not registered - {$description}\n";
            $servicesOk = false;
        }
    } catch (Exception $e) {
        echo "   ❌ {$serviceName}: Error checking registration - {$e->getMessage()}\n";
        $servicesOk = false;
    }
}

// Test 3: Service resolution
echo "\n3. Service Resolution Test:\n";
$resolutionOk = true;

foreach ($coreServices as $serviceName => $description) {
    try {
        $service = $container->get($serviceName);
        if ($service !== null) {
            echo "   ✅ {$serviceName}: Resolved successfully\n";
        } else {
            echo "   ❌ {$serviceName}: Resolved to null\n";
            $resolutionOk = false;
        }
    } catch (Exception $e) {
        echo "   ❌ {$serviceName}: Resolution failed - {$e->getMessage()}\n";
        $resolutionOk = false;
    }
}

// Test 4: Service helper functions
echo "\n4. Service Helper Functions Test:\n";
$helpersOk = true;

try {
    $configService = service('config');
    echo "   ✅ service('config') helper works\n";
} catch (Exception $e) {
    echo "   ❌ service('config') helper failed: " . $e->getMessage() . "\n";
    $helpersOk = false;
}

try {
    $loggerService = logger();
    echo "   ✅ logger() helper works\n";
} catch (Exception $e) {
    echo "   ❌ logger() helper failed: " . $e->getMessage() . "\n";
    $helpersOk = false;
}

try {
    $dbService = db();
    echo "   ✅ db() helper works\n";
} catch (Exception $e) {
    echo "   ❌ db() helper failed: " . $e->getMessage() . "\n";
    $helpersOk = false;
}

try {
    $telegramService = telegram();
    echo "   ✅ telegram() helper works\n";
} catch (Exception $e) {
    echo "   ❌ telegram() helper failed: " . $e->getMessage() . "\n";
    $helpersOk = false;
}

// Test 5: Singleton behavior
echo "\n5. Singleton Behavior Test:\n";
$singletonOk = true;

try {
    $config1 = service('config');
    $config2 = service('config');
    
    if ($config1 === $config2) {
        echo "   ✅ Config service maintains singleton behavior\n";
    } else {
        echo "   ⚠️  Config service creates new instances (not singleton)\n";
    }
} catch (Exception $e) {
    echo "   ❌ Singleton test failed: " . $e->getMessage() . "\n";
    $singletonOk = false;
}

// Test 6: Service functionality
echo "\n6. Service Functionality Test:\n";
$functionalityOk = true;

try {
    $config = service('config');
    if (method_exists($config, 'get')) {
        echo "   ✅ Config service has get() method\n";
    } else {
        echo "   ❌ Config service missing get() method\n";
        $functionalityOk = false;
    }
} catch (Exception $e) {
    echo "   ❌ Config service functionality test failed: " . $e->getMessage() . "\n";
    $functionalityOk = false;
}

try {
    $logger = logger();
    if (method_exists($logger, 'info')) {
        echo "   ✅ Logger service has info() method\n";
    } else {
        echo "   ❌ Logger service missing info() method\n";
        $functionalityOk = false;
    }
} catch (Exception $e) {
    echo "   ❌ Logger service functionality test failed: " . $e->getMessage() . "\n";
    $functionalityOk = false;
}

echo "\n=== Overall Status ===\n";
if ($servicesOk && $resolutionOk && $helpersOk && $functionalityOk) {
    echo "✅ DI Container is working perfectly!\n";
    echo "ℹ️  All services are properly registered and can be resolved\n";
    exit(0);
} else {
    echo "❌ DI Container has some issues.\n";
    echo "\n🔧 To fix DI Container issues:\n";
    echo "   1. Check service registration in autoload.php\n";
    echo "   2. Verify container implementation\n";
    echo "   3. Ensure all service classes exist and are autoloadable\n";
    echo "   4. Check for circular dependencies\n";
    exit(1);
}
