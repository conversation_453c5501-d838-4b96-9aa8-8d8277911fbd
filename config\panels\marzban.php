<?php

declare(strict_types=1);

/**
 * Marzban Panel Configuration
 * 
 * Configuration settings for Marzban panel integration
 * including authentication, API endpoints, and features.
 * 
 * @package WeBot\Config\Panels
 * @version 2.0
 */

return [
    /*
    |--------------------------------------------------------------------------
    | Marzban Panel Settings
    |--------------------------------------------------------------------------
    */
    'enabled' => (bool)($_ENV['MARZBAN_ENABLED'] ?? false),
    'name' => 'Marzban',
    'description' => 'Marzban VPN Panel Integration',
    'version' => '0.4.0',

    /*
    |--------------------------------------------------------------------------
    | Connection Settings
    |--------------------------------------------------------------------------
    */
    'connection' => [
        'url' => $_ENV['MARZBAN_URL'] ?? 'https://panel.example.com',
        'api_version' => 'v1',
        'timeout' => (int)($_ENV['MARZBAN_TIMEOUT'] ?? 30),
        'verify_ssl' => (bool)($_ENV['MARZBAN_VERIFY_SSL'] ?? true),
        'retry_attempts' => (int)($_ENV['MARZBAN_RETRY_ATTEMPTS'] ?? 3),
        'retry_delay' => (int)($_ENV['MARZBAN_RETRY_DELAY'] ?? 1000), // milliseconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Authentication
    |--------------------------------------------------------------------------
    */
    'auth' => [
        'username' => $_ENV['MARZBAN_USERNAME'] ?? '',
        'password' => $_ENV['MARZBAN_PASSWORD'] ?? '',
        'token_lifetime' => (int)($_ENV['MARZBAN_TOKEN_LIFETIME'] ?? 3600), // seconds
        'auto_refresh' => (bool)($_ENV['MARZBAN_AUTO_REFRESH'] ?? true),
        'refresh_threshold' => (int)($_ENV['MARZBAN_REFRESH_THRESHOLD'] ?? 300), // seconds before expiry
    ],

    /*
    |--------------------------------------------------------------------------
    | Default User Settings
    |--------------------------------------------------------------------------
    */
    'defaults' => [
        'data_limit' => (int)($_ENV['MARZBAN_DEFAULT_DATA_LIMIT'] ?? 50 * 1024 * 1024 * 1024), // 50GB
        'expire_days' => (int)($_ENV['MARZBAN_DEFAULT_EXPIRE_DAYS'] ?? 30),
        'data_limit_reset_strategy' => $_ENV['MARZBAN_DEFAULT_RESET_STRATEGY'] ?? 'no_reset',
        'status' => $_ENV['MARZBAN_DEFAULT_STATUS'] ?? 'active',
        'proxies' => [
            'vmess' => (bool)($_ENV['MARZBAN_DEFAULT_VMESS'] ?? true),
            'vless' => (bool)($_ENV['MARZBAN_DEFAULT_VLESS'] ?? true),
            'trojan' => (bool)($_ENV['MARZBAN_DEFAULT_TROJAN'] ?? false),
            'shadowsocks' => (bool)($_ENV['MARZBAN_DEFAULT_SS'] ?? false),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Supported Protocols
    |--------------------------------------------------------------------------
    */
    'protocols' => [
        'vmess' => [
            'enabled' => true,
            'security' => 'auto',
            'alter_id' => 0,
        ],
        'vless' => [
            'enabled' => true,
            'flow' => '',
            'encryption' => 'none',
        ],
        'trojan' => [
            'enabled' => false,
            'password_length' => 32,
        ],
        'shadowsocks' => [
            'enabled' => false,
            'method' => 'chacha20-ietf-poly1305',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Client Configuration
    |--------------------------------------------------------------------------
    */
    'clients' => [
        'supported_types' => [
            'v2ray' => 'V2Ray/V2RayN',
            'clash' => 'Clash/ClashX',
            'sing-box' => 'Sing-box',
            'json' => 'JSON Config',
            'uri' => 'URI Format',
        ],
        'default_type' => 'v2ray',
        'subscription_format' => 'base64',
        'include_stats' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Features Configuration
    |--------------------------------------------------------------------------
    */
    'features' => [
        'user_management' => true,
        'traffic_monitoring' => true,
        'subscription_urls' => true,
        'config_generation' => true,
        'bulk_operations' => true,
        'user_statistics' => true,
        'traffic_reset' => true,
        'user_suspension' => true,
        'expiry_management' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    */
    'rate_limiting' => [
        'enabled' => (bool)($_ENV['MARZBAN_RATE_LIMITING'] ?? true),
        'requests_per_minute' => (int)($_ENV['MARZBAN_REQUESTS_PER_MINUTE'] ?? 60),
        'burst_limit' => (int)($_ENV['MARZBAN_BURST_LIMIT'] ?? 10),
        'cooldown_period' => (int)($_ENV['MARZBAN_COOLDOWN_PERIOD'] ?? 60), // seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Caching
    |--------------------------------------------------------------------------
    */
    'caching' => [
        'enabled' => (bool)($_ENV['MARZBAN_CACHING'] ?? true),
        'user_info_ttl' => (int)($_ENV['MARZBAN_USER_CACHE_TTL'] ?? 300), // 5 minutes
        'system_info_ttl' => (int)($_ENV['MARZBAN_SYSTEM_CACHE_TTL'] ?? 60), // 1 minute
        'config_cache_ttl' => (int)($_ENV['MARZBAN_CONFIG_CACHE_TTL'] ?? 3600), // 1 hour
        'cache_prefix' => 'marzban:',
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring & Health Checks
    |--------------------------------------------------------------------------
    */
    'monitoring' => [
        'health_check_interval' => (int)($_ENV['MARZBAN_HEALTH_CHECK_INTERVAL'] ?? 300), // 5 minutes
        'health_check_timeout' => (int)($_ENV['MARZBAN_HEALTH_CHECK_TIMEOUT'] ?? 10),
        'alert_on_failure' => (bool)($_ENV['MARZBAN_ALERT_ON_FAILURE'] ?? true),
        'max_consecutive_failures' => (int)($_ENV['MARZBAN_MAX_FAILURES'] ?? 3),
        'metrics_collection' => (bool)($_ENV['MARZBAN_METRICS'] ?? true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Error Handling
    |--------------------------------------------------------------------------
    */
    'error_handling' => [
        'log_errors' => true,
        'retry_on_failure' => true,
        'fallback_enabled' => (bool)($_ENV['MARZBAN_FALLBACK'] ?? false),
        'circuit_breaker' => [
            'enabled' => (bool)($_ENV['MARZBAN_CIRCUIT_BREAKER'] ?? true),
            'failure_threshold' => 5,
            'recovery_timeout' => 60, // seconds
            'half_open_max_calls' => 3,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    */
    'security' => [
        'encrypt_credentials' => (bool)($_ENV['MARZBAN_ENCRYPT_CREDENTIALS'] ?? true),
        'validate_ssl_cert' => (bool)($_ENV['MARZBAN_VALIDATE_SSL'] ?? true),
        'allowed_ips' => explode(',', $_ENV['MARZBAN_ALLOWED_IPS'] ?? ''),
        'api_key_rotation' => (bool)($_ENV['MARZBAN_API_KEY_ROTATION'] ?? false),
        'audit_logging' => (bool)($_ENV['MARZBAN_AUDIT_LOGGING'] ?? true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup & Sync
    |--------------------------------------------------------------------------
    */
    'backup' => [
        'enabled' => (bool)($_ENV['MARZBAN_BACKUP_ENABLED'] ?? true),
        'sync_interval' => (int)($_ENV['MARZBAN_SYNC_INTERVAL'] ?? 3600), // 1 hour
        'backup_user_data' => true,
        'backup_configurations' => true,
        'retention_days' => (int)($_ENV['MARZBAN_BACKUP_RETENTION'] ?? 7),
    ],

    /*
    |--------------------------------------------------------------------------
    | Advanced Settings
    |--------------------------------------------------------------------------
    */
    'advanced' => [
        'custom_headers' => [
            'User-Agent' => 'WeBot/2.0 Marzban-Adapter',
            'X-Client-Version' => '2.0.0',
        ],
        'webhook_support' => (bool)($_ENV['MARZBAN_WEBHOOK_SUPPORT'] ?? false),
        'webhook_url' => $_ENV['MARZBAN_WEBHOOK_URL'] ?? '',
        'webhook_secret' => $_ENV['MARZBAN_WEBHOOK_SECRET'] ?? '',
        'custom_endpoints' => [
            // Custom API endpoints if needed
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Development & Testing
    |--------------------------------------------------------------------------
    */
    'development' => [
        'debug_mode' => (bool)($_ENV['MARZBAN_DEBUG'] ?? false),
        'log_requests' => (bool)($_ENV['MARZBAN_LOG_REQUESTS'] ?? false),
        'log_responses' => (bool)($_ENV['MARZBAN_LOG_RESPONSES'] ?? false),
        'mock_responses' => (bool)($_ENV['MARZBAN_MOCK'] ?? false),
        'test_credentials' => [
            'username' => $_ENV['MARZBAN_TEST_USERNAME'] ?? 'test',
            'password' => $_ENV['MARZBAN_TEST_PASSWORD'] ?? 'test',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Integration Settings
    |--------------------------------------------------------------------------
    */
    'integration' => [
        'auto_create_users' => (bool)($_ENV['MARZBAN_AUTO_CREATE_USERS'] ?? true),
        'auto_suspend_expired' => (bool)($_ENV['MARZBAN_AUTO_SUSPEND_EXPIRED'] ?? true),
        'auto_delete_expired' => (bool)($_ENV['MARZBAN_AUTO_DELETE_EXPIRED'] ?? false),
        'sync_user_status' => (bool)($_ENV['MARZBAN_SYNC_USER_STATUS'] ?? true),
        'notification_events' => [
            'user_created' => true,
            'user_suspended' => true,
            'user_expired' => true,
            'quota_exceeded' => true,
            'panel_offline' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Tuning
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'connection_pooling' => (bool)($_ENV['MARZBAN_CONNECTION_POOLING'] ?? true),
        'max_connections' => (int)($_ENV['MARZBAN_MAX_CONNECTIONS'] ?? 10),
        'keep_alive' => (bool)($_ENV['MARZBAN_KEEP_ALIVE'] ?? true),
        'compression' => (bool)($_ENV['MARZBAN_COMPRESSION'] ?? true),
        'batch_operations' => (bool)($_ENV['MARZBAN_BATCH_OPERATIONS'] ?? true),
        'async_operations' => (bool)($_ENV['MARZBAN_ASYNC_OPERATIONS'] ?? false),
    ],
];
