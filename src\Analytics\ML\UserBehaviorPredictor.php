<?php

declare(strict_types=1);

namespace WeBot\Analytics\ML;

use WeBot\Core\CacheManager;
use WeBot\Services\DatabaseService;
use WeBot\Utils\Logger;
use Monolog\Logger as MonologLogger;
use WeBot\Exceptions\WeBotException;
use WeBot\Analytics\ML\MachineLearningEngine;

/**
 * User Behavior Predictor
 *
 * Predicts user behavior patterns including churn risk, lifetime value,
 * next actions, and engagement levels using machine learning.
 *
 * @package WeBot\Analytics\ML
 * @version 2.0
 */
class UserBehaviorPredictor
{
    private CacheManager $cache;
    private DatabaseService $database;
    private MonologLogger $logger;
    private MachineLearningEngine $mlEngine;
    private array $config;
    private array $behaviorModels = [];
    private array $userSegments = [];

    public function __construct(
        CacheManager $cache,
        DatabaseService $database,
        MachineLearningEngine $mlEngine,
        array $config = []
    ) {
        $this->cache = $cache;
        $this->database = $database;
        $this->logger = Logger::getInstance();
        $this->mlEngine = $mlEngine;
        $this->config = array_merge($this->getDefaultConfig(), $config);

        $this->loadBehaviorModels();
        $this->loadUserSegments();
    }

    /**
     * Predict user churn risk
     */
    public function predictChurnRisk(int $userId): array
    {
        try {
            $cacheKey = "behavior:churn_risk:{$userId}";
            $cached = $this->cache->get($cacheKey);

            if ($cached !== null) {
                return $cached;
            }

            // Get user behavior features
            $features = $this->extractChurnFeatures($userId);

            // Apply churn prediction model
            $prediction = $this->mlEngine->predictUserBehavior($userId, 'churn_risk');

            // Enhance with behavioral analysis
            $behaviorAnalysis = $this->analyzeChurnBehavior($features);

            // Calculate intervention recommendations
            $interventions = $this->generateChurnInterventions($prediction, $behaviorAnalysis);

            $result = [
                'user_id' => $userId,
                'churn_risk_score' => $prediction['risk_score'],
                'risk_level' => $prediction['risk_level'],
                'confidence' => $prediction['confidence'],
                'key_factors' => $prediction['factors'],
                'behavior_analysis' => $behaviorAnalysis,
                'interventions' => $interventions,
                'predicted_churn_date' => $this->predictChurnDate($prediction['risk_score']),
                'retention_probability' => 1 - $prediction['risk_score'],
                'analyzed_at' => time()
            ];

            // Cache result
            $this->cache->set($cacheKey, $result, $this->config['prediction_cache_ttl']);

            return $result;
        } catch (\Exception $e) {
            $this->logger->error("Churn prediction failed", [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Predict user lifetime value
     */
    public function predictLifetimeValue(int $userId): array
    {
        try {
            $cacheKey = "behavior:ltv:{$userId}";
            $cached = $this->cache->get($cacheKey);

            if ($cached !== null) {
                return $cached;
            }

            // Get LTV features
            $features = $this->extractLTVFeatures($userId);

            // Apply LTV prediction model
            $prediction = $this->mlEngine->predictUserBehavior($userId, 'lifetime_value');

            // Calculate value segments
            $valueSegment = $this->categorizeValueSegment($prediction['predicted_ltv']);

            // Generate value optimization strategies
            $optimizations = $this->generateValueOptimizations($prediction, $features);

            $result = [
                'user_id' => $userId,
                'predicted_ltv' => $prediction['predicted_ltv'],
                'confidence' => $prediction['confidence'],
                'value_segment' => $valueSegment,
                'current_value' => $features['current_total_spent'],
                'potential_value' => $prediction['predicted_ltv'] - $features['current_total_spent'],
                'growth_factors' => $prediction['contributing_factors'],
                'optimization_strategies' => $optimizations,
                'time_horizon_months' => $this->config['ltv_time_horizon'],
                'analyzed_at' => time()
            ];

            // Cache result
            $this->cache->set($cacheKey, $result, $this->config['prediction_cache_ttl']);

            return $result;
        } catch (\Exception $e) {
            $this->logger->error("LTV prediction failed", [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Predict next user action
     */
    public function predictNextAction(int $userId): array
    {
        try {
            $userHistory = $this->getUserActionHistory($userId);
            $currentContext = $this->getCurrentUserContext($userId);
            $behaviorPatterns = $this->identifyBehaviorPatterns($userHistory);

            // Predict most likely next actions
            $actionProbabilities = $this->calculateActionProbabilities($userHistory, $currentContext);

            // Rank actions by probability
            arsort($actionProbabilities);
            $topActions = array_slice($actionProbabilities, 0, 5, true);

            // Calculate timing predictions
            $timingPredictions = $this->predictActionTiming($topActions, $behaviorPatterns);

            return [
                'user_id' => $userId,
                'predicted_actions' => $topActions,
                'timing_predictions' => $timingPredictions,
                'behavior_patterns' => $behaviorPatterns,
                'context_factors' => $currentContext,
                'confidence_score' => $this->calculatePredictionConfidence($userHistory),
                'predicted_at' => time()
            ];
        } catch (\Exception $e) {
            $this->logger->error("Next action prediction failed", [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Predict user engagement level
     */
    public function predictEngagement(int $userId): array
    {
        try {
            $engagementFeatures = $this->extractEngagementFeatures($userId);
            $prediction = $this->mlEngine->predictUserBehavior($userId, 'engagement_score');

            $engagementLevel = $this->categorizeEngagementLevel($prediction['engagement_score']);
            $engagementTrends = $this->analyzeEngagementTrends($engagementFeatures);
            $improvementStrategies = $this->generateEngagementStrategies($prediction['engagement_score'], $engagementTrends);

            return [
                'user_id' => $userId,
                'engagement_score' => $prediction['engagement_score'],
                'engagement_level' => $engagementLevel,
                'trends' => $engagementTrends,
                'key_drivers' => $this->identifyEngagementDrivers($engagementFeatures),
                'improvement_strategies' => $improvementStrategies,
                'predicted_engagement_change' => $this->predictEngagementChange($engagementFeatures, $engagementTrends),
                'analyzed_at' => time()
            ];
        } catch (\Exception $e) {
            $this->logger->error("Engagement prediction failed", [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Segment users based on behavior
     */
    public function segmentUsers(array $criteria = []): array
    {
        try {
            $cacheKey = 'behavior:user_segments:' . md5(serialize($criteria));
            $cached = $this->cache->get($cacheKey);

            if ($cached !== null) {
                return $cached;
            }

            // Get users for segmentation
            $users = $this->getUsersForSegmentation($criteria);

            // Extract behavioral features for all users
            $userFeatures = [];
            foreach ($users as $user) {
                $userFeatures[$user['userid']] = $this->extractBehaviorFeatures($user['userid']);
            }

            // Apply clustering algorithm
            $segments = $this->mlEngine->segmentUsers($criteria);

            // Enhance segments with behavioral insights
            $enhancedSegments = $this->enhanceSegmentsWithBehavior($segments, $userFeatures);

            // Generate segment strategies
            $segmentStrategies = $this->generateSegmentStrategies($enhancedSegments);

            $result = [
                'segments' => $enhancedSegments,
                'strategies' => $segmentStrategies,
                'total_users' => count($users),
                'segmentation_criteria' => $criteria,
                'segment_quality_score' => $this->calculateSegmentQuality($enhancedSegments),
                'generated_at' => time()
            ];

            // Cache result
            $this->cache->set($cacheKey, $result, $this->config['segment_cache_ttl']);

            return $result;
        } catch (\Exception $e) {
            $this->logger->error("User segmentation failed", [
                'criteria' => $criteria,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Get behavior prediction dashboard
     */
    public function getBehaviorDashboard(int $days = 30): array
    {
        $cacheKey = "behavior:dashboard:{$days}";
        $cached = $this->cache->get($cacheKey);

        if ($cached !== null) {
            return $cached;
        }

        $dashboard = [
            'overview' => $this->getBehaviorOverview($days),
            'churn_analysis' => $this->getChurnAnalysis($days),
            'ltv_analysis' => $this->getLTVAnalysis($days),
            'engagement_trends' => $this->getEngagementTrends($days),
            'behavior_patterns' => $this->getBehaviorPatterns($days),
            'prediction_accuracy' => $this->getPredictionAccuracy(),
            'segment_performance' => $this->getSegmentPerformance($days),
            'recommendations' => $this->getBusinessRecommendations($days),
            'generated_at' => time()
        ];

        $this->cache->set($cacheKey, $dashboard, $this->config['dashboard_cache_ttl']);

        return $dashboard;
    }

    /**
     * Extract churn features
     */
    private function extractChurnFeatures(int $userId): array
    {
        $user = $this->database->fetchRow("SELECT * FROM users WHERE userid = ?", [$userId]);
        if (!$user) {
            throw new WeBotException("User not found: {$userId}");
        }

        $recentActivity = $this->getRecentUserActivity($userId, 30); // Last 30 days
        $paymentHistory = $this->getUserPaymentHistory($userId, 90); // Last 90 days
        $serviceUsage = $this->getUserServiceUsage($userId, 30); // Last 30 days

        return [
            'days_since_registration' => (time() - strtotime($user['created_at'])) / 86400,
            'days_since_last_login' => $recentActivity['days_since_last_login'] ?? 999,
            'total_sessions' => $recentActivity['total_sessions'] ?? 0,
            'avg_session_duration' => $recentActivity['avg_session_duration'] ?? 0,
            'total_payments' => count($paymentHistory),
            'days_since_last_payment' => $this->getDaysSinceLastPayment($paymentHistory),
            'payment_frequency_decline' => $this->calculatePaymentFrequencyDecline($paymentHistory),
            'service_usage_decline' => $this->calculateServiceUsageDecline($serviceUsage),
            'support_ticket_ratio' => $this->calculateSupportTicketRatio($recentActivity),
            'feature_adoption_rate' => $this->calculateFeatureAdoptionRate($serviceUsage),
            'social_engagement' => $this->calculateSocialEngagement($recentActivity)
        ];
    }

    /**
     * Extract LTV features
     */
    private function extractLTVFeatures(int $userId): array
    {
        $user = $this->database->fetchRow("SELECT * FROM users WHERE userid = ?", [$userId]);
        $paymentHistory = $this->getUserPaymentHistory($userId);
        $serviceHistory = $this->getUserServiceHistory($userId);
        $recentActivity = $this->getRecentUserActivity($userId);

        return [
            'current_total_spent' => $user['total_spent'] ?? 0,
            'avg_monthly_spend' => $this->calculateAvgMonthlySpend($paymentHistory),
            'payment_consistency' => $this->calculatePaymentConsistency($paymentHistory),
            'service_diversity' => count(array_unique(array_column($serviceHistory, 'plan_id'))),
            'upgrade_tendency' => $this->calculateUpgradeTendency($serviceHistory),
            'referral_activity' => $this->calculateReferralActivity($recentActivity),
            'engagement_score' => $this->calculateCurrentEngagementScore($recentActivity),
            'tenure_months' => (time() - strtotime($user['created_at'])) / (86400 * 30),
            'support_satisfaction' => $this->calculateSupportSatisfaction($recentActivity)
        ];
    }

    /**
     * Extract engagement features
     */
    private function extractEngagementFeatures(int $userId): array
    {
        $activityData = $this->getDetailedUserActivity($userId, 30);

        return [
            'login_frequency' => $activityData['login_frequency'],
            'session_depth' => $activityData['avg_pages_per_session'],
            'feature_usage_breadth' => $activityData['unique_features_used'],
            'time_spent_daily' => $activityData['avg_daily_time'],
            'interaction_rate' => $activityData['interactions_per_session'],
            'content_consumption' => $activityData['content_views'],
            'social_interactions' => $activityData['social_actions'],
            'goal_completion_rate' => $activityData['goals_completed']
        ];
    }

    /**
     * Analyze churn behavior
     */
    private function analyzeChurnBehavior(array $features): array
    {
        $riskFactors = [];
        $protectiveFactors = [];

        // Identify risk factors
        if ($features['days_since_last_login'] > 7) {
            $riskFactors[] = 'inactive_user';
        }

        if ($features['payment_frequency_decline'] > 0.5) {
            $riskFactors[] = 'declining_payments';
        }

        if ($features['service_usage_decline'] > 0.3) {
            $riskFactors[] = 'reduced_usage';
        }

        if ($features['support_ticket_ratio'] > 0.1) {
            $riskFactors[] = 'high_support_needs';
        }

        // Identify protective factors
        if ($features['feature_adoption_rate'] > 0.7) {
            $protectiveFactors[] = 'high_feature_adoption';
        }

        if ($features['social_engagement'] > 0.5) {
            $protectiveFactors[] = 'social_engagement';
        }

        return [
            'risk_factors' => $riskFactors,
            'protective_factors' => $protectiveFactors,
            'behavior_score' => $this->calculateBehaviorScore($features),
            'trend_direction' => $this->calculateTrendDirection($features)
        ];
    }

    /**
     * Generate churn interventions
     */
    private function generateChurnInterventions(array $prediction, array $behaviorAnalysis): array
    {
        $interventions = [];

        if ($prediction['risk_score'] > 0.7) {
            $interventions[] = [
                'type' => 'immediate_outreach',
                'priority' => 'high',
                'description' => 'Personal outreach from customer success team',
                'expected_impact' => 0.3
            ];
        }

        if (in_array('declining_payments', $behaviorAnalysis['risk_factors'])) {
            $interventions[] = [
                'type' => 'payment_incentive',
                'priority' => 'medium',
                'description' => 'Offer payment discount or flexible terms',
                'expected_impact' => 0.2
            ];
        }

        if (in_array('reduced_usage', $behaviorAnalysis['risk_factors'])) {
            $interventions[] = [
                'type' => 'usage_education',
                'priority' => 'medium',
                'description' => 'Provide usage tutorials and best practices',
                'expected_impact' => 0.15
            ];
        }

        return $interventions;
    }

    /**
     * Mock helper methods - replace with actual implementations
     */
    private function predictChurnDate(float $riskScore): ?string
    {
        if ($riskScore < 0.3) {
            return null;
        }

        $daysToChurn = (1 - $riskScore) * 90; // 0-90 days based on risk
        return date('Y-m-d', time() + (int)($daysToChurn * 86400));
    }

    private function categorizeValueSegment(float $ltv): string
    {
        if ($ltv >= 1000000) {
            return 'high_value';
        }
        if ($ltv >= 500000) {
            return 'medium_value';
        }
        if ($ltv >= 100000) {
            return 'low_value';
        }
        return 'minimal_value';
    }

    private function generateValueOptimizations(array $prediction, array $features): array
    {
        return [
            ['strategy' => 'upsell_premium_services', 'potential_impact' => 150000],
            ['strategy' => 'increase_usage_frequency', 'potential_impact' => 75000],
            ['strategy' => 'referral_program', 'potential_impact' => 50000]
        ];
    }

    private function getUserActionHistory(int $userId): array
    {
        return [
            'login' => ['frequency' => rand(1, 30), 'last_action' => time() - rand(0, 86400)],
            'payment' => ['frequency' => rand(1, 10), 'last_action' => time() - rand(0, 2592000)],
            'service_creation' => ['frequency' => rand(1, 5), 'last_action' => time() - rand(0, 1296000)]
        ];
    }

    private function getCurrentUserContext(int $userId): array
    {
        return [
            'current_plan' => 'premium',
            'wallet_balance' => rand(0, 100000),
            'active_services' => rand(1, 5),
            'recent_support_tickets' => rand(0, 3)
        ];
    }

    private function identifyBehaviorPatterns(array $history): array
    {
        return [
            'login_pattern' => 'regular',
            'payment_pattern' => 'monthly',
            'usage_pattern' => 'moderate'
        ];
    }

    private function calculateActionProbabilities(array $history, array $context): array
    {
        return [
            'make_payment' => 0.7,
            'create_service' => 0.5,
            'contact_support' => 0.2,
            'upgrade_plan' => 0.3,
            'refer_friend' => 0.1
        ];
    }

    private function predictActionTiming(array $actions, array $patterns): array
    {
        $timing = [];
        foreach ($actions as $action => $probability) {
            $timing[$action] = [
                'probability' => $probability,
                'expected_days' => rand(1, 30),
                'confidence' => rand(60, 95) / 100
            ];
        }
        return $timing;
    }

    private function calculatePredictionConfidence(array $history): float
    {
        return rand(70, 95) / 100;
    }

    private function getBehaviorOverview(int $days): array
    {
        return [
            'total_predictions' => rand(100, 1000),
            'high_churn_risk_users' => rand(10, 50),
            'high_value_users' => rand(20, 100),
            'avg_engagement_score' => rand(60, 85) / 100
        ];
    }

    private function getChurnAnalysis(int $days): array
    {
        return [
            'at_risk_users' => rand(50, 200),
            'prevented_churns' => rand(5, 25),
            'intervention_success_rate' => rand(60, 80) / 100
        ];
    }

    private function getLTVAnalysis(int $days): array
    {
        return [
            'avg_predicted_ltv' => rand(200000, 800000),
            'ltv_growth_rate' => rand(5, 15) / 100,
            'high_potential_users' => rand(20, 100)
        ];
    }

    private function getEngagementTrends(int $days): array
    {
        return [
            'avg_engagement_score' => rand(60, 85) / 100,
            'engagement_trend' => 'increasing',
            'low_engagement_users' => rand(10, 50)
        ];
    }

    private function getBehaviorPatterns(int $days): array
    {
        return [
            'common_patterns' => ['regular_user', 'weekend_user', 'power_user'],
            'emerging_patterns' => ['mobile_first', 'feature_explorer'],
            'declining_patterns' => ['occasional_user']
        ];
    }

    private function getPredictionAccuracy(): array
    {
        return [
            'churn_prediction_accuracy' => rand(75, 90) / 100,
            'ltv_prediction_accuracy' => rand(70, 85) / 100,
            'action_prediction_accuracy' => rand(65, 80) / 100
        ];
    }

    private function getSegmentPerformance(int $days): array
    {
        return [
            'high_value' => ['size' => rand(50, 200), 'growth' => rand(5, 15) / 100],
            'at_risk' => ['size' => rand(20, 100), 'retention_rate' => rand(60, 80) / 100],
            'new_users' => ['size' => rand(100, 500), 'conversion_rate' => rand(20, 40) / 100]
        ];
    }

    private function getBusinessRecommendations(int $days): array
    {
        return [
            ['type' => 'retention', 'description' => 'Focus on at-risk user retention', 'priority' => 'high'],
            ['type' => 'upselling', 'description' => 'Target high-value users for upselling', 'priority' => 'medium'],
            ['type' => 'engagement', 'description' => 'Improve low-engagement user experience', 'priority' => 'medium']
        ];
    }

    /**
     * Load behavior models
     */
    private function loadBehaviorModels(): void
    {
        $this->behaviorModels = $this->cache->get('behavior:models', []);
    }

    /**
     * Load user segments
     */
    private function loadUserSegments(): void
    {
        $this->userSegments = $this->cache->get('behavior:segments', []);
    }

    /**
     * Categorize engagement level
     */
    private function categorizeEngagementLevel(float $score): string
    {
        if ($score >= 0.8) {
            return 'very_high';
        }
        if ($score >= 0.6) {
            return 'high';
        }
        if ($score >= 0.4) {
            return 'medium';
        }
        if ($score >= 0.2) {
            return 'low';
        }
        return 'very_low';
    }

    /**
     * Analyze engagement trends
     */
    private function analyzeEngagementTrends(array $activity): array
    {
        return [
            'trend_direction' => ['increasing', 'stable', 'decreasing'][rand(0, 2)],
            'trend_strength' => rand(20, 80) / 100,
            'seasonal_patterns' => ['weekend_high', 'evening_peak'],
            'consistency_score' => rand(40, 90) / 100
        ];
    }

    /**
     * Generate engagement strategies
     */
    private function generateEngagementStrategies(float $currentScore, array $trends): array
    {
        $strategies = [];

        if ($currentScore < 0.5) {
            $strategies[] = 'onboarding_improvement';
            $strategies[] = 'feature_education';
        }

        if ($trends['trend_direction'] === 'decreasing') {
            $strategies[] = 'retention_campaign';
            $strategies[] = 'personalized_content';
        }

        return $strategies;
    }

    /**
     * Identify engagement drivers
     */
    private function identifyEngagementDrivers(array $activity): array
    {
        return [
            'feature_usage' => rand(60, 90) / 100,
            'session_frequency' => rand(50, 80) / 100,
            'support_interactions' => rand(30, 70) / 100,
            'social_features' => rand(20, 60) / 100
        ];
    }

    /**
     * Predict engagement change
     */
    private function predictEngagementChange(array $currentActivity, array $trends): array
    {
        $changeDirection = $trends['trend_direction'];
        $changeMagnitude = rand(5, 25) / 100;

        return [
            'predicted_change' => $changeDirection === 'increasing' ? $changeMagnitude : -$changeMagnitude,
            'confidence' => rand(70, 90) / 100,
            'timeframe_days' => rand(7, 30)
        ];
    }

    /**
     * Get users for segmentation
     * @param array $criteria Segmentation criteria
     */
    private function getUsersForSegmentation(array $criteria = []): array
    {
        // Mock implementation
        $users = [];
        for ($i = 1; $i <= 100; $i++) {
            $users[] = [
                'user_id' => $i,
                'total_spent' => rand(50000, 500000),
                'last_activity' => time() - rand(0, 86400 * 30),
                'services_count' => rand(1, 10)
            ];
        }
        return $users;
    }

    /**
     * Extract behavior features
     */
    private function extractBehaviorFeatures(array $users): array
    {
        $features = [];
        foreach ($users as $user) {
            $features[$user['user_id']] = [
                'spending_behavior' => min(1.0, $user['total_spent'] / 1000000),
                'activity_recency' => max(0, 1 - (time() - $user['last_activity']) / (86400 * 30)),
                'service_diversity' => min(1.0, $user['services_count'] / 10)
            ];
        }
        return $features;
    }

    /**
     * Enhance segments with behavior
     */
    private function enhanceSegmentsWithBehavior(array $segments, array $behaviorData): array
    {
        foreach ($segments as $segmentId => &$segment) {
            $segment['behavior_profile'] = [
                'primary_behavior' => ['conservative', 'moderate', 'aggressive'][rand(0, 2)],
                'engagement_pattern' => ['regular', 'sporadic', 'seasonal'][rand(0, 2)],
                'growth_potential' => rand(30, 80) / 100
            ];
        }
        return $segments;
    }

    /**
     * Generate segment strategies
     */
    private function generateSegmentStrategies(array $segments): array
    {
        $strategies = [];
        foreach ($segments as $segmentId => $segment) {
            $strategies[$segmentId] = [
                'marketing_approach' => ['premium_focus', 'value_focus', 'feature_focus'][rand(0, 2)],
                'communication_frequency' => ['high', 'medium', 'low'][rand(0, 2)],
                'recommended_actions' => ['upsell', 'retention', 'acquisition'][rand(0, 2)]
            ];
        }
        return $strategies;
    }

    /**
     * Calculate segment quality
     */
    private function calculateSegmentQuality(array $segments): array
    {
        return [
            'cohesion_score' => rand(70, 95) / 100,
            'separation_score' => rand(65, 90) / 100,
            'stability_score' => rand(75, 90) / 100,
            'actionability_score' => rand(80, 95) / 100
        ];
    }

    /**
     * Get recent user activity
     */
    private function getRecentUserActivity(int $userId, int $days = 30): array
    {
        return [
            'login_count' => rand(5, 25),
            'session_duration_avg' => rand(300, 1800),
            'features_used' => rand(3, 10),
            'messages_sent' => rand(10, 100),
            'services_created' => rand(0, 5),
            'support_tickets' => rand(0, 3)
        ];
    }

    /**
     * Get user payment history
     * @param int $userId User ID
     * @param int $days Number of days to look back
     */
    private function getUserPaymentHistory(int $userId, int $days = 90): array
    {
        $payments = [];
        for ($i = 0; $i < rand(3, 12); $i++) {
            $payments[] = [
                'amount' => rand(50000, 200000),
                'date' => time() - rand(0, 86400 * 365),
                'status' => 'completed'
            ];
        }
        return $payments;
    }

    /**
     * Get user service usage
     * @param int $userId User ID
     * @param int $days Number of days to look back
     */
    private function getUserServiceUsage(int $userId, int $days = 30): array
    {
        return [
            'total_services' => rand(1, 10),
            'active_services' => rand(1, 5),
            'avg_usage_per_service' => rand(100, 1000),
            'preferred_protocols' => ['v2ray', 'shadowsocks']
        ];
    }

    /**
     * Get user service history
     */
    private function getUserServiceHistory(int $userId): array
    {
        $services = [];
        for ($i = 0; $i < rand(2, 8); $i++) {
            $services[] = [
                'service_type' => ['v2ray', 'shadowsocks', 'trojan'][rand(0, 2)],
                'created_at' => time() - rand(0, 86400 * 180),
                'status' => ['active', 'expired', 'suspended'][rand(0, 2)],
                'usage_gb' => rand(10, 500)
            ];
        }
        return $services;
    }

    /**
     * Get days since last payment
     */
    private function getDaysSinceLastPayment(array $paymentHistory): int
    {
        if (empty($paymentHistory)) {
            return 999;
        }

        $lastPayment = max(array_column($paymentHistory, 'date'));
        return (int)((time() - $lastPayment) / 86400);
    }

    /**
     * Calculate payment frequency decline
     */
    private function calculatePaymentFrequencyDecline(array $paymentHistory): float
    {
        if (count($paymentHistory) < 2) {
            return 0;
        }

        // Simple implementation - compare recent vs older payments
        $recentPayments = array_filter($paymentHistory, fn($p) => $p['date'] > time() - 86400 * 90);
        $olderPayments = array_filter($paymentHistory, fn($p) => $p['date'] <= time() - 86400 * 90);

        $recentFreq = count($recentPayments) / 3; // per month
        $olderFreq = count($olderPayments) / 9; // per month (9 months)

        return max(0, ($olderFreq - $recentFreq) / max($olderFreq, 1));
    }

    /**
     * Calculate service usage decline
     */
    private function calculateServiceUsageDecline(array $serviceHistory): float
    {
        if (empty($serviceHistory)) {
            return 0;
        }

        $recentUsage = 0;
        $olderUsage = 0;

        foreach ($serviceHistory as $service) {
            if ($service['created_at'] > time() - 86400 * 30) {
                $recentUsage += $service['usage_gb'] ?? 0;
            } else {
                $olderUsage += $service['usage_gb'] ?? 0;
            }
        }

        return max(0, ($olderUsage - $recentUsage) / max($olderUsage, 1));
    }

    /**
     * Calculate support ticket ratio
     */
    private function calculateSupportTicketRatio(array $activity): float
    {
        $tickets = $activity['support_tickets'] ?? 0;
        $sessions = $activity['login_count'] ?? 1;

        return $tickets / max($sessions, 1);
    }

    /**
     * Calculate feature adoption rate
     */
    private function calculateFeatureAdoptionRate(array $activity): float
    {
        $featuresUsed = $activity['features_used'] ?? 0;
        $totalFeatures = 15; // Assume 15 total features

        return $featuresUsed / $totalFeatures;
    }

    /**
     * Calculate social engagement
     */
    private function calculateSocialEngagement(array $activity): float
    {
        $messages = $activity['messages_sent'] ?? 0;
        $sessions = $activity['login_count'] ?? 1;

        return min(1.0, $messages / ($sessions * 5)); // 5 messages per session is high engagement
    }

    /**
     * Calculate average monthly spend
     */
    private function calculateAvgMonthlySpend(array $paymentHistory): float
    {
        if (empty($paymentHistory)) {
            return 0;
        }

        $totalSpent = array_sum(array_column($paymentHistory, 'amount'));
        $months = max(1, count($paymentHistory) / 2); // Assume bi-monthly payments

        return $totalSpent / $months;
    }

    /**
     * Calculate payment consistency
     */
    private function calculatePaymentConsistency(array $paymentHistory): float
    {
        if (count($paymentHistory) < 2) {
            return 0;
        }

        $amounts = array_column($paymentHistory, 'amount');
        $avgAmount = array_sum($amounts) / count($amounts);
        $variance = 0;

        foreach ($amounts as $amount) {
            $variance += pow($amount - $avgAmount, 2);
        }

        $stdDev = sqrt($variance / count($amounts));
        return max(0, 1 - ($stdDev / $avgAmount));
    }

    /**
     * Calculate upgrade tendency
     */
    private function calculateUpgradeTendency(array $serviceHistory): float
    {
        if (empty($serviceHistory)) {
            return 0;
        }

        $upgrades = 0;
        $totalServices = count($serviceHistory);

        // Simple heuristic: count services with higher usage as upgrades
        foreach ($serviceHistory as $service) {
            if (($service['usage_gb'] ?? 0) > 100) {
                $upgrades++;
            }
        }

        return $upgrades / max($totalServices, 1);
    }

    /**
     * Calculate referral activity
     */
    private function calculateReferralActivity(array $activity): float
    {
        // Mock implementation - in real scenario, check referral data
        return rand(0, 50) / 100;
    }

    /**
     * Calculate current engagement score
     */
    private function calculateCurrentEngagementScore(array $activity): float
    {
        $score = 0;

        // Login frequency (30%)
        $loginScore = min(1, ($activity['login_count'] ?? 0) / 20);
        $score += $loginScore * 0.3;

        // Feature usage (25%)
        $featureScore = min(1, ($activity['features_used'] ?? 0) / 10);
        $score += $featureScore * 0.25;

        // Session duration (25%)
        $durationScore = min(1, ($activity['session_duration_avg'] ?? 0) / 1800);
        $score += $durationScore * 0.25;

        // Activity diversity (20%)
        $diversityScore = min(1, ($activity['messages_sent'] ?? 0) / 50);
        $score += $diversityScore * 0.2;

        return $score;
    }

    /**
     * Calculate support satisfaction
     */
    private function calculateSupportSatisfaction(array $activity): float
    {
        $tickets = $activity['support_tickets'] ?? 0;

        // Fewer tickets = higher satisfaction (inverse relationship)
        if ($tickets === 0) {
            return 1.0;
        }
        if ($tickets <= 2) {
            return 0.8;
        }
        if ($tickets <= 5) {
            return 0.6;
        }
        return 0.4;
    }

    /**
     * Get detailed user activity
     * @param int $userId User ID
     * @param int $days Number of days to look back
     */
    private function getDetailedUserActivity(int $userId, int $days = 30): array
    {
        return [
            'daily_logins' => array_fill(0, 30, rand(0, 3)),
            'feature_usage_history' => [
                'vpn_creation' => rand(5, 20),
                'settings_access' => rand(2, 10),
                'support_contact' => rand(0, 5)
            ],
            'session_patterns' => [
                'peak_hours' => [18, 19, 20, 21],
                'avg_session_length' => rand(300, 1800),
                'sessions_per_day' => rand(1, 5)
            ]
        ];
    }

    /**
     * Calculate behavior score
     */
    private function calculateBehaviorScore(array $activity): float
    {
        $score = 0;

        // Consistency (40%)
        $consistency = 1 - (array_sum($activity['daily_logins']) > 0 ?
            (max($activity['daily_logins']) - min($activity['daily_logins'])) / max($activity['daily_logins']) : 0);
        $score += $consistency * 0.4;

        // Engagement level (35%)
        $engagement = min(1, array_sum($activity['daily_logins']) / 60); // 60 logins per month is high
        $score += $engagement * 0.35;

        // Feature adoption (25%)
        $featureAdoption = min(1, array_sum($activity['feature_usage_history']) / 50);
        $score += $featureAdoption * 0.25;

        return $score;
    }

    /**
     * Calculate trend direction
     */
    private function calculateTrendDirection(array $activity): string
    {
        $dailyLogins = $activity['daily_logins'];
        $firstHalf = array_slice($dailyLogins, 0, 15);
        $secondHalf = array_slice($dailyLogins, 15);

        $firstAvg = array_sum($firstHalf) / count($firstHalf);
        $secondAvg = array_sum($secondHalf) / count($secondHalf);

        if ($secondAvg > $firstAvg * 1.1) {
            return 'increasing';
        }
        if ($secondAvg < $firstAvg * 0.9) {
            return 'decreasing';
        }
        return 'stable';
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'prediction_cache_ttl' => 3600,     // 1 hour
            'segment_cache_ttl' => 7200,        // 2 hours
            'dashboard_cache_ttl' => 1800,      // 30 minutes
            'ltv_time_horizon' => 24,           // 24 months
            'churn_prediction_window' => 90,    // 90 days
            'engagement_window' => 30,          // 30 days
            'min_history_days' => 30,           // Minimum history for predictions
            'prediction_confidence_threshold' => 0.7, // Minimum confidence
            'segment_quality_threshold' => 0.8  // Minimum segment quality
        ];
    }
}
