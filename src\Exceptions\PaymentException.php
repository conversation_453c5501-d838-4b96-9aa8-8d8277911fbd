<?php

declare(strict_types=1);

namespace WeBot\Exceptions;

/**
 * Payment Exception
 *
 * Thrown when payment processing fails.
 *
 * @package WeBot\Exceptions
 * @version 2.0
 */
class PaymentException extends WeBotException
{
    public const GATEWAY_ERROR = 1001;
    public const INSUFFICIENT_FUNDS = 1002;
    public const PAYMENT_FAILED = 1003;
    public const PAYMENT_CANCELLED = 1004;
    public const INVALID_AMOUNT = 1005;
    public const GATEWAY_UNAVAILABLE = 1006;

    protected string $gateway = '';
    protected string $transactionId = '';

    public function __construct(
        string $message = 'Payment processing failed',
        int $code = self::PAYMENT_FAILED,
        string $gateway = '',
        string $transactionId = ''
    ) {
        parent::__construct($message, $code);

        $this->gateway = $gateway;
        $this->transactionId = $transactionId;

        $this->setUserMessage($this->getUserFriendlyMessage($code));
    }

    /**
     * Get payment gateway
     */
    public function getGateway(): string
    {
        return $this->gateway;
    }

    /**
     * Set payment gateway
     */
    public function setGateway(string $gateway): self
    {
        $this->gateway = $gateway;
        return $this;
    }

    /**
     * Get transaction ID
     */
    public function getTransactionId(): string
    {
        return $this->transactionId;
    }

    /**
     * Set transaction ID
     */
    public function setTransactionId(string $transactionId): self
    {
        $this->transactionId = $transactionId;
        return $this;
    }

    /**
     * Get user-friendly message based on error code
     */
    private function getUserFriendlyMessage(int $code): string
    {
        return match ($code) {
            self::GATEWAY_ERROR => 'خطا در اتصال به درگاه پرداخت. لطفاً مجدداً تلاش کنید.',
            self::INSUFFICIENT_FUNDS => 'موجودی کیف پول شما کافی نیست.',
            self::PAYMENT_FAILED => 'پرداخت ناموفق بود. لطفاً مجدداً تلاش کنید.',
            self::PAYMENT_CANCELLED => 'پرداخت لغو شد.',
            self::INVALID_AMOUNT => 'مبلغ پرداخت صحیح نیست.',
            self::GATEWAY_UNAVAILABLE => 'درگاه پرداخت در حال حاضر در دسترس نیست.',
            default => 'خطا در پردازش پرداخت.',
        };
    }

    /**
     * Create gateway error exception
     */
    public static function gatewayError(string $gateway, string $message = ''): self
    {
        return new self(
            $message ?: 'Gateway error',
            self::GATEWAY_ERROR,
            $gateway
        );
    }

    /**
     * Create insufficient funds exception
     */
    public static function insufficientFunds(float $required, float $available): self
    {
        return new self(
            "Insufficient funds: required {$required}, available {$available}",
            self::INSUFFICIENT_FUNDS
        );
    }

    /**
     * Create payment failed exception
     */
    public static function paymentFailed(string $reason = ''): self
    {
        return new self(
            $reason ?: 'Payment failed',
            self::PAYMENT_FAILED
        );
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'gateway' => $this->getGateway(),
            'transaction_id' => $this->getTransactionId(),
        ]);
    }
}
