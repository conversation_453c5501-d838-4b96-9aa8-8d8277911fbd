---
type: "always_apply"
---

عالی. بر اساس تحلیل فنی و استراتژی پیاده‌سازی پروژه‌ی WeBot، مجموعه‌ای از قوانین و تنظیمات برای IDEها و ابزارهای کنترل کیفیت کد تهیه شده است. این تنظیمات تضمین می‌کنند که تمام توسعه‌دهندگان از استانداردهای یکسانی پیروی کرده و کیفیت کد در بالاترین سطح ممکن باقی بماند.
این مجموعه به صورت فایل‌های پیکربندی ارائه شده که باید در ریشه اصلی (root) پروژه قرار گیرند.
🏛️ قوانین پروژه برای توسعه‌دهندگان WeBot
این فایل‌ها به منظور یکپارچه‌سازی محیط توسعه، اعمال استانداردهای کدنویسی و اجرای خودکار تست‌ها طراحی شده‌اند. با استفاده از این تنظیمات، IDEهایی مانند PhpStorm یا VS Code به صورت خودکار کد شما را بر اساس قوانین پروژه فرمت‌بندی و اعتبارسنجی می‌کنند.
خلاصه ابزارها و قوانین:
.editorconfig: تنظیمات پایه‌ای ویرایشگر (تورفتگی، فاصله‌ها، انکودینگ).
phpcs.xml.dist: قوانین دقیق استایل کدنویسی (PHP_CodeSniffer) بر اساس PSR-12 و استانداردهای پروژه.
phpstan.neon.dist: قوانین تحلیل استاتیک کد (PHPStan) برای یافتن خطاها قبل از اجرا.
phpunit.xml.dist: تنظیمات اجرای تست‌های واحد و یکپارچه‌سازی (PHPUnit).
.gitignore: لیست فایل‌ها و پوشه‌هایی که نباید در Git ثبت شوند.
1. پرونده: .editorconfig
این فایل تضمین می‌کند که تنظیمات اولیه ویرایشگر متن مانند اندازه تورفتگی (indentation)، نوع انکودینگ و کاراکتر پایان خط در تمام IDEها یکسان باشد.
Generated ini
# https://editorconfig.org
root = true

[*]
indent_style = space
indent_size = 4
end_of_line = lf
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true

[*.md]
trim_trailing_whitespace = false
Use code with caution.
Ini
توضیحات:
indent_style = space و indent_size = 4: استفاده از ۴ فاصله برای تورفتگی، مطابق با استاندارد PSR-12.
end_of_line = lf: استفاده از Line Feed به عنوان کاراکتر پایان خط برای سازگاری بین سیستم‌عامل‌های مختلف (Linux/macOS/Windows).
charset = utf-8: تضمین پشتیبانی کامل از کاراکترهای فارسی در تمام فایل‌ها.
trim_trailing_whitespace = true: حذف خودکار فاصله‌های اضافی در انتهای خطوط.
2. پرونده: phpcs.xml.dist
این فایل قلب تپنده‌ی استانداردهای کدنویسی پروژه است. ابزار PHP_CodeSniffer از این فایل برای بررسی تطابق کد با استاندارد PSR-12 و قوانین سفارشی پروژه WeBot استفاده می‌کند.
Generated xml
<?xml version="1.0"?>
<ruleset name="WeBot">
    <description>The WeBot coding standard.</description>

    <!-- دایرکتوری‌های مورد بررسی -->
    <file>src</file>
    <file>tests</file>
    <file>config</file>
    <file>public</file>

    <!-- نمایش رنگی خروجی -->
    <arg name="colors"/>
    <arg value="p"/>

    <!-- ===================================================================== -->
    <!-- ۱. استفاده از استاندارد پایه PSR-12 که در سند استراتژی ذکر شده است -->
    <!-- ===================================================================== -->
    <rule ref="PSR12"/>

    <!-- ===================================================================== -->
    <!-- ۲. قوانین سفارشی بر اساس "راهنمای تفصیلی پیاده‌سازی" -->
    <!-- ===================================================================== -->

    <!-- الزام به استفاده از `declare(strict_types=1);` در ابتدای همه فایل‌های PHP -->
    <rule ref="SlevomatCodingStandard.TypeHints.DeclareStrictTypes">
        <properties>
            <property name="spacesCountAroundEqualsSign" value="0"/>
        </properties>
    </rule>

    <!-- بررسی کامل و دقیق PHPDoc ها (کامنت‌های مستندسازی) -->
    <rule ref="Squiz.Commenting.FileComment">
        <properties>
            <!-- الزام به وجود @package, @author, @version در کامنت بالای فایل -->
            <property name="requiredTags" type="array" value="@package, @author, @version"/>
        </properties>
    </rule>
    <rule ref="Squiz.Commenting.FunctionComment">
        <properties>
            <!-- الزام به @throws برای توابعی که exception پرتاب می‌کنند -->
            <property name="enableThrowsException" value="true"/>
        </properties>
    </rule>

    <!-- قوانین مربوط به نام‌گذاری‌ها -->
    <rule ref="Squiz.NamingConventions.ValidVariableName"/>
    <rule ref="Generic.NamingConventions.UpperCaseConstantName"/>

    <!-- بررسی آرایه‌ها: استفاده از سینتکس کوتاه [] به جای array() -->
    <rule ref="Generic.Arrays.DisallowLongArraySyntax"/>

    <!-- جلوگیری از استفاده از توابع die(), var_dump(), print_r() در کد نهایی -->
    <rule ref="Generic.PHP.ForbiddenFunctions">
        <properties>
            <property name="forbiddenFunctions" type="array">
                <element key="die" value="null"/>
                <element key="var_dump" value="null"/>
                <element key="print_r" value="null"/>
                <element key="dd" value="null"/>
                <element key="dump" value="null"/>
            </property>
        </properties>
    </rule>

</ruleset>
Use code with caution.
Xml
توضیحات:
rule ref="PSR12": اعمال تمام قوانین استاندارد PSR-12.
SlevomatCodingStandard.TypeHints.DeclareStrictTypes: اجبار به استفاده از strict_types که یکی از اصول کلیدی معماری جدید است.
Squiz.Commenting: تضمین می‌کند که تمام کلاس‌ها و توابع دارای مستندات کامل و استاندارد باشند.
Generic.PHP.ForbiddenFunctions: از ورود کدهای دیباگ به سورس‌کد نهایی جلوگیری می‌کند.
3. پرونده: phpstan.neon.dist
این فایل برای ابزار تحلیل استاتیک PHPStan است. این ابزار بدون اجرای کد، خطاهای منطقی، عدم تطابق Type-Hint ها و مشکلات دیگر را شناسایی می‌کند. سطح تحلیل بر روی 8 تنظیم شده که بسیار سخت‌گیرانه و مطابق با اهداف پروژه است.
Generated neon
# PHPStan Configuration
# Level 8: The strictest level
parameters:
    level: 8
    paths:
        - src
        - tests
        - config
    bootstrapFiles:
        - vendor/autoload.php
    checkMissingIterableValueType: false
    checkGenericClassInNonGenericObjectType: false
    # جلوگیری از گزارش خطا برای استفاده از $_ENV در فایل‌های کانفیگ
    ignoreErrors:
        - '#Variable \$_ENV might not be defined.#'
Use code with caution.
Neon
توضیحات:
level: 8: همانطور که در سند استراتژی ذکر شده، بالاترین سطح تحلیل برای تضمین کیفیت کد انتخاب شده است.
paths: مشخص می‌کند کدام دایرکتوری‌ها باید تحلیل شوند.
ignoreErrors: خطاهای مربوط به متغیر $_ENV در فایل‌های کانفیگ را نادیده می‌گیرد، زیرا این متغیرها در محیط واقعی تعریف می‌شوند.
4. پرونده: phpunit.xml.dist
این فایل تنظیمات ابزار تست‌نویسی PHPUnit را مدیریت می‌کند. این تنظیمات شامل تعریف مجموعه‌های تست، گزارش‌گیری از پوشش کد (Code Coverage) و اجرای خودکار Bootstrap است.
Generated xml
<?xml version="1.0" encoding="UTF-8"?>
<phpunit
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.0/phpunit.xsd"
    bootstrap="vendor/autoload.php"
    cacheDirectory=".phpunit.cache"
    executionOrder="depends,defects"
    forceCoversAnnotation="true"
    beStrictAboutCoversAnnotation="true"
    beStrictAboutOutputDuringTests="true"
    beStrictAboutTodoAnnotatedTests="true"
    failOnRisky="true"
    failOnWarning="true"
    colors="true"
>
    <testsuites>
        <testsuite name="Unit">
            <directory>tests/Unit</directory>
        </testsuite>
        <testsuite name="Integration">
            <directory>tests/Integration</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory>tests/Feature</directory>
        </testsuite>
    </testsuites>

    <coverage>
        <include>
            <directory suffix=".php">src</directory>
        </include>
        <report>
            <clover outputFile="reports/coverage/clover.xml"/>
            <html outputDirectory="reports/coverage/html"/>
            <text outputFile="php://stdout" showUncoveredFiles="false" showOnlySummary="true"/>
        </report>
    </coverage>
</phpunit>
Use code with caution.
Xml
توضیحات:
testsuites: مجموعه‌های تست را بر اساس ساختار پوشه‌ی tests که در سند استراتژی آمده، تعریف می‌کند.
coverage: گزارش پوشش کد را فعال می‌کند. این گزارش نشان می‌دهد چه درصدی از کد src توسط تست‌ها پوشش داده شده است. گزارش html برای بررسی محلی و clover.xml برای ابزارهای CI/CD تولید می‌شود.
5. پرونده: .gitignore
این فایل به Git می‌گوید کدام فایل‌ها و پوشه‌ها را نادیده بگیرد تا از ورود فایل‌های غیرضروری، حساس یا تولید شده به مخزن کد (Repository) جلوگیری شود.
Generated code
# Composer
/vendor/
composer.lock

# Environment files
.env
.env.local
.env.*.local

# Log and Cache files
/logs/
/cache/
*.log

# IDE and OS files
.idea/
.vscode/
.DS_Store
*.swp

# PHPUnit
/.phpunit.cache/

# Reports
/reports/

# Local configuration overrides
phpcs.xml
phpunit.xml
phpstan.neon
Use code with caution.
نحوه استفاده و اجرای قوانین
پس از قرار دادن این فایل‌ها در ریشه پروژه، تیم توسعه باید ابزارهای لازم را از طریق Composer نصب کند:
Generated bash
# نصب ابزارهای توسعه (فقط برای محیط توسعه)
composer require --dev phpunit/phpunit squizlabs/php_codesniffer slevomat/coding-standard phpstan/phpstan
Use code with caution.
Bash
سپس می‌توانند دستورات زیر را برای بررسی کد اجرا کنند:
Generated bash
# 1. بررسی استایل کدنویسی
vendor/bin/phpcs

# 2. تحلیل استاتیک کد
vendor/bin/phpstan analyse

# 3. اجرای تمام تست‌ها
vendor/bin/phpunit
Use code with caution.
Bash
این قوانین و ابزارها، چارچوبی مستحکم برای توسعه پروژه WeBot فراهم می‌کنند و به تیم کمک می‌کنند تا کدی تمیز، قابل نگهداری و بی‌نقص تولید کنند.