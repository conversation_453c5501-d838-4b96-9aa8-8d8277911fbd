<?php

declare(strict_types=1);

namespace WeBot\Tests\ErrorHandling;

use WeBot\Tests\Unit\BaseTestCase;
use WeBot\Core\EnhancedErrorHandler;
use WeBot\Services\ErrorRecoveryService;
use WeBot\Services\ErrorMonitoringService;
use WeBot\Exceptions\ErrorRecoveryException;
use WeBot\Exceptions\WeBotException;
use WeBot\Core\CacheManager;
use WeBot\Services\DatabaseService;

/**
 * Error Handling Test
 * 
 * Tests error handling, recovery, and monitoring features.
 */
class ErrorHandlingTest extends BaseTestCase
{
    private EnhancedErrorHandler $errorHandler;
    private ErrorRecoveryService $recoveryService;
    private ErrorMonitoringService $monitoringService;
    private CacheManager $cache;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Initialize cache
        $this->cache = new CacheManager([
            'enabled' => true,
            'host' => 'localhost',
            'port' => 6379
        ]);
        
        // Initialize services
        $database = new DatabaseService($this->config);
        $this->recoveryService = new ErrorRecoveryService($this->cache, $database);
        $this->monitoringService = new ErrorMonitoringService($this->cache);
        $this->errorHandler = new EnhancedErrorHandler([
            'display_errors' => false,
            'log_errors' => true
        ]);
    }
    
    /**
     * Test error handler initialization
     */
    public function testErrorHandlerInitialization(): void
    {
        echo "🚀 Testing Error Handler Initialization...\n";
        
        $this->assertInstanceOf(EnhancedErrorHandler::class, $this->errorHandler);
        echo "  ✅ EnhancedErrorHandler initialized successfully\n";
    }
    
    /**
     * Test error recovery exception creation
     */
    public function testErrorRecoveryExceptionCreation(): void
    {
        echo "🔄 Testing Error Recovery Exception Creation...\n";
        
        // Test database connection recovery exception
        $dbException = ErrorRecoveryException::databaseConnectionFailed('localhost', 3306);
        
        $this->assertInstanceOf(ErrorRecoveryException::class, $dbException);
        $this->assertTrue($dbException->isRecoverable());
        $this->assertGreaterThan(0, count($dbException->getRecoveryActions()));
        
        echo "  ✅ Database connection recovery exception created\n";
        
        // Test API service recovery exception
        $apiException = ErrorRecoveryException::apiServiceUnavailable('telegram', 'https://api.telegram.org');
        
        $this->assertInstanceOf(ErrorRecoveryException::class, $apiException);
        $this->assertTrue($apiException->isRecoverable());
        $this->assertGreaterThan(0, count($apiException->getRecoveryActions()));
        
        echo "  ✅ API service recovery exception created\n";
        
        // Test file system recovery exception
        $fileException = ErrorRecoveryException::fileSystemError('write', '/tmp/test.txt');
        
        $this->assertInstanceOf(ErrorRecoveryException::class, $fileException);
        $this->assertTrue($fileException->isRecoverable());
        $this->assertGreaterThan(0, count($fileException->getRecoveryActions()));
        
        echo "  ✅ File system recovery exception created\n";
        
        // Test memory limit recovery exception
        $memoryException = ErrorRecoveryException::memoryLimitExceeded(100000000, 128000000);
        
        $this->assertInstanceOf(ErrorRecoveryException::class, $memoryException);
        $this->assertTrue($memoryException->isRecoverable());
        $this->assertGreaterThan(0, count($memoryException->getRecoveryActions()));
        
        echo "  ✅ Memory limit recovery exception created\n";
    }
    
    /**
     * Test error recovery service
     */
    public function testErrorRecoveryService(): void
    {
        echo "🛠️ Testing Error Recovery Service...\n";
        
        try {
            // Test database error recovery
            $dbException = new \Exception('Database connection failed');
            $recovered = $this->recoveryService->attemptRecovery($dbException, [
                'cache_key' => 'test_data'
            ]);
            
            // Recovery might fail in test environment, but service should handle it gracefully
            $this->assertIsBool($recovered);
            echo "  ✅ Database error recovery attempted\n";
            
            // Test API error recovery
            $apiException = new \Exception('API timeout occurred');
            $recovered = $this->recoveryService->attemptRecovery($apiException, [
                'retry_count' => 1,
                'cache_key' => 'api_response'
            ]);
            
            $this->assertIsBool($recovered);
            echo "  ✅ API error recovery attempted\n";
            
            // Test file system error recovery
            $fileException = new \Exception('File permission denied');
            $recovered = $this->recoveryService->attemptRecovery($fileException, [
                'path' => '/tmp/test_file.txt'
            ]);
            
            $this->assertIsBool($recovered);
            echo "  ✅ File system error recovery attempted\n";
            
            // Test memory error recovery
            $memoryException = new \Exception('Memory allocation failed');
            $recovered = $this->recoveryService->attemptRecovery($memoryException);
            
            $this->assertIsBool($recovered);
            echo "  ✅ Memory error recovery attempted\n";
            
        } catch (\Exception $e) {
            echo "  ⚠️ Error recovery test skipped: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * Test error monitoring service
     */
    public function testErrorMonitoringService(): void
    {
        echo "📊 Testing Error Monitoring Service...\n";
        
        try {
            // Record some test errors
            $testErrors = [
                new \Exception('Test error 1'),
                new \RuntimeException('Test runtime error'),
                new \InvalidArgumentException('Test argument error'),
                new \Error('Test fatal error')
            ];
            
            foreach ($testErrors as $error) {
                $this->monitoringService->recordError($error, [
                    'request_id' => 'test_' . uniqid(),
                    'user_id' => 'test_user',
                    'ip_address' => '127.0.0.1',
                    'url' => '/test/endpoint'
                ]);
            }
            
            echo "  ✅ Test errors recorded\n";
            
            // Get error statistics
            $stats = $this->monitoringService->getErrorStats(3600);
            
            $this->assertIsArray($stats);
            $this->assertArrayHasKey('total_errors', $stats);
            $this->assertArrayHasKey('error_rate', $stats);
            $this->assertArrayHasKey('by_type', $stats);
            $this->assertArrayHasKey('by_severity', $stats);
            
            echo "  ✅ Error statistics retrieved\n";
            echo "  📈 Total errors: " . $stats['total_errors'] . "\n";
            echo "  📈 Error rate: " . round($stats['error_rate'], 2) . " errors/min\n";
            
            // Get error trends
            $trends = $this->monitoringService->getErrorTrends(7);
            
            $this->assertIsArray($trends);
            echo "  ✅ Error trends retrieved\n";
            
            // Generate error report
            $report = $this->monitoringService->generateErrorReport(3600);
            
            $this->assertIsArray($report);
            $this->assertArrayHasKey('summary', $report);
            $this->assertArrayHasKey('statistics', $report);
            $this->assertArrayHasKey('trends', $report);
            $this->assertArrayHasKey('recommendations', $report);
            
            echo "  ✅ Error report generated\n";
            echo "  📋 Report summary: " . $report['summary']['total_errors'] . " errors\n";
            
        } catch (\Exception $e) {
            echo "  ⚠️ Error monitoring test skipped: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * Test error recovery execution
     */
    public function testErrorRecoveryExecution(): void
    {
        echo "🔧 Testing Error Recovery Execution...\n";
        
        // Create a recoverable exception with custom recovery actions
        $exception = new ErrorRecoveryException(
            'Test recoverable error',
            1001,
            null,
            ['test' => 'context'],
            [],
            true
        );
        
        // Add a successful recovery action
        $exception->addRecoveryAction('test_recovery', function($params, $exception) {
            return true; // Simulate successful recovery
        });
        
        // Test recovery execution
        $recovered = $exception->executeRecovery();
        $this->assertTrue($recovered);
        
        echo "  ✅ Successful recovery action executed\n";
        
        // Add a failing recovery action
        $exception->addRecoveryAction('failing_recovery', function($params, $exception) {
            return false; // Simulate failed recovery
        });
        
        // Test with failing action (should still succeed due to first action)
        $recovered = $exception->executeRecovery();
        $this->assertTrue($recovered);
        
        echo "  ✅ Recovery with mixed actions handled correctly\n";
        
        // Test non-recoverable exception
        $nonRecoverableException = new ErrorRecoveryException(
            'Non-recoverable error',
            1002,
            null,
            [],
            [],
            false
        );
        
        $recovered = $nonRecoverableException->executeRecovery();
        $this->assertFalse($recovered);
        
        echo "  ✅ Non-recoverable exception handled correctly\n";
    }
    
    /**
     * Test error handler methods
     */
    public function testErrorHandlerMethods(): void
    {
        echo "🔍 Testing Error Handler Methods...\n";
        
        // Test handling a regular exception
        $testException = new \Exception('Test exception for handler');
        
        // Capture output to prevent it from showing in test results
        ob_start();
        try {
            $this->errorHandler->handleException($testException);
        } catch (\Throwable $e) {
            // Expected in test environment
        }
        $output = ob_get_clean();
        
        echo "  ✅ Exception handling method executed\n";
        
        // Test handling a WeBot exception
        $webotException = new WeBotException(
            'Test WeBot exception',
            400,
            null,
            ['test' => 'context'],
            'User-friendly error message'
        );
        
        ob_start();
        try {
            $this->errorHandler->handleException($webotException);
        } catch (\Throwable $e) {
            // Expected in test environment
        }
        $output = ob_get_clean();
        
        echo "  ✅ WeBot exception handling executed\n";
        
        // Test handling a recoverable exception
        $recoverableException = ErrorRecoveryException::databaseConnectionFailed('localhost', 3306);
        
        ob_start();
        try {
            $this->errorHandler->handleException($recoverableException);
        } catch (\Throwable $e) {
            // Expected in test environment
        }
        $output = ob_get_clean();
        
        echo "  ✅ Recoverable exception handling executed\n";
    }
    
    /**
     * Test error recovery statistics
     */
    public function testErrorRecoveryStatistics(): void
    {
        echo "📈 Testing Error Recovery Statistics...\n";
        
        try {
            // Attempt some recoveries to generate statistics
            $testExceptions = [
                new \Exception('Database error'),
                new \Exception('API timeout'),
                new \Exception('File not found')
            ];
            
            foreach ($testExceptions as $exception) {
                $this->recoveryService->attemptRecovery($exception);
            }
            
            // Get recovery statistics
            $stats = $this->recoveryService->getRecoveryStats();
            
            $this->assertIsArray($stats);
            $this->assertArrayHasKey('total_attempts', $stats);
            $this->assertArrayHasKey('recent_attempts', $stats);
            $this->assertArrayHasKey('by_type', $stats);
            
            echo "  ✅ Recovery statistics retrieved\n";
            echo "  📊 Total attempts: " . $stats['total_attempts'] . "\n";
            echo "  📊 Recent attempts: " . $stats['recent_attempts'] . "\n";
            
            // Clear recovery history
            $this->recoveryService->clearRecoveryHistory();
            
            $clearedStats = $this->recoveryService->getRecoveryStats();
            $this->assertEquals(0, $clearedStats['total_attempts']);
            
            echo "  ✅ Recovery history cleared successfully\n";
            
        } catch (\Exception $e) {
            echo "  ⚠️ Recovery statistics test skipped: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * Test error monitoring alerts
     */
    public function testErrorMonitoringAlerts(): void
    {
        echo "🚨 Testing Error Monitoring Alerts...\n";
        
        try {
            // Record multiple critical errors to trigger alerts
            for ($i = 0; $i < 3; $i++) {
                $criticalError = new \Error('Critical test error ' . $i);
                $this->monitoringService->recordError($criticalError, [
                    'request_id' => 'critical_test_' . $i
                ]);
            }
            
            // Get active alerts
            $alerts = $this->monitoringService->getActiveAlerts();
            
            $this->assertIsArray($alerts);
            echo "  ✅ Active alerts retrieved\n";
            echo "  🚨 Active alerts count: " . count($alerts) . "\n";
            
            // Clear error data
            $this->monitoringService->clearErrorData(0); // Clear all
            
            echo "  ✅ Error data cleared\n";
            
        } catch (\Exception $e) {
            echo "  ⚠️ Error monitoring alerts test skipped: " . $e->getMessage() . "\n";
        }
    }
}
