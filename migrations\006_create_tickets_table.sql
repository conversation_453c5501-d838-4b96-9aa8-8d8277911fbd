-- Migration: Create tickets table
-- Version: 006
-- Description: Create tickets table for support ticket system
-- Author: WeBot Team
-- Date: 2024-01-01

BEGIN;

-- Create tickets table
CREATE TABLE IF NOT EXISTS `tickets` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `user_id` BIGINT(20) NOT NULL COMMENT 'User Telegram ID',
    `subject` VARCHAR(255) NOT NULL COMMENT 'Ticket subject',
    `description` TEXT NOT NULL COMMENT 'Ticket description',
    `category` ENUM('technical', 'payment', 'connection', 'settings', 'call', 'other') NOT NULL DEFAULT 'other' COMMENT 'Ticket category',
    `priority` ENUM('low', 'medium', 'high', 'urgent') NOT NULL DEFAULT 'medium' COMMENT 'Ticket priority',
    `status` ENUM('open', 'in_progress', 'waiting', 'resolved', 'closed') NOT NULL DEFAULT 'open' COMMENT 'Ticket status',
    `assigned_to` BIGINT(20) NULL COMMENT 'Assigned admin user ID',
    `resolved_at` TIMESTAMP NULL COMMENT 'Resolution timestamp',
    `closed_at` TIMESTAMP NULL COMMENT 'Closure timestamp',
    `metadata` JSON NULL COMMENT 'Additional metadata',
    `tags` JSON NULL COMMENT 'Ticket tags',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_tickets_user` (`user_id`),
    KEY `idx_tickets_status` (`status`),
    KEY `idx_tickets_priority` (`priority`),
    KEY `idx_tickets_category` (`category`),
    KEY `idx_tickets_assigned` (`assigned_to`),
    KEY `idx_tickets_created` (`created_at`),
    KEY `idx_tickets_updated` (`updated_at`),
    KEY `idx_tickets_resolved` (`resolved_at`),
    CONSTRAINT `fk_tickets_user` FOREIGN KEY (`user_id`) REFERENCES `users`(`userid`) ON DELETE CASCADE,
    CONSTRAINT `fk_tickets_assigned` FOREIGN KEY (`assigned_to`) REFERENCES `users`(`userid`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Support tickets';

-- Create ticket replies table
CREATE TABLE IF NOT EXISTS `ticket_replies` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `ticket_id` INT(11) NOT NULL COMMENT 'Ticket ID',
    `user_id` BIGINT(20) NOT NULL COMMENT 'Reply author user ID',
    `message` TEXT NOT NULL COMMENT 'Reply message',
    `is_staff` BOOLEAN DEFAULT FALSE COMMENT 'Is reply from staff',
    `attachments` JSON NULL COMMENT 'Reply attachments',
    `metadata` JSON NULL COMMENT 'Additional metadata',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_replies_ticket` (`ticket_id`),
    KEY `idx_replies_user` (`user_id`),
    KEY `idx_replies_staff` (`is_staff`),
    KEY `idx_replies_created` (`created_at`),
    CONSTRAINT `fk_replies_ticket` FOREIGN KEY (`ticket_id`) REFERENCES `tickets`(`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_replies_user` FOREIGN KEY (`user_id`) REFERENCES `users`(`userid`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Ticket replies';

-- Create ticket attachments table
CREATE TABLE IF NOT EXISTS `ticket_attachments` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `ticket_id` INT(11) NULL COMMENT 'Ticket ID (if attached to ticket)',
    `reply_id` INT(11) NULL COMMENT 'Reply ID (if attached to reply)',
    `filename` VARCHAR(255) NOT NULL COMMENT 'Original filename',
    `file_path` VARCHAR(500) NOT NULL COMMENT 'File storage path',
    `file_size` INT(11) NOT NULL COMMENT 'File size in bytes',
    `mime_type` VARCHAR(100) NOT NULL COMMENT 'File MIME type',
    `uploaded_by` BIGINT(20) NOT NULL COMMENT 'Uploader user ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_attachments_ticket` (`ticket_id`),
    KEY `idx_attachments_reply` (`reply_id`),
    KEY `idx_attachments_uploader` (`uploaded_by`),
    CONSTRAINT `fk_attachments_ticket` FOREIGN KEY (`ticket_id`) REFERENCES `tickets`(`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_attachments_reply` FOREIGN KEY (`reply_id`) REFERENCES `ticket_replies`(`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_attachments_uploader` FOREIGN KEY (`uploaded_by`) REFERENCES `users`(`userid`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Ticket attachments';

-- Create view for ticket statistics
CREATE OR REPLACE VIEW `ticket_stats_view` AS
SELECT 
    t.id,
    t.subject,
    t.category,
    t.priority,
    t.status,
    t.user_id,
    t.assigned_to,
    u.first_name as user_name,
    a.first_name as assigned_name,
    COUNT(tr.id) as replies_count,
    SUM(CASE WHEN tr.is_staff = 1 THEN 1 ELSE 0 END) as staff_replies_count,
    SUM(CASE WHEN tr.is_staff = 0 THEN 1 ELSE 0 END) as user_replies_count,
    TIMESTAMPDIFF(HOUR, t.created_at, COALESCE(t.resolved_at, NOW())) as age_hours,
    CASE 
        WHEN t.resolved_at IS NOT NULL THEN 
            TIMESTAMPDIFF(HOUR, t.created_at, t.resolved_at)
        ELSE NULL 
    END as resolution_time_hours,
    t.created_at,
    t.updated_at,
    t.resolved_at,
    t.closed_at
FROM tickets t
LEFT JOIN users u ON t.user_id = u.userid
LEFT JOIN users a ON t.assigned_to = a.userid
LEFT JOIN ticket_replies tr ON t.id = tr.ticket_id
GROUP BY t.id;

-- Create procedure for ticket auto-assignment
DELIMITER //
CREATE OR REPLACE PROCEDURE AutoAssignTicket(
    IN ticket_id INT,
    IN category VARCHAR(50)
)
BEGIN
    DECLARE admin_id BIGINT(20);
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;
    
    -- Find admin with least assigned tickets
    SELECT userid INTO admin_id
    FROM users u
    WHERE u.isAdmin = 1
    ORDER BY (
        SELECT COUNT(*) 
        FROM tickets t 
        WHERE t.assigned_to = u.userid 
        AND t.status IN ('open', 'in_progress', 'waiting')
    ) ASC
    LIMIT 1;
    
    -- Assign ticket if admin found
    IF admin_id IS NOT NULL THEN
        UPDATE tickets 
        SET 
            assigned_to = admin_id,
            status = 'in_progress',
            updated_at = NOW()
        WHERE id = ticket_id;
    END IF;
    
    COMMIT;
END //
DELIMITER ;

-- Create procedure for ticket escalation
DELIMITER //
CREATE OR REPLACE PROCEDURE EscalateOldTickets()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;
    
    -- Escalate tickets older than 24 hours without response
    UPDATE tickets 
    SET 
        priority = CASE 
            WHEN priority = 'low' THEN 'medium'
            WHEN priority = 'medium' THEN 'high'
            WHEN priority = 'high' THEN 'urgent'
            ELSE priority
        END,
        updated_at = NOW()
    WHERE status IN ('open', 'in_progress')
    AND created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)
    AND id NOT IN (
        SELECT DISTINCT ticket_id 
        FROM ticket_replies 
        WHERE is_staff = 1
    );
    
    COMMIT;
END //
DELIMITER ;

-- Create triggers for ticket management
DELIMITER //
CREATE OR REPLACE TRIGGER `ticket_status_update` 
BEFORE UPDATE ON `tickets`
FOR EACH ROW
BEGIN
    -- Set resolved_at when status changes to resolved
    IF NEW.status = 'resolved' AND OLD.status != 'resolved' THEN
        SET NEW.resolved_at = NOW();
    END IF;
    
    -- Set closed_at when status changes to closed
    IF NEW.status = 'closed' AND OLD.status != 'closed' THEN
        SET NEW.closed_at = NOW();
    END IF;
    
    -- Clear resolved_at if status changes from resolved
    IF OLD.status = 'resolved' AND NEW.status != 'resolved' THEN
        SET NEW.resolved_at = NULL;
    END IF;
    
    -- Clear closed_at if status changes from closed
    IF OLD.status = 'closed' AND NEW.status != 'closed' THEN
        SET NEW.closed_at = NULL;
    END IF;
END //

CREATE OR REPLACE TRIGGER `ticket_reply_notification` 
AFTER INSERT ON `ticket_replies`
FOR EACH ROW
BEGIN
    -- Update ticket status based on who replied
    IF NEW.is_staff = 1 THEN
        -- Staff replied, set to waiting for user
        UPDATE tickets 
        SET status = 'waiting', updated_at = NOW() 
        WHERE id = NEW.ticket_id AND status = 'in_progress';
    ELSE
        -- User replied, set to in_progress
        UPDATE tickets 
        SET status = 'in_progress', updated_at = NOW() 
        WHERE id = NEW.ticket_id AND status = 'waiting';
    END IF;
END //
DELIMITER ;

-- Create indexes for performance
CREATE INDEX `idx_tickets_composite_status` ON `tickets` (`status`, `priority`, `created_at`);
CREATE INDEX `idx_tickets_composite_user` ON `tickets` (`user_id`, `status`, `created_at`);
CREATE INDEX `idx_tickets_composite_assigned` ON `tickets` (`assigned_to`, `status`, `created_at`);

-- Insert sample ticket categories and priorities data
INSERT IGNORE INTO `tickets` (
    `user_id`, `subject`, `description`, `category`, `priority`, `status`
) VALUES 
(
    (SELECT userid FROM users WHERE isAdmin = 1 LIMIT 1),
    'Test Ticket - Technical Issue',
    'This is a test ticket for technical support.',
    'technical',
    'medium',
    'open'
),
(
    (SELECT userid FROM users WHERE isAdmin = 1 LIMIT 1),
    'Test Ticket - Payment Problem',
    'This is a test ticket for payment issues.',
    'payment',
    'high',
    'open'
);

-- Record migration
INSERT INTO schema_versions (version, applied_at, description) 
VALUES (6, NOW(), 'Create tickets table and support system')
ON DUPLICATE KEY UPDATE applied_at = NOW();

COMMIT;

-- Verification
SELECT 'Tickets table created successfully' as message;
SELECT COUNT(*) as tickets_count FROM tickets;
SELECT 'Ticket replies table created' as message;
SELECT COUNT(*) as replies_count FROM ticket_replies;
SELECT 'Ticket attachments table created' as message;
SELECT COUNT(*) as attachments_count FROM ticket_attachments;
