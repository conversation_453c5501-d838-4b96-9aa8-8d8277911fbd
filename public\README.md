# WeBot Public Directory
## پوشه عمومی WeBot

این پوشه شامل تمام فایل‌های قابل دسترسی عمومی WeBot است که از طریق وب سرور قابل دسترسی هستند.

---

## 📁 ساختار پوشه‌ها

### 🎨 assets/
**فایل‌های استاتیک - CSS, JS, تصاویر، فونت‌ها**

#### css/
- **main.css** - استایل‌های اصلی
- **admin.css** - استایل‌های پنل ادمین
- **responsive.css** - استایل‌های ریسپانسیو
- **rtl.css** - پشتیبانی از راست به چپ

#### js/
- **app.js** - جاوااسکریپت اصلی
- **admin.js** - جاوااسکریپت پنل ادمین
- **utils.js** - توابع کمکی
- **qrcode.js** - تولید QR Code

#### images/
- **logo.png** - لوگوی WeBot
- **icons/** - آیکون‌ها
- **backgrounds/** - تصاویر پس‌زمینه
- **ui/** - تصاویر رابط کاربری

#### fonts/
- **IRANSans.ttf** - فونت فارسی
- **Roboto.woff2** - فونت انگلیسی

### 📤 uploads/
**فایل‌های آپلود شده توسط کاربران**

#### qrcodes/
- QR Codeهای تولید شده برای کانفیگ‌ها
- نام‌گذاری: `qr_{user_id}_{timestamp}.png`

#### receipts/
- رسیدهای پرداخت آپلود شده
- نام‌گذاری: `receipt_{payment_id}_{timestamp}.jpg`

#### avatars/
- تصاویر پروفایل کاربران
- نام‌گذاری: `avatar_{user_id}.jpg`

#### configs/
- فایل‌های کانفیگ دانلودی
- نام‌گذاری: `config_{service_id}_{timestamp}.txt`

### 🗂 temp/
**فایل‌های موقت**

#### cache/
- **api_cache/** - کش API ها
- **template_cache/** - کش قالب‌ها
- **image_cache/** - کش تصاویر

#### logs/
- **access.log** - لاگ دسترسی‌ها
- **error.log** - لاگ خطاها
- **payment.log** - لاگ پرداخت‌ها
- **panel.log** - لاگ عملیات پنل‌ها

#### sessions/
- **php_sessions/** - Session های PHP
- **user_sessions/** - Session های کاربری

---

## 🔧 تنظیمات وب سرور

### Apache (.htaccess)
```apache
# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# Prevent access to sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# Cache static assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>

# Compress files
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Rewrite rules
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]
```

### Nginx
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    root /path/to/webot/public;
    index index.php;

    # Security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";

    # Static files caching
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # PHP handling
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Deny access to sensitive files
    location ~ /\. {
        deny all;
    }

    # Handle all requests through index.php
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
}
```

---

## 🔐 امنیت

### محافظت از فایل‌ها
```php
// در ابتدای فایل‌های حساس
<?php
if (!defined('WEBOT_ACCESS')) {
    die('Direct access not allowed');
}
```

### اعتبارسنجی آپلود
```php
// بررسی نوع فایل
$allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
if (!in_array($_FILES['file']['type'], $allowedTypes)) {
    throw new Exception('Invalid file type');
}

// بررسی سایز فایل
$maxSize = 5 * 1024 * 1024; // 5MB
if ($_FILES['file']['size'] > $maxSize) {
    throw new Exception('File too large');
}
```

### پاکسازی فایل‌های موقت
```php
// پاکسازی خودکار فایل‌های قدیمی
function cleanupTempFiles() {
    $tempDir = __DIR__ . '/temp/';
    $files = glob($tempDir . '*');
    $now = time();
    
    foreach ($files as $file) {
        if (is_file($file)) {
            if ($now - filemtime($file) >= 24 * 3600) { // 24 hours
                unlink($file);
            }
        }
    }
}
```

---

## 📊 مانیتورینگ

### نظارت بر فضای دیسک
```php
function checkDiskSpace() {
    $uploadDir = __DIR__ . '/uploads/';
    $freeBytes = disk_free_space($uploadDir);
    $totalBytes = disk_total_space($uploadDir);
    
    $usagePercent = (($totalBytes - $freeBytes) / $totalBytes) * 100;
    
    if ($usagePercent > 90) {
        // Send alert
        error_log("Disk usage critical: {$usagePercent}%");
    }
}
```

### آمار فایل‌ها
```php
function getFileStats() {
    return [
        'qrcodes' => count(glob(__DIR__ . '/uploads/qrcodes/*.png')),
        'receipts' => count(glob(__DIR__ . '/uploads/receipts/*.jpg')),
        'configs' => count(glob(__DIR__ . '/uploads/configs/*.txt')),
        'total_size' => formatBytes(dirSize(__DIR__ . '/uploads/'))
    ];
}
```

---

## 🔄 نگهداری

### پاکسازی دوره‌ای
```bash
#!/bin/bash
# cleanup.sh - اجرا روزانه با cron

# پاکسازی فایل‌های موقت قدیمی‌تر از 24 ساعت
find /path/to/webot/public/temp -type f -mtime +1 -delete

# پاکسازی لاگ‌های قدیمی‌تر از 30 روز
find /path/to/webot/public/temp/logs -name "*.log" -mtime +30 -delete

# فشرده‌سازی لاگ‌های قدیمی‌تر از 7 روز
find /path/to/webot/public/temp/logs -name "*.log" -mtime +7 -exec gzip {} \;
```

### بکاپ فایل‌ها
```bash
#!/bin/bash
# backup.sh - بکاپ فایل‌های مهم

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/webot_files_$DATE"

mkdir -p $BACKUP_DIR

# بکاپ فایل‌های آپلود شده
cp -r /path/to/webot/public/uploads $BACKUP_DIR/

# بکاپ تنظیمات
cp -r /path/to/webot/public/assets $BACKUP_DIR/

# فشرده‌سازی
tar -czf $BACKUP_DIR.tar.gz $BACKUP_DIR
rm -rf $BACKUP_DIR
```

---

## 📋 بهترین روش‌ها

### 1. **سازماندهی فایل‌ها**:
- استفاده از زیرپوشه‌های منطقی
- نام‌گذاری استاندارد
- جداسازی فایل‌های موقت از دائمی

### 2. **بهینه‌سازی Performance**:
- فشرده‌سازی فایل‌های CSS/JS
- استفاده از CDN برای فایل‌های استاتیک
- کش کردن مناسب

### 3. **امنیت**:
- محدودیت نوع و سایز فایل‌ها
- اسکن ویروس برای فایل‌های آپلودی
- پاکسازی دوره‌ای

### 4. **مانیتورینگ**:
- نظارت بر فضای دیسک
- لاگ کردن دسترسی‌ها
- آمارگیری استفاده

---

*این ساختار برای مدیریت حرفه‌ای فایل‌های عمومی WeBot طراحی شده است.*
