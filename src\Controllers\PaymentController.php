<?php

declare(strict_types=1);

namespace WeBot\Controllers;

use WeBot\Exceptions\PaymentException;
use WeBot\Exceptions\ValidationException;
use WeBot\Utils\Helper;

/**
 * Payment Controller
 *
 * Handles payment processing, wallet management,
 * and financial transactions.
 *
 * @package WeBot\Controllers
 * @version 2.0
 */
class PaymentController extends BaseController
{
    /**
     * Handle payment callback queries
     */
    public function handleCallback(array $callbackQuery): array
    {
        try {
            $this->initialize(['callback_query' => $callbackQuery]);

            $data = $this->data;

            return match (true) {
                $data === 'my_wallet' => $this->showWallet(),
                $data === 'add_balance' => $this->showAddBalance(),
                $data === 'payment_history' => $this->showPaymentHistory(),
                str_starts_with($data, 'pay_') => $this->handlePaymentAction($data),
                str_starts_with($data, 'gateway_') => $this->handleGatewaySelection($data),
                str_starts_with($data, 'amount_') => $this->handleAmountSelection($data),
                default => $this->handleUnknownCallback()
            };
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    /**
     * Show wallet information
     */
    public function showWallet(): array
    {
        $userInfo = $this->getUserInfo();
        $balance = $userInfo['wallet'] ?? 0;

        $text = "💰 کیف پول شما\n\n";
        $text .= "💳 موجودی فعلی: " . Helper::formatPrice($balance) . "\n\n";

        // Get recent transactions
        $transactions = $this->getRecentTransactions();

        if (!empty($transactions)) {
            $text .= "📋 آخرین تراکنش‌ها:\n";
            foreach (array_slice($transactions, 0, 5) as $transaction) {
                $type = $transaction['type'] === 'deposit' ? '➕' : '➖';
                $amount = Helper::formatPrice($transaction['amount']);
                $date = date('Y/m/d H:i', strtotime($transaction['created_at']));
                $text .= "{$type} {$amount} - {$date}\n";
            }
            $text .= "\n";
        }

        $keyboard = $this->createInlineKeyboard([
            [
                ['text' => '➕ افزایش موجودی', 'callback_data' => 'add_balance'],
                ['text' => '📋 تاریخچه تراکنش‌ها', 'callback_data' => 'payment_history']
            ],
            [
                ['text' => '🔙 بازگشت', 'callback_data' => 'main_menu']
            ]
        ]);

        return $this->editMessage($text, $keyboard);
    }

    /**
     * Show add balance options
     */
    private function showAddBalance(): array
    {
        $text = "➕ افزایش موجودی کیف پول\n\n";
        $text .= "مبلغ مورد نظر خود را انتخاب کنید:";

        $amounts = [10000, 20000, 50000, 100000, 200000, 500000];
        $keyboard = [];

        // Create amount buttons in pairs
        for ($i = 0; $i < count($amounts); $i += 2) {
            $row = [];
            $row[] = ['text' => Helper::formatPrice($amounts[$i]), 'callback_data' => "amount_{$amounts[$i]}"];
            if (isset($amounts[$i + 1])) {
                $row[] = ['text' => Helper::formatPrice($amounts[$i + 1]), 'callback_data' => "amount_{$amounts[$i + 1]}"];
            }
            $keyboard[] = $row;
        }

        $keyboard[] = [
            ['text' => '💰 مبلغ دلخواه', 'callback_data' => 'amount_custom']
        ];
        $keyboard[] = [
            ['text' => '🔙 بازگشت', 'callback_data' => 'my_wallet']
        ];

        return $this->editMessage($text, $this->createInlineKeyboard($keyboard));
    }

    /**
     * Handle amount selection
     */
    private function handleAmountSelection(string $data): array
    {
        $parts = explode('_', $data);

        if ($parts[1] === 'custom') {
            return $this->requestCustomAmount();
        }

        $amount = (int) $parts[1];

        // Validate amount
        if ($amount < 1000 || $amount > 10000000) {
            $this->answerCallback('مبلغ نامعتبر');
            return ['ok' => true];
        }

        return $this->showPaymentGateways($amount);
    }

    /**
     * Request custom amount
     */
    private function requestCustomAmount(): array
    {
        $text = "💰 مبلغ دلخواه\n\n";
        $text .= "لطفاً مبلغ مورد نظر خود را به تومان وارد کنید:\n";
        $text .= "(حداقل: 1,000 تومان - حداکثر: 10,000,000 تومان)";

        $this->setUserStep('waiting_custom_amount');

        $keyboard = $this->createInlineKeyboard([
            [['text' => '❌ لغو', 'callback_data' => 'add_balance']]
        ]);

        return $this->editMessage($text, $keyboard);
    }

    /**
     * Show payment gateways
     */
    private function showPaymentGateways(int $amount): array
    {
        $text = "💳 انتخاب درگاه پرداخت\n\n";
        $text .= "مبلغ: " . Helper::formatPrice($amount) . "\n\n";
        $text .= "لطفاً درگاه پرداخت مورد نظر خود را انتخاب کنید:";

        $gateways = $this->getAvailableGateways();
        $keyboard = [];

        foreach ($gateways as $gateway) {
            if ($gateway['active']) {
                $keyboard[] = [
                    ['text' => $gateway['title'], 'callback_data' => "gateway_{$gateway['name']}_{$amount}"]
                ];
            }
        }

        $keyboard[] = [
            ['text' => '🔙 بازگشت', 'callback_data' => 'add_balance']
        ];

        return $this->editMessage($text, $this->createInlineKeyboard($keyboard));
    }

    /**
     * Handle gateway selection
     */
    private function handleGatewaySelection(string $data): array
    {
        $parts = explode('_', $data);
        $gatewayName = $parts[1];
        $amount = (int) $parts[2];

        try {
            // Create payment record
            $paymentId = $this->createPaymentRecord($amount, $gatewayName);

            // Generate payment URL
            $paymentUrl = $this->generatePaymentUrl($paymentId, $amount, $gatewayName);

            $text = "💳 پرداخت\n\n";
            $text .= "مبلغ: " . Helper::formatPrice($amount) . "\n";
            $text .= "درگاه: {$gatewayName}\n\n";
            $text .= "برای تکمیل پرداخت روی دکمه زیر کلیک کنید:";

            $keyboard = $this->createInlineKeyboard([
                [
                    ['text' => '💳 پرداخت', 'url' => $paymentUrl]
                ],
                [
                    ['text' => '✅ تأیید پرداخت', 'callback_data' => "pay_verify_{$paymentId}"],
                    ['text' => '❌ لغو پرداخت', 'callback_data' => "pay_cancel_{$paymentId}"]
                ],
                [
                    ['text' => '🔙 بازگشت', 'callback_data' => 'my_wallet']
                ]
            ]);

            return $this->editMessage($text, $keyboard);
        } catch (PaymentException $e) {
            $this->answerCallback($e->getUserMessage());
            return ['ok' => true];
        }
    }

    /**
     * Handle payment actions
     */
    private function handlePaymentAction(string $data): array
    {
        $parts = explode('_', $data);
        $action = $parts[1];
        $paymentId = (int) $parts[2];

        return match ($action) {
            'verify' => $this->verifyPayment($paymentId),
            'cancel' => $this->cancelPayment($paymentId),
            default => $this->handleUnknownCallback()
        };
    }

    /**
     * Verify payment
     */
    private function verifyPayment(int $paymentId): array
    {
        try {
            $payment = $this->getPaymentById($paymentId);

            if (!$payment) {
                throw new PaymentException('پرداخت یافت نشد');
            }

            if ($payment['user_id'] != $this->fromId) {
                throw new PaymentException('دسترسی غیرمجاز');
            }

            if ($payment['status'] === 'completed') {
                $this->answerCallback('این پرداخت قبلاً تأیید شده است');
                return $this->showWallet();
            }

            // Verify with gateway
            $verified = $this->verifyWithGateway($payment);

            if ($verified) {
                // Update payment status
                $this->updatePaymentStatus($paymentId, 'completed');

                // Add to user wallet
                $this->addToWallet($this->fromId, $payment['amount']);

                $this->logAction('payment_verified', [
                    'payment_id' => $paymentId,
                    'amount' => $payment['amount']
                ]);

                $text = "✅ پرداخت موفق\n\n";
                $text .= "مبلغ " . Helper::formatPrice($payment['amount']) . " به کیف پول شما اضافه شد.";

                $this->answerCallback('پرداخت با موفقیت تأیید شد');
            } else {
                $text = "❌ پرداخت ناموفق\n\n";
                $text .= "پرداخت شما تأیید نشد. در صورت کسر وجه، طی 24 ساعت بازگردانده می‌شود.";

                $this->answerCallback('پرداخت تأیید نشد');
            }

            return $this->showWallet();
        } catch (PaymentException $e) {
            $this->answerCallback($e->getUserMessage());
            return ['ok' => true];
        }
    }

    /**
     * Cancel payment
     */
    private function cancelPayment(int $paymentId): array
    {
        $this->updatePaymentStatus($paymentId, 'cancelled');
        $this->answerCallback('پرداخت لغو شد');

        return $this->showWallet();
    }

    /**
     * Show payment history
     */
    private function showPaymentHistory(): array
    {
        $transactions = $this->getRecentTransactions(20);

        $text = "📋 تاریخچه تراکنش‌ها\n\n";

        if (empty($transactions)) {
            $text .= "هیچ تراکنشی یافت نشد.";
        } else {
            foreach ($transactions as $transaction) {
                $type = $transaction['type'] === 'deposit' ? '➕ واریز' : '➖ برداشت';
                $amount = Helper::formatPrice($transaction['amount']);
                $date = date('Y/m/d H:i', strtotime($transaction['created_at']));
                $status = $this->getTransactionStatusText($transaction['status']);

                $text .= "{$type}: {$amount}\n";
                $text .= "📅 {$date} | {$status}\n";
                $text .= "━━━━━━━━━━━━━━━━━━━━\n";
            }
        }

        $keyboard = $this->createInlineKeyboard([
            [['text' => '🔙 بازگشت', 'callback_data' => 'my_wallet']]
        ]);

        return $this->editMessage($text, $keyboard);
    }

    /**
     * Get recent transactions
     */
    private function getRecentTransactions(int $limit = 10): array
    {
        $stmt = $this->database->prepare("
            SELECT * FROM `transactions` 
            WHERE `user_id` = ? 
            ORDER BY `created_at` DESC 
            LIMIT ?
        ");
        $stmt->bind_param("ii", $this->fromId, $limit);
        $stmt->execute();
        $result = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
        $stmt->close();

        return $result;
    }

    /**
     * Get available payment gateways
     */
    private function getAvailableGateways(): array
    {
        // This would typically come from database or config
        return [
            [
                'name' => 'zarinpal',
                'title' => '💳 زرین‌پال',
                'active' => true
            ],
            [
                'name' => 'nowpayments',
                'title' => '🪙 ارز دیجیتال',
                'active' => true
            ]
        ];
    }

    /**
     * Create payment record
     */
    private function createPaymentRecord(int $amount, string $gateway): int
    {
        $hashId = Helper::randomString(32);

        $stmt = $this->database->prepare("
            INSERT INTO `payments` (
                `user_id`, `amount`, `gateway`, `hash_id`, 
                `status`, `created_at`
            ) VALUES (?, ?, ?, ?, 'pending', NOW())
        ");

        $stmt->bind_param("iiss", $this->fromId, $amount, $gateway, $hashId);
        $success = $stmt->execute();
        $paymentId = $this->database->getLastInsertId();
        $stmt->close();

        if (!$success) {
            throw new PaymentException('خطا در ایجاد پرداخت');
        }

        return $paymentId;
    }

    /**
     * Generate payment URL
     */
    private function generatePaymentUrl(int $paymentId, int $amount, string $gateway): string
    {
        // This would integrate with actual payment gateways
        // For now, return a placeholder URL
        return "https://example.com/payment/{$paymentId}";
    }

    /**
     * Get payment by ID
     */
    private function getPaymentById(int $paymentId): ?array
    {
        $stmt = $this->database->prepare("SELECT * FROM `payments` WHERE `id` = ?");
        $stmt->bind_param("i", $paymentId);
        $stmt->execute();
        $result = $stmt->get_result()->fetch_assoc();
        $stmt->close();

        return $result ?: null;
    }

    /**
     * Verify with gateway
     */
    private function verifyWithGateway(array $payment): bool
    {
        // This would integrate with actual payment gateway verification
        // For now, return true as placeholder
        return true;
    }

    /**
     * Update payment status
     */
    private function updatePaymentStatus(int $paymentId, string $status): bool
    {
        $stmt = $this->database->prepare("UPDATE `payments` SET `status` = ? WHERE `id` = ?");
        $stmt->bind_param("si", $status, $paymentId);
        $success = $stmt->execute();
        $stmt->close();

        return $success;
    }

    /**
     * Add to user wallet
     */
    private function addToWallet(int $userId, int $amount): bool
    {
        $stmt = $this->database->prepare("UPDATE `users` SET `wallet` = `wallet` + ? WHERE `userid` = ?");
        $stmt->bind_param("ii", $amount, $userId);
        $success = $stmt->execute();
        $stmt->close();

        return $success;
    }

    /**
     * Get transaction status text
     */
    private function getTransactionStatusText(string $status): string
    {
        return match ($status) {
            'completed' => '✅ موفق',
            'pending' => '⏳ در انتظار',
            'failed' => '❌ ناموفق',
            'cancelled' => '🚫 لغو شده',
            default => '❓ نامشخص'
        };
    }

    private function handleUnknownCallback(): array
    {
        $this->answerCallback('گزینه نامعتبر');
        return ['ok' => true];
    }
}
