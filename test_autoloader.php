<?php
/**
 * Autoloader Functionality Test for WeBot
 * 
 * This script tests the autoloader functionality and verifies
 * that all core classes can be loaded properly.
 */

declare(strict_types=1);

echo "=== WeBot Autoloader Test ===\n\n";

// Test 1: Load the main autoloader
echo "1. Main Autoloader Test:\n";
try {
    require_once __DIR__ . '/autoload.php';
    echo "   ✅ Main autoloader loaded successfully\n";
} catch (Exception $e) {
    echo "   ❌ Failed to load main autoloader: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 2: Test core WeBot classes
echo "\n2. Core Classes Loading Test:\n";
$coreClasses = [
    'WeBot\Core\Application' => 'Main application class',
    'WeBot\Core\Config' => 'Configuration manager',
    'WeBot\Core\Database' => 'Database connection',
    'WeBot\Utils\Logger' => 'Logging utility',
    'WeBot\Services\TelegramService' => 'Telegram service',
    'WeBot\Services\DatabaseService' => 'Database service'
];

$classLoadingOk = true;
foreach ($coreClasses as $className => $description) {
    if (class_exists($className)) {
        echo "   ✅ {$className} - {$description}\n";
    } else {
        echo "   ❌ {$className} - {$description}\n";
        $classLoadingOk = false;
    }
}

// Test 3: Test model classes
echo "\n3. Model Classes Loading Test:\n";
$modelClasses = [
    'WeBot\Models\User' => 'User model',
    'WeBot\Models\Service' => 'Service model',
    'WeBot\Models\Payment' => 'Payment model',
    'WeBot\Models\Panel' => 'Panel model',
    'WeBot\Models\Ticket' => 'Ticket model'
];

foreach ($modelClasses as $className => $description) {
    if (class_exists($className)) {
        echo "   ✅ {$className} - {$description}\n";
    } else {
        echo "   ❌ {$className} - {$description}\n";
        $classLoadingOk = false;
    }
}

// Test 4: Test controller classes
echo "\n4. Controller Classes Loading Test:\n";
$controllerClasses = [
    'WeBot\Controllers\BaseController' => 'Base controller',
    'WeBot\Controllers\UserController' => 'User controller',
    'WeBot\Controllers\PaymentController' => 'Payment controller',
    'WeBot\Controllers\ServiceController' => 'Service controller',
    'WeBot\Controllers\AdminController' => 'Admin controller'
];

foreach ($controllerClasses as $className => $description) {
    if (class_exists($className)) {
        echo "   ✅ {$className} - {$description}\n";
    } else {
        echo "   ❌ {$className} - {$description}\n";
        $classLoadingOk = false;
    }
}

// Test 5: Test adapter classes
echo "\n5. Adapter Classes Loading Test:\n";
$adapterClasses = [
    'WeBot\Adapters\MarzbanAdapter' => 'Marzban panel adapter',
    'WeBot\Adapters\MarzneShinAdapter' => 'MarzneShin panel adapter',
    'WeBot\Adapters\XUIAdapter' => 'X-UI panel adapter'
];

foreach ($adapterClasses as $className => $description) {
    if (class_exists($className)) {
        echo "   ✅ {$className} - {$description}\n";
    } else {
        echo "   ❌ {$className} - {$description}\n";
        $classLoadingOk = false;
    }
}

// Test 6: Test instantiation of key classes
echo "\n6. Class Instantiation Test:\n";
$instantiationOk = true;

try {
    $config = new WeBot\Core\Config();
    echo "   ✅ Config class instantiated successfully\n";
} catch (Exception $e) {
    echo "   ❌ Failed to instantiate Config: " . $e->getMessage() . "\n";
    $instantiationOk = false;
}

try {
    $logger = WeBot\Utils\Logger::getInstance();
    echo "   ✅ Logger singleton instantiated successfully\n";
} catch (Exception $e) {
    echo "   ❌ Failed to instantiate Logger: " . $e->getMessage() . "\n";
    $instantiationOk = false;
}

// Test 7: Test helper functions
echo "\n7. Helper Functions Test:\n";
$helperFunctionsOk = true;

if (function_exists('env')) {
    echo "   ✅ env() helper function available\n";
} else {
    echo "   ❌ env() helper function not available\n";
    $helperFunctionsOk = false;
}

if (function_exists('config')) {
    echo "   ✅ config() helper function available\n";
} else {
    echo "   ❌ config() helper function not available\n";
    $helperFunctionsOk = false;
}

if (function_exists('logger')) {
    echo "   ✅ logger() helper function available\n";
} else {
    echo "   ❌ logger() helper function not available\n";
    $helperFunctionsOk = false;
}

echo "\n=== Overall Status ===\n";
if ($classLoadingOk && $instantiationOk && $helperFunctionsOk) {
    echo "✅ Autoloader is working perfectly!\n";
    echo "ℹ️  All core classes can be loaded and instantiated\n";
    exit(0);
} else {
    echo "❌ Autoloader has some issues.\n";
    echo "\n🔧 To fix autoloader issues:\n";
    echo "   1. Check PSR-4 namespace configuration in composer.json\n";
    echo "   2. Run: php composer.phar dump-autoload\n";
    echo "   3. Verify class file locations match namespace structure\n";
    echo "   4. Check for syntax errors in class files\n";
    exit(1);
}
