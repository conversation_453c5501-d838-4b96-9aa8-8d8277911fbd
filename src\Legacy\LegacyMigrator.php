<?php

declare(strict_types=1);

namespace WeBot\Legacy;

use WeBot\Core\Database;
use WeBot\Core\CacheManager;
use WeBot\Utils\Logger;

/**
 * Legacy Migrator
 * 
 * Migrates data and functionality from old WeBot structure
 * to new WeBot 2.0 architecture.
 * 
 * @package WeBot\Legacy
 * @version 2.0
 */
class LegacyMigrator
{
    private Database $database;
    private CacheManager $cache;
    private Logger $logger;
    private array $migrationStats = [];
    private string $legacyPath;

    public function __construct(Database $database, CacheManager $cache, Logger $logger, string $legacyPath = '')
    {
        $this->database = $database;
        $this->cache = $cache;
        $this->logger = $logger;
        $this->legacyPath = $legacyPath ?: __DIR__ . '/../../';
    }

    /**
     * Run complete legacy migration
     */
    public function migrate(): array
    {
        $this->logger->info('Starting legacy migration process');
        
        try {
            // Step 1: Backup current data
            $this->createBackup();
            
            // Step 2: Migrate database structure
            $this->migrateDatabaseStructure();
            
            // Step 3: Migrate user data
            $this->migrateUserData();
            
            // Step 4: Migrate service data
            $this->migrateServiceData();
            
            // Step 5: Migrate payment data
            $this->migratePaymentData();
            
            // Step 6: Migrate settings
            $this->migrateSettings();
            
            // Step 7: Migrate files and assets
            $this->migrateFiles();
            
            // Step 8: Create compatibility layer
            $this->createCompatibilityLayer();
            
            $this->logger->info('Legacy migration completed successfully', $this->migrationStats);
            
            return [
                'success' => true,
                'message' => 'Migration completed successfully',
                'stats' => $this->migrationStats
            ];
            
        } catch (\Exception $e) {
            $this->logger->error('Legacy migration failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'stats' => $this->migrationStats
            ];
        }
    }

    /**
     * Create backup of current data
     */
    private function createBackup(): void
    {
        $this->logger->info('Creating backup before migration');
        
        $backupPath = 'storage/backups/legacy_migration_' . date('Y-m-d_H-i-s');
        
        if (!is_dir($backupPath)) {
            mkdir($backupPath, 0755, true);
        }
        
        // Backup database
        $this->backupDatabase($backupPath);
        
        // Backup files
        $this->backupFiles($backupPath);
        
        $this->migrationStats['backup_created'] = $backupPath;
    }

    /**
     * Migrate database structure
     */
    private function migrateDatabaseStructure(): void
    {
        $this->logger->info('Migrating database structure');
        
        // Check if legacy tables exist and need migration
        $legacyTables = $this->getLegacyTables();
        
        foreach ($legacyTables as $table) {
            $this->migrateLegacyTable($table);
        }
        
        $this->migrationStats['tables_migrated'] = count($legacyTables);
    }

    /**
     * Migrate user data from legacy format
     */
    private function migrateUserData(): void
    {
        $this->logger->info('Migrating user data');
        
        $migratedCount = 0;
        
        // Get legacy users
        $legacyUsers = $this->database->query("
            SELECT * FROM users 
            WHERE migrated_to_v2 IS NULL OR migrated_to_v2 = 0
        ");
        
        foreach ($legacyUsers as $legacyUser) {
            try {
                // Convert legacy user format to new format
                $newUserData = $this->convertLegacyUser($legacyUser);
                
                // Update user with new structure
                $this->database->update('users', $newUserData, ['userid' => $legacyUser['userid']]);
                
                // Mark as migrated
                $this->database->update('users', 
                    ['migrated_to_v2' => 1, 'migration_date' => date('Y-m-d H:i:s')], 
                    ['userid' => $legacyUser['userid']]
                );
                
                $migratedCount++;
                
            } catch (\Exception $e) {
                $this->logger->error('Failed to migrate user', [
                    'user_id' => $legacyUser['userid'],
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        $this->migrationStats['users_migrated'] = $migratedCount;
    }

    /**
     * Migrate service data
     */
    private function migrateServiceData(): void
    {
        $this->logger->info('Migrating service data');
        
        $migratedCount = 0;
        
        // Get legacy services
        $legacyServices = $this->database->query("
            SELECT * FROM services 
            WHERE migrated_to_v2 IS NULL OR migrated_to_v2 = 0
        ");
        
        foreach ($legacyServices as $legacyService) {
            try {
                // Convert legacy service format
                $newServiceData = $this->convertLegacyService($legacyService);
                
                // Update service
                $this->database->update('services', $newServiceData, ['id' => $legacyService['id']]);
                
                // Mark as migrated
                $this->database->update('services', 
                    ['migrated_to_v2' => 1, 'migration_date' => date('Y-m-d H:i:s')], 
                    ['id' => $legacyService['id']]
                );
                
                $migratedCount++;
                
            } catch (\Exception $e) {
                $this->logger->error('Failed to migrate service', [
                    'service_id' => $legacyService['id'],
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        $this->migrationStats['services_migrated'] = $migratedCount;
    }

    /**
     * Migrate payment data
     */
    private function migratePaymentData(): void
    {
        $this->logger->info('Migrating payment data');
        
        $migratedCount = 0;
        
        // Get legacy payments
        $legacyPayments = $this->database->query("
            SELECT * FROM payments 
            WHERE migrated_to_v2 IS NULL OR migrated_to_v2 = 0
        ");
        
        foreach ($legacyPayments as $legacyPayment) {
            try {
                // Convert legacy payment format
                $newPaymentData = $this->convertLegacyPayment($legacyPayment);
                
                // Update payment
                $this->database->update('payments', $newPaymentData, ['id' => $legacyPayment['id']]);
                
                // Mark as migrated
                $this->database->update('payments', 
                    ['migrated_to_v2' => 1, 'migration_date' => date('Y-m-d H:i:s')], 
                    ['id' => $legacyPayment['id']]
                );
                
                $migratedCount++;
                
            } catch (\Exception $e) {
                $this->logger->error('Failed to migrate payment', [
                    'payment_id' => $legacyPayment['id'],
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        $this->migrationStats['payments_migrated'] = $migratedCount;
    }

    /**
     * Migrate settings from legacy format
     */
    private function migrateSettings(): void
    {
        $this->logger->info('Migrating settings');
        
        // Migrate bot settings
        $this->migrateBotSettings();
        
        // Migrate panel settings
        $this->migratePanelSettings();
        
        // Migrate payment gateway settings
        $this->migratePaymentSettings();
    }

    /**
     * Migrate files and assets
     */
    private function migrateFiles(): void
    {
        $this->logger->info('Migrating files and assets');
        
        $migratedFiles = 0;
        
        // Migrate uploaded files
        $oldUploadsPath = $this->legacyPath . 'uploads/';
        $newUploadsPath = 'storage/uploads/';
        
        if (is_dir($oldUploadsPath)) {
            $this->copyDirectory($oldUploadsPath, $newUploadsPath);
            $migratedFiles += $this->countFiles($oldUploadsPath);
        }
        
        // Migrate assets
        $oldAssetsPath = $this->legacyPath . 'assets/';
        $newAssetsPath = 'public/assets/';
        
        if (is_dir($oldAssetsPath)) {
            $this->copyDirectory($oldAssetsPath, $newAssetsPath);
            $migratedFiles += $this->countFiles($oldAssetsPath);
        }
        
        $this->migrationStats['files_migrated'] = $migratedFiles;
    }

    /**
     * Create compatibility layer for legacy code
     */
    private function createCompatibilityLayer(): void
    {
        $this->logger->info('Creating compatibility layer');
        
        // Create legacy function wrappers
        $this->createLegacyFunctionWrappers();
        
        // Create legacy variable mappings
        $this->createLegacyVariableMappings();
        
        // Create legacy route mappings
        $this->createLegacyRouteMappings();
    }

    /**
     * Convert legacy user to new format
     */
    private function convertLegacyUser(array $legacyUser): array
    {
        return [
            'first_name' => $legacyUser['first_name'] ?? '',
            'last_name' => $legacyUser['last_name'] ?? '',
            'username' => $legacyUser['username'] ?? '',
            'language_code' => $legacyUser['language_code'] ?? 'fa',
            'status' => $this->convertUserStatus($legacyUser['step'] ?? ''),
            'role' => $legacyUser['isAdmin'] ? 'admin' : 'user',
            'wallet' => (float) ($legacyUser['wallet'] ?? 0),
            'updated_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Convert legacy service to new format
     */
    private function convertLegacyService(array $legacyService): array
    {
        return [
            'username' => $legacyService['username'] ?? '',
            'server_id' => $legacyService['server_id'] ?? 1,
            'panel_id' => $this->mapServerToPanel($legacyService['server_id'] ?? 1),
            'data_limit' => (int) ($legacyService['data_limit'] ?? 0),
            'expire_date' => $legacyService['expire_date'] ?? null,
            'status' => $this->convertServiceStatus($legacyService['status'] ?? ''),
            'updated_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Convert legacy payment to new format
     */
    private function convertLegacyPayment(array $legacyPayment): array
    {
        return [
            'amount' => (float) ($legacyPayment['amount'] ?? 0),
            'currency' => $legacyPayment['currency'] ?? 'USD',
            'status' => $this->convertPaymentStatus($legacyPayment['status'] ?? ''),
            'gateway' => $legacyPayment['gateway'] ?? 'manual',
            'transaction_id' => $legacyPayment['transaction_id'] ?? '',
            'description' => $legacyPayment['description'] ?? '',
            'updated_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Convert legacy user status
     */
    private function convertUserStatus(string $legacyStep): string
    {
        return match ($legacyStep) {
            'banned' => 'banned',
            'suspended' => 'suspended',
            default => 'active'
        };
    }

    /**
     * Convert legacy service status
     */
    private function convertServiceStatus(string $legacyStatus): string
    {
        return match ($legacyStatus) {
            'active' => 'active',
            'expired' => 'expired',
            'suspended' => 'suspended',
            default => 'active'
        };
    }

    /**
     * Convert legacy payment status
     */
    private function convertPaymentStatus(string $legacyStatus): string
    {
        return match ($legacyStatus) {
            'completed', 'success' => 'completed',
            'failed', 'error' => 'failed',
            'pending', 'waiting' => 'pending',
            'cancelled' => 'cancelled',
            default => 'pending'
        };
    }

    /**
     * Map legacy server ID to new panel ID
     */
    private function mapServerToPanel(int $serverId): int
    {
        // Get or create panel mapping
        $panel = $this->database->selectOne('panels', ['legacy_server_id' => $serverId]);
        
        if ($panel) {
            return $panel['id'];
        }
        
        // Create default panel if not exists
        $panelData = [
            'name' => "Legacy Server {$serverId}",
            'type' => 'marzban',
            'url' => 'https://legacy-server.example.com',
            'username' => 'admin',
            'password' => 'encrypted_password',
            'status' => 'offline',
            'is_active' => false,
            'legacy_server_id' => $serverId
        ];
        
        return $this->database->insert('panels', $panelData);
    }

    /**
     * Get legacy tables that need migration
     */
    private function getLegacyTables(): array
    {
        $tables = [];
        
        // Check for legacy table structures
        $result = $this->database->query("SHOW TABLES");
        
        foreach ($result as $row) {
            $tableName = array_values($row)[0];
            
            // Check if table needs migration
            if ($this->needsMigration($tableName)) {
                $tables[] = $tableName;
            }
        }
        
        return $tables;
    }

    /**
     * Check if table needs migration
     */
    private function needsMigration(string $tableName): bool
    {
        // Check if table has legacy structure
        $columns = $this->database->query("SHOW COLUMNS FROM `{$tableName}`");
        
        // Look for migration markers
        foreach ($columns as $column) {
            if ($column['Field'] === 'migrated_to_v2') {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Migrate legacy table structure
     */
    private function migrateLegacyTable(string $tableName): void
    {
        // Add migration tracking columns if not exists
        $this->database->query("
            ALTER TABLE `{$tableName}` 
            ADD COLUMN IF NOT EXISTS `migrated_to_v2` TINYINT(1) DEFAULT 0,
            ADD COLUMN IF NOT EXISTS `migration_date` TIMESTAMP NULL
        ");
    }

    /**
     * Additional helper methods for file operations, backups, etc.
     */
    private function backupDatabase(string $backupPath): void
    {
        // Implementation for database backup
        $this->logger->info('Database backup created', ['path' => $backupPath]);
    }

    private function backupFiles(string $backupPath): void
    {
        // Implementation for file backup
        $this->logger->info('Files backup created', ['path' => $backupPath]);
    }

    private function copyDirectory(string $source, string $destination): void
    {
        if (!is_dir($destination)) {
            mkdir($destination, 0755, true);
        }
        
        $files = scandir($source);
        foreach ($files as $file) {
            if ($file !== '.' && $file !== '..') {
                $sourcePath = $source . '/' . $file;
                $destPath = $destination . '/' . $file;
                
                if (is_dir($sourcePath)) {
                    $this->copyDirectory($sourcePath, $destPath);
                } else {
                    copy($sourcePath, $destPath);
                }
            }
        }
    }

    private function countFiles(string $directory): int
    {
        $count = 0;
        $files = scandir($directory);
        
        foreach ($files as $file) {
            if ($file !== '.' && $file !== '..') {
                $filePath = $directory . '/' . $file;
                if (is_dir($filePath)) {
                    $count += $this->countFiles($filePath);
                } else {
                    $count++;
                }
            }
        }
        
        return $count;
    }

    private function migrateBotSettings(): void
    {
        // Implementation for bot settings migration
    }

    private function migratePanelSettings(): void
    {
        // Implementation for panel settings migration
    }

    private function migratePaymentSettings(): void
    {
        // Implementation for payment settings migration
    }

    private function createLegacyFunctionWrappers(): void
    {
        // Implementation for legacy function wrappers
    }

    private function createLegacyVariableMappings(): void
    {
        // Implementation for legacy variable mappings
    }

    private function createLegacyRouteMappings(): void
    {
        // Implementation for legacy route mappings
    }
}
