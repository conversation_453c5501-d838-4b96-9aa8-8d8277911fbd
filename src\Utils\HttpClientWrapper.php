<?php

declare(strict_types=1);

namespace WeBot\Utils;

/**
 * HTTP Client Wrapper
 *
 * Provides a unified interface for HTTP clients that's compatible
 * with both GuzzleHttp\Client and MockHttpClient.
 *
 * @package WeBot\Utils
 * @version 2.0
 */
class HttpClientWrapper extends MockHttpClient
{
    public function __construct(array $config = [])
    {
        parent::__construct($config);
    }

    // HttpClientWrapper extends MockHttpClient, so all methods are inherited
}
