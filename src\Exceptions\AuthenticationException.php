<?php

declare(strict_types=1);

namespace WeBot\Exceptions;

/**
 * Authentication Exception
 *
 * Exception thrown when authentication operations fail
 *
 * @package WeBot\Exceptions
 * @version 2.0
 */
class AuthenticationException extends WeBotException
{
    /**
     * Authentication method that failed
     */
    private ?string $authMethod = null;

    /**
     * User ID that failed authentication
     */
    private ?int $userId = null;

    /**
     * Authentication token that failed
     */
    private ?string $token = null;

    public function __construct(
        string $message = '',
        int $code = 0,
        ?\Throwable $previous = null,
        ?string $authMethod = null,
        ?int $userId = null,
        ?string $token = null
    ) {
        parent::__construct($message, $code, $previous);

        $this->authMethod = $authMethod;
        $this->userId = $userId;
        $this->token = $token;
    }

    /**
     * Get authentication method that failed
     */
    public function getAuthMethod(): ?string
    {
        return $this->authMethod;
    }

    /**
     * Get user ID that failed authentication
     */
    public function getUserId(): ?int
    {
        return $this->userId;
    }

    /**
     * Get authentication token that failed
     */
    public function getToken(): ?string
    {
        return $this->token;
    }

    /**
     * Get detailed error information
     */
    public function getDetailedError(): array
    {
        return [
            'message' => $this->getMessage(),
            'code' => $this->getCode(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'auth_method' => $this->authMethod,
            'user_id' => $this->userId,
            'token' => $this->token ? substr($this->token, 0, 10) . '...' : null,
            'trace' => $this->getTraceAsString()
        ];
    }

    /**
     * Create exception for invalid credentials
     */
    public static function invalidCredentials(string $method = 'unknown'): self
    {
        return new self(
            'Invalid credentials provided',
            401,
            null,
            $method
        );
    }

    /**
     * Create exception for expired token
     */
    public static function tokenExpired(string $token): self
    {
        return new self(
            'Authentication token has expired',
            401,
            null,
            'token',
            null,
            $token
        );
    }

    /**
     * Create exception for invalid token
     */
    public static function invalidToken(string $token): self
    {
        return new self(
            'Invalid authentication token',
            401,
            null,
            'token',
            null,
            $token
        );
    }

    /**
     * Create exception for session expired
     */
    public static function sessionExpired(int $userId): self
    {
        return new self(
            'User session has expired',
            401,
            null,
            'session',
            $userId
        );
    }

    /**
     * Create exception for insufficient permissions
     */
    public static function insufficientPermissions(int $userId, string $action): self
    {
        return new self(
            "Insufficient permissions for action: {$action}",
            403,
            null,
            'permission',
            $userId
        );
    }

    /**
     * Create exception for account locked
     */
    public static function accountLocked(int $userId): self
    {
        return new self(
            'User account is locked',
            423,
            null,
            'account_status',
            $userId
        );
    }
}
