<?php

declare(strict_types=1);

namespace WeBot\Repositories;

use WeBot\Core\Database;
use WeBot\Models\Service;
use WeBot\Exceptions\ServiceNotFoundException;
use WeBot\Exceptions\DatabaseException;

/**
 * Service Repository
 *
 * Handles all database operations related to services
 * including CRUD operations, usage tracking, and renewals.
 *
 * @package WeBot\Repositories
 * @version 2.0
 */
class ServiceRepository
{
    private Database $database;
    private string $table = 'services';
    private string $usageTable = 'service_usage_logs';
    private string $renewalsTable = 'service_renewals';

    public function __construct(Database $database)
    {
        $this->database = $database;
    }

    /**
     * Find service by ID
     */
    public function findById(int $id): ?Service
    {
        $sql = "SELECT * FROM {$this->table} WHERE id = ? AND deleted_at IS NULL";
        $result = $this->database->query($sql, [$id]);

        return $result ? $this->mapToService($result[0]) : null;
    }

    /**
     * Find services by user ID
     */
    public function findByUserId(int $userId): array
    {
        $sql = "SELECT * FROM {$this->table} WHERE user_id = ? AND deleted_at IS NULL ORDER BY created_at DESC";
        $results = $this->database->query($sql, [$userId]);

        return array_map([$this, 'mapToService'], $results);
    }

    /**
     * Find services by user ID with pagination and filtering
     */
    public function findByUserIdWithFilters(int $userId, ?string $status = null, int $limit = 50, int $offset = 0): array
    {
        $sql = "SELECT * FROM {$this->table} WHERE user_id = ? AND deleted_at IS NULL";
        $params = [$userId];

        if ($status !== null) {
            $sql .= " AND status = ?";
            $params[] = $status;
        }

        $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;

        $results = $this->database->query($sql, $params);
        return array_map([$this, 'mapToService'], $results);
    }

    /**
     * Count services by panel ID
     */
    public function countByPanelId(int $panelId, ?string $status = null): int
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE panel_id = ? AND deleted_at IS NULL";
        $params = [$panelId];

        if ($status !== null) {
            $sql .= " AND status = ?";
            $params[] = $status;
        }

        $result = $this->database->query($sql, $params);
        return (int)($result[0]['count'] ?? 0);
    }

    /**
     * Find service by panel user ID
     */
    public function findByPanelUserId(string $panelUserId, string $panelType): ?Service
    {
        $sql = "SELECT * FROM {$this->table} WHERE panel_user_id = ? AND panel_type = ? AND deleted_at IS NULL";
        $result = $this->database->query($sql, [$panelUserId, $panelType]);

        return $result ? $this->mapToService($result[0]) : null;
    }

    /**
     * Create new service
     */
    public function create(array $data): Service
    {
        $requiredFields = ['user_id', 'service_name', 'service_type', 'panel_type'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                throw new \InvalidArgumentException("Required field '{$field}' is missing");
            }
        }

        // Set default values
        if (!isset($data['status'])) {
            $data['status'] = 'active';
        }

        if (!isset($data['connection_limit'])) {
            $data['connection_limit'] = 1;
        }

        $fields = array_keys($data);
        $placeholders = str_repeat('?,', count($fields) - 1) . '?';

        $sql = "INSERT INTO {$this->table} (" . implode(',', $fields) . ") VALUES ({$placeholders})";

        try {
            $this->database->execute($sql, array_values($data));
            $serviceId = $this->database->lastInsertId();

            return $this->findById($serviceId);
        } catch (\Exception $e) {
            throw new DatabaseException("Failed to create service: " . $e->getMessage());
        }
    }

    /**
     * Update service
     */
    public function update(int $id, array $data): bool
    {
        if (empty($data)) {
            return true;
        }

        // Remove fields that shouldn't be updated directly
        unset($data['id'], $data['user_id'], $data['created_at'], $data['updated_at']);

        $fields = array_keys($data);
        $setClause = implode(' = ?, ', $fields) . ' = ?';

        $sql = "UPDATE {$this->table} SET {$setClause} WHERE id = ? AND deleted_at IS NULL";
        $params = array_merge(array_values($data), [$id]);

        try {
            return $this->database->execute($sql, $params) > 0;
        } catch (\Exception $e) {
            throw new DatabaseException("Failed to update service: " . $e->getMessage());
        }
    }

    /**
     * Update service status
     */
    public function updateStatus(int $id, string $status): bool
    {
        $data = ['status' => $status];

        if ($status === 'active') {
            $data['activated_at'] = date('Y-m-d H:i:s');
            $data['suspended_at'] = null;
        } elseif ($status === 'suspended') {
            $data['suspended_at'] = date('Y-m-d H:i:s');
        }

        return $this->update($id, $data);
    }

    /**
     * Update service traffic
     */
    public function updateTraffic(int $id, int $upload, int $download): bool
    {
        $sql = "UPDATE {$this->table} SET upload_traffic = upload_traffic + ?, download_traffic = download_traffic + ? WHERE id = ? AND deleted_at IS NULL";

        try {
            return $this->database->execute($sql, [$upload, $download, $id]) > 0;
        } catch (\Exception $e) {
            throw new DatabaseException("Failed to update service traffic: " . $e->getMessage());
        }
    }

    /**
     * Reset service traffic
     */
    public function resetTraffic(int $id): bool
    {
        $sql = "UPDATE {$this->table} SET upload_traffic = 0, download_traffic = 0, total_traffic = 0, data_used = 0 WHERE id = ? AND deleted_at IS NULL";

        try {
            return $this->database->execute($sql, [$id]) > 0;
        } catch (\Exception $e) {
            throw new DatabaseException("Failed to reset service traffic: " . $e->getMessage());
        }
    }

    /**
     * Extend service expiration
     */
    public function extendExpiration(int $id, int $days): bool
    {
        $sql = "UPDATE {$this->table} SET expires_at = DATE_ADD(COALESCE(expires_at, NOW()), INTERVAL ? DAY) WHERE id = ? AND deleted_at IS NULL";

        try {
            return $this->database->execute($sql, [$days, $id]) > 0;
        } catch (\Exception $e) {
            throw new DatabaseException("Failed to extend service expiration: " . $e->getMessage());
        }
    }

    /**
     * Soft delete service
     */
    public function delete(int $id): bool
    {
        $sql = "UPDATE {$this->table} SET deleted_at = CURRENT_TIMESTAMP, status = 'cancelled' WHERE id = ?";

        try {
            return $this->database->execute($sql, [$id]) > 0;
        } catch (\Exception $e) {
            throw new DatabaseException("Failed to delete service: " . $e->getMessage());
        }
    }

    /**
     * Get all services with pagination and filters
     */
    public function getAll(int $page = 1, int $limit = 50, array $filters = []): array
    {
        $offset = ($page - 1) * $limit;
        $whereConditions = ['deleted_at IS NULL'];
        $params = [];

        // Apply filters
        if (!empty($filters['status'])) {
            $whereConditions[] = 'status = ?';
            $params[] = $filters['status'];
        }

        if (!empty($filters['service_type'])) {
            $whereConditions[] = 'service_type = ?';
            $params[] = $filters['service_type'];
        }

        if (!empty($filters['panel_type'])) {
            $whereConditions[] = 'panel_type = ?';
            $params[] = $filters['panel_type'];
        }

        if (!empty($filters['user_id'])) {
            $whereConditions[] = 'user_id = ?';
            $params[] = $filters['user_id'];
        }

        if (!empty($filters['expires_before'])) {
            $whereConditions[] = 'expires_at <= ?';
            $params[] = $filters['expires_before'];
        }

        if (!empty($filters['expires_after'])) {
            $whereConditions[] = 'expires_at >= ?';
            $params[] = $filters['expires_after'];
        }

        $whereClause = implode(' AND ', $whereConditions);

        $sql = "SELECT * FROM {$this->table} WHERE {$whereClause} ORDER BY created_at DESC LIMIT ? OFFSET ?";
        $params = array_merge($params, [$limit, $offset]);

        $results = $this->database->query($sql, $params);

        return array_map([$this, 'mapToService'], $results);
    }

    /**
     * Count services with filters
     */
    public function count(array $filters = []): int
    {
        $whereConditions = ['deleted_at IS NULL'];
        $params = [];

        // Apply same filters as getAll
        if (!empty($filters['status'])) {
            $whereConditions[] = 'status = ?';
            $params[] = $filters['status'];
        }

        if (!empty($filters['service_type'])) {
            $whereConditions[] = 'service_type = ?';
            $params[] = $filters['service_type'];
        }

        if (!empty($filters['panel_type'])) {
            $whereConditions[] = 'panel_type = ?';
            $params[] = $filters['panel_type'];
        }

        if (!empty($filters['user_id'])) {
            $whereConditions[] = 'user_id = ?';
            $params[] = $filters['user_id'];
        }

        $whereClause = implode(' AND ', $whereConditions);

        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE {$whereClause}";
        $result = $this->database->query($sql, $params);

        return (int) $result[0]['count'];
    }

    /**
     * Get expiring services
     */
    public function getExpiringServices(int $days = 7): array
    {
        $sql = "SELECT * FROM {$this->table} WHERE status = 'active' AND expires_at BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL ? DAY) AND deleted_at IS NULL ORDER BY expires_at";
        $results = $this->database->query($sql, [$days]);

        return array_map([$this, 'mapToService'], $results);
    }

    /**
     * Get expired services
     */
    public function getExpiredServices(): array
    {
        $sql = "SELECT * FROM {$this->table} WHERE status = 'active' AND expires_at < NOW() AND deleted_at IS NULL ORDER BY expires_at";
        $results = $this->database->query($sql);

        return array_map([$this, 'mapToService'], $results);
    }

    /**
     * Get services over data limit
     */
    public function getOverLimitServices(): array
    {
        $sql = "SELECT * FROM {$this->table} WHERE status = 'active' AND data_limit IS NOT NULL AND data_used >= data_limit AND deleted_at IS NULL";
        $results = $this->database->query($sql);

        return array_map([$this, 'mapToService'], $results);
    }

    /**
     * Log service usage
     */
    public function logUsage(int $serviceId, array $usageData): bool
    {
        $requiredFields = ['session_start'];
        foreach ($requiredFields as $field) {
            if (!isset($usageData[$field])) {
                throw new \InvalidArgumentException("Required field '{$field}' is missing");
            }
        }

        $usageData['service_id'] = $serviceId;

        $fields = array_keys($usageData);
        $placeholders = str_repeat('?,', count($fields) - 1) . '?';

        $sql = "INSERT INTO {$this->usageTable} (" . implode(',', $fields) . ") VALUES ({$placeholders})";

        try {
            return $this->database->execute($sql, array_values($usageData)) > 0;
        } catch (\Exception $e) {
            throw new DatabaseException("Failed to log service usage: " . $e->getMessage());
        }
    }

    /**
     * Get service usage logs
     */
    public function getUsageLogs(int $serviceId, int $limit = 100): array
    {
        $sql = "SELECT * FROM {$this->usageTable} WHERE service_id = ? ORDER BY session_start DESC LIMIT ?";
        return $this->database->query($sql, [$serviceId, $limit]);
    }

    /**
     * Create service renewal
     */
    public function createRenewal(int $serviceId, array $renewalData): bool
    {
        $requiredFields = ['renewal_type', 'extended_days', 'renewal_price'];
        foreach ($requiredFields as $field) {
            if (!isset($renewalData[$field])) {
                throw new \InvalidArgumentException("Required field '{$field}' is missing");
            }
        }

        $renewalData['service_id'] = $serviceId;

        $fields = array_keys($renewalData);
        $placeholders = str_repeat('?,', count($fields) - 1) . '?';

        $sql = "INSERT INTO {$this->renewalsTable} (" . implode(',', $fields) . ") VALUES ({$placeholders})";

        try {
            return $this->database->execute($sql, array_values($renewalData)) > 0;
        } catch (\Exception $e) {
            throw new DatabaseException("Failed to create service renewal: " . $e->getMessage());
        }
    }

    /**
     * Get service statistics
     */
    public function getStatistics(): array
    {
        $sql = "
            SELECT 
                COUNT(*) as total_services,
                COUNT(CASE WHEN status = 'active' THEN 1 END) as active_services,
                COUNT(CASE WHEN status = 'suspended' THEN 1 END) as suspended_services,
                COUNT(CASE WHEN status = 'expired' THEN 1 END) as expired_services,
                COUNT(CASE WHEN service_type = 'vpn' THEN 1 END) as vpn_services,
                COUNT(CASE WHEN service_type = 'proxy' THEN 1 END) as proxy_services,
                COUNT(CASE WHEN panel_type = 'marzban' THEN 1 END) as marzban_services,
                COUNT(CASE WHEN panel_type = 'marzneshin' THEN 1 END) as marzneshin_services,
                COUNT(CASE WHEN panel_type = 'x-ui' THEN 1 END) as xui_services,
                SUM(total_traffic) as total_traffic_used,
                AVG(CASE WHEN data_limit > 0 THEN (data_used / data_limit) * 100 END) as avg_usage_percentage,
                COUNT(CASE WHEN expires_at BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY) THEN 1 END) as expiring_soon
            FROM {$this->table} 
            WHERE deleted_at IS NULL
        ";

        $result = $this->database->query($sql);
        return $result[0];
    }

    /**
     * Map database row to Service model
     */
    private function mapToService(array $row): Service
    {
        return new Service([
            'id' => (int) $row['id'],
            'user_id' => (int) $row['user_id'],
            'service_name' => $row['service_name'],
            'service_type' => $row['service_type'],
            'panel_type' => $row['panel_type'],
            'panel_id' => $row['panel_id'] ? (int) $row['panel_id'] : null,
            'panel_user_id' => $row['panel_user_id'],
            'panel_username' => $row['panel_username'],
            'config_data' => $row['config_data'] ? json_decode($row['config_data'], true) : null,
            'subscription_url' => $row['subscription_url'],
            'qr_code_data' => $row['qr_code_data'],
            'data_limit' => $row['data_limit'] ? (int) $row['data_limit'] : null,
            'data_used' => (int) $row['data_used'],
            'connection_limit' => (int) $row['connection_limit'],
            'active_connections' => (int) $row['active_connections'],
            'duration_days' => $row['duration_days'] ? (int) $row['duration_days'] : null,
            'expires_at' => $row['expires_at'],
            'auto_renew' => (bool) $row['auto_renew'],
            'status' => $row['status'],
            'last_connected_at' => $row['last_connected_at'],
            'last_ip' => $row['last_ip'],
            'upload_traffic' => (int) $row['upload_traffic'],
            'download_traffic' => (int) $row['download_traffic'],
            'total_traffic' => (int) $row['total_traffic'],
            'renewal_price' => $row['renewal_price'] ? (float) $row['renewal_price'] : null,
            'next_renewal_at' => $row['next_renewal_at'],
            'renewal_attempts' => (int) $row['renewal_attempts'],
            'notes' => $row['notes'],
            'metadata' => $row['metadata'] ? json_decode($row['metadata'], true) : null,
            'activated_at' => $row['activated_at'],
            'suspended_at' => $row['suspended_at'],
            'created_at' => $row['created_at'],
            'updated_at' => $row['updated_at'],
            'deleted_at' => $row['deleted_at']
        ]);
    }
}
