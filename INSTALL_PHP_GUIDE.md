# 🚀 راهنمای نصب PHP برای WeBot v2.0

## 📋 پیش‌نیازها
- Windows 10/11
- دسترسی اینترنت
- دسترسی Administrator (اختیاری)

---

## 🎯 روش 1: نصب XAMPP (توصیه شده)

### مزایا:
- ✅ آسان‌ترین روش
- ✅ شامل PHP، MySQL، Apache
- ✅ رابط گرافیکی
- ✅ مناسب برای توسعه

### مراحل نصب:
1. **دانلود XAMPP**:
   - برو به: https://www.apachefriends.org/
   - دانلود نسخه PHP 8.2+
   - حجم: حدود 150 MB

2. **نصب**:
   - اجرای فایل دانلود شده
   - انتخاب مسیر نصب (پیشنهاد: `C:\xampp`)
   - انتخا<PERSON> کامپوننت‌ها: PHP، MySQL، Apache

3. **تنظیم PATH**:
   ```cmd
   # اضافه کردن به PATH
   C:\xampp\php
   ```

4. **تست**:
   ```cmd
   php --version
   ```

---

## 🎯 روش 2: نصب مستقیم PHP

### مراحل:
1. **دانلود PHP**:
   - برو به: https://windows.php.net/download/
   - دانلود PHP 8.2+ Thread Safe x64
   - فرمت: ZIP

2. **استخراج**:
   - استخراج به `C:\php`
   - کپی `php.ini-development` به `php.ini`

3. **تنظیم PATH**:
   - اضافه کردن `C:\php` به PATH

4. **تنظیم Extensions**:
   ```ini
   extension=curl
   extension=fileinfo
   extension=gd
   extension=intl
   extension=mbstring
   extension=openssl
   extension=pdo_mysql
   extension=zip
   ```

---

## 🎯 روش 3: استفاده از Chocolatey

### پیش‌نیاز: نصب Chocolatey
```powershell
# اجرا به عنوان Administrator
Set-ExecutionPolicy Bypass -Scope Process -Force
[System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
```

### نصب PHP:
```cmd
# اجرا به عنوان Administrator
choco install php --version 8.2.26 -y
```

---

## 🎯 روش 4: استفاده از Docker (پیشرفته)

### پیش‌نیاز: نصب Docker Desktop

### استفاده:
```bash
# اجرای PHP در container
docker run --rm -v "%cd%":/app -w /app php:8.2-cli php --version

# اجرای composer
docker run --rm -v "%cd%":/app -w /app composer:latest install
```

---

## ✅ تست نصب

بعد از نصب، این دستورات را اجرا کنید:

```cmd
# تست PHP
php --version

# تست Extensions
php -m

# تست Composer (اگر نصب شده)
composer --version
```

### خروجی مورد انتظار:
```
PHP 8.2.x (cli) (built: ...)
Copyright (c) The PHP Group
Zend Engine v4.2.x
```

---

## 🔧 نصب Composer

### روش 1: دانلود مستقیم
1. دانلود از: https://getcomposer.org/download/
2. اجرای `Composer-Setup.exe`
3. انتخاب مسیر PHP

### روش 2: دستی
```cmd
# دانلود composer.phar
php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"
php composer-setup.php
php -r "unlink('composer-setup.php');"
```

---

## 🚀 اجرای WeBot بعد از نصب

```cmd
# رفتن به پوشه پروژه
cd "C:\Users\<USER>\OneDrive\Documents\GitHub Rep\We-Bot"

# نصب dependencies
composer install

# تست syntax
php test-runner.php

# اجرای تست‌ها
vendor\bin\phpunit
```

---

## 🔍 عیب‌یابی

### مشکل: PHP command not found
**حل**: اضافه کردن PHP به PATH

### مشکل: Extensions not loaded
**حل**: فعال‌سازی extensions در php.ini

### مشکل: Permission denied
**حل**: اجرا به عنوان Administrator

### مشکل: Composer not found
**حل**: نصب مجدد Composer

---

## 📞 پشتیبانی

اگر مشکلی داشتید:
1. بررسی PATH
2. بررسی php.ini
3. تست با `php --version`
4. بررسی extensions با `php -m`

---

**نکته**: بعد از نصب، Terminal/CMD را restart کنید تا تغییرات PATH اعمال شود.

**توصیه**: برای توسعه، XAMPP بهترین گزینه است.
