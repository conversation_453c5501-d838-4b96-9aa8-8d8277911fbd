<?php

declare(strict_types=1);

namespace WeBot\Tests\Performance;

use WeBot\Tests\Unit\BaseTestCase;
use WeBot\Services\PerformanceService;
use WeBot\Core\DatabaseOptimizer;
use WeBot\Core\CacheManager;
use WeBot\Core\PerformanceMonitor;
use WeBot\Services\DatabaseService;

/**
 * Performance Optimization Test
 * 
 * Tests performance optimization features including
 * database optimization, caching, and monitoring.
 */
class PerformanceOptimizationTest extends BaseTestCase
{
    private PerformanceService $performanceService;
    private DatabaseOptimizer $databaseOptimizer;
    private CacheManager $cacheManager;
    private PerformanceMonitor $performanceMonitor;
    
    protected function setUp(): void
    {
        parent::setUp();

        // Initialize services
        $config = new \WeBot\Core\Config();
        $database = new DatabaseService($config);
        $this->databaseOptimizer = new DatabaseOptimizer($database);
        $this->cacheManager = new CacheManager([
            'enabled' => true,
            'host' => 'localhost',
            'port' => 6379
        ]);

        // PerformanceMonitor with default dependencies
        $this->performanceMonitor = new PerformanceMonitor(null, null, [
            'enabled' => true,
            'thresholds' => [
                'memory_usage' => 80,
                'response_time' => 2.0
            ]
        ]);

        $this->performanceService = new PerformanceService(
            $this->databaseOptimizer,
            $this->cacheManager,
            $this->performanceMonitor
        );
    }
    
    /**
     * Test performance service initialization
     */
    public function testPerformanceServiceInitialization(): void
    {
        echo "🚀 Testing Performance Service Initialization...\n";
        
        $this->assertInstanceOf(PerformanceService::class, $this->performanceService);
        echo "  ✅ PerformanceService initialized successfully\n";
    }
    
    /**
     * Test performance optimization
     */
    public function testPerformanceOptimization(): void
    {
        echo "⚡ Testing Performance Optimization...\n";
        
        $startTime = microtime(true);
        $results = $this->performanceService->optimize();
        $optimizationTime = microtime(true) - $startTime;
        
        $this->assertIsArray($results);
        $this->assertArrayHasKey('database', $results);
        $this->assertArrayHasKey('cache', $results);
        $this->assertArrayHasKey('memory', $results);
        $this->assertArrayHasKey('system', $results);
        
        echo "  ✅ Performance optimization completed in " . round($optimizationTime * 1000, 2) . "ms\n";
        echo "  📊 Database optimization: " . (isset($results['database']['error']) ? 'Failed' : 'Success') . "\n";
        echo "  📊 Cache optimization: " . (isset($results['cache']['error']) ? 'Failed' : 'Success') . "\n";
        echo "  📊 Memory optimization: " . (isset($results['memory']['error']) ? 'Failed' : 'Success') . "\n";
        echo "  📊 System optimization: " . (isset($results['system']['error']) ? 'Failed' : 'Success') . "\n";
    }
    
    /**
     * Test performance report generation
     */
    public function testPerformanceReportGeneration(): void
    {
        echo "📊 Testing Performance Report Generation...\n";
        
        $report = $this->performanceService->getPerformanceReport();
        
        $this->assertIsArray($report);
        $this->assertArrayHasKey('timestamp', $report);
        $this->assertArrayHasKey('system', $report);
        $this->assertArrayHasKey('database', $report);
        $this->assertArrayHasKey('cache', $report);
        $this->assertArrayHasKey('memory', $report);
        $this->assertArrayHasKey('performance', $report);
        $this->assertArrayHasKey('recommendations', $report);
        
        echo "  ✅ Performance report generated successfully\n";
        echo "  📈 Memory usage: " . round($report['memory']['usage_percentage'], 2) . "%\n";
        echo "  📈 Recommendations: " . count($report['recommendations']) . " items\n";
    }
    
    /**
     * Test API endpoint monitoring
     */
    public function testApiEndpointMonitoring(): void
    {
        echo "🔍 Testing API Endpoint Monitoring...\n";
        
        // Test monitoring a simple operation
        $result = $this->performanceService->monitorApiEndpoint('test_endpoint', function() {
            // Simulate some work
            usleep(100000); // 100ms
            return ['status' => 'success', 'data' => 'test'];
        });
        
        $this->assertIsArray($result);
        $this->assertEquals('success', $result['status']);
        $this->assertEquals('test', $result['data']);
        
        echo "  ✅ API endpoint monitoring working correctly\n";
        
        // Test monitoring an operation that throws exception
        $result = $this->performanceService->monitorApiEndpoint('error_endpoint', function() {
            throw new \Exception('Test error');
        });
        
        $this->assertNull($result);
        echo "  ✅ Error handling in monitoring working correctly\n";
    }
    
    /**
     * Test database optimization
     */
    public function testDatabaseOptimization(): void
    {
        echo "🗄️ Testing Database Optimization...\n";
        
        try {
            // Test slow query analysis
            $slowQueries = $this->databaseOptimizer->analyzeSlowQueries();
            $this->assertIsArray($slowQueries);
            echo "  ✅ Slow query analysis completed\n";
            
            // Test performance metrics
            $metrics = $this->databaseOptimizer->getPerformanceMetrics();
            $this->assertIsArray($metrics);
            echo "  ✅ Performance metrics retrieved\n";
            
            // Test index suggestions
            $suggestions = $this->databaseOptimizer->suggestIndexes();
            $this->assertIsArray($suggestions);
            echo "  ✅ Index suggestions generated\n";
            
        } catch (\Exception $e) {
            echo "  ⚠️ Database optimization test skipped: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * Test cache optimization
     */
    public function testCacheOptimization(): void
    {
        echo "💾 Testing Cache Optimization...\n";
        
        try {
            // Test cache operations
            $key = 'test_performance_key';
            $value = ['test' => 'data', 'timestamp' => time()];
            
            $this->cacheManager->set($key, $value, 300);
            $retrieved = $this->cacheManager->get($key);
            
            $this->assertEquals($value, $retrieved);
            echo "  ✅ Cache set/get operations working\n";
            
            // Test cache optimization
            $optimizationResults = $this->cacheManager->optimize();
            $this->assertIsArray($optimizationResults);
            echo "  ✅ Cache optimization completed\n";
            
            // Test cache statistics
            $stats = $this->cacheManager->getStats();
            $this->assertIsArray($stats);
            echo "  ✅ Cache statistics retrieved\n";
            
            // Clean up
            $this->cacheManager->delete($key);
            
        } catch (\Exception $e) {
            echo "  ⚠️ Cache optimization test skipped: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * Test memory optimization
     */
    public function testMemoryOptimization(): void
    {
        echo "🧠 Testing Memory Optimization...\n";
        
        $memoryBefore = memory_get_usage(true);
        
        // Create some objects to use memory
        $testData = [];
        for ($i = 0; $i < 1000; $i++) {
            $testData[] = str_repeat('test', 100);
        }
        
        $memoryAfter = memory_get_usage(true);
        $memoryUsed = $memoryAfter - $memoryBefore;
        
        echo "  📊 Memory used for test data: " . round($memoryUsed / 1024, 2) . " KB\n";
        
        // Test garbage collection
        unset($testData);
        $gcCycles = gc_collect_cycles();
        
        $memoryAfterGC = memory_get_usage(true);
        $memoryFreed = $memoryAfter - $memoryAfterGC;
        
        echo "  ✅ Garbage collection freed: " . round($memoryFreed / 1024, 2) . " KB\n";
        echo "  ✅ GC cycles: " . $gcCycles . "\n";
        
        $this->assertGreaterThanOrEqual(0, $gcCycles);
    }
    
    /**
     * Test performance monitoring
     */
    public function testPerformanceMonitoring(): void
    {
        echo "📈 Testing Performance Monitoring...\n";
        
        // Test timer functionality
        $this->performanceMonitor->startTimer('test_operation');
        usleep(50000); // 50ms
        $result = $this->performanceMonitor->stopTimer('test_operation');
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('duration', $result);
        $this->assertArrayHasKey('memory_used', $result);
        $this->assertGreaterThan(0.04, $result['duration']); // Should be around 50ms
        
        echo "  ✅ Timer functionality working correctly\n";
        echo "  ⏱️ Test operation took: " . round($result['duration'] * 1000, 2) . "ms\n";
        
        // Test measure functionality
        $measureResult = $this->performanceMonitor->measure('test_measure', function() {
            return array_sum(range(1, 1000));
        });
        
        $this->assertEquals(500500, $measureResult); // Sum of 1 to 1000
        echo "  ✅ Measure functionality working correctly\n";
        
        // Test performance statistics
        $stats = $this->performanceMonitor->getStats();
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('system', $stats);
        $this->assertArrayHasKey('memory', $stats);
        
        echo "  ✅ Performance statistics retrieved\n";
    }
    
    /**
     * Test performance recommendations
     */
    public function testPerformanceRecommendations(): void
    {
        echo "💡 Testing Performance Recommendations...\n";
        
        $recommendations = $this->performanceMonitor->getPerformanceRecommendations();
        
        $this->assertIsArray($recommendations);
        echo "  ✅ Performance recommendations generated\n";
        echo "  📋 Total recommendations: " . count($recommendations) . "\n";
        
        foreach ($recommendations as $recommendation) {
            $this->assertArrayHasKey('type', $recommendation);
            $this->assertArrayHasKey('priority', $recommendation);
            $this->assertArrayHasKey('message', $recommendation);
            echo "  💡 " . $recommendation['type'] . " (" . $recommendation['priority'] . "): " . 
                 substr($recommendation['message'], 0, 50) . "...\n";
        }
    }
    
    /**
     * Test real-time metrics
     */
    public function testRealTimeMetrics(): void
    {
        echo "⚡ Testing Real-time Metrics...\n";
        
        $metrics = $this->performanceMonitor->getRealTimeMetrics();
        
        $this->assertIsArray($metrics);
        $this->assertArrayHasKey('timestamp', $metrics);
        $this->assertArrayHasKey('memory', $metrics);
        $this->assertArrayHasKey('cpu', $metrics);
        
        echo "  ✅ Real-time metrics retrieved\n";
        echo "  📊 Current memory: " . round($metrics['memory']['current'] / 1024 / 1024, 2) . " MB\n";
        echo "  📊 Peak memory: " . round($metrics['memory']['peak'] / 1024 / 1024, 2) . " MB\n";
        echo "  📊 CPU load (1min): " . ($metrics['cpu']['load_1min'] ?? 'N/A') . "\n";
    }
}
