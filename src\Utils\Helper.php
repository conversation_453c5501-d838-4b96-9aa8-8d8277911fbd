<?php

declare(strict_types=1);

namespace WeBot\Utils;

/**
 * WeBot Helper Utilities
 *
 * Collection of utility functions for common operations.
 *
 * @package WeBot\Utils
 * @version 2.0
 */
class Helper
{
    /**
     * Format bytes to human readable format
     */
    public static function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Parse bytes from human readable format
     */
    public static function parseBytes(string $value): int
    {
        $value = trim($value);
        $unit = strtoupper(substr($value, -2));
        $number = (float) substr($value, 0, -2);

        return match ($unit) {
            'KB' => (int) ($number * 1024),
            'MB' => (int) ($number * 1024 * 1024),
            'GB' => (int) ($number * 1024 * 1024 * 1024),
            'TB' => (int) ($number * 1024 * 1024 * 1024 * 1024),
            default => (int) $value,
        };
    }

    /**
     * Generate random string
     */
    public static function randomString(int $length = 10, string $characters = null): string
    {
        $characters = $characters ?? '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';

        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }

        return $randomString;
    }

    /**
     * Generate UUID v4
     */
    public static function generateUuid(): string
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff)
        );
    }

    /**
     * Sanitize string for database
     */
    public static function sanitize(string $value): string
    {
        return htmlspecialchars(trim($value), ENT_QUOTES, 'UTF-8');
    }

    /**
     * Validate email address
     */
    public static function isValidEmail(string $email): bool
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Validate URL
     */
    public static function isValidUrl(string $url): bool
    {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }

    /**
     * Validate Telegram user ID
     */
    public static function isValidTelegramId($id): bool
    {
        return is_numeric($id) && $id > 0 && $id < 2147483647;
    }

    /**
     * Format price in Toman
     */
    public static function formatPrice(float $price): string
    {
        return number_format($price, 0, '.', ',') . ' تومان';
    }

    /**
     * Convert Persian/Arabic numbers to English
     */
    public static function convertNumbers(string $string): string
    {
        $persian = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
        $arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        $english = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

        $string = str_replace($persian, $english, $string);
        $string = str_replace($arabic, $english, $string);

        return $string;
    }

    /**
     * Truncate string with ellipsis
     */
    public static function truncate(string $string, int $length = 100, string $suffix = '...'): string
    {
        if (mb_strlen($string) <= $length) {
            return $string;
        }

        return mb_substr($string, 0, $length - mb_strlen($suffix)) . $suffix;
    }

    /**
     * Check if string contains any of the given needles
     */
    public static function contains(string $haystack, array $needles): bool
    {
        foreach ($needles as $needle) {
            if (str_contains($haystack, $needle)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get client IP address
     */
    public static function getClientIp(): string
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];

        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];

                // Handle comma-separated IPs
                if (str_contains($ip, ',')) {
                    $ip = trim(explode(',', $ip)[0]);
                }

                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }

    /**
     * Calculate time difference in human readable format
     */
    public static function timeAgo(string $datetime): string
    {
        $time = time() - strtotime($datetime);

        if ($time < 60) {
            return 'همین الان';
        }

        $timeUnits = [
            31536000 => 'سال',
            2592000 => 'ماه',
            604800 => 'هفته',
            86400 => 'روز',
            3600 => 'ساعت',
            60 => 'دقیقه'
        ];

        foreach ($timeUnits as $unit => $text) {
            if ($time >= $unit) {
                $numberOfUnits = floor($time / $unit);
                return $numberOfUnits . ' ' . $text . ' پیش';
            }
        }

        return 'همین الان';
    }

    /**
     * Convert seconds to human readable duration
     */
    public static function formatDuration(int $seconds): string
    {
        $units = [
            'روز' => 86400,
            'ساعت' => 3600,
            'دقیقه' => 60,
            'ثانیه' => 1
        ];

        $result = [];

        foreach ($units as $name => $divisor) {
            $quotient = intval($seconds / $divisor);
            if ($quotient) {
                $result[] = $quotient . ' ' . $name;
                $seconds %= $divisor;
            }
        }

        return implode(' و ', $result) ?: '0 ثانیه';
    }

    /**
     * Mask sensitive data
     */
    public static function maskSensitive(string $value, int $visibleChars = 4): string
    {
        $length = strlen($value);

        if ($length <= $visibleChars) {
            return str_repeat('*', $length);
        }

        $start = substr($value, 0, $visibleChars / 2);
        $end = substr($value, -($visibleChars / 2));
        $middle = str_repeat('*', $length - $visibleChars);

        return $start . $middle . $end;
    }

    /**
     * Check if current environment is development
     */
    public static function isDevelopment(): bool
    {
        return env('APP_ENV', 'production') === 'development';
    }

    /**
     * Check if current environment is production
     */
    public static function isProduction(): bool
    {
        return env('APP_ENV', 'production') === 'production';
    }

    /**
     * Get memory usage in human readable format
     */
    public static function getMemoryUsage(): string
    {
        return self::formatBytes(memory_get_usage(true));
    }

    /**
     * Get peak memory usage in human readable format
     */
    public static function getPeakMemoryUsage(): string
    {
        return self::formatBytes(memory_get_peak_usage(true));
    }
}
