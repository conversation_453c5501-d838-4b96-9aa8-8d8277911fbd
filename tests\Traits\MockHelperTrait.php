<?php

declare(strict_types=1);

namespace WeBot\Tests\Traits;

use PHPUnit\Framework\MockObject\MockObject;

/**
 * Mock Helper Trait
 * 
 * Provides helper methods for creating and managing mocks in tests.
 * 
 * @package WeBot\Tests\Traits
 * @version 2.0
 */
trait MockHelperTrait
{
    /**
     * Create a mock with proper type hints
     */
    protected function createTypedMock(string $className): MockObject
    {
        return $this->createMock($className);
    }

    /**
     * Create a mock that implements an interface
     */
    protected function createInterfaceMock(string $interfaceName): MockObject
    {
        return $this->createMock($interfaceName);
    }

    /**
     * Set up mock expectations with fluent interface
     */
    protected function expectMockCall(MockObject $mock, string $method): MockObject
    {
        return $mock->expects($this->once())->method($method);
    }

    /**
     * Set up mock to never be called
     */
    protected function expectMockNeverCalled(MockObject $mock, string $method): MockObject
    {
        return $mock->expects($this->never())->method($method);
    }

    /**
     * Set up mock to be called exactly N times
     */
    protected function expectMockCalledTimes(MockObject $mock, string $method, int $times): MockObject
    {
        return $mock->expects($this->exactly($times))->method($method);
    }

    /**
     * Create a mock with return value
     */
    protected function createMockWithReturn(string $className, string $method, $returnValue): MockObject
    {
        $mock = $this->createMock($className);
        $mock->method($method)->willReturn($returnValue);
        return $mock;
    }

    /**
     * Create a mock that throws exception
     */
    protected function createMockWithException(string $className, string $method, \Throwable $exception): MockObject
    {
        $mock = $this->createMock($className);
        $mock->method($method)->willThrowException($exception);
        return $mock;
    }
}
