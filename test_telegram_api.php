<?php
/**
 * Telegram Bot API Test for WeBot
 * 
 * This script tests the Telegram Bot API connectivity and verifies
 * that the bot can communicate with Telegram servers.
 */

declare(strict_types=1);

echo "=== WeBot Telegram Bot API Test ===\n\n";

// Load the application
try {
    require_once __DIR__ . '/autoload.php';
    echo "✅ Application loaded successfully\n\n";
} catch (Exception $e) {
    echo "❌ Failed to load application: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 1: Telegram service availability
echo "1. Telegram Service Availability Test:\n";
$telegramServiceOk = false;

try {
    $telegramService = telegram();
    echo "   ✅ Telegram service is available\n";
    $telegramServiceOk = true;
    
    if (method_exists($telegramService, 'sendMessage')) {
        echo "   ✅ TelegramService has sendMessage() method\n";
    } else {
        echo "   ❌ TelegramService missing sendMessage() method\n";
        $telegramServiceOk = false;
    }
    
    if (method_exists($telegramService, 'getMe')) {
        echo "   ✅ TelegramService has getMe() method\n";
    } else {
        echo "   ❌ TelegramService missing getMe() method\n";
        $telegramServiceOk = false;
    }
} catch (Exception $e) {
    echo "   ❌ Telegram service not available: " . $e->getMessage() . "\n";
}

// Test 2: Bot token configuration
echo "\n2. Bot Token Configuration Test:\n";
$tokenConfigOk = false;

$botToken = env('TELEGRAM_BOT_TOKEN');
if (!empty($botToken)) {
    if ($botToken === 'your_bot_token_here') {
        echo "   ⚠️  Bot token is placeholder value\n";
        echo "   ℹ️  This is expected in development/testing environment\n";
    } else {
        // Basic token format validation
        if (preg_match('/^\d+:[A-Za-z0-9_-]+$/', $botToken)) {
            echo "   ✅ Bot token format appears valid\n";
            $tokenConfigOk = true;
        } else {
            echo "   ❌ Bot token format is invalid\n";
        }
    }
} else {
    echo "   ❌ Bot token not configured\n";
}

// Test 3: Webhook URL configuration
echo "\n3. Webhook URL Configuration Test:\n";
$webhookConfigOk = true;

$webhookUrl = env('WEBHOOK_URL');
if (!empty($webhookUrl)) {
    if (filter_var($webhookUrl, FILTER_VALIDATE_URL)) {
        echo "   ✅ Webhook URL format is valid: {$webhookUrl}\n";
        
        if (strpos($webhookUrl, 'https://') === 0) {
            echo "   ✅ Webhook URL uses HTTPS\n";
        } else {
            echo "   ⚠️  Webhook URL should use HTTPS for production\n";
        }
    } else {
        echo "   ❌ Webhook URL format is invalid\n";
        $webhookConfigOk = false;
    }
} else {
    echo "   ⚠️  Webhook URL not configured\n";
}

// Test 4: HTTP client availability
echo "\n4. HTTP Client Availability Test:\n";
$httpClientOk = false;

if (class_exists('GuzzleHttp\Client')) {
    echo "   ✅ GuzzleHttp client is available\n";
    $httpClientOk = true;
    
    try {
        $client = new GuzzleHttp\Client();
        echo "   ✅ HTTP client can be instantiated\n";
    } catch (Exception $e) {
        echo "   ❌ Failed to instantiate HTTP client: " . $e->getMessage() . "\n";
        $httpClientOk = false;
    }
} else {
    echo "   ❌ GuzzleHttp client not available\n";
}

// Test 5: Bot API connectivity (if token is configured)
echo "\n5. Bot API Connectivity Test:\n";
$apiConnectivityOk = false;

if ($tokenConfigOk && $httpClientOk) {
    try {
        $client = new GuzzleHttp\Client();
        $apiUrl = "https://api.telegram.org/bot{$botToken}/getMe";
        
        $response = $client->get($apiUrl, [
            'timeout' => 10,
            'connect_timeout' => 5
        ]);
        
        if ($response->getStatusCode() === 200) {
            echo "   ✅ Bot API is reachable\n";
            
            $responseData = json_decode($response->getBody(), true);
            if (isset($responseData['ok']) && $responseData['ok'] === true) {
                echo "   ✅ Bot API response is valid\n";
                $apiConnectivityOk = true;
                
                if (isset($responseData['result']['username'])) {
                    $botUsername = $responseData['result']['username'];
                    echo "   ✅ Bot username: @{$botUsername}\n";
                }
            } else {
                echo "   ❌ Bot API response indicates error\n";
            }
        } else {
            echo "   ❌ Bot API returned status: " . $response->getStatusCode() . "\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Bot API connectivity failed: " . $e->getMessage() . "\n";
    }
} else {
    echo "   ⚠️  Skipped API connectivity test (token not configured or HTTP client unavailable)\n";
}

// Test 6: Telegram bot class availability
echo "\n6. Telegram Bot Class Availability Test:\n";
$botClassOk = false;

if (class_exists('WeBot\Core\TelegramBot')) {
    echo "   ✅ TelegramBot class exists\n";
    $botClassOk = true;
    
    try {
        $telegramBot = new WeBot\Core\TelegramBot();
        echo "   ✅ TelegramBot can be instantiated\n";
        
        $methods = ['sendMessage', 'editMessage', 'deleteMessage', 'answerCallbackQuery'];
        foreach ($methods as $method) {
            if (method_exists($telegramBot, $method)) {
                echo "   ✅ TelegramBot has {$method}() method\n";
            } else {
                echo "   ❌ TelegramBot missing {$method}() method\n";
                $botClassOk = false;
            }
        }
    } catch (Exception $e) {
        echo "   ❌ Failed to instantiate TelegramBot: " . $e->getMessage() . "\n";
        $botClassOk = false;
    }
} else {
    echo "   ❌ TelegramBot class does not exist\n";
}

// Test 7: Webhook endpoint availability
echo "\n7. Webhook Endpoint Availability Test:\n";
$webhookEndpointOk = true;

$webhookFile = 'public/webhook.php';
if (file_exists($webhookFile)) {
    echo "   ✅ Webhook endpoint file exists\n";
    
    if (is_readable($webhookFile)) {
        echo "   ✅ Webhook endpoint file is readable\n";
        
        // Basic syntax check
        $webhookContent = file_get_contents($webhookFile);
        if (strpos($webhookContent, '<?php') !== false) {
            echo "   ✅ Webhook endpoint contains PHP code\n";
        } else {
            echo "   ❌ Webhook endpoint does not contain valid PHP\n";
            $webhookEndpointOk = false;
        }
    } else {
        echo "   ❌ Webhook endpoint file is not readable\n";
        $webhookEndpointOk = false;
    }
} else {
    echo "   ❌ Webhook endpoint file does not exist\n";
    $webhookEndpointOk = false;
}

echo "\n=== Overall Status ===\n";

if ($telegramServiceOk && $httpClientOk && $botClassOk && $webhookEndpointOk) {
    if ($tokenConfigOk && $apiConnectivityOk) {
        echo "✅ Telegram Bot API is fully functional!\n";
        echo "ℹ️  Bot is configured and can communicate with Telegram\n";
    } else {
        echo "⚠️  Telegram Bot API infrastructure is ready but needs configuration\n";
        echo "ℹ️  All components are available but bot token needs to be set\n";
        echo "🔧 To complete setup:\n";
        echo "   1. Get bot token from @BotFather on Telegram\n";
        echo "   2. Set TELEGRAM_BOT_TOKEN in .env file\n";
        echo "   3. Configure webhook URL\n";
        echo "   4. Test bot functionality\n";
    }
    exit(0);
} else {
    echo "❌ Telegram Bot API has issues.\n";
    echo "\n🔧 To fix Telegram Bot API issues:\n";
    echo "   1. Ensure TelegramService and TelegramBot classes exist\n";
    echo "   2. Install GuzzleHttp for HTTP requests\n";
    echo "   3. Create webhook endpoint file\n";
    echo "   4. Configure bot token and webhook URL\n";
    exit(1);
}
