<?php

declare(strict_types=1);

namespace WeBot\Core\Cache\Drivers;

use WeBot\Core\Cache\Contracts\CacheInterface;

/**
 * Array Cache Driver
 *
 * Simple in-memory cache driver for testing and development.
 * Data is lost when the request ends.
 *
 * @package WeBot\Core\Cache\Drivers
 * @version 2.0
 */
class ArrayDriver implements CacheInterface
{
    private array $cache = [];
    private array $expiry = [];

    /**
     * Get cached value
     */
    public function get(string $key, $default = null)
    {
        // Check if key has expired
        if (isset($this->expiry[$key]) && $this->expiry[$key] < time()) {
            unset($this->cache[$key], $this->expiry[$key]);
            return $default;
        }

        return $this->cache[$key] ?? $default;
    }

    /**
     * Set cached value
     */
    public function set(string $key, $value, ?int $ttl = null): bool
    {
        $this->cache[$key] = $value;

        if ($ttl !== null) {
            $this->expiry[$key] = time() + $ttl;
        } else {
            unset($this->expiry[$key]);
        }

        return true;
    }

    /**
     * Delete cached value
     */
    public function delete(string $key): bool
    {
        unset($this->cache[$key], $this->expiry[$key]);
        return true;
    }

    /**
     * Check if key exists
     */
    public function exists(string $key): bool
    {
        // Check if key has expired
        if (isset($this->expiry[$key]) && $this->expiry[$key] < time()) {
            unset($this->cache[$key], $this->expiry[$key]);
            return false;
        }

        return isset($this->cache[$key]);
    }

    /**
     * Increment value
     */
    public function increment(string $key, int $value = 1): int
    {
        $current = $this->get($key, 0);
        $new = (int)$current + $value;
        $this->set($key, $new);
        return $new;
    }

    /**
     * Decrement value
     */
    public function decrement(string $key, int $value = 1): int
    {
        $current = $this->get($key, 0);
        $new = (int)$current - $value;
        $this->set($key, $new);
        return $new;
    }

    /**
     * Set multiple values
     */
    public function setMultiple(array $values, ?int $ttl = null): bool
    {
        foreach ($values as $key => $value) {
            $this->set($key, $value, $ttl);
        }
        return true;
    }

    /**
     * Get multiple values
     */
    public function getMultiple(array $keys, $default = null): array
    {
        $results = [];
        foreach ($keys as $key) {
            $results[$key] = $this->get($key, $default);
        }
        return $results;
    }

    /**
     * Flush all cache
     */
    public function flush(): bool
    {
        $this->cache = [];
        $this->expiry = [];
        return true;
    }

    /**
     * Get all cached keys
     */
    public function getKeys(): array
    {
        // Clean expired keys first
        $now = time();
        foreach ($this->expiry as $key => $expiry) {
            if ($expiry < $now) {
                unset($this->cache[$key], $this->expiry[$key]);
            }
        }

        return array_keys($this->cache);
    }

    /**
     * Get cache statistics
     */
    public function getStats(): array
    {
        return [
            'keys' => count($this->cache),
            'memory_usage' => memory_get_usage(),
            'driver' => 'array'
        ];
    }

    /**
     * Clear expired keys
     */
    public function clearExpired(): int
    {
        $cleared = 0;
        $now = time();

        foreach ($this->expiry as $key => $expiry) {
            if ($expiry < $now) {
                unset($this->cache[$key], $this->expiry[$key]);
                $cleared++;
            }
        }

        return $cleared;
    }
}
