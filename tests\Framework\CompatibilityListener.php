<?php

declare(strict_types=1);

namespace WeBot\Tests\Framework;

use PHPUnit\Framework\TestListener;
use PHPUnit\Framework\TestListenerDefaultImplementation;
use PHPUnit\Framework\TestSuite;
use PHPUnit\Framework\Test;

/**
 * PHPUnit 10 Compatibility Listener
 *
 * This listener provides forward-compatibility for tests written
 * for older PHPUnit versions by handling removed methods like setBackupGlobals.
 *
 * @package WeBot\Tests\Framework
 */
class CompatibilityListener implements TestListener
{
    use TestListenerDefaultImplementation;

    public function startTestSuite(TestSuite $suite): void
    {
        foreach ($suite->tests() as $test) {
            if ($test instanceof \PHPUnit\Framework\TestCase) {
                // PHPUnit 10 removed these properties.
                // We can dynamically add them if needed, but for now,
                // we just prevent fatal errors.
                if (!property_exists($test, 'backupGlobals')) {
                    $test->backupGlobals = false;
                }
                if (!property_exists($test, 'backupStaticAttributes')) {
                    $test->backupStaticAttributes = false;
                }
            }
        }
    }
} 
