<?php

declare(strict_types=1);

namespace WeBot\Middleware;

use WeBot\Exceptions\RateLimitException;
use WeBot\Utils\Logger;
use Monolog\Logger as MonologLogger;

/**
 * Advanced Rate Limit Middleware
 *
 * Implements multiple rate limiting strategies with security enhancements
 *
 * @package WeBot\Middleware
 * @version 2.0
 */
class RateLimitMiddleware
{
    private array $requests = [];
    private array $config;
    private MonologLogger $logger;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'max_requests' => 60,
            'window_size' => 60,
            'burst_limit' => 10,
            'adaptive' => true,
            'strict_mode' => false,
            'whitelist' => [],
            'blacklist' => []
        ], $config);

        $this->logger = Logger::getInstance();
    }

    public function handle(array $request, callable $next): array
    {
        $clientId = $this->getClientIdentifier($request);

        // Check whitelist/blacklist
        if ($this->isWhitelisted($clientId)) {
            return $next($request);
        }

        if ($this->isBlacklisted($clientId)) {
            $this->logSecurityViolation($clientId, 'blacklisted_access', $request);
            return [
                'method' => 'sendMessage',
                'chat_id' => $request['chat']['id'] ?? $request['from']['id'] ?? 0,
                'text' => '🚫 دسترسی شما محدود شده است.'
            ];
        }

        $rateLimitResult = $this->checkRateLimit($clientId, $request);

        if (!$rateLimitResult['allowed']) {
            $this->logRateLimitViolation($clientId, $request, $rateLimitResult);

            if ($this->config['strict_mode']) {
                throw new RateLimitException('Rate limit exceeded', 429);
            }

            return [
                'method' => 'sendMessage',
                'chat_id' => $request['chat']['id'] ?? $request['from']['id'] ?? 0,
                'text' => '⚠️ تعداد درخواست‌های شما بیش از حد مجاز است. لطفاً ' .
                         $rateLimitResult['retry_after'] . ' ثانیه صبر کنید.'
            ];
        }

        return $next($request);
    }

    /**
     * Enhanced rate limit checking with adaptive limits
     */
    private function checkRateLimit(string $clientId, array $request): array
    {
        $now = time();
        $userRequests = $this->requests[$clientId] ?? [];
        $windowSize = $this->config['window_size'];

        // Remove old requests
        $userRequests = array_filter($userRequests, fn($timestamp) => ($now - $timestamp) < $windowSize);

        // Get adaptive limit
        $limit = $this->getAdaptiveLimit($clientId, $request);

        // Check burst limit
        $recentRequests = array_filter($userRequests, fn($timestamp) => ($now - $timestamp) < 10);
        if (count($recentRequests) >= $this->config['burst_limit']) {
            return [
                'allowed' => false,
                'limit' => $limit,
                'remaining' => 0,
                'retry_after' => 10,
                'reason' => 'burst_limit'
            ];
        }

        // Check window limit
        if (count($userRequests) >= $limit) {
            $oldestRequest = min($userRequests);
            $retryAfter = $windowSize - ($now - $oldestRequest);

            return [
                'allowed' => false,
                'limit' => $limit,
                'remaining' => 0,
                'retry_after' => max(1, $retryAfter),
                'reason' => 'rate_limit'
            ];
        }

        // Add current request
        $userRequests[] = $now;
        $this->requests[$clientId] = $userRequests;

        return [
            'allowed' => true,
            'limit' => $limit,
            'remaining' => $limit - count($userRequests),
            'retry_after' => 0,
            'reason' => 'allowed'
        ];
    }

    /**
     * Get client identifier
     */
    private function getClientIdentifier(array $request): string
    {
        // Priority: user_id > telegram_id > ip
        if (isset($request['from']['id'])) {
            return 'telegram:' . $request['from']['id'];
        }

        if (isset($request['user_id'])) {
            return 'user:' . $request['user_id'];
        }

        $ip = $request['ip'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        return 'ip:' . $ip;
    }

    /**
     * Get adaptive rate limit based on user behavior
     */
    private function getAdaptiveLimit(string $clientId, array $request): int
    {
        // Suppress unused parameter warning - reserved for future enhancements
        unset($request);

        if (!$this->config['adaptive']) {
            return $this->config['max_requests'];
        }

        // Check violation history
        $violationKey = "violations:{$clientId}";
        $violations = $this->requests[$violationKey] ?? 0;

        // Reduce limit for users with violations
        $reduction = min(0.7, $violations * 0.1);
        $adaptiveLimit = (int) ($this->config['max_requests'] * (1 - $reduction));

        return max(5, $adaptiveLimit); // Minimum 5 requests
    }

    /**
     * Check if client is whitelisted
     */
    private function isWhitelisted(string $clientId): bool
    {
        foreach ($this->config['whitelist'] as $pattern) {
            if (fnmatch($pattern, $clientId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if client is blacklisted
     */
    private function isBlacklisted(string $clientId): bool
    {
        foreach ($this->config['blacklist'] as $pattern) {
            if (fnmatch($pattern, $clientId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Log rate limit violation
     */
    private function logRateLimitViolation(string $clientId, array $request, array $rateLimitResult): void
    {
        $this->logger->warning('Rate limit exceeded', [
            'client_id' => $clientId,
            'limit' => $rateLimitResult['limit'],
            'retry_after' => $rateLimitResult['retry_after'],
            'reason' => $rateLimitResult['reason'],
            'user_agent' => $request['headers']['User-Agent'] ?? 'unknown'
        ]);

        // Track violations for adaptive limiting
        $violationKey = "violations:{$clientId}";
        $this->requests[$violationKey] = ($this->requests[$violationKey] ?? 0) + 1;
    }

    /**
     * Log security violation
     */
    private function logSecurityViolation(string $clientId, string $type, array $request): void
    {
        $this->logger->error('Security violation detected', [
            'client_id' => $clientId,
            'violation_type' => $type,
            'ip' => $request['ip'] ?? 'unknown',
            'user_agent' => $request['headers']['User-Agent'] ?? 'unknown'
        ]);
    }
}
