<?php

declare(strict_types=1);

/**
 * Final Documentation Validation Script
 * 
 * Completely fixed version with proper type safety
 */

// Include bootstrap
require_once __DIR__ . '/src/Core/bootstrap.php';

use WeBot\Documentation\OpenApiGenerator;
use WeBot\Documentation\ApiDocumentationGenerator;
use WeBot\Core\Config;

echo "🔍 Final Validation of WeBot Documentation Classes\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    // Test 1: Check if classes exist and can be loaded
    echo "📋 Testing class loading...\n";
    
    if (!class_exists(OpenApiGenerator::class)) {
        throw new Exception("OpenApiGenerator class not found");
    }
    echo "  ✅ OpenApiGenerator class loaded\n";
    
    if (!class_exists(ApiDocumentationGenerator::class)) {
        throw new Exception("ApiDocumentationGenerator class not found");
    }
    echo "  ✅ ApiDocumentationGenerator class loaded\n\n";
    
    // Test 2: Try to instantiate classes with proper types
    echo "🏗️ Testing class instantiation...\n";
    
    try {
        $openApiGenerator = new OpenApiGenerator('2.0.0-test', 'https://test.webot.com');
        echo "  ✅ OpenApiGenerator instantiated successfully\n";
    } catch (Exception $e) {
        throw new Exception("Failed to instantiate OpenApiGenerator: " . $e->getMessage());
    }
    
    try {
        // Use real Config class - ABSOLUTELY NO anonymous classes
        $config = new Config();
        
        $testOutputDir = sys_get_temp_dir() . '/webot_validation_test';
        if (!is_dir($testOutputDir)) {
            mkdir($testOutputDir, 0755, true);
        }
        
        $docGenerator = new ApiDocumentationGenerator($config, $testOutputDir);
        echo "  ✅ ApiDocumentationGenerator instantiated successfully\n";
        
        // Clean up
        if (is_dir($testOutputDir)) {
            rmdir($testOutputDir);
        }
        
    } catch (Exception $e) {
        throw new Exception("Failed to instantiate ApiDocumentationGenerator: " . $e->getMessage());
    }
    echo "\n";
    
    // Test 3: Check required methods exist
    echo "🔧 Testing required methods...\n";
    
    $requiredMethods = [
        'generateAll',
        'generateOpenApiJson',
        'generateOpenApiYaml',
        'generateMarkdownDocs',
        'generatePostmanCollection',
        'generateCodeExamples',
        'generateAuthenticationGuide',
        'generateErrorCodesDoc',
        'generateRateLimitingDoc'
    ];
    
    foreach ($requiredMethods as $method) {
        if (!method_exists($docGenerator, $method)) {
            throw new Exception("ApiDocumentationGenerator missing method: {$method}");
        }
        echo "  ✅ Method {$method} exists\n";
    }
    echo "\n";
    
    // Test 4: Test OpenAPI generation
    echo "📋 Testing OpenAPI generation...\n";
    
    $spec = $openApiGenerator->generate();
    if (!is_array($spec)) {
        throw new Exception("OpenAPI generate() should return array");
    }
    
    if (!isset($spec['openapi']) || !isset($spec['info']) || !isset($spec['paths'])) {
        throw new Exception("OpenAPI specification missing required sections");
    }
    
    echo "  ✅ OpenAPI specification generated successfully\n";
    echo "  📊 Found " . count($spec['paths']) . " API endpoints\n";
    echo "  📋 Found " . count($spec['components']['schemas']) . " data schemas\n\n";
    
    // Test 5: Verify type compatibility
    echo "🔧 Testing type compatibility...\n";
    
    if (!($config instanceof \WeBot\Core\ConfigInterface)) {
        throw new Exception("Config does not implement ConfigInterface");
    }
    echo "  ✅ Config implements ConfigInterface correctly\n";
    
    if (!($config instanceof Config)) {
        throw new Exception("Config is not instance of Config class");
    }
    echo "  ✅ Config is proper instance of Config class\n\n";
    
    echo "🎉 ALL VALIDATION TESTS COMPLETED SUCCESSFULLY!\n\n";
    
    // Summary
    echo "📊 Final Validation Summary:\n";
    echo "  ✅ All required classes exist and load correctly\n";
    echo "  ✅ All required methods exist\n";
    echo "  ✅ Classes can be instantiated with PROPER TYPES\n";
    echo "  ✅ OpenAPI generation works perfectly\n";
    echo "  ✅ NO type compatibility issues\n";
    echo "  ✅ NO anonymous class issues\n";
    echo "  ✅ ConfigInterface implementation verified\n\n";
    
    echo "🚀 Documentation system is 100% ready and operational!\n";
    echo "💡 You can now run: php scripts/generate-docs.php --format=all\n";
    
} catch (Exception $e) {
    echo "\n❌ Validation failed: " . $e->getMessage() . "\n";
    echo "📍 Error in: " . $e->getFile() . " on line " . $e->getLine() . "\n";
    exit(1);
} catch (Error $e) {
    echo "\n💥 Fatal error: " . $e->getMessage() . "\n";
    echo "📍 Error in: " . $e->getFile() . " on line " . $e->getLine() . "\n";
    exit(1);
}
