<?php

declare(strict_types=1);

namespace WeBot\Services;

use WeBot\Utils\Logger;
use WeBot\Core\CacheManager;

/**
 * Error Monitoring Service
 *
 * Monitors error patterns, tracks error rates,
 * and provides alerting for critical issues.
 *
 * @package WeBot\Services
 * @version 2.0
 */
class ErrorMonitoringService
{
    private Logger $logger;
    private CacheManager $cache;
    private array $config;
    private array $errorMetrics = [];
    private array $alertThresholds = [];

    public function __construct(CacheManager $cache, array $config = [])
    {
        $this->logger = Logger::getInstance();
        $this->cache = $cache;
        $this->config = array_merge($this->getDefaultConfig(), $config);
        $this->initializeAlertThresholds();
    }

    /**
     * Record an error occurrence
     */
    public function recordError(\Throwable $exception, array $context = []): void
    {
        $errorData = [
            'timestamp' => microtime(true),
            'type' => get_class($exception),
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'code' => $exception->getCode(),
            'severity' => $this->determineSeverity($exception),
            'context' => $context,
            'memory_usage' => memory_get_usage(true),
            'request_id' => $context['request_id'] ?? uniqid('req_', true),
            'user_id' => $context['user_id'] ?? null,
            'ip_address' => $context['ip_address'] ?? 'unknown',
            'user_agent' => $context['user_agent'] ?? 'unknown',
            'url' => $context['url'] ?? 'unknown'
        ];

        // Store error data
        $this->storeErrorData($errorData);

        // Update metrics
        $this->updateErrorMetrics($errorData);

        // Check alert thresholds
        $this->checkAlertThresholds($errorData);

        // Log the error
        $this->logError($errorData);
    }

    /**
     * Get error statistics
     */
    public function getErrorStats(int $timeframe = 3600): array
    {
        $cutoff = microtime(true) - $timeframe;
        $errors = $this->getErrorsSince($cutoff);

        $stats = [
            'total_errors' => count($errors),
            'error_rate' => count($errors) / ($timeframe / 60), // errors per minute
            'by_type' => [],
            'by_severity' => [],
            'by_hour' => [],
            'top_errors' => [],
            'memory_usage' => [
                'average' => 0,
                'peak' => 0
            ],
            'affected_users' => [],
            'error_patterns' => []
        ];

        $memoryUsages = [];
        $errorCounts = [];

        foreach ($errors as $error) {
            // By type
            $type = $error['type'];
            if (!isset($stats['by_type'][$type])) {
                $stats['by_type'][$type] = 0;
            }
            $stats['by_type'][$type]++;

            // By severity
            $severity = $error['severity'];
            if (!isset($stats['by_severity'][$severity])) {
                $stats['by_severity'][$severity] = 0;
            }
            $stats['by_severity'][$severity]++;

            // By hour
            $hour = date('H', (int)$error['timestamp']);
            if (!isset($stats['by_hour'][$hour])) {
                $stats['by_hour'][$hour] = 0;
            }
            $stats['by_hour'][$hour]++;

            // Memory usage
            $memoryUsages[] = $error['memory_usage'];

            // Error counts for top errors
            $errorKey = $error['type'] . ':' . $error['message'];
            if (!isset($errorCounts[$errorKey])) {
                $errorCounts[$errorKey] = [
                    'count' => 0,
                    'type' => $error['type'],
                    'message' => $error['message'],
                    'first_seen' => $error['timestamp'],
                    'last_seen' => $error['timestamp']
                ];
            }
            $errorCounts[$errorKey]['count']++;
            $errorCounts[$errorKey]['last_seen'] = max($errorCounts[$errorKey]['last_seen'], $error['timestamp']);

            // Affected users
            if ($error['user_id']) {
                $stats['affected_users'][$error['user_id']] = true;
            }
        }

        // Calculate memory statistics
        if (!empty($memoryUsages)) {
            $stats['memory_usage']['average'] = array_sum($memoryUsages) / count($memoryUsages);
            $stats['memory_usage']['peak'] = max($memoryUsages);
        }

        // Top errors
        arsort($errorCounts);
        $stats['top_errors'] = array_slice($errorCounts, 0, 10);

        // Affected users count
        $stats['affected_users'] = count($stats['affected_users']);

        // Error patterns
        $stats['error_patterns'] = $this->analyzeErrorPatterns($errors);

        return $stats;
    }

    /**
     * Get error trends
     */
    public function getErrorTrends(int $days = 7): array
    {
        $trends = [];
        $now = time();

        for ($i = $days - 1; $i >= 0; $i--) {
            $dayStart = strtotime('-' . $i . ' days', $now);
            $dayEnd = $dayStart + 86400; // 24 hours

            $dayErrors = $this->getErrorsBetween($dayStart, $dayEnd);

            $trends[date('Y-m-d', $dayStart)] = [
                'total_errors' => count($dayErrors),
                'critical_errors' => count(array_filter($dayErrors, fn($e) => $e['severity'] === 'critical')),
                'error_rate' => count($dayErrors) / 1440, // errors per minute
                'unique_errors' => count(array_unique(array_column($dayErrors, 'type'))),
                'affected_users' => count(array_unique(array_filter(array_column($dayErrors, 'user_id'))))
            ];
        }

        return $trends;
    }

    /**
     * Get active alerts
     */
    public function getActiveAlerts(): array
    {
        $cacheKey = 'error_monitoring:active_alerts';
        $alerts = $this->cache->get($cacheKey) ?? [];

        // Filter out expired alerts
        $now = time();
        $activeAlerts = array_filter($alerts, fn($alert) => $alert['expires_at'] > $now);

        // Update cache if alerts were filtered
        if (count($activeAlerts) !== count($alerts)) {
            $this->cache->set($cacheKey, $activeAlerts, 3600);
        }

        return $activeAlerts;
    }

    /**
     * Clear error data
     */
    public function clearErrorData(int $olderThan = 604800): void // 7 days
    {
        $cutoff = microtime(true) - $olderThan;

        // Clear from cache
        $cacheKey = 'error_monitoring:errors';
        $errors = $this->cache->get($cacheKey) ?? [];

        $filteredErrors = array_filter($errors, fn($error) => $error['timestamp'] > $cutoff);

        $this->cache->set($cacheKey, $filteredErrors, 86400);

        $this->logger->info('Cleared old error data', [
            'cutoff' => date('Y-m-d H:i:s', (int)$cutoff),
            'removed_count' => count($errors) - count($filteredErrors)
        ]);
    }

    /**
     * Generate error report
     */
    public function generateErrorReport(int $timeframe = 86400): array
    {
        $stats = $this->getErrorStats($timeframe);
        $trends = $this->getErrorTrends(7);
        $alerts = $this->getActiveAlerts();

        return [
            'generated_at' => date('Y-m-d H:i:s'),
            'timeframe_hours' => $timeframe / 3600,
            'summary' => [
                'total_errors' => $stats['total_errors'],
                'error_rate' => round($stats['error_rate'], 2),
                'critical_errors' => $stats['by_severity']['critical'] ?? 0,
                'affected_users' => $stats['affected_users'],
                'top_error_type' => array_key_first($stats['by_type']) ?? 'None'
            ],
            'statistics' => $stats,
            'trends' => $trends,
            'active_alerts' => $alerts,
            'recommendations' => $this->generateRecommendations($stats)
        ];
    }

    /**
     * Determine error severity
     */
    private function determineSeverity(\Throwable $exception): string
    {
        // Fatal errors
        if (
            $exception instanceof \Error ||
            $exception instanceof \ParseError ||
            $exception instanceof \TypeError
        ) {
            return 'critical';
        }

        // High severity errors
        if (
            strpos(strtolower($exception->getMessage()), 'database') !== false ||
            strpos(strtolower($exception->getMessage()), 'connection') !== false ||
            $exception->getCode() >= 500
        ) {
            return 'high';
        }

        // Medium severity errors
        if ($exception->getCode() >= 400) {
            return 'medium';
        }

        return 'low';
    }

    /**
     * Store error data
     */
    private function storeErrorData(array $errorData): void
    {
        $cacheKey = 'error_monitoring:errors';
        $errors = $this->cache->get($cacheKey) ?? [];

        $errors[] = $errorData;

        // Keep only recent errors (last 24 hours)
        $cutoff = microtime(true) - 86400;
        $errors = array_filter($errors, fn($error) => $error['timestamp'] > $cutoff);

        $this->cache->set($cacheKey, $errors, 86400);
    }

    /**
     * Update error metrics
     */
    private function updateErrorMetrics(array $errorData): void
    {
        $minute = date('Y-m-d H:i');

        if (!isset($this->errorMetrics[$minute])) {
            $this->errorMetrics[$minute] = [
                'count' => 0,
                'types' => [],
                'severities' => []
            ];
        }

        $this->errorMetrics[$minute]['count']++;
        $this->errorMetrics[$minute]['types'][$errorData['type']] =
            ($this->errorMetrics[$minute]['types'][$errorData['type']] ?? 0) + 1;
        $this->errorMetrics[$minute]['severities'][$errorData['severity']] =
            ($this->errorMetrics[$minute]['severities'][$errorData['severity']] ?? 0) + 1;

        // Clean old metrics (keep only last hour)
        $cutoff = date('Y-m-d H:i', strtotime('-1 hour'));
        foreach ($this->errorMetrics as $time => $metrics) {
            if ($time < $cutoff) {
                unset($this->errorMetrics[$time]);
            }
        }
    }

    /**
     * Initialize alert thresholds
     */
    private function initializeAlertThresholds(): void
    {
        $this->alertThresholds = [
            'error_rate' => $this->config['alert_thresholds']['error_rate'],
            'critical_errors' => $this->config['alert_thresholds']['critical_errors'],
            'memory_usage' => $this->config['alert_thresholds']['memory_usage'],
            'error_spike' => $this->config['alert_thresholds']['error_spike']
        ];
    }

    /**
     * Check alert thresholds
     */
    private function checkAlertThresholds(array $errorData): void
    {
        // Check error rate
        $currentMinute = date('Y-m-d H:i');
        $errorCount = $this->errorMetrics[$currentMinute]['count'] ?? 0;

        if ($errorCount >= $this->alertThresholds['error_rate']) {
            $this->triggerAlert('high_error_rate', [
                'error_count' => $errorCount,
                'threshold' => $this->alertThresholds['error_rate'],
                'minute' => $currentMinute
            ]);
        }

        // Check critical errors
        if ($errorData['severity'] === 'critical') {
            $this->triggerAlert('critical_error', [
                'error_type' => $errorData['type'],
                'message' => $errorData['message'],
                'file' => $errorData['file'],
                'line' => $errorData['line']
            ]);
        }

        // Check memory usage
        $memoryUsageMB = $errorData['memory_usage'] / 1024 / 1024;
        if ($memoryUsageMB >= $this->alertThresholds['memory_usage']) {
            $this->triggerAlert('high_memory_usage', [
                'memory_usage_mb' => $memoryUsageMB,
                'threshold_mb' => $this->alertThresholds['memory_usage']
            ]);
        }
    }

    /**
     * Trigger an alert
     */
    private function triggerAlert(string $type, array $data): void
    {
        $alert = [
            'id' => uniqid('alert_', true),
            'type' => $type,
            'severity' => $this->getAlertSeverity($type),
            'message' => $this->getAlertMessage($type, $data),
            'data' => $data,
            'triggered_at' => time(),
            'expires_at' => time() + $this->config['alert_duration'],
            'acknowledged' => false
        ];

        // Store alert
        $cacheKey = 'error_monitoring:active_alerts';
        $alerts = $this->cache->get($cacheKey) ?? [];
        $alerts[] = $alert;
        $this->cache->set($cacheKey, $alerts, 3600);

        // Log alert
        $this->logger->warning("Alert triggered: {$type}", $alert);

        // Send notification (placeholder)
        $this->sendAlertNotification($alert);
    }

    /**
     * Get errors since timestamp
     */
    private function getErrorsSince(float $timestamp): array
    {
        $cacheKey = 'error_monitoring:errors';
        $errors = $this->cache->get($cacheKey) ?? [];

        return array_filter($errors, fn($error) => $error['timestamp'] >= $timestamp);
    }

    /**
     * Get errors between timestamps
     */
    private function getErrorsBetween(int $start, int $end): array
    {
        $cacheKey = 'error_monitoring:errors';
        $errors = $this->cache->get($cacheKey) ?? [];

        return array_filter($errors, fn($error) =>
            $error['timestamp'] >= $start && $error['timestamp'] < $end);
    }

    /**
     * Analyze error patterns
     */
    private function analyzeErrorPatterns(array $errors): array
    {
        $patterns = [];

        // Group by file and line
        $filePatterns = [];
        foreach ($errors as $error) {
            $key = $error['file'] . ':' . $error['line'];
            if (!isset($filePatterns[$key])) {
                $filePatterns[$key] = 0;
            }
            $filePatterns[$key]++;
        }

        // Find hotspots (files with multiple errors)
        arsort($filePatterns);
        $patterns['error_hotspots'] = array_slice($filePatterns, 0, 5);

        // Time-based patterns
        $hourlyPattern = [];
        foreach ($errors as $error) {
            $hour = date('H', (int)$error['timestamp']);
            if (!isset($hourlyPattern[$hour])) {
                $hourlyPattern[$hour] = 0;
            }
            $hourlyPattern[$hour]++;
        }
        $patterns['hourly_distribution'] = $hourlyPattern;

        return $patterns;
    }

    /**
     * Generate recommendations
     */
    private function generateRecommendations(array $stats): array
    {
        $recommendations = [];

        // High error rate
        if ($stats['error_rate'] > 10) {
            $recommendations[] = [
                'type' => 'high_error_rate',
                'priority' => 'high',
                'message' => 'Error rate is high. Consider investigating the most frequent errors.',
                'action' => 'Review top errors and implement fixes'
            ];
        }

        // Memory issues
        if ($stats['memory_usage']['peak'] > 100 * 1024 * 1024) { // 100MB
            $recommendations[] = [
                'type' => 'memory_usage',
                'priority' => 'medium',
                'message' => 'High memory usage detected. Consider optimizing memory-intensive operations.',
                'action' => 'Profile memory usage and optimize'
            ];
        }

        // Many affected users
        if ($stats['affected_users'] > 10) {
            $recommendations[] = [
                'type' => 'user_impact',
                'priority' => 'high',
                'message' => 'Many users are affected by errors. Immediate attention required.',
                'action' => 'Prioritize fixes for user-facing errors'
            ];
        }

        return $recommendations;
    }

    /**
     * Log error
     */
    private function logError(array $errorData): void
    {
        $level = match ($errorData['severity']) {
            'critical' => 'critical',
            'high' => 'error',
            'medium' => 'warning',
            default => 'info'
        };

        $this->logger->log($level, "Error recorded: {$errorData['type']}", [
            'message' => $errorData['message'],
            'file' => $errorData['file'],
            'line' => $errorData['line'],
            'request_id' => $errorData['request_id']
        ]);
    }

    /**
     * Get alert severity
     */
    private function getAlertSeverity(string $type): string
    {
        return match ($type) {
            'critical_error' => 'critical',
            'high_error_rate', 'high_memory_usage' => 'high',
            default => 'medium'
        };
    }

    /**
     * Get alert message
     */
    private function getAlertMessage(string $type, array $data): string
    {
        return match ($type) {
            'high_error_rate' => "High error rate detected: {$data['error_count']} errors in one minute",
            'critical_error' => "Critical error: {$data['error_type']} - {$data['message']}",
            'high_memory_usage' => "High memory usage: {$data['memory_usage_mb']}MB",
            default => "Alert triggered: {$type}"
        };
    }

    /**
     * Send alert notification
     */
    private function sendAlertNotification(array $alert): void
    {
        // Placeholder for notification system
        // Could integrate with Slack, email, SMS, etc.
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'alert_thresholds' => [
                'error_rate' => 50, // errors per minute
                'critical_errors' => 1, // any critical error triggers alert
                'memory_usage' => 256, // MB
                'error_spike' => 5 // 5x normal rate
            ],
            'alert_duration' => 3600, // 1 hour
            'data_retention' => 604800, // 7 days
            'enable_notifications' => true
        ];
    }
}
