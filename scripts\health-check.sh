#!/bin/bash
# WeBot Health Check Script

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
APP_URL=${APP_URL:-"http://localhost"}
TIMEOUT=${TIMEOUT:-10}
RETRIES=${RETRIES:-3}

echo -e "${BLUE}🏥 WeBot Health Check${NC}"
echo "======================"

# Health check functions
check_web_server() {
    echo -n "🌐 Web Server: "
    if curl -f -s --max-time $TIMEOUT "$APP_URL/health" > /dev/null; then
        echo -e "${GREEN}✅ OK${NC}"
        return 0
    else
        echo -e "${RED}❌ FAILED${NC}"
        return 1
    fi
}

check_database() {
    echo -n "🗄️  Database: "
    if docker-compose exec -T mysql mysqladmin ping -h localhost --silent; then
        echo -e "${GREEN}✅ OK${NC}"
        return 0
    else
        echo -e "${RED}❌ FAILED${NC}"
        return 1
    fi
}

check_redis() {
    echo -n "🔴 Redis: "
    if docker-compose exec -T redis redis-cli ping | grep -q PONG; then
        echo -e "${GREEN}✅ OK${NC}"
        return 0
    else
        echo -e "${RED}❌ FAILED${NC}"
        return 1
    fi
}

check_disk_space() {
    echo -n "💾 Disk Space: "
    USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$USAGE" -lt 90 ]; then
        echo -e "${GREEN}✅ OK (${USAGE}% used)${NC}"
        return 0
    else
        echo -e "${RED}❌ WARNING (${USAGE}% used)${NC}"
        return 1
    fi
}

check_memory() {
    echo -n "🧠 Memory: "
    USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ "$USAGE" -lt 90 ]; then
        echo -e "${GREEN}✅ OK (${USAGE}% used)${NC}"
        return 0
    else
        echo -e "${RED}❌ WARNING (${USAGE}% used)${NC}"
        return 1
    fi
}

check_containers() {
    echo -n "🐳 Docker Containers: "
    RUNNING=$(docker-compose ps --services --filter "status=running" | wc -l)
    TOTAL=$(docker-compose ps --services | wc -l)
    
    if [ "$RUNNING" -eq "$TOTAL" ]; then
        echo -e "${GREEN}✅ OK ($RUNNING/$TOTAL running)${NC}"
        return 0
    else
        echo -e "${RED}❌ FAILED ($RUNNING/$TOTAL running)${NC}"
        return 1
    fi
}

check_ssl_certificate() {
    if [[ "$APP_URL" == https* ]]; then
        echo -n "🔒 SSL Certificate: "
        DOMAIN=$(echo "$APP_URL" | sed 's|https://||' | sed 's|/.*||')
        EXPIRY=$(echo | openssl s_client -servername "$DOMAIN" -connect "$DOMAIN:443" 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
        EXPIRY_EPOCH=$(date -d "$EXPIRY" +%s)
        CURRENT_EPOCH=$(date +%s)
        DAYS_LEFT=$(( (EXPIRY_EPOCH - CURRENT_EPOCH) / 86400 ))
        
        if [ "$DAYS_LEFT" -gt 30 ]; then
            echo -e "${GREEN}✅ OK ($DAYS_LEFT days left)${NC}"
            return 0
        elif [ "$DAYS_LEFT" -gt 7 ]; then
            echo -e "${YELLOW}⚠️  WARNING ($DAYS_LEFT days left)${NC}"
            return 0
        else
            echo -e "${RED}❌ CRITICAL ($DAYS_LEFT days left)${NC}"
            return 1
        fi
    else
        echo -n "🔒 SSL Certificate: "
        echo -e "${YELLOW}⚠️  HTTP (no SSL)${NC}"
        return 0
    fi
}

check_telegram_webhook() {
    echo -n "📱 Telegram Webhook: "
    if curl -f -s --max-time $TIMEOUT "$APP_URL/webhook" -H "Content-Type: application/json" -d '{"test": true}' > /dev/null; then
        echo -e "${GREEN}✅ OK${NC}"
        return 0
    else
        echo -e "${RED}❌ FAILED${NC}"
        return 1
    fi
}

# Performance checks
check_response_time() {
    echo -n "⚡ Response Time: "
    RESPONSE_TIME=$(curl -o /dev/null -s -w '%{time_total}' "$APP_URL/health")
    RESPONSE_MS=$(echo "$RESPONSE_TIME * 1000" | bc | cut -d. -f1)
    
    if [ "$RESPONSE_MS" -lt 1000 ]; then
        echo -e "${GREEN}✅ OK (${RESPONSE_MS}ms)${NC}"
        return 0
    elif [ "$RESPONSE_MS" -lt 3000 ]; then
        echo -e "${YELLOW}⚠️  SLOW (${RESPONSE_MS}ms)${NC}"
        return 0
    else
        echo -e "${RED}❌ TOO SLOW (${RESPONSE_MS}ms)${NC}"
        return 1
    fi
}

# Main health check
main() {
    local failed=0
    
    # Core services
    check_web_server || ((failed++))
    check_database || ((failed++))
    check_redis || ((failed++))
    check_containers || ((failed++))
    
    # System resources
    check_disk_space || ((failed++))
    check_memory || ((failed++))
    
    # Application specific
    check_telegram_webhook || ((failed++))
    check_response_time || ((failed++))
    check_ssl_certificate || ((failed++))
    
    echo ""
    echo "======================"
    
    if [ $failed -eq 0 ]; then
        echo -e "${GREEN}🎉 All checks passed!${NC}"
        exit 0
    else
        echo -e "${RED}❌ $failed check(s) failed${NC}"
        exit 1
    fi
}

# Handle arguments
case "${1:-check}" in
    "check")
        main
        ;;
    "web")
        check_web_server
        ;;
    "db")
        check_database
        ;;
    "redis")
        check_redis
        ;;
    "containers")
        check_containers
        ;;
    *)
        echo "Usage: $0 {check|web|db|redis|containers}"
        exit 1
        ;;
esac
