<?php

declare(strict_types=1);

namespace WeBot\Events;

/**
 * Base Event
 *
 * Base implementation for all events in the WeBot system.
 * Provides common functionality for event handling.
 *
 * @package WeBot\Events
 * @version 2.0
 */
abstract class BaseEvent implements EventInterface
{
    protected array $data;
    protected float $timestamp;
    protected bool $stoppable;
    protected bool $propagationStopped = false;

    public function __construct(array $data = [], bool $stoppable = false)
    {
        $this->data = $data;
        $this->timestamp = microtime(true);
        $this->stoppable = $stoppable;
    }

    /**
     * Get event name
     */
    public function getName(): string
    {
        return static::class;
    }

    /**
     * Get event data
     */
    public function getData(): array
    {
        return $this->data;
    }

    /**
     * Get specific data value
     */
    public function get(string $key, $default = null)
    {
        return $this->data[$key] ?? $default;
    }

    /**
     * Set data value
     */
    public function set(string $key, $value): void
    {
        $this->data[$key] = $value;
    }

    /**
     * Get event timestamp
     */
    public function getTimestamp(): float
    {
        return $this->timestamp;
    }

    /**
     * Check if event is stoppable
     */
    public function isStoppable(): bool
    {
        return $this->stoppable;
    }

    /**
     * Stop event propagation
     */
    public function stopPropagation(): void
    {
        if ($this->stoppable) {
            $this->propagationStopped = true;
        }
    }

    /**
     * Check if propagation is stopped
     */
    public function isPropagationStopped(): bool
    {
        return $this->propagationStopped;
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return [
            'name' => $this->getName(),
            'data' => $this->getData(),
            'timestamp' => $this->getTimestamp(),
            'stoppable' => $this->isStoppable(),
            'propagation_stopped' => $this->isPropagationStopped()
        ];
    }

    /**
     * Convert to JSON
     */
    public function toJson(): string
    {
        return json_encode($this->toArray(), JSON_UNESCAPED_UNICODE);
    }
}
