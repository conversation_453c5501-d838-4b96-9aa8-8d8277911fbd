<?php
/**
 * WeBot Autoloader Test
 * 
 * This file tests the autoloading functionality without requiring
 * external dependencies to be installed.
 * 
 * Run: php test_autoload.php
 */

echo "WeBot Autoloader Test\n";
echo "====================\n\n";

// Test 1: Check PHP version
echo "1. PHP Version Check: ";
if (version_compare(PHP_VERSION, '8.1.0', '>=')) {
    echo "✅ PHP " . PHP_VERSION . " (OK)\n";
} else {
    echo "❌ PHP " . PHP_VERSION . " (Requires 8.1+)\n";
    exit(1);
}

// Test 2: Check required extensions
echo "2. Required Extensions:\n";
$requiredExtensions = ['curl', 'json', 'pdo', 'mbstring', 'gd'];
$missingExtensions = [];

foreach ($requiredExtensions as $extension) {
    if (extension_loaded($extension)) {
        echo "   ✅ {$extension}\n";
    } else {
        echo "   ❌ {$extension}\n";
        $missingExtensions[] = $extension;
    }
}

if (!empty($missingExtensions)) {
    echo "\nMissing extensions: " . implode(', ', $missingExtensions) . "\n";
    echo "Please install missing extensions before proceeding.\n";
    exit(1);
}

// Test 3: Check file structure
echo "3. File Structure:\n";
$requiredDirs = [
    'src',
    'src/Controllers',
    'src/Services', 
    'src/Models',
    'src/Repositories',
    'src/Middleware',
    'src/Utils',
    'src/Exceptions',
    'config',
    'config/database',
    'config/telegram',
    'config/panels',
    'config/payments',
    'public',
    'public/assets',
    'public/uploads',
    'public/temp',
    'tests',
    'tests/Unit',
    'tests/Integration',
    'tests/Feature'
];

foreach ($requiredDirs as $dir) {
    if (is_dir($dir)) {
        echo "   ✅ {$dir}/\n";
    } else {
        echo "   ❌ {$dir}/\n";
    }
}

// Test 4: Check required files
echo "4. Required Files:\n";
$requiredFiles = [
    'composer.json',
    'autoload.php',
    'index.php',
    'phpunit.xml',
    '.env.example',
    '.env.testing',
    'tests/bootstrap.php',
    'PROJECT_STRUCTURE.md'
];

foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        echo "   ✅ {$file}\n";
    } else {
        echo "   ❌ {$file}\n";
    }
}

// Test 5: Check composer.json structure
echo "5. Composer Configuration:\n";
if (file_exists('composer.json')) {
    $composer = json_decode(file_get_contents('composer.json'), true);
    
    if (isset($composer['autoload']['psr-4']['WeBot\\'])) {
        echo "   ✅ PSR-4 autoloading configured\n";
    } else {
        echo "   ❌ PSR-4 autoloading not configured\n";
    }
    
    if (isset($composer['autoload-dev']['psr-4']['WeBot\\Tests\\'])) {
        echo "   ✅ Test autoloading configured\n";
    } else {
        echo "   ❌ Test autoloading not configured\n";
    }
    
    if (isset($composer['require']['php'])) {
        echo "   ✅ PHP version requirement: " . $composer['require']['php'] . "\n";
    }
    
    $requiredPackages = [
        'endroid/qr-code',
        'monolog/monolog', 
        'vlucas/phpdotenv',
        'guzzlehttp/guzzle',
        'symfony/validator',
        'league/container',
        'ramsey/uuid'
    ];
    
    echo "   Required packages:\n";
    foreach ($requiredPackages as $package) {
        if (isset($composer['require'][$package])) {
            echo "     ✅ {$package}: " . $composer['require'][$package] . "\n";
        } else {
            echo "     ❌ {$package}: Not found\n";
        }
    }
}

// Test 6: Check environment configuration
echo "6. Environment Configuration:\n";
if (file_exists('.env.example')) {
    $envExample = file_get_contents('.env.example');
    
    $requiredEnvVars = [
        'APP_ENV',
        'TELEGRAM_BOT_TOKEN',
        'DB_HOST',
        'DB_DATABASE',
        'ADMIN_ID'
    ];
    
    foreach ($requiredEnvVars as $var) {
        if (strpos($envExample, $var) !== false) {
            echo "   ✅ {$var}\n";
        } else {
            echo "   ❌ {$var}\n";
        }
    }
}

// Test 7: Check PHPUnit configuration
echo "7. PHPUnit Configuration:\n";
if (file_exists('phpunit.xml')) {
    $phpunit = simplexml_load_file('phpunit.xml');
    
    if ($phpunit->testsuites) {
        echo "   ✅ Test suites configured\n";
    }
    
    if ($phpunit->source || $phpunit->filter) {
        echo "   ✅ Code coverage configured\n";
    }
    
    if (isset($phpunit['bootstrap'])) {
        echo "   ✅ Bootstrap file: " . $phpunit['bootstrap'] . "\n";
    }
}

echo "\n";
echo "====================\n";
echo "Autoloader Test Complete\n";

// Test 8: Simulate autoloading (without vendor)
echo "8. Autoloading Simulation:\n";
echo "   Note: Actual autoloading requires 'composer install'\n";
echo "   PSR-4 Namespace: WeBot\\ -> src/\n";
echo "   Test Namespace: WeBot\\Tests\\ -> tests/\n";

// Show what would be autoloaded
$exampleClasses = [
    'WeBot\\Controllers\\AuthController' => 'src/Controllers/AuthController.php',
    'WeBot\\Services\\TelegramService' => 'src/Services/TelegramService.php',
    'WeBot\\Models\\User' => 'src/Models/User.php',
    'WeBot\\Tests\\Unit\\UserTest' => 'tests/Unit/UserTest.php'
];

foreach ($exampleClasses as $class => $file) {
    echo "   {$class} -> {$file}\n";
}

echo "\n✅ Autoloader configuration is ready!\n";
echo "Next steps:\n";
echo "1. Install Composer: https://getcomposer.org/\n";
echo "2. Run: composer install\n";
echo "3. Create .env file from .env.example\n";
echo "4. Run tests: ./vendor/bin/phpunit\n";
echo "5. Start development!\n";
