<?php

declare(strict_types=1);

namespace WeBot\Core\Cache\Managers;

use WeBot\Core\Cache\Contracts\CacheInterface;

/**
 * Cache Session Manager
 *
 * Manages session-related cache operations.
 *
 * @package WeBot\Core\Cache\Managers
 * @version 2.0
 */
class CacheSessionManager
{
    private CacheInterface $cache;
    private string $sessionPrefix = 'session:';
    private int $defaultTtl = 7200; // 2 hours

    public function __construct(CacheInterface $cache)
    {
        $this->cache = $cache;
    }

    /**
     * Store session data
     */
    public function store(string $sessionId, array $data, ?int $ttl = null): bool
    {
        $key = $this->getSessionKey($sessionId);
        return $this->cache->set($key, $data, $ttl ?? $this->defaultTtl);
    }

    /**
     * Retrieve session data
     */
    public function retrieve(string $sessionId): ?array
    {
        $key = $this->getSessionKey($sessionId);
        $data = $this->cache->get($key);

        return is_array($data) ? $data : null;
    }

    /**
     * Update session data
     */
    public function update(string $sessionId, array $data, ?int $ttl = null): bool
    {
        return $this->store($sessionId, $data, $ttl);
    }

    /**
     * Delete session
     */
    public function delete(string $sessionId): bool
    {
        $key = $this->getSessionKey($sessionId);
        return $this->cache->delete($key);
    }

    /**
     * Check if session exists
     */
    public function exists(string $sessionId): bool
    {
        $key = $this->getSessionKey($sessionId);
        return $this->cache->exists($key);
    }

    /**
     * Extend session TTL
     */
    public function extend(string $sessionId, int $ttl): bool
    {
        $data = $this->retrieve($sessionId);
        if ($data === null) {
            return false;
        }

        return $this->store($sessionId, $data, $ttl);
    }

    /**
     * Get session key
     */
    private function getSessionKey(string $sessionId): string
    {
        return $this->sessionPrefix . $sessionId;
    }

    /**
     * Clean expired sessions
     */
    public function cleanup(): int
    {
        // This would require scanning keys with session prefix
        // Implementation depends on cache driver capabilities
        return 0;
    }
}
