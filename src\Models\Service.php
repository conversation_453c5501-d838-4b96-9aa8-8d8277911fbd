<?php

declare(strict_types=1);

namespace WeBot\Models;

use WeBot\Services\DatabaseService;
use WeBot\Utils\Helper;

/**
 * Service Model
 *
 * Represents a VPN service in the WeBot system
 * with panel integration and usage tracking.
 *
 * @package WeBot\Models
 * @version 2.0
 */
class Service extends BaseModel
{
    protected string $table = 'services';
    protected string $primaryKey = 'id';

    protected array $fillable = [
        'user_id',
        'server_id',
        'plan_id',
        'remark',
        'uuid',
        'volume',
        'used_volume',
        'days',
        'status',
        'expires_at',
        'panel_user_id',
        'config_data',
        'last_sync_at'
    ];

    protected array $casts = [
        'user_id' => 'int',
        'server_id' => 'int',
        'plan_id' => 'int',
        'volume' => 'int',
        'used_volume' => 'int',
        'days' => 'int',
        'config_data' => 'json',
        'expires_at' => 'datetime',
        'last_sync_at' => 'datetime'
    ];

    // Service statuses
    public const STATUS_ACTIVE = 'active';
    public const STATUS_EXPIRED = 'expired';
    public const STATUS_SUSPENDED = 'suspended';
    public const STATUS_CANCELLED = 'cancelled';

    /**
     * Get validation rules
     */
    protected function getValidationRules(): array
    {
        return [
            'user_id' => 'required|integer|min:1',
            'server_id' => 'required|integer|min:1',
            'plan_id' => 'required|integer|min:1',
            'remark' => 'required|string|max:255',
            'uuid' => 'required|string|max:255',
            'volume' => 'required|integer|min:0',
            'days' => 'required|integer|min:1',
            'status' => 'required|string|in:active,expired,suspended,cancelled'
        ];
    }

    /**
     * Check if service is active
     */
    public function isActive(): bool
    {
        return $this->getAttribute('status') === self::STATUS_ACTIVE && !$this->isExpired();
    }

    /**
     * Check if service is expired
     */
    public function isExpired(): bool
    {
        $expiresAt = $this->getAttribute('expires_at');

        if (!$expiresAt) {
            return false;
        }

        if ($expiresAt instanceof \DateTime) {
            return $expiresAt < new \DateTime();
        }

        return strtotime($expiresAt) < time();
    }

    /**
     * Check if service is suspended
     */
    public function isSuspended(): bool
    {
        return $this->getAttribute('status') === self::STATUS_SUSPENDED;
    }

    /**
     * Check if service is cancelled
     */
    public function isCancelled(): bool
    {
        return $this->getAttribute('status') === self::STATUS_CANCELLED;
    }

    /**
     * Get remaining days
     */
    public function getRemainingDays(): int
    {
        if ($this->isExpired()) {
            return 0;
        }

        $expiresAt = $this->getAttribute('expires_at');

        if (!$expiresAt) {
            return 0;
        }

        $expiryTime = $expiresAt instanceof \DateTime ? $expiresAt->getTimestamp() : strtotime($expiresAt);
        $remainingSeconds = $expiryTime - time();

        return max(0, ceil($remainingSeconds / 86400));
    }

    /**
     * Get remaining volume
     */
    public function getRemainingVolume(): int
    {
        $totalVolume = $this->getAttribute('volume') ?? 0;
        $usedVolume = $this->getAttribute('used_volume') ?? 0;

        return max(0, $totalVolume - $usedVolume);
    }

    /**
     * Get usage percentage
     */
    public function getUsagePercentage(): float
    {
        $totalVolume = $this->getAttribute('volume') ?? 0;
        $usedVolume = $this->getAttribute('used_volume') ?? 0;

        if ($totalVolume <= 0) {
            return 0;
        }

        return min(100, ($usedVolume / $totalVolume) * 100);
    }

    /**
     * Get formatted volume
     */
    public function getFormattedVolume(): string
    {
        return Helper::formatBytes($this->getAttribute('volume') ?? 0);
    }

    /**
     * Get formatted used volume
     */
    public function getFormattedUsedVolume(): string
    {
        return Helper::formatBytes($this->getAttribute('used_volume') ?? 0);
    }

    /**
     * Get formatted remaining volume
     */
    public function getFormattedRemainingVolume(): string
    {
        return Helper::formatBytes($this->getRemainingVolume());
    }

    /**
     * Get status title
     */
    public function getStatusTitle(): string
    {
        if ($this->isExpired()) {
            return 'منقضی شده';
        }

        return match ($this->getAttribute('status')) {
            self::STATUS_ACTIVE => 'فعال',
            self::STATUS_EXPIRED => 'منقضی شده',
            self::STATUS_SUSPENDED => 'تعلیق شده',
            self::STATUS_CANCELLED => 'لغو شده',
            default => 'نامشخص'
        };
    }

    /**
     * Get status icon
     */
    public function getStatusIcon(): string
    {
        if ($this->isExpired()) {
            return '🔴';
        }

        return match ($this->getAttribute('status')) {
            self::STATUS_ACTIVE => '🟢',
            self::STATUS_EXPIRED => '🔴',
            self::STATUS_SUSPENDED => '🟡',
            self::STATUS_CANCELLED => '⚫',
            default => '⚪'
        };
    }

    /**
     * Activate service
     */
    public function activate(): bool
    {
        $this->setAttribute('status', self::STATUS_ACTIVE);
        return $this->save();
    }

    /**
     * Suspend service
     */
    public function suspend(): bool
    {
        $this->setAttribute('status', self::STATUS_SUSPENDED);
        return $this->save();
    }

    /**
     * Cancel service
     */
    public function cancel(): bool
    {
        $this->setAttribute('status', self::STATUS_CANCELLED);
        return $this->save();
    }

    /**
     * Renew service
     */
    public function renew(int $days): bool
    {
        $currentExpiry = $this->getAttribute('expires_at');

        if ($currentExpiry instanceof \DateTime) {
            $newExpiry = $currentExpiry->modify("+{$days} days");
        } else {
            $currentExpiryTime = $this->isExpired() ? time() : strtotime($currentExpiry);
            $newExpiry = new \DateTime();
            $newExpiry->setTimestamp($currentExpiryTime + ($days * 86400));
        }

        $this->setAttribute('expires_at', $newExpiry);
        $this->setAttribute('status', self::STATUS_ACTIVE);

        return $this->save();
    }

    /**
     * Update usage
     */
    public function updateUsage(int $usedVolume): bool
    {
        $this->setAttribute('used_volume', $usedVolume);
        $this->setAttribute('last_sync_at', new \DateTime());

        // Check if volume is exceeded
        if ($usedVolume >= $this->getAttribute('volume')) {
            $this->setAttribute('status', self::STATUS_SUSPENDED);
        }

        return $this->save();
    }

    /**
     * Get user
     */
    public function getUser(): ?User
    {
        $userId = $this->getAttribute('user_id');

        if (!$userId) {
            return null;
        }

        return User::find($this->database, $userId);
    }

    /**
     * Get server
     */
    public function getServer(): ?array
    {
        $serverId = $this->getAttribute('server_id');

        if (!$serverId) {
            return null;
        }

        $sql = "SELECT * FROM `server_info` WHERE `id` = ?";
        return $this->database->fetchRow($sql, [$serverId], 'i');
    }

    /**
     * Get plan
     */
    public function getPlan(): ?array
    {
        $planId = $this->getAttribute('plan_id');

        if (!$planId) {
            return null;
        }

        $sql = "SELECT * FROM `server_plans` WHERE `id` = ?";
        return $this->database->fetchRow($sql, [$planId], 'i');
    }

    /**
     * Get configuration
     */
    public function getConfig(): ?string
    {
        $configData = $this->getAttribute('config_data');

        if (is_array($configData) && isset($configData['config'])) {
            return $configData['config'];
        }

        return null;
    }

    /**
     * Set configuration
     */
    public function setConfig(string $config): bool
    {
        $configData = $this->getAttribute('config_data') ?? [];
        $configData['config'] = $config;
        $configData['updated_at'] = date('Y-m-d H:i:s');

        $this->setAttribute('config_data', $configData);

        return $this->save();
    }

    /**
     * Get QR code data
     */
    public function getQRCode(): ?string
    {
        $config = $this->getConfig();

        if (!$config) {
            return null;
        }

        // Generate QR code (this would use a QR code library)
        return base64_encode($config); // Placeholder
    }

    /**
     * Check if needs sync
     */
    public function needsSync(int $syncIntervalMinutes = 60): bool
    {
        $lastSync = $this->getAttribute('last_sync_at');

        if (!$lastSync) {
            return true;
        }

        $lastSyncTime = $lastSync instanceof \DateTime ? $lastSync->getTimestamp() : strtotime($lastSync);
        $syncInterval = $syncIntervalMinutes * 60;

        return (time() - $lastSyncTime) > $syncInterval;
    }

    /**
     * Mark as synced
     */
    public function markAsSynced(): bool
    {
        $this->setAttribute('last_sync_at', new \DateTime());
        return $this->save();
    }

    /**
     * Get service statistics
     */
    public function getStatistics(): array
    {
        return [
            'id' => $this->getKey(),
            'remark' => $this->getAttribute('remark'),
            'status' => $this->getStatusTitle(),
            'is_active' => $this->isActive(),
            'is_expired' => $this->isExpired(),
            'remaining_days' => $this->getRemainingDays(),
            'total_volume' => $this->getFormattedVolume(),
            'used_volume' => $this->getFormattedUsedVolume(),
            'remaining_volume' => $this->getFormattedRemainingVolume(),
            'usage_percentage' => round($this->getUsagePercentage(), 2),
            'expires_at' => $this->getAttribute('expires_at'),
            'created_at' => $this->getAttribute('created_at'),
            'last_sync_at' => $this->getAttribute('last_sync_at')
        ];
    }

    /**
     * Find services by user
     */
    public static function findByUser(DatabaseService $database, int $userId): array
    {
        $sql = "SELECT * FROM `services` WHERE `user_id` = ? ORDER BY `created_at` DESC";
        $results = $database->fetchAll($sql, [$userId], 'i');

        $services = [];
        foreach ($results as $attributes) {
            $services[] = new self($database, $attributes);
        }

        return $services;
    }

    /**
     * Find active services by user
     */
    public static function findActiveByUser(DatabaseService $database, int $userId): array
    {
        $sql = "SELECT * FROM `services` WHERE `user_id` = ? AND `status` = ? AND `expires_at` > NOW() ORDER BY `created_at` DESC";
        $results = $database->fetchAll($sql, [$userId, self::STATUS_ACTIVE], 'is');

        $services = [];
        foreach ($results as $attributes) {
            $services[] = new self($database, $attributes);
        }

        return $services;
    }

    /**
     * Find services by server
     */
    public static function findByServer(DatabaseService $database, int $serverId): array
    {
        $sql = "SELECT * FROM `services` WHERE `server_id` = ? ORDER BY `created_at` DESC";
        $results = $database->fetchAll($sql, [$serverId], 'i');

        $services = [];
        foreach ($results as $attributes) {
            $services[] = new self($database, $attributes);
        }

        return $services;
    }

    /**
     * Find expired services
     */
    public static function findExpired(DatabaseService $database): array
    {
        $sql = "SELECT * FROM `services` WHERE `expires_at` < NOW() AND `status` != ?";
        $results = $database->fetchAll($sql, [self::STATUS_EXPIRED], 's');

        $services = [];
        foreach ($results as $attributes) {
            $services[] = new self($database, $attributes);
        }

        return $services;
    }

    /**
     * Find services that need sync
     */
    public static function findNeedingSync(DatabaseService $database, int $syncIntervalMinutes = 60): array
    {
        $syncTime = date('Y-m-d H:i:s', time() - ($syncIntervalMinutes * 60));

        $sql = "SELECT * FROM `services` WHERE 
                `status` = ? AND 
                (`last_sync_at` IS NULL OR `last_sync_at` < ?) 
                ORDER BY `last_sync_at` ASC";

        $results = $database->fetchAll($sql, [self::STATUS_ACTIVE, $syncTime], 'ss');

        $services = [];
        foreach ($results as $attributes) {
            $services[] = new self($database, $attributes);
        }

        return $services;
    }

    /**
     * Get global service statistics
     */
    public static function getGlobalStatistics(DatabaseService $database): array
    {
        $stats = [
            'total_services' => 0,
            'active_services' => 0,
            'expired_services' => 0,
            'suspended_services' => 0,
            'cancelled_services' => 0
        ];

        // Total services
        $sql = "SELECT COUNT(*) FROM `services`";
        $stats['total_services'] = (int) $database->fetchValue($sql);

        // Active services
        $sql = "SELECT COUNT(*) FROM `services` WHERE `status` = ? AND `expires_at` > NOW()";
        $stats['active_services'] = (int) $database->fetchValue($sql, [self::STATUS_ACTIVE], 's');

        // Expired services
        $sql = "SELECT COUNT(*) FROM `services` WHERE `expires_at` < NOW()";
        $stats['expired_services'] = (int) $database->fetchValue($sql);

        // Suspended services
        $sql = "SELECT COUNT(*) FROM `services` WHERE `status` = ?";
        $stats['suspended_services'] = (int) $database->fetchValue($sql, [self::STATUS_SUSPENDED], 's');

        // Cancelled services
        $sql = "SELECT COUNT(*) FROM `services` WHERE `status` = ?";
        $stats['cancelled_services'] = (int) $database->fetchValue($sql, [self::STATUS_CANCELLED], 's');

        return $stats;
    }
}
