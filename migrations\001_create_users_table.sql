-- WeBot Database Migration: Create Users Table
-- Version: 1.0
-- Date: 2025-01-07

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    telegram_id BIGINT UNSIGNED NOT NULL UNIQUE,
    username <PERSON><PERSON><PERSON><PERSON>(255) NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(255) NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(255) NULL,
    phone VARCHAR(20) NULL,
    email VARCHAR(255) NULL,
    language_code VARCHAR(10) DEFAULT 'fa',
    status ENUM('active', 'inactive', 'banned', 'pending') DEFAULT 'active',
    role ENUM('user', 'admin', 'moderator') DEFAULT 'user',
    
    -- User preferences
    notifications_enabled BOOLEAN DEFAULT TRUE,
    timezone VARCHAR(50) DEFAULT 'Asia/Tehran',
    
    -- Subscription info
    subscription_status ENUM('free', 'premium', 'vip') DEFAULT 'free',
    subscription_expires_at TIMESTAMP NULL,
    
    -- Usage statistics
    total_usage BIGINT UNSIGNED DEFAULT 0,
    monthly_usage BIGINT UNSIGNED DEFAULT 0,
    last_usage_reset DATE NULL,
    
    -- Account info
    balance DECIMAL(10,2) DEFAULT 0.00,
    referral_code VARCHAR(20) UNIQUE NULL,
    referred_by BIGINT UNSIGNED NULL,
    referral_count INT UNSIGNED DEFAULT 0,
    
    -- Security
    is_verified BOOLEAN DEFAULT FALSE,
    verification_code VARCHAR(10) NULL,
    verification_expires_at TIMESTAMP NULL,
    last_login_at TIMESTAMP NULL,
    login_attempts INT UNSIGNED DEFAULT 0,
    locked_until TIMESTAMP NULL,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    -- Indexes
    INDEX idx_telegram_id (telegram_id),
    INDEX idx_username (username),
    INDEX idx_status (status),
    INDEX idx_role (role),
    INDEX idx_subscription_status (subscription_status),
    INDEX idx_referral_code (referral_code),
    INDEX idx_created_at (created_at),
    
    -- Foreign key constraints
    FOREIGN KEY (referred_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create trigger for updating updated_at
DELIMITER $$
CREATE TRIGGER users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW 
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$
DELIMITER ;

-- Insert default admin user (if not exists)
INSERT IGNORE INTO users (
    telegram_id, 
    username, 
    first_name, 
    role, 
    status,
    is_verified,
    subscription_status
) VALUES (
    123456789, -- Replace with actual admin telegram ID
    'admin',
    'WeBot Admin',
    'admin',
    'active',
    TRUE,
    'vip'
);
