#!/usr/bin/env php
<?php
/**
 * Environment Variables Validation Script
 * 
 * This script validates environment variables according to defined rules
 * and provides detailed feedback about configuration issues.
 * 
 * Usage:
 *   php scripts/validate-env.php [--env=.env.example]
 * 
 * @package WeBot
 * @version 2.0
 */

declare(strict_types=1);

// Load autoloader
require_once __DIR__ . '/../autoload.php';

use WeBot\Core\EnvironmentValidator;

/**
 * Parse command line arguments
 */
function parseArguments(array $argv): array
{
    $options = [
        'env_file' => '.env',
        'help' => false,
        'verbose' => false,
        'json' => false,
    ];
    
    for ($i = 1; $i < count($argv); $i++) {
        $arg = $argv[$i];
        
        if ($arg === '--help' || $arg === '-h') {
            $options['help'] = true;
        } elseif ($arg === '--verbose' || $arg === '-v') {
            $options['verbose'] = true;
        } elseif ($arg === '--json') {
            $options['json'] = true;
        } elseif (strpos($arg, '--env=') === 0) {
            $options['env_file'] = substr($arg, 6);
        }
    }
    
    return $options;
}

/**
 * Show help message
 */
function showHelp(): void
{
    echo "Environment Variables Validation Script\n";
    echo "=====================================\n\n";
    echo "Usage: php scripts/validate-env.php [options]\n\n";
    echo "Options:\n";
    echo "  --env=FILE     Specify environment file to validate (default: .env)\n";
    echo "  --verbose, -v  Show verbose output\n";
    echo "  --json         Output results in JSON format\n";
    echo "  --help, -h     Show this help message\n\n";
    echo "Examples:\n";
    echo "  php scripts/validate-env.php\n";
    echo "  php scripts/validate-env.php --env=.env.production\n";
    echo "  php scripts/validate-env.php --verbose\n";
    echo "  php scripts/validate-env.php --json\n\n";
}

/**
 * Load environment file
 */
function loadEnvironmentFile(string $envFile): bool
{
    if (!file_exists($envFile)) {
        echo "❌ Environment file '{$envFile}' not found!\n";
        return false;
    }
    
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    
    foreach ($lines as $line) {
        $line = trim($line);
        
        // Skip comments and empty lines
        if (empty($line) || $line[0] === '#') {
            continue;
        }
        
        // Parse key=value pairs
        if (strpos($line, '=') !== false) {
            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value, '"\'');
            
            if (!empty($key)) {
                $_ENV[$key] = $value;
                putenv("{$key}={$value}");
            }
        }
    }
    
    return true;
}

/**
 * Main execution
 */
function main(array $argv): int
{
    $options = parseArguments($argv);
    
    if ($options['help']) {
        showHelp();
        return 0;
    }
    
    // Load environment file
    if (!loadEnvironmentFile($options['env_file'])) {
        return 1;
    }
    
    if ($options['verbose']) {
        echo "🔍 Validating environment file: {$options['env_file']}\n";
        echo "📊 Loaded " . count($_ENV) . " environment variables\n\n";
    }
    
    // Create validator and run validation
    $validator = new EnvironmentValidator();
    $result = $validator->validate();
    
    // Output results
    if ($options['json']) {
        echo json_encode([
            'valid' => $result['valid'],
            'errors' => $result['errors'],
            'warnings' => $result['warnings'],
            'env_file' => $options['env_file'],
            'timestamp' => date('Y-m-d H:i:s'),
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    } else {
        echo $validator->generateReport();
        
        if ($options['verbose']) {
            echo "\n📋 Validation Summary:\n";
            echo "   Environment: " . ($_ENV['APP_ENV'] ?? 'unknown') . "\n";
            echo "   Errors: " . count($result['errors']) . "\n";
            echo "   Warnings: " . count($result['warnings']) . "\n";
            echo "   File: {$options['env_file']}\n";
        }
    }
    
    // Return appropriate exit code
    return $result['valid'] ? 0 : 1;
}

// Run the script
try {
    $exitCode = main($argv);
    exit($exitCode);
} catch (Exception $e) {
    echo "❌ Fatal error: " . $e->getMessage() . "\n";
    if (isset($options['verbose']) && $options['verbose']) {
        echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    }
    exit(1);
}
