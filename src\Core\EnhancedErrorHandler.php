<?php

declare(strict_types=1);

namespace WeBot\Core;

use WeBot\Utils\Logger;
use WeBot\Exceptions\ErrorRecoveryException;
use WeBot\Exceptions\WeBotException;

/**
 * Enhanced Error Handler
 *
 * Advanced error handling with recovery mechanisms,
 * detailed logging, and user-friendly error messages.
 *
 * @package WeBot\Core
 * @version 2.0
 */
class EnhancedErrorHandler
{
    private Logger $logger;
    private array $config;
    private array $errorStats = [];
    private array $recoveryStrategies = [];
    private bool $isProduction;

    public function __construct(array $config = [])
    {
        $this->logger = Logger::getInstance();
        $this->config = array_merge($this->getDefaultConfig(), $config);
        $this->isProduction = ($_ENV['APP_ENV'] ?? 'production') === 'production';

        $this->initializeRecoveryStrategies();
        $this->registerHandlers();
    }

    /**
     * Register error and exception handlers
     */
    public function registerHandlers(): void
    {
        set_error_handler([$this, 'handleError']);
        set_exception_handler([$this, 'handleException']);
        register_shutdown_function([$this, 'handleShutdown']);
    }

    /**
     * Handle PHP errors
     */
    public function handleError(int $severity, string $message, string $file, int $line): bool
    {
        if (!(error_reporting() & $severity)) {
            return false;
        }

        $errorData = [
            'type' => 'php_error',
            'severity' => $severity,
            'severity_name' => $this->getSeverityName($severity),
            'message' => $message,
            'file' => $file,
            'line' => $line,
            'timestamp' => microtime(true),
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
            'request_id' => $this->generateRequestId(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'ip_address' => $this->getClientIp(),
            'url' => $_SERVER['REQUEST_URI'] ?? 'CLI',
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'CLI'
        ];

        // Update error statistics
        $this->updateErrorStats($errorData);

        // Log the error
        $this->logError($errorData);

        // Check if error needs immediate attention
        if ($this->isCriticalError($severity)) {
            $this->handleCriticalError($errorData);
        }

        // Display error in development
        if (!$this->isProduction && $this->config['display_errors']) {
            $this->displayError($errorData);
        }

        return true;
    }

    /**
     * Handle exceptions
     */
    public function handleException(\Throwable $exception): void
    {
        $errorData = [
            'type' => 'exception',
            'exception_class' => get_class($exception),
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'code' => $exception->getCode(),
            'trace' => $exception->getTraceAsString(),
            'timestamp' => microtime(true),
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
            'request_id' => $this->generateRequestId(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'ip_address' => $this->getClientIp(),
            'url' => $_SERVER['REQUEST_URI'] ?? 'CLI',
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'CLI'
        ];

        // Add context if it's a WeBot exception
        if ($exception instanceof WeBotException) {
            $errorData['context'] = $exception->getContext();
            $errorData['user_message'] = $exception->getUserMessage();
        }

        // Update error statistics
        $this->updateErrorStats($errorData);

        // Log the exception
        $this->logException($exception, $errorData);

        // Try recovery if it's a recoverable exception
        if ($exception instanceof ErrorRecoveryException && $exception->isRecoverable()) {
            if ($this->attemptRecovery($exception)) {
                return; // Recovery successful
            }
        }

        // Handle critical exceptions
        if ($this->isCriticalException($exception)) {
            $this->handleCriticalException($exception, $errorData);
        }

        // Send appropriate response
        $this->sendErrorResponse($exception, $errorData);
    }

    /**
     * Handle shutdown errors (fatal errors)
     */
    public function handleShutdown(): void
    {
        $error = error_get_last();

        if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            $errorData = [
                'type' => 'fatal_error',
                'severity' => $error['type'],
                'severity_name' => $this->getSeverityName($error['type']),
                'message' => $error['message'],
                'file' => $error['file'],
                'line' => $error['line'],
                'timestamp' => microtime(true),
                'memory_usage' => memory_get_usage(true),
                'peak_memory' => memory_get_peak_usage(true),
                'request_id' => $this->generateRequestId()
            ];

            // Log fatal error
            $this->logger->emergency('Fatal Error', $errorData);

            // Update error statistics
            $this->updateErrorStats($errorData);

            // Send emergency notification
            $this->sendEmergencyNotification($errorData);

            // Send error response
            if (!headers_sent()) {
                http_response_code(500);
                if (php_sapi_name() !== 'cli') {
                    echo json_encode([
                        'error' => true,
                        'message' => $this->isProduction ?
                            'خطای سیستمی رخ داده است. لطفاً بعداً تلاش کنید.' :
                            $error['message'],
                        'request_id' => $errorData['request_id']
                    ]);
                }
            }
        }
    }

    /**
     * Attempt error recovery
     */
    private function attemptRecovery(ErrorRecoveryException $exception): bool
    {
        try {
            $this->logger->info('Attempting error recovery', [
                'exception' => get_class($exception),
                'message' => $exception->getMessage(),
                'retry_count' => $exception->getRetryCount()
            ]);

            $recovered = $exception->executeRecovery();

            if ($recovered) {
                $this->logger->info('Error recovery successful', [
                    'exception' => get_class($exception),
                    'message' => $exception->getMessage()
                ]);
                return true;
            } else {
                $exception->incrementRetryCount();
                $this->logger->warning('Error recovery failed', [
                    'exception' => get_class($exception),
                    'message' => $exception->getMessage(),
                    'retry_count' => $exception->getRetryCount()
                ]);
            }
        } catch (\Throwable $e) {
            $this->logger->error('Error during recovery attempt', [
                'original_exception' => get_class($exception),
                'recovery_exception' => $e->getMessage()
            ]);
        }

        return false;
    }

    /**
     * Get severity name
     */
    private function getSeverityName(int $severity): string
    {
        return match ($severity) {
            E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR => 'ERROR',
            E_WARNING, E_CORE_WARNING, E_COMPILE_WARNING, E_USER_WARNING => 'WARNING',
            E_NOTICE, E_USER_NOTICE => 'NOTICE',
            E_STRICT => 'STRICT',
            E_DEPRECATED, E_USER_DEPRECATED => 'DEPRECATED',
            E_PARSE => 'PARSE',
            default => 'UNKNOWN'
        };
    }

    /**
     * Check if error is critical
     */
    private function isCriticalError(int $severity): bool
    {
        return in_array($severity, [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR, E_PARSE]);
    }

    /**
     * Check if exception is critical
     */
    private function isCriticalException(\Throwable $exception): bool
    {
        return $exception instanceof \Error ||
               $exception instanceof \ParseError ||
               $exception instanceof \TypeError ||
               ($exception instanceof WeBotException && $exception->getCode() >= 5000);
    }

    /**
     * Generate unique request ID
     */
    private function generateRequestId(): string
    {
        return uniqid('req_', true);
    }

    /**
     * Get client IP address
     */
    private function getClientIp(): string
    {
        $headers = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP'];

        foreach ($headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ips = explode(',', $_SERVER[$header]);
                return trim($ips[0]);
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'display_errors' => !$this->isProduction,
            'log_errors' => true,
            'max_error_rate' => 100, // errors per minute
            'critical_error_notification' => true,
            'recovery_enabled' => true,
            'error_page_template' => null,
            'user_friendly_messages' => [
                'database' => 'خطا در اتصال به پایگاه داده. لطفاً بعداً تلاش کنید.',
                'api' => 'خطا در ارتباط با سرویس. لطفاً بعداً تلاش کنید.',
                'file' => 'خطا در پردازش فایل. لطفاً مجدداً تلاش کنید.',
                'memory' => 'خطای حافظه سیستم. لطفاً بعداً تلاش کنید.',
                'general' => 'خطایی رخ داده است. لطفاً بعداً تلاش کنید.'
            ]
        ];
    }

    /**
     * Initialize recovery strategies
     */
    private function initializeRecoveryStrategies(): void
    {
        $this->recoveryStrategies = [
            'database' => [
                'retry_connection',
                'use_backup_database',
                'enable_read_only_mode'
            ],
            'api' => [
                'retry_request',
                'use_cache',
                'use_fallback_service'
            ],
            'file' => [
                'retry_operation',
                'create_directory',
                'use_temp_location'
            ],
            'memory' => [
                'garbage_collection',
                'increase_memory_limit',
                'reduce_memory_usage'
            ]
        ];
    }

    /**
     * Update error statistics
     */
    private function updateErrorStats(array $errorData): void
    {
        $minute = date('Y-m-d H:i');

        if (!isset($this->errorStats[$minute])) {
            $this->errorStats[$minute] = 0;
        }

        $this->errorStats[$minute]++;

        // Clean old statistics (keep only last hour)
        $cutoff = date('Y-m-d H:i', strtotime('-1 hour'));
        foreach ($this->errorStats as $time => $count) {
            if ($time < $cutoff) {
                unset($this->errorStats[$time]);
            }
        }
    }

    /**
     * Log error
     */
    private function logError(array $errorData): void
    {
        $level = $this->isCriticalError($errorData['severity']) ? 'error' : 'warning';

        $this->logger->log($level, "PHP {$errorData['severity_name']}: {$errorData['message']}", [
            'file' => $errorData['file'],
            'line' => $errorData['line'],
            'memory_usage' => $errorData['memory_usage'],
            'request_id' => $errorData['request_id'],
            'ip_address' => $errorData['ip_address'],
            'url' => $errorData['url']
        ]);
    }

    /**
     * Log exception
     */
    private function logException(\Throwable $exception, array $errorData): void
    {
        $level = $this->isCriticalException($exception) ? 'critical' : 'error';

        $this->logger->log($level, "Exception: {$exception->getMessage()}", [
            'exception_class' => get_class($exception),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'code' => $exception->getCode(),
            'trace' => $exception->getTraceAsString(),
            'memory_usage' => $errorData['memory_usage'],
            'request_id' => $errorData['request_id'],
            'ip_address' => $errorData['ip_address'],
            'url' => $errorData['url']
        ]);
    }

    /**
     * Handle critical error
     */
    private function handleCriticalError(array $errorData): void
    {
        // Send immediate notification
        if ($this->config['critical_error_notification']) {
            $this->sendCriticalErrorNotification($errorData);
        }

        // Log to emergency log
        $this->logger->emergency('Critical PHP Error', $errorData);
    }

    /**
     * Handle critical exception
     */
    private function handleCriticalException(\Throwable $exception, array $errorData): void
    {
        // Send immediate notification
        if ($this->config['critical_error_notification']) {
            $this->sendCriticalExceptionNotification($exception, $errorData);
        }

        // Log to emergency log
        $this->logger->emergency('Critical Exception', $errorData);
    }

    /**
     * Display error (development mode)
     */
    private function displayError(array $errorData): void
    {
        if (php_sapi_name() === 'cli') {
            echo "\n[{$errorData['severity_name']}] {$errorData['message']}\n";
            echo "File: {$errorData['file']} on line {$errorData['line']}\n";
        } else {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; margin: 10px; border: 1px solid #f5c6cb; border-radius: 4px;'>";
            echo "<strong>[{$errorData['severity_name']}]</strong> {$errorData['message']}<br>";
            echo "<small>File: {$errorData['file']} on line {$errorData['line']}</small>";
            echo "</div>";
        }
    }

    /**
     * Send error response
     */
    private function sendErrorResponse(\Throwable $exception, array $errorData): void
    {
        if (headers_sent()) {
            return;
        }

        $statusCode = $this->getHttpStatusCode($exception);
        http_response_code($statusCode);

        if (php_sapi_name() === 'cli') {
            echo "Error: " . ($this->isProduction ? 'An error occurred' : $exception->getMessage()) . "\n";
            exit(1);
        }

        $response = [
            'error' => true,
            'message' => $this->getUserFriendlyMessage($exception),
            'request_id' => $errorData['request_id']
        ];

        if (!$this->isProduction) {
            $response['debug'] = [
                'exception' => get_class($exception),
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine()
            ];
        }

        echo json_encode($response, JSON_UNESCAPED_UNICODE);
    }

    /**
     * Get HTTP status code for exception
     */
    private function getHttpStatusCode(\Throwable $exception): int
    {
        if ($exception instanceof WeBotException) {
            $code = $exception->getCode();
            if ($code >= 400 && $code < 600) {
                return $code;
            }
        }

        return 500;
    }

    /**
     * Get user-friendly error message
     */
    private function getUserFriendlyMessage(\Throwable $exception): string
    {
        if ($exception instanceof WeBotException && $exception->getUserMessage()) {
            return $exception->getUserMessage();
        }

        // Determine error category
        $message = strtolower($exception->getMessage());

        if (strpos($message, 'database') !== false || strpos($message, 'connection') !== false) {
            return $this->config['user_friendly_messages']['database'];
        } elseif (strpos($message, 'api') !== false || strpos($message, 'service') !== false) {
            return $this->config['user_friendly_messages']['api'];
        } elseif (strpos($message, 'file') !== false || strpos($message, 'directory') !== false) {
            return $this->config['user_friendly_messages']['file'];
        } elseif (strpos($message, 'memory') !== false) {
            return $this->config['user_friendly_messages']['memory'];
        }

        return $this->config['user_friendly_messages']['general'];
    }

    /**
     * Send critical error notification
     */
    private function sendCriticalErrorNotification(array $errorData): void
    {
        // Placeholder for notification system
        // Could integrate with Slack, email, SMS, etc.
    }

    /**
     * Send critical exception notification
     */
    private function sendCriticalExceptionNotification(\Throwable $exception, array $errorData): void
    {
        // Placeholder for notification system
        // Could integrate with Slack, email, SMS, etc.
    }

    /**
     * Send emergency notification
     */
    private function sendEmergencyNotification(array $errorData): void
    {
        // Placeholder for emergency notification system
        // Could integrate with monitoring services
    }
}
