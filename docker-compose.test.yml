# WeBot v2.0 Testing Environment Docker Compose Configuration
version: '3.8'

services:
  # WeBot Test Application
  webot-test:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: webot_test_app
    restart: "no"
    ports:
      - "8080:80"
    environment:
      - APP_ENV=testing
      - APP_DEBUG=true
      - DB_HOST=mysql-test
      - DB_PORT=3306
      - DB_DATABASE=webot_test
      - DB_USERNAME=root
      - DB_PASSWORD=test_password
      - REDIS_HOST=redis-test
      - REDIS_PORT=6379
      - TELEGRAM_BOT_TOKEN=test_token_123456789
      - WEBHOOK_URL=http://localhost:8080/webhook
      - DISABLE_EXTERNAL_APIS=true
      - MOCK_TELEGRAM_API=true
      - MOCK_PANEL_API=true
      - MOCK_PAYMENT_GATEWAYS=true
    volumes:
      - .:/var/www/html
      - ./storage/logs:/var/www/html/storage/logs
      - ./tests:/var/www/html/tests
      - ./vendor:/var/www/html/vendor
    depends_on:
      mysql-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    networks:
      - webot_test_network
    working_dir: /var/www/html
    command: >
      sh -c "
        composer install --no-interaction --prefer-dist &&
        php scripts/run-migrations.php --env=testing &&
        php -S 0.0.0.0:80 -t public
      "

  # MySQL Test Database
  mysql-test:
    image: mysql:8.0
    container_name: webot_mysql_test
    restart: "no"
    environment:
      MYSQL_ROOT_PASSWORD: test_password
      MYSQL_DATABASE: webot_test
      MYSQL_USER: webot_test
      MYSQL_PASSWORD: test_password
    volumes:
      - mysql_test_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
      - ./migrations:/docker-entrypoint-initdb.d/migrations
    ports:
      - "3307:3306"
    networks:
      - webot_test_network
    command: --default-authentication-plugin=mysql_native_password --innodb-flush-log-at-trx-commit=2 --innodb-flush-method=O_DIRECT
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-ptest_password"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis Test Cache
  redis-test:
    image: redis:7-alpine
    container_name: webot_redis_test
    restart: "no"
    ports:
      - "6380:6379"
    volumes:
      - redis_test_data:/data
    networks:
      - webot_test_network
    command: redis-server --appendonly yes --maxmemory 128mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # PHPUnit Test Runner
  phpunit:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: webot_phpunit
    restart: "no"
    environment:
      - APP_ENV=testing
      - APP_DEBUG=true
      - DB_HOST=mysql-test
      - DB_PORT=3306
      - DB_DATABASE=webot_test
      - DB_USERNAME=root
      - DB_PASSWORD=test_password
      - REDIS_HOST=redis-test
      - REDIS_PORT=6379
      - XDEBUG_MODE=coverage
    volumes:
      - .:/var/www/html
      - ./coverage:/var/www/html/coverage
      - ./reports:/var/www/html/reports
    depends_on:
      mysql-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    networks:
      - webot_test_network
    working_dir: /var/www/html
    profiles:
      - testing

  # Code Quality Tools
  code-quality:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: webot_code_quality
    restart: "no"
    volumes:
      - .:/var/www/html
      - ./reports:/var/www/html/reports
    working_dir: /var/www/html
    profiles:
      - quality

# Test-specific volumes
volumes:
  mysql_test_data:
    driver: local
  redis_test_data:
    driver: local

# Test network
networks:
  webot_test_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
