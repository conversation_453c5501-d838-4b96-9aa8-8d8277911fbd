<?php

declare(strict_types=1);

namespace WeBot\Core\Cache\Managers;

use WeBot\Core\Cache\Contracts\CacheInterface;

/**
 * Cache Tag Manager
 *
 * Manages cache tags for grouped cache invalidation.
 *
 * @package WeBot\Core\Cache\Managers
 * @version 2.0
 */
class CacheTagManager
{
    private CacheInterface $cache;
    private array $currentTags = [];
    private string $tagPrefix = 'tag:';

    public function __construct(CacheInterface $cache)
    {
        $this->cache = $cache;
    }

    /**
     * Set current tags for subsequent operations
     */
    public function tags(array $tags): self
    {
        $this->currentTags = $tags;
        return $this;
    }

    /**
     * Set value with tags
     */
    public function set(string $key, $value, ?int $ttl = null): bool
    {
        $result = $this->cache->set($key, $value, $ttl);

        if ($result && !empty($this->currentTags)) {
            $this->associateKeyWithTags($key, $this->currentTags);
        }

        return $result;
    }

    /**
     * Get value (tags don't affect retrieval)
     */
    public function get(string $key, $default = null)
    {
        return $this->cache->get($key, $default);
    }

    /**
     * Remember with tags
     */
    public function remember(string $key, callable $callback, ?int $ttl = null)
    {
        $value = $this->cache->get($key);

        if ($value !== null) {
            return $value;
        }

        $value = $callback();
        $this->set($key, $value, $ttl);

        return $value;
    }

    /**
     * Flush all keys associated with given tags
     */
    public function flush(array $tags): bool
    {
        $success = true;

        foreach ($tags as $tag) {
            $keys = $this->getKeysForTag($tag);

            foreach ($keys as $key) {
                if (!$this->cache->delete($key)) {
                    $success = false;
                }
            }

            // Remove the tag itself
            $this->cache->delete($this->getTagKey($tag));
        }

        return $success;
    }

    /**
     * Get all keys associated with a tag
     */
    public function getKeysForTag(string $tag): array
    {
        $tagKey = $this->getTagKey($tag);
        $keys = $this->cache->get($tagKey, []);

        return is_array($keys) ? $keys : [];
    }

    /**
     * Associate a key with tags
     */
    private function associateKeyWithTags(string $key, array $tags): void
    {
        foreach ($tags as $tag) {
            $tagKey = $this->getTagKey($tag);
            $keys = $this->getKeysForTag($tag);

            if (!in_array($key, $keys)) {
                $keys[] = $key;
                $this->cache->set($tagKey, $keys);
            }
        }
    }

    /**
     * Get tag key
     */
    private function getTagKey(string $tag): string
    {
        return $this->tagPrefix . $tag;
    }

    /**
     * Clear current tags
     */
    public function clearTags(): self
    {
        $this->currentTags = [];
        return $this;
    }

    /**
     * Get current tags
     */
    public function getCurrentTags(): array
    {
        return $this->currentTags;
    }

    /**
     * Check if key is associated with tag
     */
    public function hasTag(string $key, string $tag): bool
    {
        $keys = $this->getKeysForTag($tag);
        return in_array($key, $keys);
    }

    /**
     * Remove key from tag
     */
    public function removeFromTag(string $key, string $tag): bool
    {
        $tagKey = $this->getTagKey($tag);
        $keys = $this->getKeysForTag($tag);

        $index = array_search($key, $keys);
        if ($index !== false) {
            unset($keys[$index]);
            return $this->cache->set($tagKey, array_values($keys));
        }

        return true;
    }

    /**
     * Get all tags
     */
    public function getAllTags(): array
    {
        // This would require scanning all keys with tag prefix
        // Implementation depends on cache driver capabilities
        return [];
    }
}
