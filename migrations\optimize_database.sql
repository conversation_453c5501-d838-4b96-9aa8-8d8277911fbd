-- WeBot Database Optimization Migration
-- Creates optimized indexes and improves database performance

-- Enable performance monitoring
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 1;
SET GLOBAL log_queries_not_using_indexes = 'ON';

-- Users table optimizations
ALTER TABLE users 
ADD INDEX idx_users_userid (userid),
ADD INDEX idx_users_step (step),
ADD INDEX idx_users_admin (isAdmin),
ADD INDEX idx_users_banned (banned),
ADD INDEX idx_users_created (created_at),
ADD INDEX idx_users_wallet (wallet),
ADD INDEX idx_users_active (isAdmin, banned);

-- Payments table optimizations
ALTER TABLE payments 
ADD INDEX idx_payments_user (user_id),
ADD INDEX idx_payments_status (status),
ADD INDEX idx_payments_gateway (gateway),
ADD INDEX idx_payments_created (created_at),
ADD INDEX idx_payments_amount (amount),
ADD INDEX idx_payments_user_status (user_id, status),
ADD INDEX idx_payments_gateway_status (gateway, status),
ADD INDEX idx_payments_date_range (created_at, status);

-- Services table optimizations
ALTER TABLE services 
ADD INDEX idx_services_user (user_id),
ADD INDEX idx_services_server (server_id),
ADD INDEX idx_services_status (status),
ADD INDEX idx_services_expires (expires_at),
ADD INDEX idx_services_user_status (user_id, status),
ADD INDEX idx_services_server_status (server_id, status),
ADD INDEX idx_services_active (status, expires_at),
ADD INDEX idx_services_usage (used_volume, volume);

-- Server info table optimizations
ALTER TABLE server_info 
ADD INDEX idx_server_status (status),
ADD INDEX idx_server_panel_type (panel_type),
ADD INDEX idx_server_active (status, panel_type);

-- Plans table optimizations (if exists)
CREATE TABLE IF NOT EXISTS plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    volume BIGINT NOT NULL,
    days INT NOT NULL,
    price INT NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_plans_status (status),
    INDEX idx_plans_price (price),
    INDEX idx_plans_volume (volume)
);

-- Referrals table optimizations (if exists)
CREATE TABLE IF NOT EXISTS referrals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    referrer_id BIGINT NOT NULL,
    referred_id BIGINT NOT NULL,
    commission_amount INT DEFAULT 0,
    status ENUM('pending', 'paid', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_referrals_referrer (referrer_id),
    INDEX idx_referrals_referred (referred_id),
    INDEX idx_referrals_status (status),
    INDEX idx_referrals_commission (commission_amount),
    UNIQUE KEY unique_referral (referrer_id, referred_id)
);

-- Logs table for audit trail
CREATE TABLE IF NOT EXISTS audit_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50),
    entity_id BIGINT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_logs_user (user_id),
    INDEX idx_logs_action (action),
    INDEX idx_logs_entity (entity_type, entity_id),
    INDEX idx_logs_created (created_at),
    INDEX idx_logs_user_action (user_id, action)
);

-- Sessions table for better session management
CREATE TABLE IF NOT EXISTS user_sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id BIGINT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    payload TEXT,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_sessions_user (user_id),
    INDEX idx_sessions_activity (last_activity),
    INDEX idx_sessions_ip (ip_address)
);

-- Cache table for application caching
CREATE TABLE IF NOT EXISTS cache_entries (
    cache_key VARCHAR(255) PRIMARY KEY,
    cache_value LONGTEXT,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_cache_expires (expires_at)
);

-- Statistics table for analytics
CREATE TABLE IF NOT EXISTS statistics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,2),
    dimensions JSON,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_stats_metric (metric_name),
    INDEX idx_stats_recorded (recorded_at),
    INDEX idx_stats_metric_date (metric_name, recorded_at)
);

-- Optimize existing tables
OPTIMIZE TABLE users;
OPTIMIZE TABLE payments;
OPTIMIZE TABLE services;
OPTIMIZE TABLE server_info;

-- Update table statistics
ANALYZE TABLE users;
ANALYZE TABLE payments;
ANALYZE TABLE services;
ANALYZE TABLE server_info;

-- Create views for common queries
CREATE OR REPLACE VIEW active_users AS
SELECT userid, first_name, last_name, username, wallet, created_at
FROM users 
WHERE banned = 0;

CREATE OR REPLACE VIEW successful_payments AS
SELECT p.*, u.first_name, u.username
FROM payments p
JOIN users u ON p.user_id = u.userid
WHERE p.status = 'completed';

CREATE OR REPLACE VIEW active_services AS
SELECT s.*, u.first_name, u.username, si.name as server_name
FROM services s
JOIN users u ON s.user_id = u.userid
JOIN server_info si ON s.server_id = si.id
WHERE s.status = 'active' AND s.expires_at > NOW();

CREATE OR REPLACE VIEW user_statistics AS
SELECT 
    u.userid,
    u.first_name,
    u.username,
    u.wallet,
    COUNT(DISTINCT p.id) as total_payments,
    COALESCE(SUM(CASE WHEN p.status = 'completed' THEN p.amount ELSE 0 END), 0) as total_paid,
    COUNT(DISTINCT s.id) as total_services,
    COUNT(DISTINCT CASE WHEN s.status = 'active' THEN s.id END) as active_services
FROM users u
LEFT JOIN payments p ON u.userid = p.user_id
LEFT JOIN services s ON u.userid = s.user_id
GROUP BY u.userid;

-- Create stored procedures for common operations
DELIMITER //

CREATE PROCEDURE GetUserDashboard(IN user_id BIGINT)
BEGIN
    -- User info
    SELECT userid, first_name, last_name, username, wallet, created_at
    FROM users WHERE userid = user_id;
    
    -- Active services
    SELECT s.*, si.name as server_name
    FROM services s
    JOIN server_info si ON s.server_id = si.id
    WHERE s.user_id = user_id AND s.status = 'active';
    
    -- Recent payments
    SELECT * FROM payments 
    WHERE user_id = user_id 
    ORDER BY created_at DESC 
    LIMIT 10;
END //

CREATE PROCEDURE GetServerStatistics(IN server_id INT)
BEGIN
    SELECT 
        si.name,
        si.url,
        si.panel_type,
        COUNT(s.id) as total_services,
        COUNT(CASE WHEN s.status = 'active' THEN 1 END) as active_services,
        SUM(s.volume) as total_volume,
        SUM(s.used_volume) as used_volume
    FROM server_info si
    LEFT JOIN services s ON si.id = s.server_id
    WHERE si.id = server_id
    GROUP BY si.id;
END //

CREATE PROCEDURE CleanupExpiredSessions()
BEGIN
    DELETE FROM user_sessions 
    WHERE last_activity < DATE_SUB(NOW(), INTERVAL 24 HOUR);
    
    DELETE FROM cache_entries 
    WHERE expires_at < NOW();
END //

DELIMITER ;

-- Create events for automatic maintenance
CREATE EVENT IF NOT EXISTS cleanup_sessions
ON SCHEDULE EVERY 1 HOUR
DO CALL CleanupExpiredSessions();

CREATE EVENT IF NOT EXISTS optimize_tables_weekly
ON SCHEDULE EVERY 1 WEEK
STARTS TIMESTAMP(CURDATE() + INTERVAL 1 DAY, '02:00:00')
DO BEGIN
    OPTIMIZE TABLE users, payments, services, server_info;
    ANALYZE TABLE users, payments, services, server_info;
END;

-- Enable events
SET GLOBAL event_scheduler = ON;

-- Performance monitoring queries
-- These can be used to monitor database performance

-- Query to check index usage
-- SELECT 
--     TABLE_SCHEMA,
--     TABLE_NAME,
--     INDEX_NAME,
--     CARDINALITY,
--     NULLABLE
-- FROM information_schema.STATISTICS 
-- WHERE TABLE_SCHEMA = DATABASE()
-- ORDER BY TABLE_NAME, INDEX_NAME;

-- Query to check table sizes
-- SELECT 
--     TABLE_NAME,
--     ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Size (MB)',
--     TABLE_ROWS
-- FROM information_schema.TABLES 
-- WHERE TABLE_SCHEMA = DATABASE()
-- ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC;

-- Query to check slow queries
-- SELECT 
--     query_time,
--     lock_time,
--     rows_sent,
--     rows_examined,
--     sql_text
-- FROM mysql.slow_log 
-- ORDER BY query_time DESC 
-- LIMIT 10;
