<?php

declare(strict_types=1);

namespace WeBot\Services;

use WeBot\Utils\Logger;
use WeBot\Exceptions\WeBotException;

/**
 * Message Template Engine
 *
 * Advanced template engine for message rendering with support for
 * conditionals, loops, filters, and multi-language templates.
 *
 * @package WeBot\Services
 * @version 2.0
 */
class MessageTemplateEngine
{
    private Logger $logger;
    private array $config;
    private array $filters = [];
    private array $globals = [];
    private string $templatePath;

    public function __construct(array $config = [])
    {
        $this->logger = Logger::getInstance();
        $this->config = array_merge($this->getDefaultConfig(), $config);
        $this->templatePath = $this->config['templates_path'];

        $this->initializeFilters();
        $this->initializeGlobals();
    }

    /**
     * Render template with variables
     */
    public function render(string $template, array $variables = [], string $language = 'fa'): string
    {
        try {
            // Load template content
            $content = $this->loadTemplate($template, $language);

            // Merge with global variables
            $allVariables = array_merge($this->globals, $variables);

            // Process template
            $rendered = $this->processTemplate($content, $allVariables);

            $this->logger->debug('Template rendered successfully', [
                'template' => $template,
                'language' => $language,
                'variables_count' => count($allVariables)
            ]);

            return $rendered;
        } catch (\Exception $e) {
            $this->logger->error('Template rendering failed', [
                'template' => $template,
                'language' => $language,
                'error' => $e->getMessage()
            ]);

            throw new WeBotException(
                "Template rendering failed: {$e->getMessage()}",
                500,
                $e,
                ['template' => $template, 'language' => $language],
                'خطا در پردازش قالب پیام.'
            );
        }
    }

    /**
     * Render inline template
     */
    public function renderInline(string $content, array $variables = []): string
    {
        $allVariables = array_merge($this->globals, $variables);
        return $this->processTemplate($content, $allVariables);
    }

    /**
     * Check if template exists
     */
    public function templateExists(string $template, string $language = 'fa'): bool
    {
        $path = $this->getTemplatePath($template, $language);
        return file_exists($path);
    }

    /**
     * Get available templates
     */
    public function getAvailableTemplates(string $language = 'fa'): array
    {
        $languagePath = $this->templatePath . '/' . $language;

        if (!is_dir($languagePath)) {
            return [];
        }

        $templates = [];
        $files = glob($languagePath . '/*.php');

        foreach ($files as $file) {
            $templates[] = basename($file, '.php');
        }

        return $templates;
    }

    /**
     * Add custom filter
     */
    public function addFilter(string $name, callable $filter): void
    {
        $this->filters[$name] = $filter;
    }

    /**
     * Add global variable
     */
    public function addGlobal(string $name, $value): void
    {
        $this->globals[$name] = $value;
    }

    /**
     * Load template content
     */
    private function loadTemplate(string $template, string $language): string
    {
        $path = $this->getTemplatePath($template, $language);

        // Try requested language first
        if (file_exists($path)) {
            return $this->loadTemplateFile($path);
        }

        // Fallback to default language
        if ($language !== $this->config['default_language']) {
            $fallbackPath = $this->getTemplatePath($template, $this->config['default_language']);
            if (file_exists($fallbackPath)) {
                return $this->loadTemplateFile($fallbackPath);
            }
        }

        throw new WeBotException("Template not found: {$template} for language: {$language}");
    }

    /**
     * Load template file
     */
    private function loadTemplateFile(string $path): string
    {
        ob_start();
        include $path;
        $content = ob_get_clean();

        if ($content === false) {
            throw new WeBotException("Failed to load template file: {$path}");
        }

        return $content;
    }

    /**
     * Get template file path
     */
    private function getTemplatePath(string $template, string $language): string
    {
        return $this->templatePath . '/' . $language . '/' . $template . '.php';
    }

    /**
     * Process template with variables
     */
    private function processTemplate(string $content, array $variables): string
    {
        // Replace simple variables: {variable}
        $content = $this->replaceVariables($content, $variables);

        // Process conditionals: {if condition}...{endif}
        $content = $this->processConditionals($content, $variables);

        // Process loops: {foreach items as item}...{endforeach}
        $content = $this->processLoops($content, $variables);

        // Process filters: {variable|filter}
        $content = $this->processFilters($content, $variables);

        // Process includes: {include template}
        $content = $this->processIncludes($content, $variables);

        return $content;
    }

    /**
     * Replace simple variables
     */
    private function replaceVariables(string $content, array $variables): string
    {
        foreach ($variables as $key => $value) {
            if (is_scalar($value) || is_null($value)) {
                $content = str_replace('{' . $key . '}', (string)$value, $content);
            }
        }

        return $content;
    }

    /**
     * Process conditional statements
     */
    private function processConditionals(string $content, array $variables): string
    {
        $pattern = '/\{if\s+([^}]+)\}(.*?)\{endif\}/s';

        return preg_replace_callback($pattern, function ($matches) use ($variables) {
            $condition = trim($matches[1]);
            $innerContent = $matches[2];

            if ($this->evaluateCondition($condition, $variables)) {
                return $this->processTemplate($innerContent, $variables);
            }

            return '';
        }, $content);
    }

    /**
     * Process loop statements
     */
    private function processLoops(string $content, array $variables): string
    {
        $pattern = '/\{foreach\s+(\w+)\s+as\s+(\w+)\}(.*?)\{endforeach\}/s';

        return preg_replace_callback($pattern, function ($matches) use ($variables) {
            $arrayName = $matches[1];
            $itemName = $matches[2];
            $innerContent = $matches[3];

            if (!isset($variables[$arrayName]) || !is_array($variables[$arrayName])) {
                return '';
            }

            $result = '';
            foreach ($variables[$arrayName] as $index => $item) {
                $loopVariables = array_merge($variables, [
                    $itemName => $item,
                    'loop_index' => $index,
                    'loop_first' => $index === 0,
                    'loop_last' => $index === count($variables[$arrayName]) - 1
                ]);

                $result .= $this->processTemplate($innerContent, $loopVariables);
            }

            return $result;
        }, $content);
    }

    /**
     * Process filters
     */
    private function processFilters(string $content, array $variables): string
    {
        $pattern = '/\{(\w+)\|(\w+)(?:\(([^)]*)\))?\}/';

        return preg_replace_callback($pattern, function ($matches) use ($variables) {
            $variableName = $matches[1];
            $filterName = $matches[2];
            $filterArgs = isset($matches[3]) ? explode(',', $matches[3]) : [];

            if (!isset($variables[$variableName])) {
                return '';
            }

            $value = $variables[$variableName];

            if (isset($this->filters[$filterName])) {
                return call_user_func($this->filters[$filterName], $value, ...$filterArgs);
            }

            return (string)$value;
        }, $content);
    }

    /**
     * Process includes
     */
    private function processIncludes(string $content, array $variables): string
    {
        $pattern = '/\{include\s+([^}]+)\}/';

        return preg_replace_callback($pattern, function ($matches) use ($variables) {
            $templateName = trim($matches[1]);

            try {
                return $this->render($templateName, $variables);
            } catch (\Exception $e) {
                $this->logger->warning('Include template failed', [
                    'template' => $templateName,
                    'error' => $e->getMessage()
                ]);
                return '';
            }
        }, $content);
    }

    /**
     * Evaluate condition
     */
    private function evaluateCondition(string $condition, array $variables): bool
    {
        // Simple condition evaluation
        // Supports: variable, !variable, variable == value, variable != value

        $condition = trim($condition);

        // Negation
        if (str_starts_with($condition, '!')) {
            $variable = substr($condition, 1);
            return empty($variables[$variable]);
        }

        // Equality check
        if (str_contains($condition, '==')) {
            [$left, $right] = explode('==', $condition, 2);
            $left = trim($left);
            $right = trim($right, ' "\'');

            return isset($variables[$left]) && $variables[$left] == $right;
        }

        // Inequality check
        if (str_contains($condition, '!=')) {
            [$left, $right] = explode('!=', $condition, 2);
            $left = trim($left);
            $right = trim($right, ' "\'');

            return !isset($variables[$left]) || $variables[$left] != $right;
        }

        // Simple variable check
        return !empty($variables[$condition]);
    }

    /**
     * Initialize built-in filters
     */
    private function initializeFilters(): void
    {
        $this->filters = [
            'upper' => fn($value) => mb_strtoupper((string)$value),
            'lower' => fn($value) => mb_strtolower((string)$value),
            'title' => fn($value) => mb_convert_case((string)$value, MB_CASE_TITLE),
            'length' => fn($value) => is_array($value) ? count($value) : mb_strlen((string)$value),
            'number_format' => fn($value) => number_format((float)$value),
            'date' => fn($value, $format = 'Y-m-d H:i:s') => date($format, is_numeric($value) ? $value : strtotime($value)),
            'truncate' => fn($value, $length = 100) => mb_substr((string)$value, 0, (int)$length) . (mb_strlen((string)$value) > (int)$length ? '...' : ''),
            'nl2br' => fn($value) => nl2br((string)$value),
            'escape' => fn($value) => htmlspecialchars((string)$value, ENT_QUOTES, 'UTF-8'),
            'json' => fn($value) => json_encode($value, JSON_UNESCAPED_UNICODE),
            'default' => fn($value, $default = '') => empty($value) ? $default : $value
        ];
    }

    /**
     * Initialize global variables
     */
    private function initializeGlobals(): void
    {
        $this->globals = [
            'app_name' => 'WeBot',
            'current_date' => date('Y-m-d'),
            'current_time' => date('H:i:s'),
            'current_datetime' => date('Y-m-d H:i:s'),
            'bot_username' => '@WeBot_VPN_Bot'
        ];
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'templates_path' => __DIR__ . '/../../resources/templates',
            'default_language' => 'fa',
            'cache_enabled' => true,
            'cache_ttl' => 3600
        ];
    }
}
