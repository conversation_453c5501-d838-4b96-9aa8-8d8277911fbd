#!/bin/sh
# WeBot Docker Entrypoint Script

set -e

echo "🚀 Starting WeBot v2.0..."

# Wait for database to be ready
echo "⏳ Waiting for database connection..."
until nc -z mysql 3306; do
    echo "Database not ready, waiting..."
    sleep 2
done
echo "✅ Database connection established"

# Wait for Redis to be ready
echo "⏳ Waiting for Redis connection..."
until nc -z redis 6379; do
    echo "Redis not ready, waiting..."
    sleep 2
done
echo "✅ Redis connection established"

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p storage/logs storage/cache storage/sessions storage/uploads
mkdir -p public/assets

# Set proper permissions
echo "🔐 Setting permissions..."
chmod -R 755 storage public
chmod -R 644 storage/logs

# Clear cache if in production
if [ "$APP_ENV" = "production" ]; then
    echo "🧹 Clearing production cache..."
    rm -rf storage/cache/*
fi

# Run database migrations if needed
echo "🗄️ Checking database migrations..."
if [ -f "migrations/migrate.php" ]; then
    php migrations/migrate.php
    echo "✅ Database migrations completed"
fi

# Generate application key if not exists
if [ ! -f "storage/.app_key" ]; then
    echo "🔑 Generating application key..."
    php -r "echo bin2hex(random_bytes(32));" > storage/.app_key
    chmod 600 storage/.app_key
    echo "✅ Application key generated"
fi

# Optimize autoloader for production
if [ "$APP_ENV" = "production" ]; then
    echo "⚡ Optimizing autoloader..."
    composer dump-autoload --optimize --no-dev
    echo "✅ Autoloader optimized"
fi

# Start PHP-FPM in background
echo "🐘 Starting PHP-FPM..."
php-fpm -D

# Start Nginx in background
echo "🌐 Starting Nginx..."
nginx -g "daemon off;" &

# Health check
echo "🏥 Performing health check..."
sleep 5
if curl -f http://localhost/health > /dev/null 2>&1; then
    echo "✅ Health check passed"
else
    echo "❌ Health check failed"
    exit 1
fi

echo "🎉 WeBot v2.0 started successfully!"

# Execute the main command
exec "$@"
