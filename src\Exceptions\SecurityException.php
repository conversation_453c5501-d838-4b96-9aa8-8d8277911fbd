<?php

declare(strict_types=1);

namespace WeBot\Exceptions;

/**
 * Security Exception
 *
 * Exception thrown when security violations or threats are detected
 *
 * @package WeBot\Exceptions
 * @version 2.0
 */
class SecurityException extends WeBotException
{
    /**
     * Security threat type
     */
    private ?string $threatType = null;

    /**
     * IP address involved in the security incident
     */
    private ?string $ipAddress = null;

    /**
     * User agent involved in the security incident
     */
    private ?string $userAgent = null;

    /**
     * Additional security context
     */
    private array $securityContext = [];

    public function __construct(
        string $message = '',
        int $code = 0,
        ?\Throwable $previous = null,
        ?string $threatType = null,
        ?string $ipAddress = null,
        ?string $userAgent = null,
        array $securityContext = []
    ) {
        parent::__construct($message, $code, $previous);

        $this->threatType = $threatType;
        $this->ipAddress = $ipAddress;
        $this->userAgent = $userAgent;
        $this->securityContext = $securityContext;
    }

    /**
     * Get threat type
     */
    public function getThreatType(): ?string
    {
        return $this->threatType;
    }

    /**
     * Get IP address
     */
    public function getIpAddress(): ?string
    {
        return $this->ipAddress;
    }

    /**
     * Get user agent
     */
    public function getUserAgent(): ?string
    {
        return $this->userAgent;
    }

    /**
     * Get security context
     */
    public function getSecurityContext(): array
    {
        return $this->securityContext;
    }

    /**
     * Get detailed security information
     */
    public function getSecurityDetails(): array
    {
        return [
            'message' => $this->getMessage(),
            'code' => $this->getCode(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'threat_type' => $this->threatType,
            'ip_address' => $this->ipAddress,
            'user_agent' => $this->userAgent ? substr($this->userAgent, 0, 100) . '...' : null,
            'security_context' => $this->securityContext,
            'timestamp' => date('Y-m-d H:i:s'),
            'trace' => $this->getTraceAsString()
        ];
    }

    /**
     * Create exception for rate limit exceeded
     */
    public static function rateLimitExceeded(string $ipAddress, int $attempts): self
    {
        return new self(
            "Rate limit exceeded: {$attempts} attempts from IP {$ipAddress}",
            429,
            null,
            'rate_limit',
            $ipAddress,
            null,
            ['attempts' => $attempts]
        );
    }

    /**
     * Create exception for suspicious activity
     */
    public static function suspiciousActivity(string $activity, string $ipAddress, string $userAgent = null): self
    {
        return new self(
            "Suspicious activity detected: {$activity}",
            403,
            null,
            'suspicious_activity',
            $ipAddress,
            $userAgent,
            ['activity' => $activity]
        );
    }

    /**
     * Create exception for blocked IP
     */
    public static function blockedIp(string $ipAddress, string $reason = 'Security policy'): self
    {
        return new self(
            "Access denied from blocked IP: {$ipAddress}",
            403,
            null,
            'blocked_ip',
            $ipAddress,
            null,
            ['reason' => $reason]
        );
    }

    /**
     * Create exception for CSRF token mismatch
     */
    public static function csrfTokenMismatch(string $ipAddress): self
    {
        return new self(
            'CSRF token validation failed',
            403,
            null,
            'csrf_violation',
            $ipAddress
        );
    }

    /**
     * Create exception for SQL injection attempt
     */
    public static function sqlInjectionAttempt(string $query, string $ipAddress): self
    {
        return new self(
            'SQL injection attempt detected',
            403,
            null,
            'sql_injection',
            $ipAddress,
            null,
            ['suspicious_query' => substr($query, 0, 200)]
        );
    }

    /**
     * Create exception for XSS attempt
     */
    public static function xssAttempt(string $input, string $ipAddress): self
    {
        return new self(
            'XSS attempt detected in user input',
            403,
            null,
            'xss_attempt',
            $ipAddress,
            null,
            ['suspicious_input' => substr($input, 0, 200)]
        );
    }

    /**
     * Create exception for brute force attack
     */
    public static function bruteForceAttempt(string $target, string $ipAddress, int $attempts): self
    {
        return new self(
            "Brute force attack detected on {$target}",
            403,
            null,
            'brute_force',
            $ipAddress,
            null,
            ['target' => $target, 'attempts' => $attempts]
        );
    }

    /**
     * Create exception for invalid file upload
     */
    public static function invalidFileUpload(string $filename, string $reason, string $ipAddress): self
    {
        return new self(
            "Invalid file upload: {$filename} - {$reason}",
            403,
            null,
            'invalid_upload',
            $ipAddress,
            null,
            ['filename' => $filename, 'reason' => $reason]
        );
    }

    /**
     * Create exception for directory traversal attempt
     */
    public static function directoryTraversalAttempt(string $path, string $ipAddress): self
    {
        return new self(
            'Directory traversal attempt detected',
            403,
            null,
            'directory_traversal',
            $ipAddress,
            null,
            ['attempted_path' => $path]
        );
    }

    /**
     * Create exception for unauthorized access
     */
    public static function unauthorizedAccess(string $resource, string $ipAddress, ?int $userId = null): self
    {
        return new self(
            "Unauthorized access attempt to: {$resource}",
            401,
            null,
            'unauthorized_access',
            $ipAddress,
            null,
            ['resource' => $resource, 'user_id' => $userId]
        );
    }
}
