-- WeBot Database Migration: Create Sessions Table
-- Version: 1.0
-- Date: 2025-01-07

-- Create sessions table for user session management
CREATE TABLE IF NOT EXISTS sessions (
    id VARCHAR(255) PRIMARY KEY,
    user_id BIGINT UNSIGNED NULL,
    telegram_id BIGINT UNSIGNED NULL,
    
    -- Session data
    payload LONGTEXT NOT NULL,
    last_activity INT UNSIGNED NOT NULL,
    
    -- Session metadata
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    
    -- Bot conversation state
    conversation_step VARCHAR(100) NULL,
    conversation_data JSON NULL,
    language VARCHAR(10) DEFAULT 'fa',
    
    -- Temporary data storage
    temp_data JSON NULL,
    form_data JSON NULL,
    
    -- Session settings
    expires_at TIMESTAMP NULL,
    is_persistent BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_user_id (user_id),
    INDEX idx_telegram_id (telegram_id),
    INDEX idx_last_activity (last_activity),
    INDEX idx_conversation_step (conversation_step),
    INDEX idx_expires_at (expires_at),
    
    -- Foreign key constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create telegram_updates table for webhook processing
CREATE TABLE IF NOT EXISTS telegram_updates (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    update_id BIGINT UNSIGNED NOT NULL UNIQUE,
    
    -- Update data
    update_type ENUM('message', 'callback_query', 'inline_query', 'chosen_inline_result', 'edited_message', 'channel_post', 'edited_channel_post') NOT NULL,
    raw_data JSON NOT NULL,
    
    -- Processing status
    status ENUM('pending', 'processing', 'completed', 'failed', 'ignored') DEFAULT 'pending',
    processed_at TIMESTAMP NULL,
    processing_time_ms INT UNSIGNED NULL,
    
    -- Error handling
    error_message TEXT NULL,
    retry_count INT UNSIGNED DEFAULT 0,
    max_retries INT UNSIGNED DEFAULT 3,
    
    -- User info
    user_id BIGINT UNSIGNED NULL,
    telegram_id BIGINT UNSIGNED NULL,
    chat_id BIGINT NULL,
    
    -- Message info
    message_id BIGINT NULL,
    message_text TEXT NULL,
    command VARCHAR(100) NULL,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_update_id (update_id),
    INDEX idx_update_type (update_type),
    INDEX idx_status (status),
    INDEX idx_telegram_id (telegram_id),
    INDEX idx_chat_id (chat_id),
    INDEX idx_command (command),
    INDEX idx_created_at (created_at),
    
    -- Foreign key constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create bot_commands table for command tracking
CREATE TABLE IF NOT EXISTS bot_commands (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    
    -- Command info
    command VARCHAR(100) NOT NULL,
    description TEXT NULL,
    category VARCHAR(50) NULL,
    
    -- Access control
    required_role ENUM('user', 'admin', 'moderator') DEFAULT 'user',
    is_enabled BOOLEAN DEFAULT TRUE,
    
    -- Usage statistics
    usage_count BIGINT UNSIGNED DEFAULT 0,
    last_used_at TIMESTAMP NULL,
    
    -- Command configuration
    response_type ENUM('text', 'keyboard', 'inline', 'file', 'photo') DEFAULT 'text',
    response_data JSON NULL,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    UNIQUE KEY unique_command (command),
    INDEX idx_category (category),
    INDEX idx_required_role (required_role),
    INDEX idx_is_enabled (is_enabled),
    INDEX idx_usage_count (usage_count)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create user_activities table for activity logging
CREATE TABLE IF NOT EXISTS user_activities (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    
    -- Activity details
    activity_type VARCHAR(50) NOT NULL,
    activity_description TEXT NULL,
    
    -- Context
    context VARCHAR(100) NULL,
    entity_type VARCHAR(50) NULL,
    entity_id BIGINT UNSIGNED NULL,
    
    -- Request details
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    
    -- Additional data
    metadata JSON NULL,
    
    -- Timestamp
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_user_id (user_id),
    INDEX idx_activity_type (activity_type),
    INDEX idx_context (context),
    INDEX idx_entity_type_id (entity_type, entity_id),
    INDEX idx_created_at (created_at),
    
    -- Foreign key constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create system_logs table for application logging
CREATE TABLE IF NOT EXISTS system_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    
    -- Log details
    level ENUM('emergency', 'alert', 'critical', 'error', 'warning', 'notice', 'info', 'debug') NOT NULL,
    message TEXT NOT NULL,
    context JSON NULL,
    
    -- Source information
    channel VARCHAR(50) NULL,
    logger VARCHAR(100) NULL,
    
    -- Request context
    request_id VARCHAR(36) NULL,
    user_id BIGINT UNSIGNED NULL,
    ip_address VARCHAR(45) NULL,
    
    -- Exception details
    exception_class VARCHAR(255) NULL,
    exception_file VARCHAR(500) NULL,
    exception_line INT UNSIGNED NULL,
    stack_trace LONGTEXT NULL,
    
    -- Timestamp
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_level (level),
    INDEX idx_channel (channel),
    INDEX idx_request_id (request_id),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    
    -- Foreign key constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default bot commands
INSERT IGNORE INTO bot_commands (command, description, category, required_role, response_type) VALUES
('/start', 'شروع کار با ربات', 'basic', 'user', 'keyboard'),
('/help', 'راهنمای استفاده از ربات', 'basic', 'user', 'text'),
('/menu', 'منوی اصلی', 'basic', 'user', 'keyboard'),
('/profile', 'مشاهده پروفایل کاربری', 'user', 'user', 'text'),
('/services', 'مشاهده سرویس‌های من', 'user', 'user', 'keyboard'),
('/buy', 'خرید سرویس جدید', 'user', 'user', 'keyboard'),
('/balance', 'مشاهده موجودی', 'user', 'user', 'text'),
('/support', 'پشتیبانی', 'user', 'user', 'text'),
('/admin', 'پنل مدیریت', 'admin', 'admin', 'keyboard'),
('/stats', 'آمار سیستم', 'admin', 'admin', 'text');

-- Create cleanup procedure for old sessions
DELIMITER $$
CREATE PROCEDURE CleanupExpiredSessions()
BEGIN
    -- Delete expired sessions
    DELETE FROM sessions 
    WHERE expires_at IS NOT NULL 
    AND expires_at < CURRENT_TIMESTAMP;
    
    -- Delete old telegram updates (keep last 7 days)
    DELETE FROM telegram_updates 
    WHERE created_at < DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 7 DAY)
    AND status IN ('completed', 'ignored');
    
    -- Delete old user activities (keep last 30 days)
    DELETE FROM user_activities 
    WHERE created_at < DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 30 DAY);
    
    -- Delete old system logs (keep last 30 days for info/debug, 90 days for errors)
    DELETE FROM system_logs 
    WHERE (level IN ('info', 'debug', 'notice') AND created_at < DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 30 DAY))
    OR (level IN ('warning', 'error', 'critical', 'alert', 'emergency') AND created_at < DATE_SUB(CURRENT_TIMESTAMP, INTERVAL 90 DAY));
END$$
DELIMITER ;
