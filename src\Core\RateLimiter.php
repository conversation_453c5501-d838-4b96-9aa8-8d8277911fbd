<?php

declare(strict_types=1);

namespace WeBot\Core;

use WeBot\Core\Cache\Contracts\CacheInterface;

/**
 * Rate Limiter
 *
 * Advanced rate limiting with multiple algorithms,
 * DDoS protection, and intelligent blocking.
 *
 * @package WeBot\Core
 * @version 2.0
 */
class RateLimiter
{
    private CacheInterface $cache;
    private array $config;
    private array $algorithms;

    public function __construct(CacheInterface $cache, array $config = [])
    {
        $this->cache = $cache;
        $this->config = array_merge([
            'default_algorithm' => 'sliding_window',
            'default_limit' => 60,
            'default_window' => 60,
            'burst_limit' => 10,
            'burst_window' => 1,
            'ban_threshold' => 5,
            'ban_duration' => 3600,
            'whitelist' => [],
            'blacklist' => [],
            'enabled' => true
        ], $config);

        $this->initializeAlgorithms();
    }

    /**
     * Check if request is allowed
     */
    public function isAllowed(string $key, ?array $options = null): bool
    {
        if (!$this->config['enabled']) {
            return true;
        }

        $options = $options ?? [];
        $algorithm = $options['algorithm'] ?? $this->config['default_algorithm'];
        $limit = $options['limit'] ?? $this->config['default_limit'];
        $window = $options['window'] ?? $this->config['default_window'];

        // Check whitelist/blacklist
        if ($this->isWhitelisted($key)) {
            return true;
        }

        if ($this->isBlacklisted($key) || $this->isBanned($key)) {
            return false;
        }

        // Apply rate limiting algorithm
        $allowed = $this->algorithms[$algorithm]($key, $limit, $window, $options);

        // Track violations for potential banning
        if (!$allowed) {
            $this->trackViolation($key);
        }

        return $allowed;
    }

    /**
     * Record a hit
     */
    public function hit(string $key, ?array $options = null): array
    {
        $options = $options ?? [];
        $algorithm = $options['algorithm'] ?? $this->config['default_algorithm'];
        $limit = $options['limit'] ?? $this->config['default_limit'];
        $window = $options['window'] ?? $this->config['default_window'];

        $cacheKey = $this->makeCacheKey($key, $algorithm);
        $now = time();

        switch ($algorithm) {
            case 'token_bucket':
                return $this->hitTokenBucket($cacheKey, $limit, $window, $now);

            case 'sliding_window':
                return $this->hitSlidingWindow($cacheKey, $limit, $window, $now);

            case 'fixed_window':
                return $this->hitFixedWindow($cacheKey, $limit, $window, $now);

            default:
                return $this->hitSlidingWindow($cacheKey, $limit, $window, $now);
        }
    }

    /**
     * Get remaining requests
     */
    public function remaining(string $key, ?array $options = null): int
    {
        $options = $options ?? [];
        $algorithm = $options['algorithm'] ?? $this->config['default_algorithm'];
        $limit = $options['limit'] ?? $this->config['default_limit'];
        $window = $options['window'] ?? $this->config['default_window'];

        $cacheKey = $this->makeCacheKey($key, $algorithm);
        $data = $this->cache->get($cacheKey, []);

        switch ($algorithm) {
            case 'token_bucket':
                return max(0, $limit - ($data['tokens'] ?? 0));

            case 'sliding_window':
                $now = time();
                $windowStart = $now - $window;
                $hits = array_filter($data, fn($timestamp) => $timestamp > $windowStart);
                return max(0, $limit - count($hits));

            case 'fixed_window':
                $currentWindow = floor(time() / $window);
                $windowKey = "window_{$currentWindow}";
                return max(0, $limit - ($data[$windowKey] ?? 0));

            default:
                return 0;
        }
    }

    /**
     * Reset rate limit for key
     */
    public function reset(string $key, ?string $algorithm = null): bool
    {
        $algorithm = $algorithm ?? $this->config['default_algorithm'];
        $cacheKey = $this->makeCacheKey($key, $algorithm);
        return $this->cache->delete($cacheKey);
    }

    /**
     * Ban an IP/key
     */
    public function ban(string $key, ?int $duration = null): bool
    {
        $duration = $duration ?? $this->config['ban_duration'];
        $banKey = "ban:{$key}";

        return $this->cache->set($banKey, [
            'banned_at' => time(),
            'duration' => $duration,
            'reason' => 'rate_limit_violation'
        ], $duration);
    }

    /**
     * Unban an IP/key
     */
    public function unban(string $key): bool
    {
        $banKey = "ban:{$key}";
        return $this->cache->delete($banKey);
    }

    /**
     * Check if key is banned
     */
    public function isBanned(string $key): bool
    {
        $banKey = "ban:{$key}";
        return $this->cache->exists($banKey);
    }

    /**
     * Add to whitelist
     */
    public function whitelist(string $key): void
    {
        $this->config['whitelist'][] = $key;
        $this->cache->set('rate_limiter:whitelist', $this->config['whitelist'], 86400);
    }

    /**
     * Add to blacklist
     */
    public function blacklist(string $key): void
    {
        $this->config['blacklist'][] = $key;
        $this->cache->set('rate_limiter:blacklist', $this->config['blacklist'], 86400);
    }

    /**
     * Get remaining attempts for a key
     */
    public function getRemainingAttempts(string $key, ?array $options = null): int
    {
        $options = $options ?? [];
        $algorithm = $options['algorithm'] ?? $this->config['default_algorithm'];
        $limit = $options['limit'] ?? $this->config['default_limit'];

        $cacheKey = $this->makeCacheKey($key, $algorithm);
        $current = (int) $this->cache->get($cacheKey, 0);

        return max(0, $limit - $current);
    }

    /**
     * Get time until reset for a key
     */
    public function getTimeUntilReset(string $key, ?array $options = null): int
    {
        $options = $options ?? [];
        $algorithm = $options['algorithm'] ?? $this->config['default_algorithm'];

        $cacheKey = $this->makeCacheKey($key, $algorithm) . ':reset';
        $resetTime = $this->cache->get($cacheKey);

        if ($resetTime === null) {
            return 0;
        }

        return max(0, (int) $resetTime - time());
    }

    /**
     * Ban a user for specified duration
     */
    public function banUser(string $key, ?int $duration = null): bool
    {
        return $this->ban($key, $duration);
    }

    /**
     * Get rate limiting statistics
     */
    public function getStats(): array
    {
        $stats = [
            'enabled' => $this->config['enabled'],
            'total_requests' => 0,
            'blocked_requests' => 0,
            'banned_ips' => 0,
            'whitelist_size' => count($this->config['whitelist']),
            'blacklist_size' => count($this->config['blacklist']),
            'algorithms' => array_keys($this->algorithms)
        ];

        // Get metrics from cache
        $metrics = $this->cache->get('rate_limiter:metrics', []);
        $stats = array_merge($stats, $metrics);

        return $stats;
    }

    /**
     * Detect DDoS patterns
     */
    public function detectDDoS(): array
    {
        $suspiciousIPs = [];
        $now = time();
        $window = 300; // 5 minutes

        // Get recent violations
        $violations = $this->cache->get('rate_limiter:violations', []);

        foreach ($violations as $ip => $violationData) {
            $recentViolations = array_filter(
                $violationData,
                fn($timestamp) => $timestamp > ($now - $window)
            );

            if (count($recentViolations) >= $this->config['ban_threshold']) {
                $suspiciousIPs[] = [
                    'ip' => $ip,
                    'violations' => count($recentViolations),
                    'first_violation' => min($recentViolations),
                    'last_violation' => max($recentViolations),
                    'severity' => $this->calculateSeverity(count($recentViolations))
                ];
            }
        }

        return [
            'detected_at' => $now,
            'suspicious_ips' => $suspiciousIPs,
            'total_suspicious' => count($suspiciousIPs),
            'recommendations' => $this->getDDoSRecommendations($suspiciousIPs)
        ];
    }

    /**
     * Auto-ban suspicious IPs
     */
    public function autoBan(): array
    {
        $ddosDetection = $this->detectDDoS();
        $bannedIPs = [];

        foreach ($ddosDetection['suspicious_ips'] as $suspiciousIP) {
            if ($suspiciousIP['severity'] >= 'high') {
                $this->ban($suspiciousIP['ip']);
                $bannedIPs[] = $suspiciousIP['ip'];
            }
        }

        return [
            'banned_count' => count($bannedIPs),
            'banned_ips' => $bannedIPs,
            'detection_data' => $ddosDetection
        ];
    }

    /**
     * Initialize rate limiting algorithms
     */
    private function initializeAlgorithms(): void
    {
        $this->algorithms = [
            'token_bucket' => [$this, 'tokenBucketAlgorithm'],
            'sliding_window' => [$this, 'slidingWindowAlgorithm'],
            'fixed_window' => [$this, 'fixedWindowAlgorithm']
        ];
    }

    /**
     * Token bucket algorithm
     */
    private function tokenBucketAlgorithm(string $key, int $limit, int $window, array $options): bool
    {
        $cacheKey = $this->makeCacheKey($key, 'token_bucket');
        $now = time();

        $bucket = $this->cache->get($cacheKey, [
            'tokens' => $limit,
            'last_refill' => $now
        ]);

        // Refill tokens
        $timePassed = $now - $bucket['last_refill'];
        $tokensToAdd = floor($timePassed * ($limit / $window));
        $bucket['tokens'] = min($limit, $bucket['tokens'] + $tokensToAdd);
        $bucket['last_refill'] = $now;

        // Check if request is allowed
        if ($bucket['tokens'] > 0) {
            $bucket['tokens']--;
            $this->cache->set($cacheKey, $bucket, $window * 2);
            return true;
        }

        $this->cache->set($cacheKey, $bucket, $window * 2);
        return false;
    }

    /**
     * Sliding window algorithm
     */
    private function slidingWindowAlgorithm(string $key, int $limit, int $window, array $options): bool
    {
        $cacheKey = $this->makeCacheKey($key, 'sliding_window');
        $now = time();
        $windowStart = $now - $window;

        $hits = $this->cache->get($cacheKey, []);

        // Ensure $hits is an array before filtering
        if (!is_array($hits)) {
            $hits = [];
        }

        // Remove old hits
        $hits = array_filter($hits, fn($timestamp) => $timestamp > $windowStart);

        // Check if limit exceeded
        if (count($hits) >= $limit) {
            return false;
        }

        // Add current hit
        $hits[] = $now;
        $this->cache->set($cacheKey, $hits, $window);

        return true;
    }

    /**
     * Fixed window algorithm
     */
    private function fixedWindowAlgorithm(string $key, int $limit, int $window, array $options): bool
    {
        $cacheKey = $this->makeCacheKey($key, 'fixed_window');
        $currentWindow = floor(time() / $window);
        $windowKey = "window_{$currentWindow}";

        $data = $this->cache->get($cacheKey, []);
        $currentCount = $data[$windowKey] ?? 0;

        if ($currentCount >= $limit) {
            return false;
        }

        $data[$windowKey] = $currentCount + 1;

        // Clean old windows
        $cutoff = $currentWindow - 2;
        foreach (array_keys($data) as $key) {
            if (str_starts_with($key, 'window_')) {
                $windowNum = (int) str_replace('window_', '', $key);
                if ($windowNum < $cutoff) {
                    unset($data[$key]);
                }
            }
        }

        $this->cache->set($cacheKey, $data, $window * 3);
        return true;
    }

    /**
     * Hit token bucket
     */
    private function hitTokenBucket(string $cacheKey, int $limit, int $window, int $now): array
    {
        $bucket = $this->cache->get($cacheKey, [
            'tokens' => $limit,
            'last_refill' => $now
        ]);

        $timePassed = $now - $bucket['last_refill'];
        $tokensToAdd = floor($timePassed * ($limit / $window));
        $bucket['tokens'] = min($limit, $bucket['tokens'] + $tokensToAdd);
        $bucket['last_refill'] = $now;

        $allowed = $bucket['tokens'] > 0;
        if ($allowed) {
            $bucket['tokens']--;
        }

        $this->cache->set($cacheKey, $bucket, $window * 2);

        return [
            'allowed' => $allowed,
            'remaining' => $bucket['tokens'],
            'reset_time' => $now + $window
        ];
    }

    /**
     * Hit sliding window
     */
    private function hitSlidingWindow(string $cacheKey, int $limit, int $window, int $now): array
    {
        $windowStart = $now - $window;
        $hits = $this->cache->get($cacheKey, []);

        $hits = array_filter($hits, fn($timestamp) => $timestamp > $windowStart);

        $allowed = count($hits) < $limit;
        if ($allowed) {
            $hits[] = $now;
        }

        $this->cache->set($cacheKey, $hits, $window);

        return [
            'allowed' => $allowed,
            'remaining' => max(0, $limit - count($hits)),
            'reset_time' => $windowStart + $window
        ];
    }

    /**
     * Hit fixed window
     */
    private function hitFixedWindow(string $cacheKey, int $limit, int $window, int $now): array
    {
        $currentWindow = floor($now / $window);
        $windowKey = "window_{$currentWindow}";

        $data = $this->cache->get($cacheKey, []);
        $currentCount = $data[$windowKey] ?? 0;

        $allowed = $currentCount < $limit;
        if ($allowed) {
            $data[$windowKey] = $currentCount + 1;
            $this->cache->set($cacheKey, $data, $window * 3);
        }

        return [
            'allowed' => $allowed,
            'remaining' => max(0, $limit - ($currentCount + ($allowed ? 1 : 0))),
            'reset_time' => ($currentWindow + 1) * $window
        ];
    }

    /**
     * Track violation
     */
    private function trackViolation(string $key): void
    {
        $violationsKey = 'rate_limiter:violations';
        $violations = $this->cache->get($violationsKey, []);

        if (!isset($violations[$key])) {
            $violations[$key] = [];
        }

        $violations[$key][] = time();

        // Keep only recent violations
        $cutoff = time() - 3600; // 1 hour
        $violations[$key] = array_filter(
            $violations[$key],
            fn($timestamp) => $timestamp > $cutoff
        );

        $this->cache->set($violationsKey, $violations, 3600);

        // Auto-ban if threshold exceeded
        if (count($violations[$key]) >= $this->config['ban_threshold']) {
            $this->ban($key);
        }
    }

    /**
     * Check if whitelisted
     */
    private function isWhitelisted(string $key): bool
    {
        return in_array($key, $this->config['whitelist']);
    }

    /**
     * Check if blacklisted
     */
    private function isBlacklisted(string $key): bool
    {
        return in_array($key, $this->config['blacklist']);
    }

    /**
     * Make cache key
     */
    private function makeCacheKey(string $key, string $algorithm): string
    {
        return "rate_limit:{$algorithm}:{$key}";
    }

    /**
     * Calculate severity
     */
    private function calculateSeverity(int $violations): string
    {
        if ($violations >= 20) {
            return 'critical';
        } elseif ($violations >= 10) {
            return 'high';
        } elseif ($violations >= 5) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * Get DDoS recommendations
     */
    private function getDDoSRecommendations(array $suspiciousIPs): array
    {
        $recommendations = [];

        if (count($suspiciousIPs) > 10) {
            $recommendations[] = 'Consider implementing IP-based blocking at firewall level';
        }

        if (count($suspiciousIPs) > 5) {
            $recommendations[] = 'Enable automatic banning for high-severity violations';
        }

        foreach ($suspiciousIPs as $ip) {
            if ($ip['severity'] === 'critical') {
                $recommendations[] = "Immediately ban IP: {$ip['ip']}";
            }
        }

        return $recommendations;
    }

    /**
     * Get rate limiting middleware
     */
    public function middleware(array $options = []): callable
    {
        return function ($request, $next) use ($options) {
            $key = $this->extractKeyFromRequest($request);

            if (!$this->isAllowed($key, $options)) {
                return $this->createRateLimitResponse($key, $options);
            }

            $this->hit($key, $options);
            return $next($request);
        };
    }

    /**
     * Extract key from request
     */
    private function extractKeyFromRequest($request): string
    {
        // Extract IP address
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

        // Check for forwarded IP
        if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $forwarded = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
            $ip = trim($forwarded[0]);
        } elseif (isset($_SERVER['HTTP_X_REAL_IP'])) {
            $ip = $_SERVER['HTTP_X_REAL_IP'];
        }

        return $ip;
    }

    /**
     * Create rate limit response
     */
    private function createRateLimitResponse(string $key, array $options): array
    {
        $remaining = $this->remaining($key, $options);
        $resetTime = time() + ($options['window'] ?? $this->config['default_window']);

        return [
            'error' => true,
            'message' => 'Rate limit exceeded',
            'code' => 429,
            'data' => [
                'limit' => $options['limit'] ?? $this->config['default_limit'],
                'remaining' => $remaining,
                'reset_time' => $resetTime,
                'retry_after' => $resetTime - time()
            ]
        ];
    }
}
