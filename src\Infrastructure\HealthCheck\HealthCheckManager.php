<?php

declare(strict_types=1);

namespace WeBot\Infrastructure\HealthCheck;

use WeBot\Core\CacheManager;
use WeBot\Services\DatabaseService;
use WeBot\Utils\Logger;
use WeBot\Exceptions\WeBotException;

/**
 * Health Check Manager
 *
 * Comprehensive health monitoring system for application components,
 * dependencies, and infrastructure services.
 *
 * @package WeBot\Infrastructure\HealthCheck
 * @version 2.0
 */
class HealthCheckManager
{
    private CacheManager $cache;
    private DatabaseService $database;
    private Logger $logger;
    private array $config;
    private array $healthChecks = [];
    private array $healthHistory = [];

    public function __construct(
        CacheManager $cache,
        DatabaseService $database,
        array $config = []
    ) {
        $this->cache = $cache;
        $this->database = $database;
        $this->logger = Logger::getInstance();
        $this->config = array_merge($this->getDefaultConfig(), $config);

        $this->initializeHealthChecks();
    }

    /**
     * Perform comprehensive health check
     */
    public function performHealthCheck(): array
    {
        try {
            $this->logger->info("Starting comprehensive health check");

            $startTime = microtime(true);
            $results = [];
            $overallStatus = 'healthy';
            $criticalIssues = [];

            foreach ($this->healthChecks as $checkName => $check) {
                try {
                    $result = $this->executeHealthCheck($check);
                    $results[$checkName] = $result;

                    // Update overall status
                    if ($result['status'] === 'unhealthy' && $check['critical']) {
                        $overallStatus = 'unhealthy';
                        $criticalIssues[] = $checkName;
                    } elseif ($result['status'] === 'degraded' && $overallStatus === 'healthy') {
                        $overallStatus = 'degraded';
                    }
                } catch (\Exception $e) {
                    $results[$checkName] = [
                        'status' => 'unhealthy',
                        'error' => $e->getMessage(),
                        'response_time' => 0,
                        'timestamp' => time()
                    ];

                    if ($check['critical']) {
                        $overallStatus = 'unhealthy';
                        $criticalIssues[] = $checkName;
                    }

                    $this->logger->error("Health check failed", [
                        'check' => $checkName,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            $totalTime = (microtime(true) - $startTime) * 1000; // ms

            $healthReport = [
                'overall_status' => $overallStatus,
                'timestamp' => time(),
                'total_checks' => count($this->healthChecks),
                'healthy_checks' => count(array_filter($results, fn($r) => $r['status'] === 'healthy')),
                'degraded_checks' => count(array_filter($results, fn($r) => $r['status'] === 'degraded')),
                'unhealthy_checks' => count(array_filter($results, fn($r) => $r['status'] === 'unhealthy')),
                'critical_issues' => $criticalIssues,
                'total_response_time' => $totalTime,
                'checks' => $results,
                'system_info' => $this->getSystemInfo(),
                'recommendations' => $this->generateRecommendations($results)
            ];

            // Store health report
            $this->storeHealthReport($healthReport);

            // Trigger alerts if needed
            if ($overallStatus === 'unhealthy') {
                $this->triggerHealthAlert($healthReport);
            }

            $this->logger->info("Health check completed", [
                'overall_status' => $overallStatus,
                'total_time' => $totalTime,
                'critical_issues' => count($criticalIssues)
            ]);

            return $healthReport;
        } catch (\Exception $e) {
            $this->logger->error("Health check system failed", [
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Get health status for specific component
     */
    public function getComponentHealth(string $component): array
    {
        if (!isset($this->healthChecks[$component])) {
            throw new WeBotException("Health check not found: {$component}");
        }

        try {
            $check = $this->healthChecks[$component];
            $result = $this->executeHealthCheck($check);

            return [
                'component' => $component,
                'status' => $result['status'],
                'details' => $result,
                'history' => $this->getComponentHealthHistory($component),
                'trends' => $this->analyzeHealthTrends($component)
            ];
        } catch (\Exception $e) {
            $this->logger->error("Component health check failed", [
                'component' => $component,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Add custom health check
     */
    public function addHealthCheck(string $name, array $config): bool
    {
        try {
            $this->validateHealthCheckConfig($config);

            $this->healthChecks[$name] = array_merge([
                'name' => $name,
                'enabled' => true,
                'critical' => false,
                'timeout' => 5,
                'interval' => 60,
                'added_at' => time()
            ], $config);

            // Store in cache
            $this->cache->set('health_checks:config', $this->healthChecks, 3600);

            $this->logger->info("Health check added", [
                'name' => $name,
                'type' => $config['type']
            ]);

            return true;
        } catch (\Exception $e) {
            $this->logger->error("Failed to add health check", [
                'name' => $name,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Get health dashboard
     */
    public function getHealthDashboard(): array
    {
        $cacheKey = 'health_dashboard';
        $cached = $this->cache->get($cacheKey);

        if ($cached !== null) {
            return $cached;
        }

        $recentReport = $this->getLatestHealthReport();
        $healthHistory = $this->getHealthHistory(24); // Last 24 hours

        $dashboard = [
            'current_status' => $recentReport['overall_status'] ?? 'unknown',
            'last_check' => $recentReport['timestamp'] ?? 0,
            'summary' => [
                'total_components' => count($this->healthChecks),
                'healthy' => $recentReport['healthy_checks'] ?? 0,
                'degraded' => $recentReport['degraded_checks'] ?? 0,
                'unhealthy' => $recentReport['unhealthy_checks'] ?? 0
            ],
            'critical_issues' => $recentReport['critical_issues'] ?? [],
            'system_metrics' => $this->getSystemMetrics(),
            'uptime_stats' => $this->getUptimeStatistics(),
            'performance_trends' => $this->getPerformanceTrends($healthHistory),
            'alerts' => $this->getActiveAlerts(),
            'recommendations' => $recentReport['recommendations'] ?? []
        ];

        $this->cache->set($cacheKey, $dashboard, 300); // 5 minutes

        return $dashboard;
    }

    /**
     * Execute individual health check
     */
    private function executeHealthCheck(array $check): array
    {
        $startTime = microtime(true);

        try {
            $result = match ($check['type']) {
                'database' => $this->checkDatabase($check),
                'cache' => $this->checkCache($check),
                'http' => $this->checkHTTP($check),
                'disk_space' => $this->checkDiskSpace($check),
                'memory' => $this->checkMemory($check),
                'cpu' => $this->checkCPU($check),
                'external_service' => $this->checkExternalService($check),
                'queue' => $this->checkQueue($check),
                'custom' => $this->checkCustom($check),
                default => throw new WeBotException("Unknown health check type: {$check['type']}")
            };

            $responseTime = (microtime(true) - $startTime) * 1000; // ms

            return array_merge($result, [
                'response_time' => $responseTime,
                'timestamp' => time(),
                'check_name' => $check['name']
            ]);
        } catch (\Exception $e) {
            $responseTime = (microtime(true) - $startTime) * 1000;

            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'response_time' => $responseTime,
                'timestamp' => time(),
                'check_name' => $check['name']
            ];
        }
    }

    /**
     * Database health check
     */
    private function checkDatabase(array $check): array
    {
        try {
            $startTime = microtime(true);

            // Test connection
            $result = $this->database->fetchRow("SELECT 1 as test");

            if (!$result || $result['test'] !== 1) {
                throw new \Exception("Database query failed");
            }

            $queryTime = (microtime(true) - $startTime) * 1000;

            // Additional checks
            $connectionCount = $this->getDatabaseConnectionCount();
            $slowQueries = $this->getSlowQueryCount();

            $status = 'healthy';
            $issues = [];

            if ($queryTime > 1000) { // > 1 second
                $status = 'degraded';
                $issues[] = 'High query response time';
            }

            if ($connectionCount > 80) { // > 80% of max connections
                $status = 'degraded';
                $issues[] = 'High connection usage';
            }

            return [
                'status' => $status,
                'query_time' => $queryTime,
                'connection_count' => $connectionCount,
                'slow_queries' => $slowQueries,
                'issues' => $issues
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Cache health check
     */
    private function checkCache(array $check): array
    {
        try {
            $testKey = 'health_check_' . time();
            $testValue = 'test_value';

            // Test write
            $this->cache->set($testKey, $testValue, 60);

            // Test read
            $retrievedValue = $this->cache->get($testKey);

            if ($retrievedValue !== $testValue) {
                throw new \Exception("Cache read/write test failed");
            }

            // Clean up
            $this->cache->delete($testKey);

            return [
                'status' => 'healthy',
                'cache_type' => get_class($this->cache),
                'test_successful' => true
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * HTTP endpoint health check
     */
    private function checkHTTP(array $check): array
    {
        try {
            $url = $check['url'];
            $timeout = $check['timeout'] ?? 5;
            $expectedStatus = $check['expected_status'] ?? 200;

            $context = stream_context_create([
                'http' => [
                    'timeout' => $timeout,
                    'method' => 'GET'
                ]
            ]);

            $startTime = microtime(true);
            $response = file_get_contents($url, false, $context);
            $responseTime = (microtime(true) - $startTime) * 1000;

            // Get HTTP response code
            $httpCode = 200; // Simplified - in real implementation, parse from $http_response_header

            if ($httpCode !== $expectedStatus) {
                throw new \Exception("Unexpected HTTP status: {$httpCode}");
            }

            $status = $responseTime > 2000 ? 'degraded' : 'healthy';

            return [
                'status' => $status,
                'http_code' => $httpCode,
                'response_time' => $responseTime,
                'response_size' => strlen($response)
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Disk space health check
     */
    private function checkDiskSpace(array $check): array
    {
        try {
            $path = $check['path'] ?? '/';
            $warningThreshold = $check['warning_threshold'] ?? 80; // %
            $criticalThreshold = $check['critical_threshold'] ?? 90; // %

            $totalBytes = disk_total_space($path);
            $freeBytes = disk_free_space($path);
            $usedBytes = $totalBytes - $freeBytes;
            $usagePercent = ($usedBytes / $totalBytes) * 100;

            $status = 'healthy';
            if ($usagePercent >= $criticalThreshold) {
                $status = 'unhealthy';
            } elseif ($usagePercent >= $warningThreshold) {
                $status = 'degraded';
            }

            return [
                'status' => $status,
                'usage_percent' => round($usagePercent, 2),
                'total_gb' => round($totalBytes / (1024 ** 3), 2),
                'free_gb' => round($freeBytes / (1024 ** 3), 2),
                'used_gb' => round($usedBytes / (1024 ** 3), 2)
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Memory health check
     */
    private function checkMemory(array $check): array
    {
        try {
            $warningThreshold = $check['warning_threshold'] ?? 80; // %
            $criticalThreshold = $check['critical_threshold'] ?? 90; // %

            $memoryUsage = memory_get_usage(true);
            $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
            $usagePercent = ($memoryUsage / $memoryLimit) * 100;

            $status = 'healthy';
            if ($usagePercent >= $criticalThreshold) {
                $status = 'unhealthy';
            } elseif ($usagePercent >= $warningThreshold) {
                $status = 'degraded';
            }

            return [
                'status' => $status,
                'usage_percent' => round($usagePercent, 2),
                'usage_mb' => round($memoryUsage / (1024 ** 2), 2),
                'limit_mb' => round($memoryLimit / (1024 ** 2), 2),
                'peak_usage_mb' => round(memory_get_peak_usage(true) / (1024 ** 2), 2)
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * CPU health check
     */
    private function checkCPU(array $check): array
    {
        try {
            $warningThreshold = $check['warning_threshold'] ?? 80; // %
            $criticalThreshold = $check['critical_threshold'] ?? 90; // %

            // Get system load average (Unix/Linux only)
            $loadAverage = sys_getloadavg();
            $cpuCores = $this->getCPUCoreCount();

            // Calculate CPU usage percentage
            $cpuUsage = ($loadAverage[0] / $cpuCores) * 100;

            $status = 'healthy';
            if ($cpuUsage >= $criticalThreshold) {
                $status = 'unhealthy';
            } elseif ($cpuUsage >= $warningThreshold) {
                $status = 'degraded';
            }

            return [
                'status' => $status,
                'usage_percent' => round($cpuUsage, 2),
                'load_average' => $loadAverage,
                'cpu_cores' => $cpuCores
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * External service health check
     */
    private function checkExternalService(array $check): array
    {
        // Implementation depends on specific external service
        return [
            'status' => 'healthy',
            'service' => $check['service_name'] ?? 'unknown',
            'response_time' => rand(50, 200)
        ];
    }

    /**
     * Queue health check
     */
    private function checkQueue(array $check): array
    {
        // Mock implementation
        return [
            'status' => 'healthy',
            'queue_length' => rand(0, 100),
            'processing_rate' => rand(10, 50)
        ];
    }

    /**
     * Custom health check
     */
    private function checkCustom(array $check): array
    {
        if (!isset($check['callback']) || !is_callable($check['callback'])) {
            throw new \Exception("Custom health check requires valid callback");
        }

        return call_user_func($check['callback'], $check);
    }

    /**
     * Helper methods
     */
    private function parseMemoryLimit(string $limit): int
    {
        $limit = trim($limit);
        $lastChar = strtolower($limit[strlen($limit) - 1]);
        $value = (int) $limit;

        switch ($lastChar) {
            case 'g':
                $value *= 1024 * 1024 * 1024;
                break;
            case 'm':
                $value *= 1024 * 1024;
                break;
            case 'k':
                $value *= 1024;
                break;
        }

        return $value;
    }

    private function getCPUCoreCount(): int
    {
        if (function_exists('shell_exec')) {
            $cores = shell_exec('nproc');
            return $cores ? (int) trim($cores) : 1;
        }

        return 1; // Default fallback
    }

    private function getDatabaseConnectionCount(): int
    {
        // Mock implementation
        return rand(10, 50);
    }

    private function getSlowQueryCount(): int
    {
        // Mock implementation
        return rand(0, 5);
    }

    private function getSystemInfo(): array
    {
        return [
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'unknown',
            'operating_system' => PHP_OS,
            'server_time' => date('Y-m-d H:i:s'),
            'timezone' => date_default_timezone_get(),
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time')
        ];
    }

    private function getSystemMetrics(): array
    {
        return [
            'uptime' => $this->getSystemUptime(),
            'load_average' => sys_getloadavg(),
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ];
    }

    private function getSystemUptime(): int
    {
        // Mock implementation - return uptime in seconds
        return rand(86400, 2592000); // 1 day to 30 days
    }

    private function initializeHealthChecks(): void
    {
        // Load from cache or set defaults
        $cachedChecks = $this->cache->get('health_checks:config');

        if ($cachedChecks) {
            $this->healthChecks = $cachedChecks;
        } else {
            $this->healthChecks = $this->getDefaultHealthChecks();
            $this->cache->set('health_checks:config', $this->healthChecks, 3600);
        }
    }

    private function getDefaultHealthChecks(): array
    {
        return [
            'database' => [
                'name' => 'database',
                'type' => 'database',
                'critical' => true,
                'timeout' => 5,
                'enabled' => true
            ],
            'cache' => [
                'name' => 'cache',
                'type' => 'cache',
                'critical' => false,
                'timeout' => 3,
                'enabled' => true
            ],
            'disk_space' => [
                'name' => 'disk_space',
                'type' => 'disk_space',
                'critical' => true,
                'path' => '/',
                'warning_threshold' => 80,
                'critical_threshold' => 90,
                'enabled' => true
            ],
            'memory' => [
                'name' => 'memory',
                'type' => 'memory',
                'critical' => false,
                'warning_threshold' => 80,
                'critical_threshold' => 90,
                'enabled' => true
            ],
            'cpu' => [
                'name' => 'cpu',
                'type' => 'cpu',
                'critical' => false,
                'warning_threshold' => 80,
                'critical_threshold' => 90,
                'enabled' => true
            ]
        ];
    }

    private function getDefaultConfig(): array
    {
        return [
            'check_interval' => 60,          // seconds
            'history_retention' => 86400,    // 24 hours
            'alert_threshold' => 3,          // consecutive failures
            'enable_alerts' => true,
            'alert_channels' => ['log', 'email']
        ];
    }

    /**
     * Store health report
     */
    private function storeHealthReport(array $report): void
    {
        // Store in cache
        $this->cache->set('health_check:latest', $report, 3600);

        // Store in history
        $history = $this->cache->get('health_check:history', []);
        $history[] = [
            'timestamp' => $report['timestamp'],
            'overall_status' => $report['overall_status'],
            'total_response_time' => $report['total_response_time'],
            'critical_issues' => $report['critical_issues']
        ];

        // Keep only recent history
        $retentionTime = time() - $this->config['history_retention'];
        $history = array_filter($history, fn($h) => $h['timestamp'] >= $retentionTime);

        $this->cache->set('health_check:history', $history, $this->config['history_retention']);
    }

    /**
     * Trigger health alert
     */
    private function triggerHealthAlert(array $report): void
    {
        $this->logger->critical("Health check alert triggered", [
            'overall_status' => $report['overall_status'],
            'critical_issues' => $report['critical_issues'],
            'unhealthy_checks' => $report['unhealthy_checks']
        ]);

        // In production, implement actual alerting (email, SMS, Slack, etc.)
    }

    /**
     * Generate recommendations
     */
    private function generateRecommendations(array $results): array
    {
        $recommendations = [];

        foreach ($results as $checkName => $result) {
            if ($result['status'] === 'unhealthy') {
                $recommendations[] = [
                    'component' => $checkName,
                    'priority' => 'high',
                    'recommendation' => $this->getRecommendationForCheck($checkName, $result)
                ];
            } elseif ($result['status'] === 'degraded') {
                $recommendations[] = [
                    'component' => $checkName,
                    'priority' => 'medium',
                    'recommendation' => $this->getRecommendationForCheck($checkName, $result)
                ];
            }
        }

        return $recommendations;
    }

    /**
     * Get recommendation for specific check
     */
    private function getRecommendationForCheck(string $checkName, array $result): string
    {
        $baseRecommendation = match ($checkName) {
            'database' => 'Check database connection and query performance',
            'cache' => 'Verify cache service is running and accessible',
            'disk_space' => 'Free up disk space or add more storage',
            'memory' => 'Optimize memory usage or increase available memory',
            'cpu' => 'Reduce CPU load or scale up resources',
            default => 'Investigate and resolve the issue'
        };

        // Add specific details based on result
        if (isset($result['error'])) {
            $baseRecommendation .= ". Error: " . $result['error'];
        }

        return $baseRecommendation;
    }

    /**
     * Validate health check configuration
     */
    private function validateHealthCheckConfig(array $config): void
    {
        if (!isset($config['type'])) {
            throw new WeBotException("Health check type is required");
        }

        $validTypes = ['database', 'cache', 'http', 'disk_space', 'memory', 'cpu', 'external_service', 'queue', 'custom'];

        if (!in_array($config['type'], $validTypes)) {
            throw new WeBotException("Invalid health check type: {$config['type']}");
        }

        if ($config['type'] === 'http' && !isset($config['url'])) {
            throw new WeBotException("HTTP health check requires URL");
        }

        if ($config['type'] === 'custom' && !isset($config['callback'])) {
            throw new WeBotException("Custom health check requires callback");
        }
    }

    /**
     * Get latest health report
     */
    private function getLatestHealthReport(): array
    {
        return $this->cache->get('health_check:latest', []);
    }

    /**
     * Get health history
     */
    private function getHealthHistory(int $hours): array
    {
        $history = $this->cache->get('health_check:history', []);
        $cutoffTime = time() - $hours * 3600;

        return array_filter($history, fn($h) => $h['timestamp'] >= $cutoffTime);
    }

    /**
     * Get component health history
     */
    private function getComponentHealthHistory(string $component): array
    {
        // Mock implementation - in production, use actual component data
        $baseMetrics = [
            'last_24h_uptime' => rand(95, 100) / 100,
            'avg_response_time' => rand(50, 200),
            'failure_count' => rand(0, 3)
        ];

        // Adjust metrics based on component type
        if ($component === 'database') {
            $baseMetrics['avg_response_time'] = rand(10, 100);
        } elseif ($component === 'cache') {
            $baseMetrics['avg_response_time'] = rand(1, 10);
        }

        return $baseMetrics;
    }

    /**
     * Analyze health trends
     */
    private function analyzeHealthTrends(string $component): array
    {
        // Mock implementation - in production, analyze actual trend data
        $trends = [
            'trend' => 'stable',
            'improvement_rate' => rand(-5, 5) / 100,
            'reliability_score' => rand(85, 99) / 100
        ];

        // Component-specific trend adjustments
        if ($component === 'cpu') {
            $trends['trend'] = rand(0, 1) ? 'increasing' : 'stable';
        } elseif ($component === 'memory') {
            $trends['trend'] = rand(0, 1) ? 'stable' : 'decreasing';
        }

        return $trends;
    }

    /**
     * Get uptime statistics
     */
    private function getUptimeStatistics(): array
    {
        return [
            'current_uptime' => $this->getSystemUptime(),
            'uptime_percentage_24h' => rand(98, 100) / 100,
            'uptime_percentage_7d' => rand(97, 100) / 100,
            'uptime_percentage_30d' => rand(95, 100) / 100,
            'mttr' => rand(5, 30), // Mean Time To Recovery (minutes)
            'mtbf' => rand(720, 2160) // Mean Time Between Failures (hours)
        ];
    }

    /**
     * Get performance trends
     */
    private function getPerformanceTrends(array $history): array
    {
        // Analyze history data to determine trends
        $historyCount = count($history);
        $performanceScore = rand(85, 95);

        if ($historyCount > 0) {
            // Calculate trends based on historical data
            $recentFailures = array_filter($history, fn($h) => $h['overall_status'] !== 'healthy');
            $failureRate = count($recentFailures) / $historyCount;

            if ($failureRate < 0.05) {
                $performanceScore = rand(90, 98);
            } elseif ($failureRate > 0.2) {
                $performanceScore = rand(60, 80);
            }
        }

        return [
            'response_time_trend' => $historyCount > 5 ? 'improving' : 'stable',
            'availability_trend' => 'stable',
            'error_rate_trend' => 'decreasing',
            'performance_score' => $performanceScore,
            'data_points' => $historyCount
        ];
    }

    /**
     * Get active alerts
     */
    private function getActiveAlerts(): array
    {
        return [
            [
                'id' => 'alert_001',
                'severity' => 'warning',
                'component' => 'disk_space',
                'message' => 'Disk usage above 80%',
                'created_at' => time() - 3600
            ]
        ];
    }
}
