# WeBot Redis Configuration
# Optimized for production caching and session storage

# Network
bind 0.0.0.0
port 6379
tcp-backlog 511
timeout 300
tcp-keepalive 300

# General
daemonize no
supervised no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""
databases 16

# Snapshotting
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# Replication
replica-serve-stale-data yes
replica-read-only yes
repl-diskless-sync no
repl-diskless-sync-delay 5
repl-ping-replica-period 10
repl-timeout 60
repl-disable-tcp-nodelay no
repl-backlog-size 1mb
repl-backlog-ttl 3600

# Security
# requirepass your_redis_password_here
# rename-command FLUSHDB ""
# rename-command FLUSHALL ""
# rename-command DEBUG ""
# rename-command CONFIG ""

# Memory Management
maxmemory 512mb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# Lazy Freeing
lazyfree-lazy-eviction yes
lazyfree-lazy-expire yes
lazyfree-lazy-server-del yes
replica-lazy-flush yes

# Append Only File
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# Lua Scripting
lua-time-limit 5000

# Slow Log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Latency Monitor
latency-monitor-threshold 100

# Event Notification
notify-keyspace-events "Ex"

# Hash
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# List
list-max-ziplist-size -2
list-compress-depth 0

# Set
set-max-intset-entries 512

# Sorted Set
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog
hll-sparse-max-bytes 3000

# Streams
stream-node-max-bytes 4096
stream-node-max-entries 100

# Active Rehashing
activerehashing yes

# Client Output Buffer Limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Client Query Buffer
client-query-buffer-limit 1gb

# Protocol Buffer
proto-max-bulk-len 512mb

# Frequency
hz 10

# Dynamic HZ
dynamic-hz yes

# AOF Rewrite
aof-rewrite-incremental-fsync yes

# RDB Save
rdb-save-incremental-fsync yes

# LFU
lfu-log-factor 10
lfu-decay-time 1

# TLS (if needed)
# tls-port 6380
# tls-cert-file redis.crt
# tls-key-file redis.key
# tls-ca-cert-file ca.crt

# Modules (if any)
# loadmodule /path/to/module.so

# Performance Tuning for WeBot
# Optimize for cache workload
maxclients 10000
tcp-keepalive 60

# Memory optimization
hash-max-ziplist-entries 1000
hash-max-ziplist-value 1024
list-max-ziplist-size -1
set-max-intset-entries 1024
zset-max-ziplist-entries 256
zset-max-ziplist-value 128

# Persistence optimization for cache
save 3600 1
save 300 100
save 60 10000

# Eviction policy for cache
maxmemory-policy volatile-lru

# Enable keyspace notifications for cache invalidation
notify-keyspace-events "KEA"
