<?php

declare(strict_types=1);

namespace WeBot\DTOs;

use JsonSerializable;

/**
 * Base Data Transfer Object
 *
 * Provides common functionality for all DTOs including
 * validation, serialization, and data transformation.
 *
 * @package WeBot\DTOs
 * @version 2.0
 */
abstract class BaseDTO implements JsonSerializable
{
    protected array $data = [];
    protected array $errors = [];

    public function __construct(array $data = [])
    {
        $this->data = $data;
        $this->validate();
    }

    /**
     * Validate DTO data
     */
    abstract protected function validate(): void;

    /**
     * Get validation rules
     */
    abstract protected function rules(): array;

    /**
     * Get field labels for error messages
     */
    protected function labels(): array
    {
        return [];
    }

    /**
     * Check if DTO is valid
     */
    public function isValid(): bool
    {
        return empty($this->errors);
    }

    /**
     * Get validation errors
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Get first error message
     */
    public function getFirstError(): ?string
    {
        return !empty($this->errors) ? reset($this->errors) : null;
    }

    /**
     * Add validation error
     */
    protected function addError(string $field, string $message): void
    {
        $this->errors[$field] = $message;
    }

    /**
     * Get data value
     */
    public function get(string $key, $default = null)
    {
        return $this->data[$key] ?? $default;
    }

    /**
     * Set data value
     */
    public function set(string $key, $value): self
    {
        $this->data[$key] = $value;
        return $this;
    }

    /**
     * Check if key exists
     */
    public function has(string $key): bool
    {
        return array_key_exists($key, $this->data);
    }

    /**
     * Get all data
     */
    public function toArray(): array
    {
        return $this->data;
    }

    /**
     * Create from array
     */
    public static function fromArray(array $data): static
    {
        return new static($data);
    }

    /**
     * JSON serialization
     */
    public function jsonSerialize(): array
    {
        return $this->data;
    }

    /**
     * Convert to JSON
     */
    public function toJson(): string
    {
        return json_encode($this->data, JSON_UNESCAPED_UNICODE);
    }

    /**
     * Magic getter
     */
    public function __get(string $name)
    {
        return $this->get($name);
    }

    /**
     * Magic setter
     */
    public function __set(string $name, $value): void
    {
        $this->set($name, $value);
    }

    /**
     * Magic isset
     */
    public function __isset(string $name): bool
    {
        return $this->has($name);
    }

    /**
     * Validate required fields
     */
    protected function validateRequired(array $fields): void
    {
        foreach ($fields as $field) {
            if (!$this->has($field) || $this->get($field) === '' || $this->get($field) === null) {
                $label = $this->labels()[$field] ?? $field;
                $this->addError($field, "فیلد {$label} الزامی است.");
            }
        }
    }

    /**
     * Validate string length
     */
    protected function validateLength(string $field, int $min = 0, int $max = 255): void
    {
        $value = $this->get($field);
        if ($value !== null && is_string($value)) {
            $length = mb_strlen($value);
            $label = $this->labels()[$field] ?? $field;

            if ($length < $min) {
                $this->addError($field, "فیلد {$label} باید حداقل {$min} کاراکتر باشد.");
            }

            if ($length > $max) {
                $this->addError($field, "فیلد {$label} نباید بیشتر از {$max} کاراکتر باشد.");
            }
        }
    }

    /**
     * Validate email format
     */
    protected function validateEmail(string $field): void
    {
        $value = $this->get($field);
        if ($value !== null && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
            $label = $this->labels()[$field] ?? $field;
            $this->addError($field, "فرمت {$label} صحیح نیست.");
        }
    }

    /**
     * Validate numeric value
     */
    protected function validateNumeric(string $field, float $min = null, float $max = null): void
    {
        $value = $this->get($field);
        if ($value !== null) {
            if (!is_numeric($value)) {
                $label = $this->labels()[$field] ?? $field;
                $this->addError($field, "فیلد {$label} باید عددی باشد.");
                return;
            }

            $numValue = (float)$value;
            $label = $this->labels()[$field] ?? $field;

            if ($min !== null && $numValue < $min) {
                $this->addError($field, "فیلد {$label} باید حداقل {$min} باشد.");
            }

            if ($max !== null && $numValue > $max) {
                $this->addError($field, "فیلد {$label} نباید بیشتر از {$max} باشد.");
            }
        }
    }

    /**
     * Validate array of allowed values
     */
    protected function validateIn(string $field, array $allowedValues): void
    {
        $value = $this->get($field);
        if ($value !== null && !in_array($value, $allowedValues, true)) {
            $label = $this->labels()[$field] ?? $field;
            $allowed = implode('، ', $allowedValues);
            $this->addError($field, "فیلد {$label} باید یکی از مقادیر زیر باشد: {$allowed}");
        }
    }

    /**
     * Validate date format
     */
    protected function validateDate(string $field, string $format = 'Y-m-d H:i:s'): void
    {
        $value = $this->get($field);
        if ($value !== null) {
            $date = \DateTime::createFromFormat($format, $value);
            if (!$date || $date->format($format) !== $value) {
                $label = $this->labels()[$field] ?? $field;
                $this->addError($field, "فرمت {$label} صحیح نیست.");
            }
        }
    }

    /**
     * Validate phone number (Iranian format)
     */
    protected function validatePhone(string $field): void
    {
        $value = $this->get($field);
        if ($value !== null) {
            // Remove spaces and dashes
            $phone = preg_replace('/[\s\-]/', '', $value);

            // Check Iranian phone number format
            if (!preg_match('/^(\+98|0)?9\d{9}$/', $phone)) {
                $label = $this->labels()[$field] ?? $field;
                $this->addError($field, "فرمت {$label} صحیح نیست.");
            }
        }
    }

    /**
     * Validate Telegram ID
     */
    protected function validateTelegramId(string $field): void
    {
        $value = $this->get($field);
        if ($value !== null) {
            if (!is_numeric($value) || $value <= 0) {
                $label = $this->labels()[$field] ?? $field;
                $this->addError($field, "فیلد {$label} باید یک شناسه تلگرام معتبر باشد.");
            }
        }
    }
}
