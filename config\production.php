<?php

declare(strict_types=1);

/**
 * Production Configuration
 * 
 * Optimized configuration settings for production environment.
 * This file loads modular configuration files for better maintainability.
 * 
 * @package WeBot\Config
 * @version 2.0
 */

// Load modular configuration files
$config = [];

// Load app configuration
if (file_exists(__DIR__ . '/app.php')) {
    $appConfig = require __DIR__ . '/app.php';
    $config = array_merge($config, $appConfig);
}

// Load database configuration
if (file_exists(__DIR__ . '/database.php')) {
    $config['database'] = require __DIR__ . '/database.php';
}

// Load cache configuration
if (file_exists(__DIR__ . '/cache.php')) {
    $config['cache'] = require __DIR__ . '/cache.php';
}

// Load logging configuration
if (file_exists(__DIR__ . '/logging.php')) {
    $config['logging'] = require __DIR__ . '/logging.php';
}

// Production-specific overrides
$config['env'] = 'production';
$config['debug'] = false;

// Security settings for production
$config['security'] = [
    'rate_limit_enabled' => true,
    'rate_limit_max_attempts' => 10,
    'rate_limit_time_window' => 60,
    'spam_protection' => true,
    'csrf_protection' => true,
    'ssl_enabled' => true,
    'ssl_redirect' => true,
];

// Performance settings for production
$config['performance'] = [
    'cache_ttl' => 3600,
    'session_timeout' => 1800,
    'opcache_enabled' => true,
    'gzip_enabled' => true,
];

// Monitoring settings for production
$config['monitoring'] = [
    'enabled' => true,
    'metrics_enabled' => true,
    'health_check_enabled' => true,
];

return $config;
