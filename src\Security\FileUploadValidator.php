<?php

declare(strict_types=1);

namespace WeBot\Security;

use WeBot\Exceptions\SecurityException;
use WeBot\Utils\Logger;
use Monolog\Logger as MonologLogger;

/**
 * Secure File Upload Validator
 *
 * Provides comprehensive file upload security validation:
 * - File type validation
 * - Content validation
 * - Size limits
 * - Malware scanning
 * - Path traversal prevention
 *
 * @package WeBot\Security
 * @version 2.0
 */
class FileUploadValidator
{
    private array $config;
    private MonologLogger $logger;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'max_size' => 10 * 1024 * 1024, // 10MB
            'allowed_types' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'txt'],
            'allowed_mime_types' => [
                'image/jpeg',
                'image/png',
                'image/gif',
                'application/pdf',
                'text/plain'
            ],
            'forbidden_extensions' => [
                'php', 'phtml', 'php3', 'php4', 'php5', 'php7', 'phps',
                'js', 'html', 'htm', 'shtml', 'shtm', 'xhtml',
                'exe', 'com', 'bat', 'cmd', 'scr', 'pif',
                'vbs', 'vbe', 'jse', 'wsf', 'wsh', 'msi'
            ],
            'scan_content' => true,
            'quarantine_suspicious' => true,
            'upload_path' => 'storage/uploads/',
            'temp_path' => 'storage/temp/',
        ], $config);

        $this->logger = Logger::getInstance();
    }

    /**
     * Validate uploaded file
     */
    public function validate(array $file): array
    {
        $result = [
            'valid' => false,
            'errors' => [],
            'warnings' => [],
            'file_info' => []
        ];

        try {
            // Basic file validation
            $this->validateBasicFile($file);

            // Size validation
            $this->validateFileSize($file);

            // Extension validation
            $this->validateFileExtension($file);

            // MIME type validation
            $this->validateMimeType($file);

            // Content validation
            if ($this->config['scan_content']) {
                $this->validateFileContent($file);
            }

            // Path traversal prevention
            $this->validateFileName($file);

            $result['valid'] = true;
            $result['file_info'] = $this->getFileInfo($file);
        } catch (SecurityException $e) {
            $result['errors'][] = $e->getMessage();
            $this->logger->warning('File upload validation failed', [
                'file' => $file['name'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);
        }

        return $result;
    }

    /**
     * Validate basic file properties
     */
    private function validateBasicFile(array $file): void
    {
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            throw new SecurityException('Invalid file upload');
        }

        if ($file['error'] !== UPLOAD_ERR_OK) {
            $errorMessage = $this->getUploadErrorMessage($file['error']);
            throw new SecurityException("Upload error: {$errorMessage}");
        }

        if (empty($file['name'])) {
            throw new SecurityException('File name is required');
        }
    }

    /**
     * Validate file size
     */
    private function validateFileSize(array $file): void
    {
        $size = $file['size'] ?? 0;

        if ($size <= 0) {
            throw new SecurityException('File is empty');
        }

        if ($size > $this->config['max_size']) {
            $maxSizeMB = round($this->config['max_size'] / 1024 / 1024, 2);
            throw new SecurityException("File size exceeds maximum allowed size of {$maxSizeMB}MB");
        }

        // Double-check actual file size
        $actualSize = filesize($file['tmp_name']);
        if ($actualSize !== $size) {
            throw new SecurityException('File size mismatch detected');
        }
    }

    /**
     * Validate file extension
     */
    private function validateFileExtension(array $file): void
    {
        $fileName = $file['name'];
        $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

        // Check forbidden extensions
        if (in_array($extension, $this->config['forbidden_extensions'])) {
            throw new SecurityException("File extension '{$extension}' is not allowed");
        }

        // Check allowed extensions
        if (!in_array($extension, $this->config['allowed_types'])) {
            $allowedTypes = implode(', ', $this->config['allowed_types']);
            throw new SecurityException("File extension '{$extension}' is not allowed. Allowed types: {$allowedTypes}");
        }

        // Check for double extensions
        if (substr_count($fileName, '.') > 1) {
            throw new SecurityException('Multiple file extensions are not allowed');
        }
    }

    /**
     * Validate MIME type
     */
    private function validateMimeType(array $file): void
    {
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        if (!in_array($mimeType, $this->config['allowed_mime_types'])) {
            throw new SecurityException("MIME type '{$mimeType}' is not allowed");
        }

        // Cross-check with reported MIME type
        $reportedMimeType = $file['type'] ?? '';
        if ($reportedMimeType !== $mimeType) {
            $this->logger->warning('MIME type mismatch detected', [
                'reported' => $reportedMimeType,
                'actual' => $mimeType,
                'file' => $file['name']
            ]);
        }
    }

    /**
     * Validate file content
     */
    private function validateFileContent(array $file): void
    {
        $content = file_get_contents($file['tmp_name'], false, null, 0, 1024); // Read first 1KB

        // Check for PHP tags
        if (strpos($content, '<?php') !== false || strpos($content, '<?=') !== false) {
            throw new SecurityException('PHP code detected in file');
        }

        // Check for script tags
        if (preg_match('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', $content)) {
            throw new SecurityException('Script tags detected in file');
        }

        // Check for suspicious patterns
        $suspiciousPatterns = [
            '/eval\s*\(/i',
            '/exec\s*\(/i',
            '/system\s*\(/i',
            '/shell_exec\s*\(/i',
            '/passthru\s*\(/i',
            '/base64_decode\s*\(/i',
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                throw new SecurityException('Suspicious content detected in file');
            }
        }

        // Image-specific validation
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
            $this->validateImageFile($file);
        }
    }

    /**
     * Validate image file
     */
    private function validateImageFile(array $file): void
    {
        $imageInfo = getimagesize($file['tmp_name']);

        if ($imageInfo === false) {
            throw new SecurityException('Invalid image file');
        }

        // Check image dimensions
        [$width, $height] = $imageInfo;
        $maxDimension = 4096; // 4K max

        if ($width > $maxDimension || $height > $maxDimension) {
            throw new SecurityException("Image dimensions too large. Maximum: {$maxDimension}x{$maxDimension}");
        }

        // Validate image type
        $allowedImageTypes = [IMAGETYPE_JPEG, IMAGETYPE_PNG, IMAGETYPE_GIF];
        if (!in_array($imageInfo[2], $allowedImageTypes)) {
            throw new SecurityException('Invalid image type');
        }
    }

    /**
     * Validate file name for path traversal
     */
    private function validateFileName(array $file): void
    {
        $fileName = $file['name'];

        // Check for path traversal attempts
        if (strpos($fileName, '..') !== false) {
            throw new SecurityException('Path traversal attempt detected');
        }

        if (strpos($fileName, '/') !== false || strpos($fileName, '\\') !== false) {
            throw new SecurityException('Directory separators not allowed in filename');
        }

        // Check for null bytes
        if (strpos($fileName, "\0") !== false) {
            throw new SecurityException('Null byte detected in filename');
        }

        // Validate filename length
        if (strlen($fileName) > 255) {
            throw new SecurityException('Filename too long');
        }

        // Check for reserved names (Windows)
        $reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'];
        $baseName = pathinfo($fileName, PATHINFO_FILENAME);

        if (in_array(strtoupper($baseName), $reservedNames)) {
            throw new SecurityException('Reserved filename not allowed');
        }
    }

    /**
     * Get file information
     */
    private function getFileInfo(array $file): array
    {
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        return [
            'name' => $file['name'],
            'size' => $file['size'],
            'type' => $mimeType,
            'extension' => strtolower(pathinfo($file['name'], PATHINFO_EXTENSION)),
            'hash' => hash_file('sha256', $file['tmp_name'])
        ];
    }

    /**
     * Get upload error message
     */
    private function getUploadErrorMessage(int $errorCode): string
    {
        $errors = [
            UPLOAD_ERR_INI_SIZE => 'File exceeds upload_max_filesize directive',
            UPLOAD_ERR_FORM_SIZE => 'File exceeds MAX_FILE_SIZE directive',
            UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
            UPLOAD_ERR_NO_FILE => 'No file was uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'File upload stopped by extension'
        ];

        return $errors[$errorCode] ?? 'Unknown upload error';
    }

    /**
     * Sanitize filename
     */
    public function sanitizeFileName(string $fileName): string
    {
        // Remove path components
        $fileName = basename($fileName);

        // Remove dangerous characters
        $fileName = preg_replace('/[^a-zA-Z0-9._-]/', '_', $fileName);

        // Limit length
        if (strlen($fileName) > 100) {
            $extension = pathinfo($fileName, PATHINFO_EXTENSION);
            $baseName = substr(pathinfo($fileName, PATHINFO_FILENAME), 0, 100 - strlen($extension) - 1);
            $fileName = $baseName . '.' . $extension;
        }

        return $fileName;
    }

    /**
     * Generate secure filename
     */
    public function generateSecureFileName(string $originalName): string
    {
        $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
        $hash = bin2hex(random_bytes(16));
        $timestamp = time();

        return "{$timestamp}_{$hash}.{$extension}";
    }
}
