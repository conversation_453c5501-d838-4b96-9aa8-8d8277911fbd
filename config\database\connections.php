<?php

declare(strict_types=1);

/**
 * Database Connections Configuration
 * 
 * Defines database connection settings for different environments
 * and database drivers with connection pooling and optimization.
 * 
 * @package WeBot\Config\Database
 * @version 2.0
 */

return [
    /*
    |--------------------------------------------------------------------------
    | Default Database Connection
    |--------------------------------------------------------------------------
    */
    'default' => $_ENV['DB_CONNECTION'] ?? 'mysql',

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    */
    'connections' => [
        'mysql' => [
            'driver' => 'mysql',
            'host' => $_ENV['DB_HOST'] ?? 'localhost',
            'port' => (int)($_ENV['DB_PORT'] ?? 3306),
            'database' => $_ENV['DB_DATABASE'] ?? 'webot',
            'username' => $_ENV['DB_USERNAME'] ?? 'root',
            'password' => $_ENV['DB_PASSWORD'] ?? '',
            'charset' => $_ENV['DB_CHARSET'] ?? 'utf8mb4',
            'collation' => $_ENV['DB_COLLATION'] ?? 'utf8mb4_unicode_ci',
            'prefix' => $_ENV['DB_PREFIX'] ?? '',
            'strict' => true,
            'engine' => 'InnoDB',
            'timezone' => '+00:00',
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_PERSISTENT => (bool)($_ENV['DB_PERSISTENT'] ?? true),
                PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci",
                PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
                PDO::ATTR_TIMEOUT => (int)($_ENV['DB_TIMEOUT'] ?? 30),
            ],
        ],

        'pgsql' => [
            'driver' => 'pgsql',
            'host' => $_ENV['DB_HOST'] ?? 'localhost',
            'port' => (int)($_ENV['DB_PORT'] ?? 5432),
            'database' => $_ENV['DB_DATABASE'] ?? 'webot',
            'username' => $_ENV['DB_USERNAME'] ?? 'postgres',
            'password' => $_ENV['DB_PASSWORD'] ?? '',
            'charset' => 'utf8',
            'prefix' => $_ENV['DB_PREFIX'] ?? '',
            'schema' => $_ENV['DB_SCHEMA'] ?? 'public',
            'sslmode' => $_ENV['DB_SSLMODE'] ?? 'prefer',
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_PERSISTENT => (bool)($_ENV['DB_PERSISTENT'] ?? true),
                PDO::ATTR_TIMEOUT => (int)($_ENV['DB_TIMEOUT'] ?? 30),
            ],
        ],

        'sqlite' => [
            'driver' => 'sqlite',
            'database' => $_ENV['DB_DATABASE'] ?? 'storage/database/webot.sqlite',
            'prefix' => $_ENV['DB_PREFIX'] ?? '',
            'foreign_key_constraints' => true,
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_TIMEOUT => (int)($_ENV['DB_TIMEOUT'] ?? 30),
            ],
        ],

        'testing' => [
            'driver' => 'sqlite',
            'database' => ':memory:',
            'prefix' => '',
            'foreign_key_constraints' => true,
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Connection Pool Configuration
    |--------------------------------------------------------------------------
    */
    'pool' => [
        'enabled' => (bool)($_ENV['DB_POOL_ENABLED'] ?? true),
        'min_connections' => (int)($_ENV['DB_POOL_MIN'] ?? 2),
        'max_connections' => (int)($_ENV['DB_POOL_MAX'] ?? 10),
        'idle_timeout' => (int)($_ENV['DB_POOL_IDLE_TIMEOUT'] ?? 300), // 5 minutes
        'max_lifetime' => (int)($_ENV['DB_POOL_MAX_LIFETIME'] ?? 3600), // 1 hour
        'validation_query' => 'SELECT 1',
        'validation_interval' => 30, // seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Query Configuration
    |--------------------------------------------------------------------------
    */
    'query' => [
        'log_queries' => (bool)($_ENV['DB_LOG_QUERIES'] ?? false),
        'slow_query_threshold' => (float)($_ENV['DB_SLOW_QUERY_THRESHOLD'] ?? 1.0), // seconds
        'max_query_time' => (int)($_ENV['DB_MAX_QUERY_TIME'] ?? 30), // seconds
        'explain_slow_queries' => (bool)($_ENV['DB_EXPLAIN_SLOW_QUERIES'] ?? false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Read/Write Splitting
    |--------------------------------------------------------------------------
    */
    'read_write_splitting' => [
        'enabled' => (bool)($_ENV['DB_READ_WRITE_SPLITTING'] ?? false),
        'write_connection' => 'mysql',
        'read_connections' => [
            'mysql_read_1' => [
                'driver' => 'mysql',
                'host' => $_ENV['DB_READ_HOST_1'] ?? $_ENV['DB_HOST'] ?? 'localhost',
                'port' => (int)($_ENV['DB_READ_PORT_1'] ?? $_ENV['DB_PORT'] ?? 3306),
                'database' => $_ENV['DB_DATABASE'] ?? 'webot',
                'username' => $_ENV['DB_READ_USERNAME_1'] ?? $_ENV['DB_USERNAME'] ?? 'root',
                'password' => $_ENV['DB_READ_PASSWORD_1'] ?? $_ENV['DB_PASSWORD'] ?? '',
                'charset' => $_ENV['DB_CHARSET'] ?? 'utf8mb4',
                'collation' => $_ENV['DB_COLLATION'] ?? 'utf8mb4_unicode_ci',
            ],
        ],
        'sticky' => true, // Use write connection for subsequent reads in same request
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup Configuration
    |--------------------------------------------------------------------------
    */
    'backup' => [
        'enabled' => (bool)($_ENV['DB_BACKUP_ENABLED'] ?? true),
        'path' => $_ENV['DB_BACKUP_PATH'] ?? 'storage/backups',
        'schedule' => $_ENV['DB_BACKUP_SCHEDULE'] ?? '0 2 * * *', // Daily at 2 AM
        'retention_days' => (int)($_ENV['DB_BACKUP_RETENTION'] ?? 30),
        'compression' => (bool)($_ENV['DB_BACKUP_COMPRESSION'] ?? true),
        'encryption' => (bool)($_ENV['DB_BACKUP_ENCRYPTION'] ?? false),
        'encryption_key' => $_ENV['DB_BACKUP_ENCRYPTION_KEY'] ?? '',
        'exclude_tables' => [
            'sessions',
            'telegram_updates',
            'system_logs'
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Optimization
    |--------------------------------------------------------------------------
    */
    'optimization' => [
        'query_cache' => [
            'enabled' => (bool)($_ENV['DB_QUERY_CACHE_ENABLED'] ?? true),
            'ttl' => (int)($_ENV['DB_QUERY_CACHE_TTL'] ?? 300), // 5 minutes
            'max_size' => (int)($_ENV['DB_QUERY_CACHE_MAX_SIZE'] ?? 1000),
        ],
        'prepared_statements' => [
            'enabled' => true,
            'cache_size' => 100,
        ],
        'connection_recycling' => [
            'enabled' => true,
            'max_requests_per_connection' => 1000,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring & Alerting
    |--------------------------------------------------------------------------
    */
    'monitoring' => [
        'enabled' => (bool)($_ENV['DB_MONITORING_ENABLED'] ?? true),
        'metrics' => [
            'connection_count' => true,
            'query_count' => true,
            'slow_queries' => true,
            'failed_queries' => true,
            'connection_errors' => true,
        ],
        'alerts' => [
            'slow_query_threshold' => 2.0, // seconds
            'connection_failure_threshold' => 5, // failures per minute
            'high_connection_count_threshold' => 80, // percentage of max connections
        ],
        'retention_hours' => 24,
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    */
    'security' => [
        'ssl' => [
            'enabled' => (bool)($_ENV['DB_SSL_ENABLED'] ?? false),
            'ca_cert' => $_ENV['DB_SSL_CA'] ?? '',
            'client_cert' => $_ENV['DB_SSL_CERT'] ?? '',
            'client_key' => $_ENV['DB_SSL_KEY'] ?? '',
            'verify_server_cert' => (bool)($_ENV['DB_SSL_VERIFY'] ?? true),
        ],
        'encryption' => [
            'enabled' => (bool)($_ENV['DB_ENCRYPTION_ENABLED'] ?? false),
            'key' => $_ENV['DB_ENCRYPTION_KEY'] ?? '',
            'algorithm' => $_ENV['DB_ENCRYPTION_ALGORITHM'] ?? 'AES-256-CBC',
        ],
        'access_control' => [
            'allowed_hosts' => explode(',', $_ENV['DB_ALLOWED_HOSTS'] ?? ''),
            'blocked_hosts' => explode(',', $_ENV['DB_BLOCKED_HOSTS'] ?? ''),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Development Configuration
    |--------------------------------------------------------------------------
    */
    'development' => [
        'debug_queries' => (bool)($_ENV['DB_DEBUG_QUERIES'] ?? false),
        'explain_queries' => (bool)($_ENV['DB_EXPLAIN_QUERIES'] ?? false),
        'query_profiling' => (bool)($_ENV['DB_QUERY_PROFILING'] ?? false),
        'schema_validation' => (bool)($_ENV['DB_SCHEMA_VALIDATION'] ?? true),
    ],
];
