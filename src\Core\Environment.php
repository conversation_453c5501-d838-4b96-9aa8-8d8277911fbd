<?php

declare(strict_types=1);

namespace WeBot\Core;

/**
 * Environment Manager
 *
 * Manages environment variables, configuration validation,
 * and secure environment handling for WeBot.
 *
 * @package WeBot\Core
 * @version 2.0
 */
class Environment
{
    private static ?self $instance = null;
    private array $variables = [];
    private array $required = [];
    private array $defaults = [];
    private bool $loaded = false;

    private function __construct()
    {
        $this->defineRequiredVariables();
        $this->defineDefaultValues();
    }

    /**
     * Get singleton instance
     */
    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Load environment variables
     */
    public function load(string $envFile = '.env'): void
    {
        if ($this->loaded) {
            return;
        }

        // Load from system environment first
        $this->loadSystemEnvironment();

        // Load from .env file if exists
        if (file_exists($envFile)) {
            $this->loadFromFile($envFile);
        }

        // Apply defaults for missing variables
        $this->applyDefaults();

        // Validate required variables
        $this->validateRequired();

        $this->loaded = true;
    }

    /**
     * Get environment variable
     */
    public function get(string $key, $default = null)
    {
        if (!$this->loaded) {
            $this->load();
        }

        return $this->variables[$key] ?? $default;
    }

    /**
     * Set environment variable
     */
    public function set(string $key, $value): void
    {
        $this->variables[$key] = $value;
        putenv("{$key}={$value}");
        $_ENV[$key] = $value;
    }

    /**
     * Check if environment variable exists
     */
    public function has(string $key): bool
    {
        if (!$this->loaded) {
            $this->load();
        }

        return isset($this->variables[$key]);
    }

    /**
     * Get environment name
     */
    public function getEnvironment(): string
    {
        return $this->get('APP_ENV', 'production');
    }

    /**
     * Check if in production
     */
    public function isProduction(): bool
    {
        return $this->getEnvironment() === 'production';
    }

    /**
     * Check if in development
     */
    public function isDevelopment(): bool
    {
        return $this->getEnvironment() === 'development';
    }

    /**
     * Check if in testing
     */
    public function isTesting(): bool
    {
        return $this->getEnvironment() === 'testing';
    }

    /**
     * Check if debug mode is enabled
     */
    public function isDebug(): bool
    {
        return $this->getBool('APP_DEBUG', false);
    }

    /**
     * Get boolean value
     */
    public function getBool(string $key, bool $default = false): bool
    {
        $value = $this->get($key, $default);

        if (is_bool($value)) {
            return $value;
        }

        return in_array(strtolower((string) $value), ['true', '1', 'yes', 'on'], true);
    }

    /**
     * Get integer value
     */
    public function getInt(string $key, int $default = 0): int
    {
        return (int) $this->get($key, $default);
    }

    /**
     * Get float value
     */
    public function getFloat(string $key, float $default = 0.0): float
    {
        return (float) $this->get($key, $default);
    }

    /**
     * Get array value (comma-separated)
     */
    public function getArray(string $key, array $default = []): array
    {
        $value = $this->get($key);

        if ($value === null) {
            return $default;
        }

        if (is_array($value)) {
            return $value;
        }

        return array_filter(array_map('trim', explode(',', (string) $value)));
    }

    /**
     * Load system environment variables
     */
    private function loadSystemEnvironment(): void
    {
        $this->variables = array_merge($this->variables, $_ENV, getenv());
    }

    /**
     * Load environment from file
     */
    private function loadFromFile(string $file): void
    {
        $lines = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);

        foreach ($lines as $line) {
            $line = trim($line);

            // Skip comments and empty lines
            if (empty($line) || str_starts_with($line, '#')) {
                continue;
            }

            // Parse key=value
            if (str_contains($line, '=')) {
                [$key, $value] = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value);

                // Remove quotes
                if (preg_match('/^(["\'])(.*)\\1$/', $value, $matches)) {
                    $value = $matches[2];
                }

                $this->set($key, $value);
            }
        }
    }

    /**
     * Apply default values
     */
    private function applyDefaults(): void
    {
        foreach ($this->defaults as $key => $value) {
            if (!isset($this->variables[$key])) {
                $this->set($key, $value);
            }
        }
    }

    /**
     * Validate required variables
     */
    private function validateRequired(): void
    {
        $missing = [];

        foreach ($this->required as $key) {
            if (!isset($this->variables[$key]) || $this->variables[$key] === '') {
                $missing[] = $key;
            }
        }

        if (!empty($missing)) {
            throw new \RuntimeException(
                'Missing required environment variables: ' . implode(', ', $missing)
            );
        }
    }

    /**
     * Define required environment variables
     */
    private function defineRequiredVariables(): void
    {
        $this->required = [
            'APP_ENV',
            'DB_HOST',
            'DB_DATABASE',
            'DB_USERNAME',
            'DB_PASSWORD',
            'TELEGRAM_BOT_TOKEN'
        ];
    }

    /**
     * Define default values
     */
    private function defineDefaultValues(): void
    {
        $this->defaults = [
            'APP_ENV' => 'production',
            'APP_DEBUG' => 'false',
            'APP_TIMEZONE' => 'Asia/Tehran',
            'APP_LOCALE' => 'fa',
            'DB_PORT' => '3306',
            'DB_CHARSET' => 'utf8mb4',
            'DB_COLLATION' => 'utf8mb4_unicode_ci',
            'REDIS_HOST' => 'redis',
            'REDIS_PORT' => '6379',
            'REDIS_DB' => '0',
            'CACHE_DRIVER' => 'redis',
            'SESSION_DRIVER' => 'redis',
            'SESSION_LIFETIME' => '120',
            'LOG_LEVEL' => 'warning',
            'RATE_LIMIT_API' => '60',
            'RATE_LIMIT_WEBHOOK' => '1000',
            'UPLOAD_MAX_SIZE' => '52428800',
            'BCRYPT_ROUNDS' => '12'
        ];
    }

    /**
     * Get all environment variables (for debugging)
     */
    public function all(): array
    {
        if (!$this->loaded) {
            $this->load();
        }

        // Filter sensitive variables in production
        if ($this->isProduction()) {
            return $this->filterSensitive($this->variables);
        }

        return $this->variables;
    }

    /**
     * Filter sensitive environment variables
     */
    private function filterSensitive(array $variables): array
    {
        $sensitive = [
            'DB_PASSWORD',
            'REDIS_PASSWORD',
            'TELEGRAM_BOT_TOKEN',
            'ZARINPAL_MERCHANT_ID',
            'NOWPAYMENTS_API_KEY',
            'APP_KEY',
            'MAIL_PASSWORD'
        ];

        $filtered = [];
        foreach ($variables as $key => $value) {
            if (in_array($key, $sensitive)) {
                $filtered[$key] = '***HIDDEN***';
            } else {
                $filtered[$key] = $value;
            }
        }

        return $filtered;
    }

    /**
     * Validate environment configuration
     */
    public function validate(): array
    {
        $errors = [];

        // Check database configuration
        if (!$this->get('DB_HOST')) {
            $errors[] = 'Database host not configured';
        }

        // Check Telegram configuration
        if (!$this->get('TELEGRAM_BOT_TOKEN')) {
            $errors[] = 'Telegram bot token not configured';
        }

        // Check production-specific requirements
        if ($this->isProduction()) {
            if ($this->isDebug()) {
                $errors[] = 'Debug mode should be disabled in production';
            }

            if (!$this->getBool('SSL_ENABLED')) {
                $errors[] = 'SSL should be enabled in production';
            }
        }

        return $errors;
    }

    /**
     * Generate secure random key
     */
    public static function generateKey(): string
    {
        return bin2hex(random_bytes(32));
    }
}
