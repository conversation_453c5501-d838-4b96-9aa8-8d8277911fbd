<?php

declare(strict_types=1);

namespace WeBot\Controllers;

use WeBot\Core\DIContainer;

/**
 * User Controller
 *
 * Handles user-related actions like registration, profile, etc.
 *
 * @package WeBot\Controllers
 * @version 2.0
 */
class UserController extends BaseController
{
    /**
     * Handle /start command
     */
    public function handleStart(array $message): array
    {
        $this->initialize(['message' => $message]);
        $this->logger->info('Start command received', ['user_id' => $this->fromId]);

        $user = $this->db->selectOne('users', ['userid' => $this->fromId]);

        if (!$user) {
            return $this->registerNewUser();
        }

        return $this->showMainMenu($user);
    }

    private function registerNewUser(): array
    {
        $this->db->insert('users', [
            'userid' => $this->fromId,
            'first_name' => $this->message['from']['first_name'] ?? '',
            'last_name' => $this->message['from']['last_name'] ?? '',
            'username' => $this->message['from']['username'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ]);

        $this->logger->info('New user registered', ['user_id' => $this->fromId]);

        return $this->sendMessage('Welcome!');
    }

    private function showMainMenu(array $user): array
    {
        return $this->sendMessage("Welcome back, {$user['first_name']}!");
    }

    /**
     * Handle general messages
     */
    public function handleMessage(array $message): array
    {
        $this->initialize(['message' => $message]);
        $text = $message['text'] ?? '';

        // Handle commands
        if (str_starts_with($text, '/')) {
            return $this->handleCommand($text, $message);
        }

        // Handle phone number validation
        if (preg_match('/^\+?[1-9]\d{1,14}$/', $text)) {
            return $this->sendMessage('شماره تلفن شما تایید شد.');
        }

        // Handle invalid input
        return $this->sendMessage('ورودی نامعتبر است. لطفاً دستور معتبر وارد کنید.');
    }

    /**
     * Handle callback queries
     */
    public function handleCallback(array $callbackQuery): array
    {
        $this->initialize(['callback_query' => $callbackQuery]);
        $data = $callbackQuery['data'] ?? '';

        switch ($data) {
            case 'profile':
                return $this->showProfile();
            case 'services':
                return $this->showServices();
            case 'support':
                return $this->showSupport();
            default:
                return $this->sendMessage('گزینه نامعتبر است.');
        }
    }

    /**
     * Handle commands
     */
    private function handleCommand(string $command, array $message): array
    {
        switch ($command) {
            case '/start':
                return $this->handleStart($message);
            case '/menu':
                return $this->showMainMenuWithKeyboard();
            case '/profile':
                return $this->showProfile();
            default:
                return $this->sendMessage('دستور نامعتبر است.');
        }
    }

    /**
     * Show main menu with keyboard
     */
    private function showMainMenuWithKeyboard(): array
    {
        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => 'پروفایل', 'callback_data' => 'profile'],
                    ['text' => 'سرویس‌ها', 'callback_data' => 'services']
                ],
                [
                    ['text' => 'پشتیبانی', 'callback_data' => 'support']
                ]
            ]
        ];

        return $this->sendMessage('منوی اصلی:', $keyboard, 'HTML');
    }

    /**
     * Show user profile
     */
    private function showProfile(): array
    {
        $user = $this->db->selectOne('users', ['userid' => $this->fromId]);

        if (!$user) {
            return $this->sendMessage('کاربر یافت نشد.');
        }

        $profileText = "پروفایل شما:\n";
        $profileText .= "نام: {$user['first_name']}\n";
        $profileText .= "نام کاربری: {$user['username']}\n";

        return $this->sendMessage($profileText);
    }

    /**
     * Show services
     */
    private function showServices(): array
    {
        return $this->sendMessage('لیست سرویس‌های موجود...');
    }

    /**
     * Show support
     */
    private function showSupport(): array
    {
        return $this->sendMessage('برای پشتیبانی با ما تماس بگیرید.');
    }
}
