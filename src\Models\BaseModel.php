<?php

declare(strict_types=1);

namespace WeBot\Models;

use WeBot\Services\DatabaseService;
use WeBot\Utils\Validator;

/**
 * Base Model
 *
 * Base class for all WeBot models providing common functionality
 * including validation, timestamps, and basic CRUD operations.
 *
 * @package WeBot\Models
 * @version 2.0
 */
abstract class BaseModel
{
    protected DatabaseService $database;
    protected string $table = '';
    protected string $primaryKey = 'id';
    protected array $fillable = [];
    protected array $hidden = [];
    protected array $casts = [];
    protected array $dates = ['created_at', 'updated_at'];
    protected bool $timestamps = true;

    protected array $attributes = [];
    protected array $original = [];
    protected bool $exists = false;

    public function __construct($database, array $attributes = [])
    {
        $this->database = $database;

        if (!empty($attributes)) {
            $this->fill($attributes);
            $this->exists = true;
            $this->original = $this->attributes;
        }
    }

    /**
     * Fill model with attributes
     */
    public function fill(array $attributes): self
    {
        foreach ($attributes as $key => $value) {
            if ($this->isFillable($key)) {
                $this->setAttribute($key, $value);
            }
        }

        return $this;
    }

    /**
     * Set attribute value
     */
    public function setAttribute(string $key, $value): self
    {
        // Cast value if needed
        if (isset($this->casts[$key])) {
            $value = $this->castAttribute($key, $value);
        }

        $this->attributes[$key] = $value;

        return $this;
    }

    /**
     * Get attribute value
     */
    public function getAttribute(string $key): mixed
    {
        if (!array_key_exists($key, $this->attributes)) {
            return null;
        }

        $value = $this->attributes[$key];

        // Cast value if needed
        if (isset($this->casts[$key])) {
            $value = $this->castAttribute($key, $value);
        }

        return $value;
    }

    /**
     * Check if attribute is fillable
     */
    protected function isFillable(string $key): bool
    {
        return in_array($key, $this->fillable) || empty($this->fillable);
    }

    /**
     * Cast attribute to specified type
     */
    protected function castAttribute(string $key, $value)
    {
        $castType = $this->casts[$key];

        if ($value === null) {
            return null;
        }

        return match ($castType) {
            'int', 'integer' => (int) $value,
            'float', 'double' => (float) $value,
            'string' => (string) $value,
            'bool', 'boolean' => (bool) $value,
            'array' => is_string($value) ? json_decode($value, true) : (array) $value,
            'json' => is_string($value) ? json_decode($value, true) : $value,
            'datetime' => is_string($value) ? new \DateTime($value) : $value,
            default => $value
        };
    }

    /**
     * Save model to database
     */
    public function save(): bool
    {
        // Validate before saving
        $this->validate();

        if ($this->exists) {
            return $this->performUpdate();
        } else {
            return $this->performInsert();
        }
    }

    /**
     * Perform insert operation
     */
    protected function performInsert(): bool
    {
        $attributes = $this->getAttributesForInsert();

        if ($this->timestamps) {
            $now = date('Y-m-d H:i:s');
            $attributes['created_at'] = $now;
            $attributes['updated_at'] = $now;
        }

        $id = $this->database->insert($this->table, $attributes);

        if ($id) {
            $this->setAttribute($this->primaryKey, $id);
            $this->exists = true;
            $this->original = $this->attributes;
            return true;
        }

        return false;
    }

    /**
     * Perform update operation
     */
    protected function performUpdate(): bool
    {
        $dirty = $this->getDirtyAttributes();

        if (empty($dirty)) {
            return true; // No changes to save
        }

        if ($this->timestamps) {
            $dirty['updated_at'] = date('Y-m-d H:i:s');
        }

        $where = [$this->primaryKey => $this->getAttribute($this->primaryKey)];
        $affected = $this->database->update($this->table, $dirty, $where);

        if ($affected > 0) {
            $this->original = $this->attributes;
            return true;
        }

        return false;
    }

    /**
     * Delete model from database
     */
    public function delete(): bool
    {
        if (!$this->exists) {
            return false;
        }

        $where = [$this->primaryKey => $this->getAttribute($this->primaryKey)];
        $affected = $this->database->delete($this->table, $where);

        if ($affected > 0) {
            $this->exists = false;
            return true;
        }

        return false;
    }

    /**
     * Get attributes for insert
     */
    protected function getAttributesForInsert(): array
    {
        $attributes = $this->attributes;

        // Remove primary key if it's auto-increment
        if (isset($attributes[$this->primaryKey]) && empty($attributes[$this->primaryKey])) {
            unset($attributes[$this->primaryKey]);
        }

        return $this->prepareAttributesForDatabase($attributes);
    }

    /**
     * Get dirty (changed) attributes
     */
    protected function getDirtyAttributes(): array
    {
        $dirty = [];

        foreach ($this->attributes as $key => $value) {
            if (!array_key_exists($key, $this->original) || $this->original[$key] !== $value) {
                $dirty[$key] = $value;
            }
        }

        return $this->prepareAttributesForDatabase($dirty);
    }

    /**
     * Prepare attributes for database storage
     */
    protected function prepareAttributesForDatabase(array $attributes): array
    {
        foreach ($attributes as $key => $value) {
            if (isset($this->casts[$key])) {
                $castType = $this->casts[$key];

                if (in_array($castType, ['array', 'json']) && is_array($value)) {
                    $attributes[$key] = json_encode($value);
                } elseif ($castType === 'datetime' && $value instanceof \DateTime) {
                    $attributes[$key] = $value->format('Y-m-d H:i:s');
                }
            }
        }

        return $attributes;
    }

    /**
     * Validate model attributes
     */
    protected function validate(): void
    {
        $rules = $this->getValidationRules();

        if (empty($rules)) {
            return;
        }

        $validator = Validator::make($this->attributes, $rules);
        $validator->validateOrFail();
    }

    /**
     * Get validation rules (to be overridden in child classes)
     */
    protected function getValidationRules(): array
    {
        return [];
    }

    /**
     * Convert model to array
     */
    public function toArray(): array
    {
        $attributes = $this->attributes;

        // Remove hidden attributes
        foreach ($this->hidden as $hidden) {
            unset($attributes[$hidden]);
        }

        // Cast attributes for output
        foreach ($attributes as $key => $value) {
            if (isset($this->casts[$key])) {
                $attributes[$key] = $this->castAttribute($key, $value);
            }
        }

        return $attributes;
    }

    /**
     * Convert model to JSON
     */
    public function toJson(): string
    {
        return json_encode($this->toArray(), JSON_UNESCAPED_UNICODE);
    }

    /**
     * Check if model exists in database
     */
    public function exists(): bool
    {
        return $this->exists;
    }

    /**
     * Set exists status
     */
    public function setExists(bool $exists): self
    {
        $this->exists = $exists;
        return $this;
    }

    /**
     * Get primary key value
     */
    public function getKey()
    {
        return $this->getAttribute($this->primaryKey);
    }

    /**
     * Get table name
     */
    public function getTable(): string
    {
        return $this->table;
    }

    /**
     * Get primary key name
     */
    public function getKeyName(): string
    {
        return $this->primaryKey;
    }

    /**
     * Refresh model from database
     */
    public function refresh(): self
    {
        if (!$this->exists) {
            return $this;
        }

        $sql = "SELECT * FROM `{$this->table}` WHERE `{$this->primaryKey}` = ?";
        $fresh = $this->database->fetchRow($sql, [$this->getKey()]);

        if ($fresh) {
            $this->attributes = $fresh;
            $this->original = $fresh;
        }

        return $this;
    }

    /**
     * Create new instance
     */
    public static function create(DatabaseService $database, array $attributes): static
    {
        $instance = new static($database);
        $instance->fill($attributes);
        $instance->save();

        return $instance;
    }

    /**
     * Find by primary key
     */
    public static function find(DatabaseService $database, $id): ?static
    {
        $instance = new static($database);

        $sql = "SELECT * FROM `{$instance->table}` WHERE `{$instance->primaryKey}` = ?";
        $attributes = $database->fetchRow($sql, [$id]);

        if ($attributes) {
            return new static($database, $attributes);
        }

        return null;
    }

    /**
     * Find by attributes
     */
    public static function where(DatabaseService $database, array $conditions): array
    {
        $instance = new static($database);

        $whereParts = [];
        $params = [];
        $types = '';

        foreach ($conditions as $key => $value) {
            $whereParts[] = "`{$key}` = ?";
            $params[] = $value;
            $types .= is_int($value) ? 'i' : 's';
        }

        $sql = "SELECT * FROM `{$instance->table}` WHERE " . implode(' AND ', $whereParts);
        $results = $database->fetchAll($sql, $params, $types);

        $models = [];
        foreach ($results as $attributes) {
            $models[] = new static($database, $attributes);
        }

        return $models;
    }

    /**
     * Get all records
     */
    public static function all(DatabaseService $database): array
    {
        $instance = new static($database);

        $sql = "SELECT * FROM `{$instance->table}`";
        $results = $database->fetchAll($sql);

        $models = [];
        foreach ($results as $attributes) {
            $models[] = new static($database, $attributes);
        }

        return $models;
    }

    /**
     * Magic getter
     */
    public function __get(string $key)
    {
        return $this->getAttribute($key);
    }

    /**
     * Magic setter
     */
    public function __set(string $key, $value): void
    {
        $this->setAttribute($key, $value);
    }

    /**
     * Magic isset
     */
    public function __isset(string $key): bool
    {
        return array_key_exists($key, $this->attributes);
    }

    /**
     * Magic unset
     */
    public function __unset(string $key): void
    {
        unset($this->attributes[$key]);
    }

    /**
     * Magic toString
     */
    public function __toString(): string
    {
        return $this->toJson();
    }
}
