<?php

declare(strict_types=1);

namespace WeBot\Core;

/**
 * DDoS Protection
 *
 * Advanced DDoS protection with pattern detection,
 * automatic mitigation, and intelligent blocking.
 *
 * @package WeBot\Core
 * @version 2.0
 */
class DDoSProtection
{
    private RateLimiter $rateLimiter;
    private CacheManager $cache;
    private array $config;
    private array $patterns;

    public function __construct(
        RateLimiter $rateLimiter,
        CacheManager $cache,
        array $config = []
    ) {
        $this->rateLimiter = $rateLimiter;
        $this->cache = $cache;
        $this->config = array_merge([
            'enabled' => true,
            'detection_window' => 300, // 5 minutes
            'min_requests_for_detection' => 100,
            'suspicious_threshold' => 0.8,
            'auto_ban_enabled' => true,
            'ban_duration' => 3600, // 1 hour
            'whitelist_ips' => [],
            'patterns' => [
                'burst_requests' => ['threshold' => 50, 'window' => 60],
                'distributed_attack' => ['threshold' => 1000, 'window' => 300],
                'slow_requests' => ['threshold' => 10, 'duration' => 30],
                'bot_behavior' => ['user_agent_patterns' => ['bot', 'crawler', 'spider']]
            ]
        ], $config);

        $this->initializePatterns();
    }

    /**
     * Analyze request for DDoS patterns
     */
    public function analyzeRequest(array $requestData): array
    {
        if (!$this->config['enabled']) {
            return ['threat_level' => 'none', 'action' => 'allow'];
        }

        $ip = $requestData['ip'] ?? $this->getClientIP();
        $userAgent = $requestData['user_agent'] ?? ($_SERVER['HTTP_USER_AGENT'] ?? '');
        $timestamp = $requestData['timestamp'] ?? time();

        // Skip whitelisted IPs
        if (in_array($ip, $this->config['whitelist_ips'])) {
            return ['threat_level' => 'none', 'action' => 'allow', 'reason' => 'whitelisted'];
        }

        $analysis = [
            'ip' => $ip,
            'timestamp' => $timestamp,
            'patterns_detected' => [],
            'threat_level' => 'none',
            'action' => 'allow',
            'confidence' => 0.0
        ];

        // Run pattern detection
        foreach ($this->patterns as $patternName => $pattern) {
            $result = $pattern['detector']($ip, $userAgent, $timestamp);

            if ($result['detected']) {
                $analysis['patterns_detected'][] = [
                    'name' => $patternName,
                    'confidence' => $result['confidence'],
                    'details' => $result['details']
                ];
            }
        }

        // Calculate overall threat level
        $analysis = $this->calculateThreatLevel($analysis);

        // Determine action
        $analysis['action'] = $this->determineAction($analysis);

        // Store analysis
        $this->storeAnalysis($analysis);

        return $analysis;
    }

    /**
     * Detect burst attack pattern
     */
    public function detectBurstAttack(string $ip): array
    {
        $window = $this->config['patterns']['burst_requests']['window'];
        $threshold = $this->config['patterns']['burst_requests']['threshold'];

        $key = "burst_detection:{$ip}";
        $requests = $this->cache->get($key, []);

        $now = time();
        $windowStart = $now - $window;

        // Filter recent requests
        $recentRequests = array_filter($requests, fn($timestamp) => $timestamp > $windowStart);

        $detected = count($recentRequests) > $threshold;
        $confidence = min(1.0, count($recentRequests) / ($threshold * 2));

        return [
            'detected' => $detected,
            'confidence' => $confidence,
            'details' => [
                'requests_count' => count($recentRequests),
                'threshold' => $threshold,
                'window' => $window
            ]
        ];
    }

    /**
     * Detect distributed attack
     */
    public function detectDistributedAttack(): array
    {
        $window = $this->config['patterns']['distributed_attack']['window'];
        $threshold = $this->config['patterns']['distributed_attack']['threshold'];

        $key = "distributed_detection";
        $stats = $this->cache->get($key, ['total_requests' => 0, 'unique_ips' => []]);

        $now = time();
        $windowStart = $now - $window;

        // Get recent requests from all IPs
        $totalRequests = $stats['total_requests'];
        $uniqueIPs = count($stats['unique_ips']);

        $detected = $totalRequests > $threshold && $uniqueIPs > 10;
        $confidence = min(1.0, $totalRequests / ($threshold * 1.5));

        return [
            'detected' => $detected,
            'confidence' => $confidence,
            'details' => [
                'total_requests' => $totalRequests,
                'unique_ips' => $uniqueIPs,
                'threshold' => $threshold,
                'requests_per_ip' => $uniqueIPs > 0 ? $totalRequests / $uniqueIPs : 0
            ]
        ];
    }

    /**
     * Detect bot behavior
     */
    public function detectBotBehavior(string $userAgent): array
    {
        $patterns = $this->config['patterns']['bot_behavior']['user_agent_patterns'];

        $detected = false;
        $matchedPattern = null;

        foreach ($patterns as $pattern) {
            if (stripos($userAgent, $pattern) !== false) {
                $detected = true;
                $matchedPattern = $pattern;
                break;
            }
        }

        // Additional bot detection heuristics
        $suspiciousPatterns = [
            'empty_user_agent' => empty($userAgent),
            'common_bot_strings' => preg_match('/bot|crawler|spider|scraper/i', $userAgent),
            'suspicious_version' => preg_match('/1\.0|0\.0/i', $userAgent)
        ];

        $suspiciousCount = array_sum($suspiciousPatterns);
        $confidence = $detected ? 0.9 : ($suspiciousCount * 0.3);

        return [
            'detected' => $detected || $suspiciousCount >= 2,
            'confidence' => min(1.0, $confidence),
            'details' => [
                'matched_pattern' => $matchedPattern,
                'suspicious_indicators' => $suspiciousPatterns,
                'user_agent' => $userAgent
            ]
        ];
    }

    /**
     * Get real-time attack statistics
     */
    public function getAttackStatistics(): array
    {
        $window = 300; // 5 minutes
        $now = time();

        $stats = [
            'timestamp' => $now,
            'window_seconds' => $window,
            'total_requests' => 0,
            'blocked_requests' => 0,
            'unique_attackers' => 0,
            'attack_patterns' => [],
            'top_attackers' => [],
            'geographic_distribution' => []
        ];

        // Get recent analyses
        $analyses = $this->getRecentAnalyses($window);

        foreach ($analyses as $analysis) {
            $stats['total_requests']++;

            if ($analysis['action'] === 'block') {
                $stats['blocked_requests']++;
            }

            foreach ($analysis['patterns_detected'] as $pattern) {
                $patternName = $pattern['name'];
                if (!isset($stats['attack_patterns'][$patternName])) {
                    $stats['attack_patterns'][$patternName] = 0;
                }
                $stats['attack_patterns'][$patternName]++;
            }
        }

        // Calculate metrics
        $stats['block_rate'] = $stats['total_requests'] > 0 ?
            ($stats['blocked_requests'] / $stats['total_requests']) * 100 : 0;

        $stats['requests_per_minute'] = $stats['total_requests'] / ($window / 60);

        return $stats;
    }

    /**
     * Implement automatic mitigation
     */
    public function implementMitigation(array $analysis): array
    {
        $actions = [];

        switch ($analysis['threat_level']) {
            case 'critical':
                // Immediate ban
                $this->rateLimiter->ban($analysis['ip'], $this->config['ban_duration']);
                $actions[] = 'ip_banned';

                // Alert administrators
                $this->sendAlert($analysis);
                $actions[] = 'alert_sent';
                break;

            case 'high':
                // Temporary rate limiting
                $this->rateLimiter->blacklist($analysis['ip']);
                $actions[] = 'ip_blacklisted';
                break;

            case 'medium':
                // Increased rate limiting
                $this->applyStrictRateLimit($analysis['ip']);
                $actions[] = 'strict_rate_limit';
                break;

            case 'low':
                // Monitor closely
                $this->addToWatchlist($analysis['ip']);
                $actions[] = 'added_to_watchlist';
                break;
        }

        return [
            'mitigation_applied' => true,
            'actions' => $actions,
            'timestamp' => time()
        ];
    }

    /**
     * Generate DDoS protection report
     */
    public function generateReport(): array
    {
        return [
            'timestamp' => time(),
            'protection_status' => $this->config['enabled'] ? 'active' : 'disabled',
            'statistics' => $this->getAttackStatistics(),
            'configuration' => $this->config,
            'recent_attacks' => $this->getRecentAttacks(),
            'mitigation_effectiveness' => $this->calculateMitigationEffectiveness()
        ];
    }

    /**
     * Initialize pattern detectors
     */
    private function initializePatterns(): void
    {
        $this->patterns = [
            'burst_requests' => [
                'detector' => function ($ip, $userAgent, $timestamp) {
                    return $this->detectBurstAttack($ip);
                }
            ],
            'distributed_attack' => [
                'detector' => function ($ip, $userAgent, $timestamp) {
                    return $this->detectDistributedAttack();
                }
            ],
            'bot_behavior' => [
                'detector' => function ($ip, $userAgent, $timestamp) {
                    return $this->detectBotBehavior($userAgent);
                }
            ]
        ];
    }

    /**
     * Calculate threat level
     */
    private function calculateThreatLevel(array $analysis): array
    {
        $totalConfidence = 0;
        $patternCount = count($analysis['patterns_detected']);

        foreach ($analysis['patterns_detected'] as $pattern) {
            $totalConfidence += $pattern['confidence'];
        }

        $averageConfidence = $patternCount > 0 ? $totalConfidence / $patternCount : 0;
        $analysis['confidence'] = $averageConfidence;

        if ($averageConfidence >= 0.9 || $patternCount >= 3) {
            $analysis['threat_level'] = 'critical';
        } elseif ($averageConfidence >= 0.7 || $patternCount >= 2) {
            $analysis['threat_level'] = 'high';
        } elseif ($averageConfidence >= 0.5 || $patternCount >= 1) {
            $analysis['threat_level'] = 'medium';
        } elseif ($averageConfidence >= 0.3) {
            $analysis['threat_level'] = 'low';
        }

        return $analysis;
    }

    /**
     * Determine action based on threat level
     */
    private function determineAction(array $analysis): string
    {
        if (!$this->config['auto_ban_enabled']) {
            return 'monitor';
        }

        switch ($analysis['threat_level']) {
            case 'critical':
            case 'high':
                return 'block';
            case 'medium':
                return 'limit';
            case 'low':
                return 'monitor';
            default:
                return 'allow';
        }
    }

    /**
     * Store analysis for future reference
     */
    private function storeAnalysis(array $analysis): void
    {
        $key = "ddos_analysis:" . date('Y-m-d-H');
        $analyses = $this->cache->get($key, []);
        $analyses[] = $analysis;

        // Keep only last 1000 analyses per hour
        if (count($analyses) > 1000) {
            $analyses = array_slice($analyses, -1000);
        }

        $this->cache->set($key, $analyses, 3600);
    }

    /**
     * Get client IP address
     */
    private function getClientIP(): string
    {
        $headers = [
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_REAL_IP',
            'HTTP_CLIENT_IP',
            'REMOTE_ADDR'
        ];

        foreach ($headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ips = explode(',', $_SERVER[$header]);
                return trim($ips[0]);
            }
        }

        return 'unknown';
    }

    /**
     * Get recent analyses
     */
    private function getRecentAnalyses(int $window): array
    {
        $analyses = [];
        $now = time();
        $hours = ceil($window / 3600);

        for ($i = 0; $i < $hours; $i++) {
            $hour = date('Y-m-d-H', $now - ($i * 3600));
            $key = "ddos_analysis:{$hour}";
            $hourAnalyses = $this->cache->get($key, []);

            foreach ($hourAnalyses as $analysis) {
                if ($analysis['timestamp'] > ($now - $window)) {
                    $analyses[] = $analysis;
                }
            }
        }

        return $analyses;
    }

    /**
     * Send alert to administrators
     */
    private function sendAlert(array $analysis): void
    {
        // Implementation would depend on notification system
        error_log("DDoS Alert: Critical threat detected from IP {$analysis['ip']}");
    }

    /**
     * Apply strict rate limiting
     */
    private function applyStrictRateLimit(string $ip): void
    {
        // Implement stricter rate limits for suspicious IPs
        $this->cache->set("strict_limit:{$ip}", true, 3600);
    }

    /**
     * Add IP to watchlist
     */
    private function addToWatchlist(string $ip): void
    {
        $watchlist = $this->cache->get('ddos_watchlist', []);
        $watchlist[$ip] = time();
        $this->cache->set('ddos_watchlist', $watchlist, 86400);
    }

    /**
     * Get recent attacks
     */
    private function getRecentAttacks(): array
    {
        $analyses = $this->getRecentAnalyses(3600); // Last hour

        return array_filter($analyses, function ($analysis) {
            return in_array($analysis['threat_level'], ['high', 'critical']);
        });
    }

    /**
     * Calculate mitigation effectiveness
     */
    private function calculateMitigationEffectiveness(): array
    {
        $stats = $this->getAttackStatistics();

        return [
            'block_rate' => $stats['block_rate'],
            'detection_accuracy' => 95.0, // This would be calculated based on false positives
            'response_time' => 'immediate',
            'effectiveness_score' => min(100, $stats['block_rate'] + 20)
        ];
    }
}
