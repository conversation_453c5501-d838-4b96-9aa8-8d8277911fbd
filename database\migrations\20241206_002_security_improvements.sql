-- Security Improvements Migration
-- Adds security enhancements to existing tables and creates security audit tables

BEGIN;

-- Add security columns to send_list table
ALTER TABLE `send_list` 
ADD COLUMN IF NOT EXISTS `priority` TINYINT DEFAULT 1 COMMENT 'Message priority (1=low, 5=high)',
ADD COLUMN IF NOT EXISTS `processed_at` TIMESTAMP NULL COMMENT 'When message was processed',
ADD COLUMN IF NOT EXISTS `failed_at` TIMESTAMP NULL COMMENT 'When message failed',
ADD COLUMN IF NOT EXISTS `error` TEXT NULL COMMENT 'Error message if failed',
ADD COLUMN IF NOT EXISTS `ip_address` VARCHAR(45) NULL COMMENT 'IP address of sender',
ADD COLUMN IF NOT EXISTS `user_agent` TEXT NULL COMMENT 'User agent of sender',
ADD COLUMN IF NOT EXISTS `security_hash` VARCHAR(64) NULL COMMENT 'Security hash for integrity';

-- Add indexes for performance and security
CREATE INDEX IF NOT EXISTS `idx_send_list_state_priority` ON `send_list` (`state`, `priority`, `created_at`);
CREATE INDEX IF NOT EXISTS `idx_send_list_ip_address` ON `send_list` (`ip_address`);
CREATE INDEX IF NOT EXISTS `idx_send_list_security_hash` ON `send_list` (`security_hash`);

-- Add security columns to setting table
ALTER TABLE `setting`
ADD COLUMN IF NOT EXISTS `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS `security_hash` VARCHAR(64) NULL COMMENT 'Security hash for integrity',
ADD COLUMN IF NOT EXISTS `encrypted` BOOLEAN DEFAULT FALSE COMMENT 'Whether value is encrypted';

-- Create security audit log table
CREATE TABLE IF NOT EXISTS `security_audit` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `event_type` VARCHAR(50) NOT NULL COMMENT 'Type of security event',
    `severity` ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    `user_id` BIGINT UNSIGNED NULL,
    `telegram_id` BIGINT NULL,
    `ip_address` VARCHAR(45) NULL,
    `user_agent` TEXT NULL,
    `event_data` JSON NULL COMMENT 'Additional event data',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX `idx_security_audit_event_type` (`event_type`),
    INDEX `idx_security_audit_severity` (`severity`),
    INDEX `idx_security_audit_user_id` (`user_id`),
    INDEX `idx_security_audit_telegram_id` (`telegram_id`),
    INDEX `idx_security_audit_ip_address` (`ip_address`),
    INDEX `idx_security_audit_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create rate limiting table
CREATE TABLE IF NOT EXISTS `rate_limits` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `identifier` VARCHAR(255) NOT NULL COMMENT 'Client identifier (IP, user_id, etc.)',
    `identifier_type` ENUM('ip', 'user', 'telegram', 'session') NOT NULL,
    `requests` INT UNSIGNED DEFAULT 1,
    `window_start` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `last_request` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `blocked_until` TIMESTAMP NULL,
    `violations` INT UNSIGNED DEFAULT 0,
    
    UNIQUE KEY `idx_rate_limits_identifier_type` (`identifier`, `identifier_type`),
    INDEX `idx_rate_limits_window_start` (`window_start`),
    INDEX `idx_rate_limits_blocked_until` (`blocked_until`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create session security table
CREATE TABLE IF NOT EXISTS `session_security` (
    `session_id` VARCHAR(128) PRIMARY KEY,
    `user_id` BIGINT UNSIGNED NULL,
    `telegram_id` BIGINT NULL,
    `ip_address` VARCHAR(45) NOT NULL,
    `user_agent_hash` VARCHAR(64) NOT NULL,
    `fingerprint` VARCHAR(64) NOT NULL,
    `csrf_token` VARCHAR(64) NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `last_activity` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `expires_at` TIMESTAMP NOT NULL,
    `is_valid` BOOLEAN DEFAULT TRUE,
    
    INDEX `idx_session_security_user_id` (`user_id`),
    INDEX `idx_session_security_telegram_id` (`telegram_id`),
    INDEX `idx_session_security_ip_address` (`ip_address`),
    INDEX `idx_session_security_expires_at` (`expires_at`),
    INDEX `idx_session_security_last_activity` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create file upload security table
CREATE TABLE IF NOT EXISTS `file_uploads` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `user_id` BIGINT UNSIGNED NULL,
    `telegram_id` BIGINT NULL,
    `original_name` VARCHAR(255) NOT NULL,
    `stored_name` VARCHAR(255) NOT NULL,
    `file_path` VARCHAR(500) NOT NULL,
    `file_size` BIGINT UNSIGNED NOT NULL,
    `mime_type` VARCHAR(100) NOT NULL,
    `file_hash` VARCHAR(64) NOT NULL,
    `scan_status` ENUM('pending', 'clean', 'infected', 'error') DEFAULT 'pending',
    `scan_result` TEXT NULL,
    `ip_address` VARCHAR(45) NULL,
    `user_agent` TEXT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `expires_at` TIMESTAMP NULL,
    
    INDEX `idx_file_uploads_user_id` (`user_id`),
    INDEX `idx_file_uploads_telegram_id` (`telegram_id`),
    INDEX `idx_file_uploads_file_hash` (`file_hash`),
    INDEX `idx_file_uploads_scan_status` (`scan_status`),
    INDEX `idx_file_uploads_created_at` (`created_at`),
    INDEX `idx_file_uploads_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create security configuration table
CREATE TABLE IF NOT EXISTS `security_config` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `config_key` VARCHAR(100) NOT NULL UNIQUE,
    `config_value` TEXT NOT NULL,
    `config_type` ENUM('string', 'integer', 'boolean', 'json', 'encrypted') DEFAULT 'string',
    `description` TEXT NULL,
    `is_active` BOOLEAN DEFAULT TRUE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_security_config_key` (`config_key`),
    INDEX `idx_security_config_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default security configurations
INSERT IGNORE INTO `security_config` (`config_key`, `config_value`, `config_type`, `description`) VALUES
('rate_limit_max_requests', '60', 'integer', 'Maximum requests per window'),
('rate_limit_window_size', '60', 'integer', 'Rate limit window size in seconds'),
('rate_limit_burst_limit', '10', 'integer', 'Burst limit for rapid requests'),
('session_timeout', '1800', 'integer', 'Session timeout in seconds'),
('session_max_lifetime', '7200', 'integer', 'Maximum session lifetime in seconds'),
('csrf_token_lifetime', '3600', 'integer', 'CSRF token lifetime in seconds'),
('file_upload_max_size', '10485760', 'integer', 'Maximum file upload size in bytes'),
('file_upload_allowed_types', '["jpg","jpeg","png","gif","pdf","txt"]', 'json', 'Allowed file upload types'),
('security_headers_enabled', 'true', 'boolean', 'Enable security headers'),
('xss_protection_enabled', 'true', 'boolean', 'Enable XSS protection'),
('sql_injection_protection_enabled', 'true', 'boolean', 'Enable SQL injection protection'),
('audit_log_retention_days', '90', 'integer', 'Security audit log retention in days');

-- Add foreign key constraints for better data integrity
ALTER TABLE `security_audit` 
ADD CONSTRAINT `fk_security_audit_user_id` 
FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) 
ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `session_security` 
ADD CONSTRAINT `fk_session_security_user_id` 
FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `file_uploads` 
ADD CONSTRAINT `fk_file_uploads_user_id` 
FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- Create triggers for automatic security hash generation
DELIMITER $$

CREATE TRIGGER IF NOT EXISTS `tr_send_list_security_hash` 
BEFORE INSERT ON `send_list`
FOR EACH ROW
BEGIN
    SET NEW.security_hash = SHA2(CONCAT(NEW.chat_id, NEW.message, NEW.created_at, RAND()), 256);
END$$

CREATE TRIGGER IF NOT EXISTS `tr_setting_security_hash` 
BEFORE INSERT ON `setting`
FOR EACH ROW
BEGIN
    SET NEW.security_hash = SHA2(CONCAT(NEW.type, NEW.value, NEW.created_at, RAND()), 256);
END$$

CREATE TRIGGER IF NOT EXISTS `tr_setting_security_hash_update` 
BEFORE UPDATE ON `setting`
FOR EACH ROW
BEGIN
    SET NEW.security_hash = SHA2(CONCAT(NEW.type, NEW.value, NEW.updated_at, RAND()), 256);
END$$

DELIMITER ;

-- Create cleanup procedures for old data
DELIMITER $$

CREATE PROCEDURE IF NOT EXISTS `CleanupSecurityAudit`()
BEGIN
    DECLARE retention_days INT DEFAULT 90;
    
    SELECT config_value INTO retention_days 
    FROM security_config 
    WHERE config_key = 'audit_log_retention_days' AND is_active = TRUE;
    
    DELETE FROM security_audit 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    SELECT ROW_COUNT() as deleted_records;
END$$

CREATE PROCEDURE IF NOT EXISTS `CleanupExpiredSessions`()
BEGIN
    DELETE FROM session_security 
    WHERE expires_at < NOW() OR last_activity < DATE_SUB(NOW(), INTERVAL 24 HOUR);
    
    SELECT ROW_COUNT() as deleted_sessions;
END$$

CREATE PROCEDURE IF NOT EXISTS `CleanupExpiredFiles`()
BEGIN
    DELETE FROM file_uploads 
    WHERE expires_at IS NOT NULL AND expires_at < NOW();
    
    SELECT ROW_COUNT() as deleted_files;
END$$

DELIMITER ;

-- Create views for security monitoring
CREATE OR REPLACE VIEW `v_security_summary` AS
SELECT 
    DATE(created_at) as date,
    event_type,
    severity,
    COUNT(*) as event_count
FROM security_audit 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(created_at), event_type, severity
ORDER BY date DESC, event_count DESC;

CREATE OR REPLACE VIEW `v_rate_limit_violations` AS
SELECT 
    identifier,
    identifier_type,
    requests,
    violations,
    blocked_until,
    last_request
FROM rate_limits 
WHERE violations > 0 OR blocked_until > NOW()
ORDER BY violations DESC, last_request DESC;

-- Record this migration
INSERT INTO `migrations` (`migration`, `batch`) 
VALUES ('20241206_002_security_improvements', 2)
ON DUPLICATE KEY UPDATE `executed_at` = CURRENT_TIMESTAMP;

COMMIT;
