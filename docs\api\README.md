# 🔌 WeBot 2.0 API Documentation

Complete API documentation for WeBot 2.0 VPN Management Bot.

## 📚 API Overview

WeBot provides a comprehensive REST API for managing VPN services, users, payments, and administrative functions. The API is designed with modern standards and includes:

- **RESTful Design** - Standard HTTP methods and status codes
- **J<PERSON>N Responses** - Consistent JSON format for all responses
- **Authentication** - JWT and API key authentication
- **Rate Limiting** - Request throttling and abuse prevention
- **Comprehensive Errors** - Detailed error messages and codes

## 🚀 Quick Start

### Base URL
```
Production: https://yourdomain.com/api/v1
Staging: https://staging.yourdomain.com/api/v1
```

### Authentication
```bash
# API Key Authentication
curl -H "Authorization: Bearer YOUR_API_KEY" \
     https://yourdomain.com/api/v1/users

# JWT Authentication
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     https://yourdomain.com/api/v1/admin/stats
```

### Basic Request
```bash
# Get user information
curl -X GET "https://yourdomain.com/api/v1/users/123456789" \
     -H "Authorization: Bearer YOUR_API_KEY" \
     -H "Content-Type: application/json"
```

## 📖 Documentation Structure

### 🔐 Authentication & Security
- **[AUTHENTICATION.md](AUTHENTICATION.md)** - Authentication methods and security
- **[RATE_LIMITING.md](RATE_LIMITING.md)** - Rate limiting policies
- **[ERROR_HANDLING.md](ERROR_HANDLING.md)** - Error codes and handling

### 📋 API Reference
- **[USERS_API.md](USERS_API.md)** - User management endpoints
- **[SERVICES_API.md](SERVICES_API.md)** - VPN service endpoints
- **[PAYMENTS_API.md](PAYMENTS_API.md)** - Payment processing endpoints
- **[ADMIN_API.md](ADMIN_API.md)** - Administrative endpoints
- **[WEBHOOKS_API.md](WEBHOOKS_API.md)** - Webhook endpoints

### 🛠️ Development Tools
- **[CODE_EXAMPLES.md](CODE_EXAMPLES.md)** - Code examples in multiple languages
- **[POSTMAN_COLLECTION.json](POSTMAN_COLLECTION.json)** - Postman collection
- **[OPENAPI_SPEC.yaml](OPENAPI_SPEC.yaml)** - OpenAPI 3.0 specification

## 🚀 Quick Start

### 1. Authentication
```bash
# Login and get JWT token
curl -X POST https://api.webot.com/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "telegram_id": 123456789,
    "username": "your_username",
    "first_name": "Your Name"
  }'
```

### 2. Make API Calls
```bash
# Get user profile
curl -X GET https://api.webot.com/api/users/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 3. Create a Service
```bash
# Create VPN service
curl -X POST https://api.webot.com/api/services \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "plan_id": 1,
    "server_id": 1,
    "duration_days": 30
  }'
```

## 🛠️ Development Tools

### OpenAPI/Swagger
Use the OpenAPI specification with popular tools:

```bash
# Swagger UI (Docker)
docker run -p 8080:8080 -e SWAGGER_JSON=/docs/openapi.json \
  -v $(pwd):/docs swaggerapi/swagger-ui

# Swagger Codegen
swagger-codegen generate -i openapi.json -l javascript -o ./sdk/js
```

### Postman
1. Import `WeBot_API.postman_collection.json` into Postman
2. Set up environment variables:
   - `base_url`: https://api.webot.com
   - `jwt_token`: Your JWT token
3. Start testing endpoints

### Code Generation
Generate client SDKs using the OpenAPI specification:

```bash
# JavaScript/TypeScript
npx @openapitools/openapi-generator-cli generate \
  -i openapi.yaml \
  -g typescript-axios \
  -o ./sdk/typescript

# PHP
openapi-generator generate \
  -i openapi.yaml \
  -g php \
  -o ./sdk/php

# Python
openapi-generator generate \
  -i openapi.yaml \
  -g python \
  -o ./sdk/python
```

## 📖 API Overview

### Base URLs
- **Production**: `https://api.webot.com`
- **Staging**: `https://staging-api.webot.com`
- **Development**: `http://localhost:8000`

### Authentication
- **JWT Bearer Token** (recommended)
- **Telegram Bot API Secret Token** (webhooks)
- **Session-based** (web applications)

### Response Format
All responses follow a consistent format:
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { /* response data */ }
}
```

### Rate Limits
- **General API**: 100 requests/minute per IP
- **Authentication**: 10 requests/minute per IP
- **Webhooks**: 1000 requests/minute per IP

## 🔧 Generating Documentation

This documentation is automatically generated using our CLI tool:

```bash
# Generate all documentation
php scripts/generate-docs.php --format=all --verbose

# Generate specific formats
php scripts/generate-docs.php --format=openapi
php scripts/generate-docs.php --format=markdown
php scripts/generate-docs.php --format=postman

# Custom output directory
php scripts/generate-docs.php --output=custom/docs/path

# Development environment
php scripts/generate-docs.php --base-url=http://localhost:8000
```

### Available Formats
- `all` - Generate all documentation formats
- `openapi` - OpenAPI specification (JSON + YAML)
- `markdown` - Markdown documentation
- `postman` - Postman collection
- `examples` - Code examples
- `auth` - Authentication guide
- `errors` - Error codes documentation
- `rate-limiting` - Rate limiting documentation

## 📋 API Endpoints Summary

### Authentication
- `POST /api/auth/login` - User authentication
- `POST /api/auth/refresh` - Token refresh

### User Management
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile

### Service Management
- `GET /api/services` - List user services
- `POST /api/services` - Create new service
- `GET /api/services/{id}` - Get service details
- `GET /api/services/{id}/config` - Get service configuration

### Payment Processing
- `POST /api/payments` - Create payment
- `GET /api/payments/{id}` - Get payment status

### Admin Functions
- `GET /api/admin/users` - List all users (admin only)
- `GET /api/admin/stats` - Get system statistics

### Webhooks
- `POST /webhook/telegram` - Telegram webhook handler

### Health & Monitoring
- `GET /api/health` - API health check

## 🔍 Testing

### Manual Testing
Use the provided Postman collection or cURL examples in the documentation.

### Automated Testing
```bash
# Run API tests
npm test

# Test specific endpoints
npm run test:auth
npm run test:services
npm run test:payments
```

### Load Testing
```bash
# Basic load test
ab -n 1000 -c 10 https://api.webot.com/api/health

# Rate limit testing
php scripts/test-rate-limits.php
```

## 🐛 Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Check JWT token validity
   - Ensure proper Authorization header format
   - Verify token hasn't expired

2. **Rate Limiting**
   - Monitor rate limit headers
   - Implement exponential backoff
   - Consider upgrading to higher plan

3. **Validation Errors**
   - Check request body format
   - Verify required fields
   - Validate data types and formats

### Debug Mode
Enable debug mode for detailed error information:
```bash
curl -X GET https://api.webot.com/api/users/profile \
  -H "Authorization: Bearer TOKEN" \
  -H "X-Debug: true"
```

## 📞 Support

### Documentation Issues
If you find issues with the documentation:
1. Check the latest version in this repository
2. Create an issue with specific details
3. Include the endpoint and expected behavior

### API Support
For API-related questions:
- **Email**: <EMAIL>
- **Documentation**: https://docs.webot.com
- **GitHub Issues**: https://github.com/webot/webot-api/issues
- **Telegram**: @webot_support

### Feature Requests
Submit feature requests through:
- GitHub Issues with `enhancement` label
- <NAME_EMAIL>
- Community discussions

## 📝 Contributing

### Documentation Updates
1. Fork the repository
2. Update documentation files
3. Test documentation generation
4. Submit pull request

### API Changes
When making API changes:
1. Update OpenAPI specification
2. Regenerate documentation
3. Update code examples
4. Test all endpoints

### Code Examples
To add new code examples:
1. Add examples to `CODE_EXAMPLES.md`
2. Test all examples
3. Include error handling
4. Document dependencies

## 📄 License

This documentation is part of the WeBot project and is licensed under the MIT License. See the main project LICENSE file for details.

## 🔄 Changelog

### Version 2.0.0 (2024-01-15)
- Complete API redesign with RESTful principles
- Added comprehensive OpenAPI 3.0 specification
- Implemented JWT authentication
- Enhanced error handling and response format
- Added rate limiting documentation
- Improved code examples and guides

### Version 1.x (Legacy)
- Basic webhook-only API
- Limited documentation
- Deprecated - migration required

---

**Last Updated**: January 15, 2024  
**API Version**: 2.0.0  
**Documentation Version**: 2.0.0
