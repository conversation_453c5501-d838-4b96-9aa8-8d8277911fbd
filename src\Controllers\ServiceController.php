<?php

declare(strict_types=1);

namespace WeBot\Controllers;

use WeBot\Exceptions\WeBotException;
use WeBot\Exceptions\PaymentException;
use WeBot\Utils\Helper;

/**
 * Service Controller
 *
 * Handles VPN service management including purchasing,
 * renewal, configuration, and service monitoring.
 *
 * @package WeBot\Controllers
 * @version 2.0
 */
class ServiceController extends BaseController
{
    /**
     * Handle service callback queries
     */
    public function handleCallback(array $callbackQuery): array
    {
        try {
            $this->initialize(['callback_query' => $callbackQuery]);

            $data = $this->data;

            return match (true) {
                $data === 'my_services' => $this->showMyServices(),
                $data === 'buy_service' => $this->showBuyService(),
                str_starts_with($data, 'service_') => $this->handleServiceAction($data),
                str_starts_with($data, 'plan_') => $this->handlePlanAction($data),
                str_starts_with($data, 'server_') => $this->handleServerAction($data),
                str_starts_with($data, 'buy_') => $this->handleBuyAction($data),
                default => $this->handleUnknownCallback()
            };
        } catch (\Throwable $e) {
            return $this->handleError($e);
        }
    }

    /**
     * Show user's services
     */
    public function showMyServices(): array
    {
        $services = $this->getUserServices();

        $text = "📋 سرویس‌های من\n\n";

        if (empty($services)) {
            $text .= "شما هنوز هیچ سرویسی خریداری نکرده‌اید.\n\n";
            $text .= "برای خرید سرویس جدید روی دکمه زیر کلیک کنید:";

            $keyboard = $this->createInlineKeyboard([
                [['text' => '🛒 خرید سرویس جدید', 'callback_data' => 'buy_service']],
                [['text' => '🔙 بازگشت', 'callback_data' => 'main_menu']]
            ]);
        } else {
            $text .= "تعداد سرویس‌های شما: " . count($services) . "\n\n";

            $keyboard = [];

            foreach ($services as $service) {
                $status = $this->getServiceStatusIcon($service['status']);
                $expiry = $this->getExpiryText($service['expires_at']);

                $keyboard[] = [
                    ['text' => "{$status} {$service['remark']} - {$expiry}", 'callback_data' => "service_manage_{$service['id']}"]
                ];
            }

            $keyboard[] = [
                ['text' => '🛒 خرید سرویس جدید', 'callback_data' => 'buy_service']
            ];
            $keyboard[] = [
                ['text' => '🔙 بازگشت', 'callback_data' => 'main_menu']
            ];
        }

        return $this->editMessage($text, $this->createInlineKeyboard($keyboard));
    }

    /**
     * Show buy service menu
     */
    public function showBuyService(): array
    {
        $servers = $this->getAvailableServers();

        $text = "🛒 خرید سرویس جدید\n\n";
        $text .= "لطفاً سرور مورد نظر خود را انتخاب کنید:";

        $keyboard = [];

        foreach ($servers as $server) {
            if ($server['active']) {
                $flag = $this->getCountryFlag($server['country']);
                $keyboard[] = [
                    ['text' => "{$flag} {$server['title']}", 'callback_data' => "server_select_{$server['id']}"]
                ];
            }
        }

        $keyboard[] = [
            ['text' => '🔙 بازگشت', 'callback_data' => 'main_menu']
        ];

        return $this->editMessage($text, $this->createInlineKeyboard($keyboard));
    }

    /**
     * Handle server selection
     */
    private function handleServerAction(string $data): array
    {
        $parts = explode('_', $data);
        $action = $parts[1];
        $serverId = (int) $parts[2];

        return match ($action) {
            'select' => $this->showServerPlans($serverId),
            default => $this->handleUnknownCallback()
        };
    }

    /**
     * Show server plans
     */
    private function showServerPlans(int $serverId): array
    {
        $server = $this->getServerById($serverId);
        $plans = $this->getServerPlans($serverId);

        if (!$server) {
            $this->answerCallback('سرور یافت نشد');
            return $this->showBuyService();
        }

        $flag = $this->getCountryFlag($server['country']);
        $text = "📋 پلن‌های {$flag} {$server['title']}\n\n";
        $text .= "لطفاً پلن مورد نظر خود را انتخاب کنید:";

        $keyboard = [];

        foreach ($plans as $plan) {
            if ($plan['active']) {
                $volume = Helper::formatBytes($plan['volume']);
                $price = Helper::formatPrice($plan['price']);
                $days = $plan['days'];

                $keyboard[] = [
                    ['text' => "📦 {$volume} - {$days} روز - {$price}", 'callback_data' => "plan_select_{$plan['id']}_{$serverId}"]
                ];
            }
        }

        $keyboard[] = [
            ['text' => '🔙 بازگشت', 'callback_data' => 'buy_service']
        ];

        return $this->editMessage($text, $this->createInlineKeyboard($keyboard));
    }

    /**
     * Handle plan selection
     */
    private function handlePlanAction(string $data): array
    {
        $parts = explode('_', $data);
        $action = $parts[1];
        $planId = (int) $parts[2];
        $serverId = (int) ($parts[3] ?? 0);

        return match ($action) {
            'select' => $this->confirmPurchase($planId, $serverId),
            default => $this->handleUnknownCallback()
        };
    }

    /**
     * Confirm purchase
     */
    private function confirmPurchase(int $planId, int $serverId): array
    {
        $plan = $this->getPlanById($planId);
        $server = $this->getServerById($serverId);
        $userInfo = $this->getUserInfo();

        if (!$plan || !$server) {
            $this->answerCallback('اطلاعات نامعتبر');
            return $this->showBuyService();
        }

        $volume = Helper::formatBytes($plan['volume']);
        $price = $plan['price'];
        $days = $plan['days'];
        $userWallet = $userInfo['wallet'] ?? 0;

        $flag = $this->getCountryFlag($server['country']);

        $text = "🛒 تأیید خرید\n\n";
        $text .= "📋 جزئیات سرویس:\n";
        $text .= "🖥 سرور: {$flag} {$server['title']}\n";
        $text .= "📦 حجم: {$volume}\n";
        $text .= "⏰ مدت: {$days} روز\n";
        $text .= "💰 قیمت: " . Helper::formatPrice($price) . "\n\n";
        $text .= "💳 موجودی کیف پول: " . Helper::formatPrice($userWallet) . "\n\n";

        if ($userWallet >= $price) {
            $text .= "✅ موجودی شما کافی است.\n";
            $text .= "آیا از خرید این سرویس اطمینان دارید؟";

            $keyboard = $this->createInlineKeyboard([
                [
                    ['text' => '✅ تأیید خرید', 'callback_data' => "buy_confirm_{$planId}_{$serverId}"],
                    ['text' => '❌ لغو', 'callback_data' => "server_select_{$serverId}"]
                ]
            ]);
        } else {
            $needed = $price - $userWallet;
            $text .= "❌ موجودی شما کافی نیست.\n";
            $text .= "مبلغ مورد نیاز: " . Helper::formatPrice($needed) . "\n";

            $keyboard = $this->createInlineKeyboard([
                [
                    ['text' => '💰 افزایش موجودی', 'callback_data' => 'add_balance'],
                    ['text' => '🔙 بازگشت', 'callback_data' => "server_select_{$serverId}"]
                ]
            ]);
        }

        return $this->editMessage($text, $keyboard);
    }

    /**
     * Handle buy action
     */
    private function handleBuyAction(string $data): array
    {
        $parts = explode('_', $data);
        $action = $parts[1];
        $planId = (int) $parts[2];
        $serverId = (int) $parts[3];

        return match ($action) {
            'confirm' => $this->processPurchase($planId, $serverId),
            default => $this->handleUnknownCallback()
        };
    }

    /**
     * Process purchase
     */
    private function processPurchase(int $planId, int $serverId): array
    {
        try {
            $plan = $this->getPlanById($planId);
            $server = $this->getServerById($serverId);
            $userInfo = $this->getUserInfo();

            if (!$plan || !$server) {
                throw new WeBotException('اطلاعات نامعتبر');
            }

            $price = $plan['price'];
            $userWallet = $userInfo['wallet'] ?? 0;

            if ($userWallet < $price) {
                throw new PaymentException('موجودی کافی نیست', PaymentException::INSUFFICIENT_FUNDS);
            }

            // Start transaction
            $this->database->autocommit(false);

            try {
                // Deduct from wallet
                $this->deductFromWallet($this->fromId, $price);

                // Create service
                $serviceId = $this->createService($planId, $serverId);

                // Create account on panel
                $accountInfo = $this->createPanelAccount($serviceId, $plan, $server);

                // Update service with account info
                $this->updateServiceAccount($serviceId, $accountInfo);

                // Commit transaction
                $this->database->commit();

                $this->logAction('service_purchased', [
                    'service_id' => $serviceId,
                    'plan_id' => $planId,
                    'server_id' => $serverId,
                    'price' => $price
                ]);

                return $this->showServiceCreated($serviceId);
            } catch (\Exception $e) {
                $this->database->rollback();
                throw $e;
            } finally {
                $this->database->autocommit(true);
            }
        } catch (\Throwable $e) {
            $this->logger->error('Purchase failed', [
                'user_id' => $this->fromId,
                'plan_id' => $planId,
                'server_id' => $serverId,
                'error' => $e->getMessage()
            ]);

            $this->answerCallback('خطا در خرید سرویس');
            return $this->showBuyService();
        }
    }

    /**
     * Show service created
     */
    private function showServiceCreated(int $serviceId): array
    {
        $service = $this->getServiceById($serviceId);

        $text = "✅ سرویس با موفقیت ایجاد شد!\n\n";
        $text .= "🆔 شناسه سرویس: {$service['id']}\n";
        $text .= "📝 نام: {$service['remark']}\n";
        $text .= "📅 تاریخ انقضا: " . date('Y/m/d', strtotime($service['expires_at'])) . "\n\n";
        $text .= "برای مشاهده جزئیات و دانلود کانفیگ روی دکمه زیر کلیک کنید:";

        $keyboard = $this->createInlineKeyboard([
            [['text' => '📋 مشاهده جزئیات', 'callback_data' => "service_manage_{$serviceId}"]],
            [['text' => '🏠 منوی اصلی', 'callback_data' => 'main_menu']]
        ]);

        return $this->editMessage($text, $keyboard);
    }

    /**
     * Handle service management
     */
    private function handleServiceAction(string $data): array
    {
        $parts = explode('_', $data);
        $action = $parts[1];
        $serviceId = (int) $parts[2];

        return match ($action) {
            'manage' => $this->showServiceDetails($serviceId),
            'config' => $this->showServiceConfig($serviceId),
            'qr' => $this->showServiceQR($serviceId),
            'renew' => $this->showServiceRenewal($serviceId),
            default => $this->handleUnknownCallback()
        };
    }

    /**
     * Show service details
     */
    private function showServiceDetails(int $serviceId): array
    {
        $service = $this->getServiceById($serviceId);

        if (!$service || $service['user_id'] != $this->fromId) {
            $this->answerCallback('سرویس یافت نشد');
            return $this->showMyServices();
        }

        $server = $this->getServerById($service['server_id']);
        $status = $this->getServiceStatusText($service['status']);
        $expiry = date('Y/m/d H:i', strtotime($service['expires_at']));
        $volume = Helper::formatBytes($service['volume']);
        $used = Helper::formatBytes($service['used_volume'] ?? 0);

        $flag = $this->getCountryFlag($server['country']);

        $text = "📋 جزئیات سرویس\n\n";
        $text .= "🆔 شناسه: {$service['id']}\n";
        $text .= "📝 نام: {$service['remark']}\n";
        $text .= "🖥 سرور: {$flag} {$server['title']}\n";
        $text .= "📊 وضعیت: {$status}\n";
        $text .= "📅 انقضا: {$expiry}\n";
        $text .= "📦 حجم: {$volume}\n";
        $text .= "📈 مصرف: {$used}\n";

        $keyboard = $this->createInlineKeyboard([
            [
                ['text' => '📱 کانفیگ', 'callback_data' => "service_config_{$serviceId}"],
                ['text' => '📱 QR Code', 'callback_data' => "service_qr_{$serviceId}"]
            ],
            [
                ['text' => '🔄 تمدید', 'callback_data' => "service_renew_{$serviceId}"],
                ['text' => '📊 آمار', 'callback_data' => "service_stats_{$serviceId}"]
            ],
            [
                ['text' => '🔙 بازگشت', 'callback_data' => 'my_services']
            ]
        ]);

        return $this->editMessage($text, $keyboard);
    }

    // Helper methods
    private function getUserServices(): array
    {
        $stmt = $this->database->prepare("SELECT * FROM `services` WHERE `user_id` = ? ORDER BY `created_at` DESC");
        $stmt->bind_param("i", $this->fromId);
        $stmt->execute();
        $result = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
        $stmt->close();

        return $result;
    }

    private function getAvailableServers(): array
    {
        $stmt = $this->database->prepare("SELECT * FROM `server_info` WHERE `active` = 1 ORDER BY `id` ASC");
        $stmt->execute();
        $result = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
        $stmt->close();

        return $result;
    }

    private function getServerById(int $serverId): ?array
    {
        $stmt = $this->database->prepare("SELECT * FROM `server_info` WHERE `id` = ?");
        $stmt->bind_param("i", $serverId);
        $stmt->execute();
        $result = $stmt->get_result()->fetch_assoc();
        $stmt->close();

        return $result ?: null;
    }

    private function getServerPlans(int $serverId): array
    {
        $stmt = $this->database->prepare("SELECT * FROM `server_plans` WHERE `server_id` = ? AND `active` = 1 ORDER BY `price` ASC");
        $stmt->bind_param("i", $serverId);
        $stmt->execute();
        $result = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
        $stmt->close();

        return $result;
    }

    private function getPlanById(int $planId): ?array
    {
        $stmt = $this->database->prepare("SELECT * FROM `server_plans` WHERE `id` = ?");
        $stmt->bind_param("i", $planId);
        $stmt->execute();
        $result = $stmt->get_result()->fetch_assoc();
        $stmt->close();

        return $result ?: null;
    }

    private function getServiceById(int $serviceId): ?array
    {
        $stmt = $this->database->prepare("SELECT * FROM `services` WHERE `id` = ?");
        $stmt->bind_param("i", $serviceId);
        $stmt->execute();
        $result = $stmt->get_result()->fetch_assoc();
        $stmt->close();

        return $result ?: null;
    }

    private function getServiceStatusIcon(string $status): string
    {
        return match ($status) {
            'active' => '🟢',
            'expired' => '🔴',
            'suspended' => '🟡',
            default => '⚪'
        };
    }

    private function getServiceStatusText(string $status): string
    {
        return match ($status) {
            'active' => '🟢 فعال',
            'expired' => '🔴 منقضی',
            'suspended' => '🟡 تعلیق',
            default => '⚪ نامشخص'
        };
    }

    private function getExpiryText(string $expiryDate): string
    {
        $expiry = strtotime($expiryDate);
        $now = time();
        $diff = $expiry - $now;

        if ($diff <= 0) {
            return 'منقضی شده';
        }

        $days = floor($diff / 86400);
        return "{$days} روز باقی‌مانده";
    }

    private function getCountryFlag(string $country): string
    {
        return match (strtolower($country)) {
            'iran', 'ir' => '🇮🇷',
            'germany', 'de' => '🇩🇪',
            'netherlands', 'nl' => '🇳🇱',
            'usa', 'us' => '🇺🇸',
            'uk', 'gb' => '🇬🇧',
            'france', 'fr' => '🇫🇷',
            default => '🌍'
        };
    }

    // Placeholder methods for complex operations
    private function deductFromWallet(int $userId, int $amount): bool
    {
        $stmt = $this->database->prepare("UPDATE `users` SET `wallet` = `wallet` - ? WHERE `userid` = ?");
        $stmt->bind_param("ii", $amount, $userId);
        $success = $stmt->execute();
        $stmt->close();

        return $success;
    }

    private function createService(int $planId, int $serverId): int
    {
        // TODO: Implement service creation
        unset($planId, $serverId); // Suppress unused variable warnings

        // This would create a service record
        // Placeholder implementation
        return 1;
    }

    private function createPanelAccount(int $serviceId, array $plan, array $server): array
    {
        // TODO: Implement panel account creation
        unset($serviceId, $plan, $server); // Suppress unused variable warnings

        // This would create account on the actual panel (Marzban, X-UI, etc.)
        // Placeholder implementation
        return ['uuid' => Helper::generateUuid(), 'config' => 'placeholder_config'];
    }

    private function updateServiceAccount(int $serviceId, array $accountInfo): bool
    {
        // TODO: Implement service account update
        unset($serviceId, $accountInfo); // Suppress unused variable warnings

        // This would update service with panel account info
        // Placeholder implementation
        return true;
    }

    private function showServiceConfig(int $serviceId): array
    {
        return $this->editMessage(
            '🚧 نمایش کانفیگ در حال توسعه است...',
            $this->createInlineKeyboard([[['text' => '🔙 بازگشت', 'callback_data' => "service_manage_{$serviceId}"]]])
        );
    }

    private function showServiceQR(int $serviceId): array
    {
        return $this->editMessage(
            '🚧 نمایش QR Code در حال توسعه است...',
            $this->createInlineKeyboard([[['text' => '🔙 بازگشت', 'callback_data' => "service_manage_{$serviceId}"]]])
        );
    }

    private function showServiceRenewal(int $serviceId): array
    {
        return $this->editMessage(
            '🚧 تمدید سرویس در حال توسعه است...',
            $this->createInlineKeyboard([[['text' => '🔙 بازگشت', 'callback_data' => "service_manage_{$serviceId}"]]])
        );
    }

    private function handleUnknownCallback(): array
    {
        $this->answerCallback('گزینه نامعتبر');
        return ['ok' => true];
    }
}
