<?php

declare(strict_types=1);

namespace WeBot\Models;

use WeBot\Utils\Helper;

/**
 * Ticket Model
 *
 * Represents a support ticket in the WeBot system
 * with status tracking, priority management, and reply handling.
 *
 * @package WeBot\Models
 * @version 2.0
 */
class Ticket extends BaseModel
{
    protected string $table = 'tickets';
    protected string $primaryKey = 'id';

    protected array $fillable = [
        'user_id',
        'subject',
        'description',
        'category',
        'priority',
        'status',
        'assigned_to',
        'resolved_at',
        'closed_at',
        'metadata',
        'tags'
    ];

    protected array $casts = [
        'user_id' => 'int',
        'assigned_to' => 'int',
        'metadata' => 'json',
        'tags' => 'json',
        'resolved_at' => 'datetime',
        'closed_at' => 'datetime'
    ];

    // Ticket categories
    public const CATEGORY_TECHNICAL = 'technical';
    public const CATEGORY_PAYMENT = 'payment';
    public const CATEGORY_CONNECTION = 'connection';
    public const CATEGORY_SETTINGS = 'settings';
    public const CATEGORY_CALL = 'call';
    public const CATEGORY_OTHER = 'other';

    // Ticket priorities
    public const PRIORITY_LOW = 'low';
    public const PRIORITY_MEDIUM = 'medium';
    public const PRIORITY_HIGH = 'high';
    public const PRIORITY_URGENT = 'urgent';

    // Ticket statuses
    public const STATUS_OPEN = 'open';
    public const STATUS_IN_PROGRESS = 'in_progress';
    public const STATUS_WAITING = 'waiting';
    public const STATUS_RESOLVED = 'resolved';
    public const STATUS_CLOSED = 'closed';

    /**
     * Get validation rules
     */
    protected function getValidationRules(): array
    {
        return [
            'user_id' => 'required|integer|min:1',
            'subject' => 'required|string|max:255',
            'description' => 'required|string|max:2000',
            'category' => 'required|string|in:technical,payment,connection,settings,call,other',
            'priority' => 'required|string|in:low,medium,high,urgent',
            'status' => 'string|in:open,in_progress,waiting,resolved,closed',
            'assigned_to' => 'integer|min:1'
        ];
    }

    /**
     * Check if ticket is open
     */
    public function isOpen(): bool
    {
        return in_array($this->getAttribute('status'), [
            self::STATUS_OPEN,
            self::STATUS_IN_PROGRESS,
            self::STATUS_WAITING
        ]);
    }

    /**
     * Check if ticket is closed
     */
    public function isClosed(): bool
    {
        return in_array($this->getAttribute('status'), [
            self::STATUS_RESOLVED,
            self::STATUS_CLOSED
        ]);
    }

    /**
     * Check if ticket is urgent
     */
    public function isUrgent(): bool
    {
        return $this->getAttribute('priority') === self::PRIORITY_URGENT;
    }

    /**
     * Check if ticket is assigned
     */
    public function isAssigned(): bool
    {
        return !empty($this->getAttribute('assigned_to'));
    }

    /**
     * Get category title
     */
    public function getCategoryTitle(): string
    {
        return match ($this->getAttribute('category')) {
            self::CATEGORY_TECHNICAL => 'مشکل فنی',
            self::CATEGORY_PAYMENT => 'مشکل پرداخت',
            self::CATEGORY_CONNECTION => 'مشکل اتصال',
            self::CATEGORY_SETTINGS => 'تنظیمات',
            self::CATEGORY_CALL => 'درخواست تماس',
            self::CATEGORY_OTHER => 'سایر موارد',
            default => 'نامشخص'
        };
    }

    /**
     * Get priority title
     */
    public function getPriorityTitle(): string
    {
        return match ($this->getAttribute('priority')) {
            self::PRIORITY_LOW => 'کم',
            self::PRIORITY_MEDIUM => 'متوسط',
            self::PRIORITY_HIGH => 'بالا',
            self::PRIORITY_URGENT => 'فوری',
            default => 'نامشخص'
        };
    }

    /**
     * Get status title
     */
    public function getStatusTitle(): string
    {
        return match ($this->getAttribute('status')) {
            self::STATUS_OPEN => 'باز',
            self::STATUS_IN_PROGRESS => 'در حال بررسی',
            self::STATUS_WAITING => 'در انتظار پاسخ',
            self::STATUS_RESOLVED => 'حل شده',
            self::STATUS_CLOSED => 'بسته شده',
            default => 'نامشخص'
        };
    }

    /**
     * Get priority emoji
     */
    public function getPriorityEmoji(): string
    {
        return match ($this->getAttribute('priority')) {
            self::PRIORITY_LOW => '🟢',
            self::PRIORITY_MEDIUM => '🟡',
            self::PRIORITY_HIGH => '🟠',
            self::PRIORITY_URGENT => '🔴',
            default => '⚪'
        };
    }

    /**
     * Get status emoji
     */
    public function getStatusEmoji(): string
    {
        return match ($this->getAttribute('status')) {
            self::STATUS_OPEN => '🟢',
            self::STATUS_IN_PROGRESS => '🟡',
            self::STATUS_WAITING => '🔵',
            self::STATUS_RESOLVED => '✅',
            self::STATUS_CLOSED => '⚫',
            default => '❓'
        };
    }

    /**
     * Get category emoji
     */
    public function getCategoryEmoji(): string
    {
        return match ($this->getAttribute('category')) {
            self::CATEGORY_TECHNICAL => '🔧',
            self::CATEGORY_PAYMENT => '💰',
            self::CATEGORY_CONNECTION => '📱',
            self::CATEGORY_SETTINGS => '⚙️',
            self::CATEGORY_CALL => '📞',
            self::CATEGORY_OTHER => '❓',
            default => '📝'
        };
    }

    /**
     * Get ticket age in hours
     */
    public function getAgeInHours(): int
    {
        $createdAt = $this->getAttribute('created_at');
        if (!$createdAt) {
            return 0;
        }

        $created = strtotime($createdAt);
        $now = time();

        return (int) (($now - $created) / 3600);
    }

    /**
     * Get ticket age formatted
     */
    public function getFormattedAge(): string
    {
        $hours = $this->getAgeInHours();

        if ($hours < 1) {
            return 'کمتر از یک ساعت';
        } elseif ($hours < 24) {
            return $hours . ' ساعت';
        } else {
            $days = (int) ($hours / 24);
            return $days . ' روز';
        }
    }

    /**
     * Get response time in hours
     */
    public function getResponseTimeInHours(): ?int
    {
        $replies = $this->getReplies();
        if (empty($replies)) {
            return null;
        }

        $firstReply = $replies[0];
        $createdAt = strtotime($this->getAttribute('created_at'));
        $repliedAt = strtotime($firstReply['created_at']);

        return (int) (($repliedAt - $createdAt) / 3600);
    }

    /**
     * Update ticket status
     */
    public function updateStatus(string $status): bool
    {
        $this->setAttribute('status', $status);

        // Set timestamps based on status
        if ($status === self::STATUS_RESOLVED) {
            $this->setAttribute('resolved_at', date('Y-m-d H:i:s'));
        } elseif ($status === self::STATUS_CLOSED) {
            $this->setAttribute('closed_at', date('Y-m-d H:i:s'));
        }

        return $this->save();
    }

    /**
     * Assign ticket to user
     */
    public function assignTo(int $userId): bool
    {
        $this->setAttribute('assigned_to', $userId);

        // Update status to in_progress if it's open
        if ($this->getAttribute('status') === self::STATUS_OPEN) {
            $this->setAttribute('status', self::STATUS_IN_PROGRESS);
        }

        return $this->save();
    }

    /**
     * Add tag to ticket
     */
    public function addTag(string $tag): bool
    {
        $tags = $this->getAttribute('tags') ?? [];

        if (!in_array($tag, $tags)) {
            $tags[] = $tag;
            $this->setAttribute('tags', $tags);
            return $this->save();
        }

        return true;
    }

    /**
     * Remove tag from ticket
     */
    public function removeTag(string $tag): bool
    {
        $tags = $this->getAttribute('tags') ?? [];
        $index = array_search($tag, $tags);

        if ($index !== false) {
            unset($tags[$index]);
            $this->setAttribute('tags', array_values($tags));
            return $this->save();
        }

        return true;
    }

    /**
     * Get ticket tags
     */
    public function getTags(): array
    {
        return $this->getAttribute('tags') ?? [];
    }

    /**
     * Get ticket metadata
     */
    public function getMetadata(): array
    {
        return $this->getAttribute('metadata') ?? [];
    }

    /**
     * Update ticket metadata
     */
    public function updateMetadata(array $metadata): bool
    {
        $currentMetadata = $this->getMetadata();
        $updatedMetadata = array_merge($currentMetadata, $metadata);

        $this->setAttribute('metadata', $updatedMetadata);

        return $this->save();
    }

    /**
     * Get ticket user
     */
    public function getUser(): ?array
    {
        $userId = $this->getAttribute('user_id');
        if (!$userId) {
            return null;
        }

        return $this->database->selectOne('users', ['userid' => $userId]);
    }

    /**
     * Get assigned user
     */
    public function getAssignedUser(): ?array
    {
        $assignedTo = $this->getAttribute('assigned_to');
        if (!$assignedTo) {
            return null;
        }

        return $this->database->selectOne('users', ['userid' => $assignedTo]);
    }

    /**
     * Get ticket replies
     */
    public function getReplies(): array
    {
        return $this->database->select(
            'ticket_replies',
            ['ticket_id' => $this->getKey()],
            ['order_by' => 'created_at ASC']
        );
    }

    /**
     * Get replies count
     */
    public function getRepliesCount(): int
    {
        $sql = "SELECT COUNT(*) FROM `ticket_replies` WHERE `ticket_id` = ?";
        $count = $this->database->fetchValue($sql, [$this->getKey()], 'i');

        return (int) ($count ?? 0);
    }

    /**
     * Get last reply
     */
    public function getLastReply(): ?array
    {
        $sql = "SELECT * FROM `ticket_replies` WHERE `ticket_id` = ? ORDER BY `created_at` DESC LIMIT 1";
        return $this->database->fetchRow($sql, [$this->getKey()], 'i');
    }

    /**
     * Add reply to ticket
     */
    public function addReply(int $userId, string $message, bool $isStaff = false): bool
    {
        $replyData = [
            'ticket_id' => $this->getKey(),
            'user_id' => $userId,
            'message' => $message,
            'is_staff' => $isStaff,
            'created_at' => date('Y-m-d H:i:s')
        ];

        $replyId = $this->database->insert('ticket_replies', $replyData);

        if ($replyId) {
            // Update ticket status based on who replied
            if ($isStaff) {
                $this->updateStatus(self::STATUS_WAITING);
            } else {
                $this->updateStatus(self::STATUS_IN_PROGRESS);
            }

            return true;
        }

        return false;
    }

    /**
     * Get ticket summary
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->getKey(),
            'subject' => $this->getAttribute('subject'),
            'category' => $this->getCategoryTitle(),
            'category_emoji' => $this->getCategoryEmoji(),
            'priority' => $this->getPriorityTitle(),
            'priority_emoji' => $this->getPriorityEmoji(),
            'status' => $this->getStatusTitle(),
            'status_emoji' => $this->getStatusEmoji(),
            'is_open' => $this->isOpen(),
            'is_closed' => $this->isClosed(),
            'is_urgent' => $this->isUrgent(),
            'is_assigned' => $this->isAssigned(),
            'age_hours' => $this->getAgeInHours(),
            'formatted_age' => $this->getFormattedAge(),
            'replies_count' => $this->getRepliesCount(),
            'created_at' => $this->getAttribute('created_at'),
            'updated_at' => $this->getAttribute('updated_at'),
            'resolved_at' => $this->getAttribute('resolved_at'),
            'closed_at' => $this->getAttribute('closed_at')
        ];
    }

    /**
     * Scope: Open tickets
     */
    public function scopeOpen($query)
    {
        return $query->whereIn('status', [
            self::STATUS_OPEN,
            self::STATUS_IN_PROGRESS,
            self::STATUS_WAITING
        ]);
    }

    /**
     * Scope: Closed tickets
     */
    public function scopeClosed($query)
    {
        return $query->whereIn('status', [
            self::STATUS_RESOLVED,
            self::STATUS_CLOSED
        ]);
    }

    /**
     * Scope: By priority
     */
    public function scopeByPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope: By category
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope: Urgent tickets
     */
    public function scopeUrgent($query)
    {
        return $query->where('priority', self::PRIORITY_URGENT);
    }

    /**
     * Scope: Assigned tickets
     */
    public function scopeAssigned($query)
    {
        return $query->whereNotNull('assigned_to');
    }

    /**
     * Scope: Unassigned tickets
     */
    public function scopeUnassigned($query)
    {
        return $query->whereNull('assigned_to');
    }
}
