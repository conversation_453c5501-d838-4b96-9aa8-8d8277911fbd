<?php

declare(strict_types=1);

namespace WeBot\Exceptions;

/**
 * Rate Limit Exception
 *
 * Thrown when rate limits are exceeded
 *
 * @package WeBot\Exceptions
 * @version 2.0
 */
class RateLimitException extends SecurityException
{
    private array $rateLimitData;

    public function __construct(
        string $message = 'Rate limit exceeded',
        int $code = 429,
        array $rateLimitData = [],
        ?\Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        $this->rateLimitData = $rateLimitData;
    }

    /**
     * Get rate limit data
     */
    public function getRateLimitData(): array
    {
        return $this->rateLimitData;
    }

    /**
     * Get retry after seconds
     */
    public function getRetryAfter(): int
    {
        return $this->rateLimitData['retry_after'] ?? 60;
    }

    /**
     * Get rate limit
     */
    public function getLimit(): int
    {
        return $this->rateLimitData['limit'] ?? 60;
    }

    /**
     * Get remaining requests
     */
    public function getRemaining(): int
    {
        return $this->rateLimitData['remaining'] ?? 0;
    }

    /**
     * Get reset timestamp
     */
    public function getReset(): int
    {
        return $this->rateLimitData['reset'] ?? time() + 60;
    }
}
