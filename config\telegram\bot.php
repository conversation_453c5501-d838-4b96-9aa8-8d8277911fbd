<?php

declare(strict_types=1);

/**
 * Telegram Bot Configuration
 * 
 * Configuration for Telegram bot including API settings,
 * webhook configuration, and bot behavior settings.
 * 
 * @package WeBot\Config\Telegram
 * @version 2.0
 */

return [
    /*
    |--------------------------------------------------------------------------
    | Bot Configuration
    |--------------------------------------------------------------------------
    */
    'bot' => [
        'token' => $_ENV['TELEGRAM_BOT_TOKEN'] ?? '',
        'username' => $_ENV['TELEGRAM_BOT_USERNAME'] ?? '',
        'name' => $_ENV['TELEGRAM_BOT_NAME'] ?? 'WeBot',
        'description' => $_ENV['TELEGRAM_BOT_DESCRIPTION'] ?? 'Advanced VPN Management Bot',
        'about' => $_ENV['TELEGRAM_BOT_ABOUT'] ?? 'Manage your VPN services easily',
        'api_url' => $_ENV['TELEGRAM_API_URL'] ?? 'https://api.telegram.org',
        'timeout' => (int)($_ENV['TELEGRAM_TIMEOUT'] ?? 30),
        'connect_timeout' => (int)($_ENV['TELEGRAM_CONNECT_TIMEOUT'] ?? 10),
    ],

    /*
    |--------------------------------------------------------------------------
    | Webhook Configuration
    |--------------------------------------------------------------------------
    */
    'webhook' => [
        'enabled' => (bool)($_ENV['TELEGRAM_WEBHOOK_ENABLED'] ?? true),
        'url' => $_ENV['TELEGRAM_WEBHOOK_URL'] ?? '',
        'secret_token' => $_ENV['TELEGRAM_WEBHOOK_SECRET'] ?? '',
        'certificate' => $_ENV['TELEGRAM_WEBHOOK_CERTIFICATE'] ?? '',
        'ip_address' => $_ENV['TELEGRAM_WEBHOOK_IP'] ?? '',
        'max_connections' => (int)($_ENV['TELEGRAM_WEBHOOK_MAX_CONNECTIONS'] ?? 40),
        'allowed_updates' => [
            'message',
            'callback_query',
            'inline_query',
            'chosen_inline_result',
            'my_chat_member',
            'chat_member',
            'pre_checkout_query',
            'shipping_query'
        ],
        'drop_pending_updates' => (bool)($_ENV['TELEGRAM_DROP_PENDING_UPDATES'] ?? false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Polling Configuration (Alternative to Webhook)
    |--------------------------------------------------------------------------
    */
    'polling' => [
        'enabled' => (bool)($_ENV['TELEGRAM_POLLING_ENABLED'] ?? false),
        'interval' => (int)($_ENV['TELEGRAM_POLLING_INTERVAL'] ?? 1), // seconds
        'limit' => (int)($_ENV['TELEGRAM_POLLING_LIMIT'] ?? 100),
        'timeout' => (int)($_ENV['TELEGRAM_POLLING_TIMEOUT'] ?? 0),
        'allowed_updates' => [
            'message',
            'callback_query',
            'inline_query',
            'chosen_inline_result'
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Bot Commands
    |--------------------------------------------------------------------------
    */
    'commands' => [
        'auto_register' => (bool)($_ENV['TELEGRAM_AUTO_REGISTER_COMMANDS'] ?? true),
        'scope' => $_ENV['TELEGRAM_COMMANDS_SCOPE'] ?? 'default', // 'default', 'all_private_chats', 'all_group_chats', 'all_chat_administrators'
        'language_code' => $_ENV['TELEGRAM_COMMANDS_LANGUAGE'] ?? 'fa',
        'list' => [
            [
                'command' => 'start',
                'description' => 'شروع کار با ربات',
            ],
            [
                'command' => 'help',
                'description' => 'راهنمای استفاده',
            ],
            [
                'command' => 'menu',
                'description' => 'منوی اصلی',
            ],
            [
                'command' => 'profile',
                'description' => 'پروفایل کاربری',
            ],
            [
                'command' => 'services',
                'description' => 'سرویس‌های من',
            ],
            [
                'command' => 'buy',
                'description' => 'خرید سرویس',
            ],
            [
                'command' => 'balance',
                'description' => 'موجودی حساب',
            ],
            [
                'command' => 'support',
                'description' => 'پشتیبانی',
            ],
        ],
        'admin_commands' => [
            [
                'command' => 'admin',
                'description' => 'پنل مدیریت',
            ],
            [
                'command' => 'stats',
                'description' => 'آمار سیستم',
            ],
            [
                'command' => 'broadcast',
                'description' => 'ارسال پیام همگانی',
            ],
            [
                'command' => 'users',
                'description' => 'مدیریت کاربران',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Message Settings
    |--------------------------------------------------------------------------
    */
    'messages' => [
        'parse_mode' => $_ENV['TELEGRAM_PARSE_MODE'] ?? 'HTML', // 'HTML', 'Markdown', 'MarkdownV2'
        'disable_web_page_preview' => (bool)($_ENV['TELEGRAM_DISABLE_WEB_PREVIEW'] ?? true),
        'disable_notification' => (bool)($_ENV['TELEGRAM_DISABLE_NOTIFICATION'] ?? false),
        'protect_content' => (bool)($_ENV['TELEGRAM_PROTECT_CONTENT'] ?? false),
        'allow_sending_without_reply' => (bool)($_ENV['TELEGRAM_ALLOW_SENDING_WITHOUT_REPLY'] ?? true),
        'max_message_length' => (int)($_ENV['TELEGRAM_MAX_MESSAGE_LENGTH'] ?? 4096),
        'max_caption_length' => (int)($_ENV['TELEGRAM_MAX_CAPTION_LENGTH'] ?? 1024),
    ],

    /*
    |--------------------------------------------------------------------------
    | File Upload Settings
    |--------------------------------------------------------------------------
    */
    'files' => [
        'max_file_size' => (int)($_ENV['TELEGRAM_MAX_FILE_SIZE'] ?? 50 * 1024 * 1024), // 50MB
        'allowed_types' => [
            'photo' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'document' => ['pdf', 'doc', 'docx', 'txt', 'zip', 'rar'],
            'video' => ['mp4', 'avi', 'mov', 'mkv'],
            'audio' => ['mp3', 'wav', 'ogg', 'm4a'],
        ],
        'upload_path' => $_ENV['TELEGRAM_UPLOAD_PATH'] ?? 'storage/uploads/telegram',
        'auto_delete_after' => (int)($_ENV['TELEGRAM_AUTO_DELETE_FILES'] ?? 86400), // 24 hours
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    */
    'rate_limiting' => [
        'enabled' => (bool)($_ENV['TELEGRAM_RATE_LIMITING'] ?? true),
        'global_limit' => (int)($_ENV['TELEGRAM_GLOBAL_RATE_LIMIT'] ?? 30), // messages per second
        'per_chat_limit' => (int)($_ENV['TELEGRAM_PER_CHAT_RATE_LIMIT'] ?? 1), // messages per second per chat
        'burst_limit' => (int)($_ENV['TELEGRAM_BURST_LIMIT'] ?? 5),
        'cooldown_period' => (int)($_ENV['TELEGRAM_COOLDOWN_PERIOD'] ?? 60), // seconds
        'whitelist' => explode(',', $_ENV['TELEGRAM_RATE_LIMIT_WHITELIST'] ?? ''),
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    */
    'security' => [
        'verify_webhook' => (bool)($_ENV['TELEGRAM_VERIFY_WEBHOOK'] ?? true),
        'allowed_ips' => [
            '*************/20',
            '**********/22',
            '***********/22',
            '**********/22',
            '*************/22',
            '*************/22',
            '*************/22',
        ],
        'block_forwarded_messages' => (bool)($_ENV['TELEGRAM_BLOCK_FORWARDED'] ?? false),
        'block_edited_messages' => (bool)($_ENV['TELEGRAM_BLOCK_EDITED'] ?? false),
        'admin_only_mode' => (bool)($_ENV['TELEGRAM_ADMIN_ONLY_MODE'] ?? false),
        'maintenance_mode' => (bool)($_ENV['TELEGRAM_MAINTENANCE_MODE'] ?? false),
        'spam_protection' => (bool)($_ENV['TELEGRAM_SPAM_PROTECTION'] ?? true),
    ],

    /*
    |--------------------------------------------------------------------------
    | User Management
    |--------------------------------------------------------------------------
    */
    'users' => [
        'auto_register' => (bool)($_ENV['TELEGRAM_AUTO_REGISTER_USERS'] ?? true),
        'require_username' => (bool)($_ENV['TELEGRAM_REQUIRE_USERNAME'] ?? false),
        'block_bots' => (bool)($_ENV['TELEGRAM_BLOCK_BOTS'] ?? true),
        'max_users' => (int)($_ENV['TELEGRAM_MAX_USERS'] ?? 0), // 0 = unlimited
        'user_timeout' => (int)($_ENV['TELEGRAM_USER_TIMEOUT'] ?? 1800), // 30 minutes
        'track_user_activity' => (bool)($_ENV['TELEGRAM_TRACK_USER_ACTIVITY'] ?? true),
        'store_user_data' => (bool)($_ENV['TELEGRAM_STORE_USER_DATA'] ?? true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Admin Settings
    |--------------------------------------------------------------------------
    */
    'admin' => [
        'super_admin_id' => (int)($_ENV['TELEGRAM_SUPER_ADMIN_ID'] ?? 0),
        'admin_ids' => array_filter(explode(',', $_ENV['TELEGRAM_ADMIN_IDS'] ?? '')),
        'moderator_ids' => array_filter(explode(',', $_ENV['TELEGRAM_MODERATOR_IDS'] ?? '')),
        'admin_notifications' => (bool)($_ENV['TELEGRAM_ADMIN_NOTIFICATIONS'] ?? true),
        'log_admin_actions' => (bool)($_ENV['TELEGRAM_LOG_ADMIN_ACTIONS'] ?? true),
        'require_admin_confirmation' => (bool)($_ENV['TELEGRAM_REQUIRE_ADMIN_CONFIRMATION'] ?? false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Inline Features
    |--------------------------------------------------------------------------
    */
    'inline' => [
        'enabled' => (bool)($_ENV['TELEGRAM_INLINE_ENABLED'] ?? true),
        'cache_time' => (int)($_ENV['TELEGRAM_INLINE_CACHE_TIME'] ?? 300), // 5 minutes
        'max_results' => (int)($_ENV['TELEGRAM_INLINE_MAX_RESULTS'] ?? 50),
        'personal' => (bool)($_ENV['TELEGRAM_INLINE_PERSONAL'] ?? false),
        'next_offset' => $_ENV['TELEGRAM_INLINE_NEXT_OFFSET'] ?? '',
        'switch_pm_text' => $_ENV['TELEGRAM_INLINE_SWITCH_PM_TEXT'] ?? 'Open WeBot',
        'switch_pm_parameter' => $_ENV['TELEGRAM_INLINE_SWITCH_PM_PARAMETER'] ?? 'start',
    ],

    /*
    |--------------------------------------------------------------------------
    | Payments (Telegram Payments)
    |--------------------------------------------------------------------------
    */
    'payments' => [
        'enabled' => (bool)($_ENV['TELEGRAM_PAYMENTS_ENABLED'] ?? false),
        'provider_token' => $_ENV['TELEGRAM_PAYMENT_PROVIDER_TOKEN'] ?? '',
        'currency' => $_ENV['TELEGRAM_PAYMENT_CURRENCY'] ?? 'USD',
        'max_tip_amount' => (int)($_ENV['TELEGRAM_MAX_TIP_AMOUNT'] ?? 0),
        'suggested_tip_amounts' => array_map('intval', explode(',', $_ENV['TELEGRAM_SUGGESTED_TIP_AMOUNTS'] ?? '')),
        'need_name' => (bool)($_ENV['TELEGRAM_PAYMENT_NEED_NAME'] ?? true),
        'need_phone_number' => (bool)($_ENV['TELEGRAM_PAYMENT_NEED_PHONE'] ?? false),
        'need_email' => (bool)($_ENV['TELEGRAM_PAYMENT_NEED_EMAIL'] ?? true),
        'need_shipping_address' => (bool)($_ENV['TELEGRAM_PAYMENT_NEED_SHIPPING'] ?? false),
        'send_phone_number_to_provider' => (bool)($_ENV['TELEGRAM_SEND_PHONE_TO_PROVIDER'] ?? false),
        'send_email_to_provider' => (bool)($_ENV['TELEGRAM_SEND_EMAIL_TO_PROVIDER'] ?? false),
        'is_flexible' => (bool)($_ENV['TELEGRAM_PAYMENT_IS_FLEXIBLE'] ?? false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging & Monitoring
    |--------------------------------------------------------------------------
    */
    'logging' => [
        'enabled' => (bool)($_ENV['TELEGRAM_LOGGING'] ?? true),
        'log_level' => $_ENV['TELEGRAM_LOG_LEVEL'] ?? 'info',
        'log_file' => $_ENV['TELEGRAM_LOG_FILE'] ?? 'storage/logs/telegram.log',
        'log_updates' => (bool)($_ENV['TELEGRAM_LOG_UPDATES'] ?? false),
        'log_outgoing' => (bool)($_ENV['TELEGRAM_LOG_OUTGOING'] ?? false),
        'log_errors' => (bool)($_ENV['TELEGRAM_LOG_ERRORS'] ?? true),
        'retention_days' => (int)($_ENV['TELEGRAM_LOG_RETENTION'] ?? 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'async_processing' => (bool)($_ENV['TELEGRAM_ASYNC_PROCESSING'] ?? true),
        'queue_updates' => (bool)($_ENV['TELEGRAM_QUEUE_UPDATES'] ?? true),
        'batch_processing' => (bool)($_ENV['TELEGRAM_BATCH_PROCESSING'] ?? false),
        'cache_responses' => (bool)($_ENV['TELEGRAM_CACHE_RESPONSES'] ?? true),
        'cache_ttl' => (int)($_ENV['TELEGRAM_CACHE_TTL'] ?? 300), // 5 minutes
        'connection_pooling' => (bool)($_ENV['TELEGRAM_CONNECTION_POOLING'] ?? true),
        'max_connections' => (int)($_ENV['TELEGRAM_MAX_CONNECTIONS'] ?? 10),
    ],

    /*
    |--------------------------------------------------------------------------
    | Development & Testing
    |--------------------------------------------------------------------------
    */
    'development' => [
        'debug_mode' => (bool)($_ENV['TELEGRAM_DEBUG'] ?? false),
        'test_mode' => (bool)($_ENV['TELEGRAM_TEST_MODE'] ?? false),
        'mock_responses' => (bool)($_ENV['TELEGRAM_MOCK_RESPONSES'] ?? false),
        'log_raw_updates' => (bool)($_ENV['TELEGRAM_LOG_RAW_UPDATES'] ?? false),
        'simulate_delays' => (bool)($_ENV['TELEGRAM_SIMULATE_DELAYS'] ?? false),
        'test_chat_id' => (int)($_ENV['TELEGRAM_TEST_CHAT_ID'] ?? 0),
    ],

    /*
    |--------------------------------------------------------------------------
    | Localization
    |--------------------------------------------------------------------------
    */
    'localization' => [
        'default_language' => $_ENV['TELEGRAM_DEFAULT_LANGUAGE'] ?? 'fa',
        'supported_languages' => explode(',', $_ENV['TELEGRAM_SUPPORTED_LANGUAGES'] ?? 'fa,en'),
        'auto_detect_language' => (bool)($_ENV['TELEGRAM_AUTO_DETECT_LANGUAGE'] ?? true),
        'fallback_language' => $_ENV['TELEGRAM_FALLBACK_LANGUAGE'] ?? 'en',
        'rtl_languages' => ['fa', 'ar', 'he', 'ur'],
    ],
];
