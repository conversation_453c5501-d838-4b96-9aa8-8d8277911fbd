<?php

declare(strict_types=1);

namespace WeBot\Middleware;

/**
 * CORS Middleware
 *
 * Handles Cross-Origin Resource Sharing (CORS) headers
 * for API requests and browser security.
 *
 * @package WeBot\Middleware
 * @version 2.0
 */
class CorsMiddleware
{
    private array $config;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'allowed_origins' => ['*'],
            'allowed_methods' => ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
            'allowed_headers' => ['Content-Type', 'Authorization', 'X-Requested-With', 'X-CSRF-Token'],
            'exposed_headers' => ['X-Total-Count', 'X-Page-Count'],
            'max_age' => 86400, // 24 hours
            'allow_credentials' => true,
            'vary_header' => true
        ], $config);
    }

    /**
     * Handle CORS middleware
     */
    public function handle(array $request, callable $next): array
    {
        // Handle preflight OPTIONS request
        if (($request['method'] ?? 'GET') === 'OPTIONS') {
            return $this->handlePreflightRequest($request);
        }

        // Process the request
        $response = $next($request);

        // Add CORS headers to response
        return $this->addCorsHeaders($request, $response);
    }

    /**
     * Handle preflight OPTIONS request
     */
    private function handlePreflightRequest(array $request): array
    {
        $origin = $request['headers']['Origin'] ?? '';

        if (!$this->isOriginAllowed($origin)) {
            return [
                'success' => false,
                'error' => 'Origin not allowed',
                'status_code' => 403
            ];
        }

        $headers = [
            'Access-Control-Allow-Origin' => $this->getAllowedOrigin($origin),
            'Access-Control-Allow-Methods' => implode(', ', $this->config['allowed_methods']),
            'Access-Control-Allow-Headers' => implode(', ', $this->config['allowed_headers']),
            'Access-Control-Max-Age' => (string) $this->config['max_age']
        ];

        if ($this->config['allow_credentials']) {
            $headers['Access-Control-Allow-Credentials'] = 'true';
        }

        if ($this->config['vary_header']) {
            $headers['Vary'] = 'Origin, Access-Control-Request-Method, Access-Control-Request-Headers';
        }

        return [
            'success' => true,
            'status_code' => 204,
            'headers' => $headers,
            'body' => ''
        ];
    }

    /**
     * Add CORS headers to response
     */
    private function addCorsHeaders(array $request, array $response): array
    {
        $origin = $request['headers']['Origin'] ?? '';

        if (!$this->isOriginAllowed($origin)) {
            return $response;
        }

        $corsHeaders = [
            'Access-Control-Allow-Origin' => $this->getAllowedOrigin($origin)
        ];

        if ($this->config['allow_credentials']) {
            $corsHeaders['Access-Control-Allow-Credentials'] = 'true';
        }

        if (!empty($this->config['exposed_headers'])) {
            $corsHeaders['Access-Control-Expose-Headers'] = implode(', ', $this->config['exposed_headers']);
        }

        if ($this->config['vary_header']) {
            $corsHeaders['Vary'] = 'Origin';
        }

        // Merge CORS headers with existing response headers
        $response['headers'] = array_merge($response['headers'] ?? [], $corsHeaders);

        return $response;
    }

    /**
     * Check if origin is allowed
     */
    private function isOriginAllowed(string $origin): bool
    {
        if (empty($origin)) {
            return true; // Allow requests without Origin header (same-origin)
        }

        if (in_array('*', $this->config['allowed_origins'])) {
            return true;
        }

        return in_array($origin, $this->config['allowed_origins']);
    }

    /**
     * Get allowed origin for response
     */
    private function getAllowedOrigin(string $origin): string
    {
        if (in_array('*', $this->config['allowed_origins']) && !$this->config['allow_credentials']) {
            return '*';
        }

        return $origin;
    }
}
