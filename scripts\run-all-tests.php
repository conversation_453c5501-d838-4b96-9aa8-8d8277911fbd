<?php

declare(strict_types=1);

/**
 * WeBot Complete Test Suite Runner
 * 
 * Runs all tests including unit, integration, and E2E tests
 * with comprehensive reporting and quality gates.
 * 
 * @package WeBot\Scripts
 * @version 2.0
 */

// Configuration
const TEST_TIMEOUT = 300; // 5 minutes
const MIN_COVERAGE = 80.0;
const MAX_MEMORY = '512M';

// Colors for output
const COLOR_RED = "\033[31m";
const COLOR_GREEN = "\033[32m";
const COLOR_YELLOW = "\033[33m";
const COLOR_BLUE = "\033[34m";
const COLOR_CYAN = "\033[36m";
const COLOR_RESET = "\033[0m";

/**
 * Print colored message
 */
function printMessage(string $message, string $color = COLOR_RESET): void
{
    echo $color . $message . COLOR_RESET . PHP_EOL;
}

/**
 * Print test header
 */
function printHeader(): void
{
    printMessage("", COLOR_BLUE);
    printMessage("🧪 WeBot Complete Test Suite", COLOR_BLUE);
    printMessage("============================", COLOR_BLUE);
    printMessage("Running comprehensive tests with quality gates", COLOR_CYAN);
    printMessage("");
}

/**
 * Check prerequisites
 */
function checkPrerequisites(): bool
{
    printMessage("🔍 Checking prerequisites...", COLOR_BLUE);
    
    $checks = [
        'PHP version >= 8.1' => version_compare(PHP_VERSION, '8.1.0', '>='),
        'Composer installed' => file_exists('vendor/autoload.php'),
        'PHPUnit available' => file_exists('vendor/bin/phpunit'),
        'PHPStan available' => file_exists('vendor/bin/phpstan'),
        'PHPCS available' => file_exists('vendor/bin/phpcs'),
        'Storage directory writable' => is_writable('storage'),
        'Reports directory exists' => is_dir('reports') || mkdir('reports', 0755, true)
    ];
    
    $allPassed = true;
    foreach ($checks as $check => $passed) {
        $status = $passed ? '✅' : '❌';
        $color = $passed ? COLOR_GREEN : COLOR_RED;
        printMessage("  $status $check", $color);
        
        if (!$passed) {
            $allPassed = false;
        }
    }
    
    if ($allPassed) {
        printMessage("✅ All prerequisites met", COLOR_GREEN);
    } else {
        printMessage("❌ Some prerequisites failed", COLOR_RED);
    }
    
    return $allPassed;
}

/**
 * Run command with timeout and capture output
 */
function runCommand(string $command, int $timeout = TEST_TIMEOUT): array
{
    $descriptorspec = [
        0 => ['pipe', 'r'],
        1 => ['pipe', 'w'],
        2 => ['pipe', 'w']
    ];
    
    $process = proc_open($command, $descriptorspec, $pipes);
    
    if (!is_resource($process)) {
        return ['success' => false, 'output' => '', 'error' => 'Failed to start process'];
    }
    
    fclose($pipes[0]);
    
    $output = '';
    $error = '';
    $start = time();
    
    while (time() - $start < $timeout) {
        $status = proc_get_status($process);
        
        if (!$status['running']) {
            $output = stream_get_contents($pipes[1]);
            $error = stream_get_contents($pipes[2]);
            break;
        }
        
        usleep(100000); // 0.1 second
    }
    
    fclose($pipes[1]);
    fclose($pipes[2]);
    
    $exitCode = proc_close($process);
    
    return [
        'success' => $exitCode === 0,
        'exit_code' => $exitCode,
        'output' => $output,
        'error' => $error
    ];
}

/**
 * Run syntax check
 */
function runSyntaxCheck(): bool
{
    printMessage("🔍 Running PHP syntax check...", COLOR_BLUE);
    
    $files = [];
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator('src', RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($iterator as $file) {
        if ($file->getExtension() === 'php') {
            $files[] = $file->getPathname();
        }
    }
    
    $errors = 0;
    foreach ($files as $file) {
        $result = runCommand("php -l \"$file\"", 10);
        if (!$result['success']) {
            printMessage("  ❌ Syntax error in $file", COLOR_RED);
            printMessage("     " . trim($result['error']), COLOR_RED);
            $errors++;
        }
    }
    
    if ($errors === 0) {
        printMessage("✅ PHP syntax check passed", COLOR_GREEN);
        return true;
    } else {
        printMessage("❌ PHP syntax check failed ($errors errors)", COLOR_RED);
        return false;
    }
}

/**
 * Run code style check
 */
function runCodeStyleCheck(): bool
{
    printMessage("🎨 Running code style check...", COLOR_BLUE);
    
    $result = runCommand('vendor/bin/phpcs --standard=phpcs.xml --report=summary');
    
    if ($result['success']) {
        printMessage("✅ Code style check passed", COLOR_GREEN);
        return true;
    } else {
        printMessage("❌ Code style violations found", COLOR_RED);
        printMessage($result['output'], COLOR_YELLOW);
        return false;
    }
}

/**
 * Run static analysis
 */
function runStaticAnalysis(): bool
{
    printMessage("🔬 Running static analysis...", COLOR_BLUE);
    
    $result = runCommand('vendor/bin/phpstan analyse --configuration=phpstan.neon --no-progress');
    
    if ($result['success']) {
        printMessage("✅ Static analysis passed", COLOR_GREEN);
        return true;
    } else {
        printMessage("❌ Static analysis failed", COLOR_RED);
        printMessage($result['output'], COLOR_YELLOW);
        return false;
    }
}

/**
 * Run unit tests
 */
function runUnitTests(): bool
{
    printMessage("🧪 Running unit tests...", COLOR_BLUE);
    
    $result = runCommand('vendor/bin/phpunit --testsuite=Unit --coverage-clover=reports/unit-coverage.xml --log-junit=reports/unit-junit.xml');
    
    if ($result['success']) {
        printMessage("✅ Unit tests passed", COLOR_GREEN);
        return true;
    } else {
        printMessage("❌ Unit tests failed", COLOR_RED);
        printMessage($result['output'], COLOR_YELLOW);
        return false;
    }
}

/**
 * Run integration tests
 */
function runIntegrationTests(): bool
{
    printMessage("🔗 Running integration tests...", COLOR_BLUE);
    
    // Check if test database is available
    if (!checkTestDatabase()) {
        printMessage("⚠️  Test database not available, skipping integration tests", COLOR_YELLOW);
        return true;
    }
    
    $result = runCommand('vendor/bin/phpunit --testsuite=Integration --log-junit=reports/integration-junit.xml');
    
    if ($result['success']) {
        printMessage("✅ Integration tests passed", COLOR_GREEN);
        return true;
    } else {
        printMessage("❌ Integration tests failed", COLOR_RED);
        printMessage($result['output'], COLOR_YELLOW);
        return false;
    }
}

/**
 * Check if test database is available
 */
function checkTestDatabase(): bool
{
    // Simple check - in real implementation, you'd check actual database connection
    return getenv('DB_HOST') !== false || file_exists('storage/database.sqlite');
}

/**
 * Run E2E tests
 */
function runE2ETests(): bool
{
    printMessage("🌐 Running E2E tests...", COLOR_BLUE);
    
    if (!file_exists('tests/E2E')) {
        printMessage("⚠️  No E2E tests found, skipping", COLOR_YELLOW);
        return true;
    }
    
    $result = runCommand('vendor/bin/phpunit --testsuite=E2E --log-junit=reports/e2e-junit.xml');
    
    if ($result['success']) {
        printMessage("✅ E2E tests passed", COLOR_GREEN);
        return true;
    } else {
        printMessage("❌ E2E tests failed", COLOR_RED);
        printMessage($result['output'], COLOR_YELLOW);
        return false;
    }
}

/**
 * Check coverage threshold
 */
function checkCoverageThreshold(): bool
{
    printMessage("📊 Checking coverage threshold...", COLOR_BLUE);
    
    if (!file_exists('reports/unit-coverage.xml')) {
        printMessage("⚠️  Coverage report not found", COLOR_YELLOW);
        return true;
    }
    
    $result = runCommand('php scripts/check-coverage.php');
    
    if ($result['success']) {
        printMessage("✅ Coverage threshold met", COLOR_GREEN);
        return true;
    } else {
        printMessage("❌ Coverage threshold not met", COLOR_RED);
        printMessage($result['output'], COLOR_YELLOW);
        return false;
    }
}

/**
 * Generate final report
 */
function generateFinalReport(array $results): void
{
    printMessage("📋 Generating final report...", COLOR_BLUE);
    
    $report = [
        'timestamp' => date('Y-m-d H:i:s'),
        'php_version' => PHP_VERSION,
        'results' => $results,
        'summary' => [
            'total_tests' => count($results),
            'passed' => count(array_filter($results)),
            'failed' => count($results) - count(array_filter($results)),
            'success_rate' => round((count(array_filter($results)) / count($results)) * 100, 2)
        ]
    ];
    
    file_put_contents('reports/test-report.json', json_encode($report, JSON_PRETTY_PRINT));
    
    // Generate HTML report
    $html = generateHtmlReport($report);
    file_put_contents('reports/test-report.html', $html);
    
    printMessage("✅ Reports generated:", COLOR_GREEN);
    printMessage("  - reports/test-report.json", COLOR_CYAN);
    printMessage("  - reports/test-report.html", COLOR_CYAN);
}

/**
 * Generate HTML report
 */
function generateHtmlReport(array $report): string
{
    $successRate = $report['summary']['success_rate'];
    $statusColor = $successRate >= 80 ? 'green' : ($successRate >= 60 ? 'orange' : 'red');
    
    return "<!DOCTYPE html>
<html lang='fa' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <title>WeBot Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .summary { margin: 20px 0; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        .passed { background: #d4edda; color: #155724; }
        .failed { background: #f8d7da; color: #721c24; }
        .success-rate { font-size: 24px; color: $statusColor; font-weight: bold; }
    </style>
</head>
<body>
    <div class='header'>
        <h1>گزارش تست WeBot</h1>
        <p>تاریخ: {$report['timestamp']}</p>
        <p>نسخه PHP: {$report['php_version']}</p>
    </div>
    
    <div class='summary'>
        <h2>خلاصه نتایج</h2>
        <p>کل تست‌ها: {$report['summary']['total_tests']}</p>
        <p>موفق: {$report['summary']['passed']}</p>
        <p>ناموفق: {$report['summary']['failed']}</p>
        <p class='success-rate'>نرخ موفقیت: {$report['summary']['success_rate']}%</p>
    </div>
    
    <div class='results'>
        <h2>جزئیات تست‌ها</h2>";
    
    foreach ($report['results'] as $test => $passed) {
        $class = $passed ? 'passed' : 'failed';
        $status = $passed ? '✅ موفق' : '❌ ناموفق';
        $html .= "<div class='test-result $class'>$test: $status</div>";
    }
    
    $html .= "
    </div>
</body>
</html>";
    
    return $html;
}

/**
 * Main execution
 */
function main(): int
{
    printHeader();
    
    if (!checkPrerequisites()) {
        return 1;
    }
    
    $tests = [
        'Syntax Check' => 'runSyntaxCheck',
        'Code Style Check' => 'runCodeStyleCheck',
        'Static Analysis' => 'runStaticAnalysis',
        'Unit Tests' => 'runUnitTests',
        'Integration Tests' => 'runIntegrationTests',
        'E2E Tests' => 'runE2ETests',
        'Coverage Check' => 'checkCoverageThreshold'
    ];
    
    $results = [];
    $overallSuccess = true;
    
    foreach ($tests as $testName => $testFunction) {
        $success = $testFunction();
        $results[$testName] = $success;
        
        if (!$success) {
            $overallSuccess = false;
        }
        
        printMessage(""); // Empty line for readability
    }
    
    generateFinalReport($results);
    
    printMessage("", COLOR_BLUE);
    printMessage("🏁 Test Suite Complete", COLOR_BLUE);
    printMessage("======================", COLOR_BLUE);
    
    if ($overallSuccess) {
        printMessage("🎉 All tests passed successfully!", COLOR_GREEN);
        printMessage("WeBot is ready for deployment.", COLOR_GREEN);
        return 0;
    } else {
        printMessage("💥 Some tests failed!", COLOR_RED);
        printMessage("Please review the results and fix issues before deployment.", COLOR_YELLOW);
        return 1;
    }
}

// Set memory limit
ini_set('memory_limit', MAX_MEMORY);

// Run the test suite
exit(main());
