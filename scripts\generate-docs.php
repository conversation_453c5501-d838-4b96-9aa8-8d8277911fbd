#!/usr/bin/env php
<?php

declare(strict_types=1);

/**
 * WeBot API Documentation Generator CLI
 * 
 * Command-line tool for generating comprehensive API documentation
 * including OpenAPI specs, Markdown docs, and code examples.
 * 
 * Usage:
 *   php scripts/generate-docs.php [options]
 * 
 * Options:
 *   --format=FORMAT    Output format (all, openapi, markdown, postman, examples)
 *   --output=DIR       Output directory (default: docs/api)
 *   --base-url=URL     Base API URL (default: https://api.webot.com)
 *   --version=VERSION  API version (default: 2.0.0)
 *   --verbose          Verbose output
 *   --help             Show this help message
 * 
 * Examples:
 *   php scripts/generate-docs.php --format=all --verbose
 *   php scripts/generate-docs.php --format=openapi --output=docs/openapi
 *   php scripts/generate-docs.php --format=markdown --base-url=http://localhost:8000
 * 
 * @package WeBot\Scripts
 * @version 2.0
 */

// Autoload dependencies
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../src/Core/bootstrap.php';

use WeBot\Core\Config;
use WeBot\Documentation\ApiDocumentationGenerator;
use WeBot\Utils\Logger;

class DocumentationCLI
{
    private array $options;
    private bool $verbose;
    private Logger $logger;
    
    public function __construct(array $argv)
    {
        $this->parseArguments($argv);
        $this->verbose = isset($this->options['verbose']);
        $this->logger = Logger::getInstance();
        
        if (isset($this->options['help'])) {
            $this->showHelp();
            exit(0);
        }
    }
    
    /**
     * Run documentation generation
     */
    public function run(): int
    {
        try {
            $this->log("🚀 Starting WeBot API Documentation Generation");
            
            // Validate options
            $this->validateOptions();
            
            // Initialize configuration
            $config = new Config();
            
            // Set up generator
            $outputDir = $this->options['output'] ?? 'docs/api';
            $generator = new ApiDocumentationGenerator($config, $outputDir);
            
            // Generate documentation
            $format = $this->options['format'] ?? 'all';
            $result = $this->generateDocumentation($generator, $format);
            
            if ($result['success']) {
                $this->log("✅ Documentation generation completed successfully!");
                $this->showResults($result['files']);
                return 0;
            } else {
                $this->error("❌ Documentation generation failed: " . $result['error']);
                return 1;
            }
            
        } catch (\Exception $e) {
            $this->error("💥 Fatal error: " . $e->getMessage());
            if ($this->verbose) {
                $this->error("Stack trace:\n" . $e->getTraceAsString());
            }
            return 1;
        }
    }
    
    /**
     * Generate documentation based on format
     */
    private function generateDocumentation(ApiDocumentationGenerator $generator, string $format): array
    {
        switch ($format) {
            case 'all':
                $this->log("📚 Generating all documentation formats...");
                return $generator->generateAll();
                
            case 'openapi':
                $this->log("📋 Generating OpenAPI specification...");
                $files = [
                    'openapi_json' => $generator->generateOpenApiJson(),
                    'openapi_yaml' => $generator->generateOpenApiYaml()
                ];
                return ['success' => true, 'files' => $files];
                
            case 'markdown':
                $this->log("📝 Generating Markdown documentation...");
                $files = ['markdown' => $generator->generateMarkdownDocs()];
                return ['success' => true, 'files' => $files];
                
            case 'postman':
                $this->log("📮 Generating Postman collection...");
                $files = ['postman' => $generator->generatePostmanCollection()];
                return ['success' => true, 'files' => $files];
                
            case 'examples':
                $this->log("💻 Generating code examples...");
                $files = ['examples' => $generator->generateCodeExamples()];
                return ['success' => true, 'files' => $files];
                
            case 'auth':
                $this->log("🔐 Generating authentication guide...");
                $files = ['auth_guide' => $generator->generateAuthenticationGuide()];
                return ['success' => true, 'files' => $files];
                
            case 'errors':
                $this->log("⚠️ Generating error codes documentation...");
                $files = ['error_codes' => $generator->generateErrorCodesDoc()];
                return ['success' => true, 'files' => $files];
                
            case 'rate-limiting':
                $this->log("🚦 Generating rate limiting documentation...");
                $files = ['rate_limiting' => $generator->generateRateLimitingDoc()];
                return ['success' => true, 'files' => $files];
                
            default:
                throw new \InvalidArgumentException("Unknown format: {$format}");
        }
    }
    
    /**
     * Parse command line arguments
     */
    private function parseArguments(array $argv): void
    {
        $this->options = [];
        
        for ($i = 1; $i < count($argv); $i++) {
            $arg = $argv[$i];
            
            if (str_starts_with($arg, '--')) {
                if (str_contains($arg, '=')) {
                    [$key, $value] = explode('=', substr($arg, 2), 2);
                    $this->options[$key] = $value;
                } else {
                    $this->options[substr($arg, 2)] = true;
                }
            }
        }
    }
    
    /**
     * Validate command line options
     */
    private function validateOptions(): void
    {
        $validFormats = ['all', 'openapi', 'markdown', 'postman', 'examples', 'auth', 'errors', 'rate-limiting'];
        
        if (isset($this->options['format'])) {
            $format = $this->options['format'];
            if (!in_array($format, $validFormats)) {
                throw new \InvalidArgumentException(
                    "Invalid format '{$format}'. Valid formats: " . implode(', ', $validFormats)
                );
            }
        }
        
        if (isset($this->options['output'])) {
            $outputDir = $this->options['output'];
            if (!is_dir(dirname($outputDir)) && !mkdir(dirname($outputDir), 0755, true)) {
                throw new \RuntimeException("Cannot create output directory: {$outputDir}");
            }
        }
        
        if (isset($this->options['base-url'])) {
            $baseUrl = $this->options['base-url'];
            if (!filter_var($baseUrl, FILTER_VALIDATE_URL)) {
                throw new \InvalidArgumentException("Invalid base URL: {$baseUrl}");
            }
        }
        
        if (isset($this->options['version'])) {
            $version = $this->options['version'];
            if (!preg_match('/^\d+\.\d+\.\d+$/', $version)) {
                throw new \InvalidArgumentException("Invalid version format: {$version}. Use semantic versioning (e.g., 2.0.0)");
            }
        }
    }
    
    /**
     * Show help message
     */
    private function showHelp(): void
    {
        echo <<<'HELP'
WeBot API Documentation Generator

USAGE:
    php scripts/generate-docs.php [OPTIONS]

OPTIONS:
    --format=FORMAT      Output format (default: all)
                        Valid formats:
                          all           - Generate all documentation
                          openapi       - OpenAPI specification (JSON + YAML)
                          markdown      - Markdown documentation
                          postman       - Postman collection
                          examples      - Code examples
                          auth          - Authentication guide
                          errors        - Error codes documentation
                          rate-limiting - Rate limiting documentation

    --output=DIR         Output directory (default: docs/api)
    --base-url=URL       Base API URL (default: https://api.webot.com)
    --version=VERSION    API version (default: 2.0.0)
    --verbose            Enable verbose output
    --help               Show this help message

EXAMPLES:
    # Generate all documentation with verbose output
    php scripts/generate-docs.php --format=all --verbose

    # Generate only OpenAPI specification
    php scripts/generate-docs.php --format=openapi --output=docs/openapi

    # Generate documentation for local development
    php scripts/generate-docs.php --base-url=http://localhost:8000 --version=2.1.0

    # Generate specific documentation types
    php scripts/generate-docs.php --format=markdown
    php scripts/generate-docs.php --format=postman
    php scripts/generate-docs.php --format=examples

OUTPUT:
    The generated documentation will be saved to the specified output directory.
    Files generated depend on the format option:

    all:           All documentation files
    openapi:       openapi.json, openapi.yaml
    markdown:      API_DOCUMENTATION.md
    postman:       WeBot_API.postman_collection.json
    examples:      CODE_EXAMPLES.md
    auth:          AUTHENTICATION_GUIDE.md
    errors:        ERROR_CODES.md
    rate-limiting: RATE_LIMITING.md

HELP;
    }
    
    /**
     * Show generation results
     */
    private function showResults(array $files): void
    {
        $this->log("\n📁 Generated files:");
        
        foreach ($files as $type => $filename) {
            $size = file_exists($filename) ? $this->formatFileSize(filesize($filename)) : 'Unknown';
            $this->log("  ✓ {$type}: {$filename} ({$size})");
        }
        
        $this->log("\n🎉 Documentation is ready!");
        $this->log("📖 You can now:");
        $this->log("   • View the documentation files in your output directory");
        $this->log("   • Import the Postman collection for API testing");
        $this->log("   • Use the OpenAPI spec with Swagger UI or other tools");
        $this->log("   • Share the documentation with your team");
    }
    
    /**
     * Format file size for display
     */
    private function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $factor = floor((strlen((string) $bytes) - 1) / 3);
        
        return sprintf("%.1f %s", $bytes / pow(1024, $factor), $units[$factor]);
    }
    
    /**
     * Log message with timestamp
     */
    private function log(string $message): void
    {
        if ($this->verbose) {
            $timestamp = date('Y-m-d H:i:s');
            echo "[{$timestamp}] {$message}\n";
        } else {
            echo "{$message}\n";
        }
    }
    
    /**
     * Log error message
     */
    private function error(string $message): void
    {
        $timestamp = date('Y-m-d H:i:s');
        fwrite(STDERR, "[{$timestamp}] {$message}\n");
    }
}

// Run the CLI tool
$cli = new DocumentationCLI($argv);
exit($cli->run());
