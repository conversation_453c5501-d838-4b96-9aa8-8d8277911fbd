<?php

declare(strict_types=1);

namespace WeBot\Core\Cache;

use WeBot\Core\Cache\Contracts\CacheInterface;
use WeBot\Core\Cache\Drivers\RedisDriver;
use WeBot\Core\Cache\Drivers\ArrayDriver;
use WeBot\Core\Cache\Managers\CacheTagManager;
use WeBot\Core\Cache\Managers\CacheSessionManager;
use WeBot\Core\Cache\Managers\CacheStatsManager;
use WeBot\Core\Cache\Managers\CacheSpecializedManager;

/**
 * Cache Manager
 *
 * Main cache manager that coordinates different cache operations
 * and delegates specialized tasks to appropriate managers.
 *
 * @package WeBot\Core\Cache
 * @version 2.0
 */
class CacheManager implements CacheInterface
{
    private CacheInterface $driver;
    private CacheTagManager $tagManager;
    private CacheSessionManager $sessionManager;
    private CacheStatsManager $statsManager;
    private CacheSpecializedManager $specializedManager;

    private string $prefix;
    private int $defaultTtl;
    private bool $enabled;

    public function __construct(array $config = [])
    {
        $this->prefix = $config['prefix'] ?? 'webot:';
        $this->defaultTtl = $config['default_ttl'] ?? 3600;
        $this->enabled = $config['enabled'] ?? true;

        // Initialize driver
        $this->driver = $this->createDriver($config);

        // Initialize managers
        $this->tagManager = new CacheTagManager($this->driver);
        $this->sessionManager = new CacheSessionManager($this->driver);
        $this->statsManager = new CacheStatsManager($this->driver);
        $this->specializedManager = new CacheSpecializedManager($this->driver);
    }

    /**
     * Create cache driver based on configuration
     */
    private function createDriver(array $config): CacheInterface
    {
        $driver = $config['driver'] ?? 'array';

        switch ($driver) {
            case 'redis':
                return new RedisDriver($config);
            case 'array':
            default:
                return new ArrayDriver();
        }
    }

    /**
     * Get cached value
     */
    public function get(string $key, $default = null)
    {
        if (!$this->enabled) {
            return $default;
        }

        return $this->driver->get($this->makeKey($key), $default);
    }

    /**
     * Set cached value
     */
    public function set(string $key, $value, ?int $ttl = null): bool
    {
        if (!$this->enabled) {
            return false;
        }

        $ttl = $ttl ?? $this->defaultTtl;
        return $this->driver->set($this->makeKey($key), $value, $ttl);
    }

    /**
     * Remember pattern - get from cache or execute callback
     */
    public function remember(string $key, callable $callback, ?int $ttl = null)
    {
        $value = $this->get($key);

        if ($value !== null) {
            return $value;
        }

        $value = $callback();
        $this->set($key, $value, $ttl);

        return $value;
    }

    /**
     * Delete cached value
     */
    public function delete(string $key): bool
    {
        if (!$this->enabled) {
            return false;
        }

        return $this->driver->delete($this->makeKey($key));
    }

    /**
     * Check if key exists
     */
    public function exists(string $key): bool
    {
        if (!$this->enabled) {
            return false;
        }

        return $this->driver->exists($this->makeKey($key));
    }

    /**
     * Increment value
     */
    public function increment(string $key, int $value = 1): int
    {
        if (!$this->enabled) {
            return 0;
        }

        return $this->driver->increment($this->makeKey($key), $value);
    }

    /**
     * Decrement value
     */
    public function decrement(string $key, int $value = 1): int
    {
        if (!$this->enabled) {
            return 0;
        }

        return $this->driver->decrement($this->makeKey($key), $value);
    }

    /**
     * Set multiple values
     */
    public function setMultiple(array $values, ?int $ttl = null): bool
    {
        if (!$this->enabled) {
            return false;
        }

        $prefixedValues = [];
        foreach ($values as $key => $value) {
            $prefixedValues[$this->makeKey($key)] = $value;
        }

        return $this->driver->setMultiple($prefixedValues, $ttl ?? $this->defaultTtl);
    }

    /**
     * Get multiple values
     */
    public function getMultiple(array $keys, $default = null): array
    {
        if (!$this->enabled) {
            return array_fill_keys($keys, $default);
        }

        $prefixedKeys = array_map([$this, 'makeKey'], $keys);
        $results = $this->driver->getMultiple($prefixedKeys, $default);

        // Remove prefix from keys in results
        $finalResults = [];
        foreach ($keys as $originalKey) {
            $prefixedKey = $this->makeKey($originalKey);
            $finalResults[$originalKey] = $results[$prefixedKey] ?? $default;
        }

        return $finalResults;
    }

    /**
     * Flush all cache
     */
    public function flush(): bool
    {
        if (!$this->enabled) {
            return false;
        }

        return $this->driver->flush();
    }

    /**
     * Get tag manager
     */
    public function tags(): CacheTagManager
    {
        return $this->tagManager;
    }

    /**
     * Get session manager
     */
    public function sessions(): CacheSessionManager
    {
        return $this->sessionManager;
    }

    /**
     * Get stats manager
     */
    public function stats(): CacheStatsManager
    {
        return $this->statsManager;
    }

    /**
     * Get specialized manager
     */
    public function specialized(): CacheSpecializedManager
    {
        return $this->specializedManager;
    }

    /**
     * Enable cache
     */
    public function enable(): bool
    {
        $this->enabled = true;
        return true;
    }

    /**
     * Disable cache
     */
    public function disable(): bool
    {
        $this->enabled = false;
        return true;
    }

    /**
     * Check if cache is enabled
     */
    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    /**
     * Make cache key with prefix
     */
    private function makeKey(string $key): string
    {
        return $this->prefix . $key;
    }

    /**
     * Get driver instance
     */
    public function getDriver(): CacheInterface
    {
        return $this->driver;
    }
}
