# 🧪 WeBot 2.0 Testing Guide

Comprehensive testing guide for WeBot 2.0 including unit tests, integration tests, and debugging strategies.

## 📋 Testing Overview

WeBot 2.0 maintains **100% test coverage** with a comprehensive testing strategy:

- **Unit Tests** - Individual component testing
- **Integration Tests** - Service interaction testing  
- **E2E Tests** - Complete workflow testing
- **Performance Tests** - Load and stress testing
- **Security Tests** - Vulnerability assessment

## 🚀 Quick Start

### Run All Tests
```bash
# Run complete test suite
php vendor/bin/phpunit

# Run with coverage
php vendor/bin/phpunit --coverage-html coverage/

# Run specific test suite
php vendor/bin/phpunit tests/Unit/
php vendor/bin/phpunit tests/Integration/
php vendor/bin/phpunit tests/E2E/
```

### Test Environment Setup
```bash
# Copy test environment
cp .env.testing.example .env.testing

# Setup test database
php migrate.php migrate --env=testing

# Run tests
php vendor/bin/phpunit
```

## 🏗️ Test Structure

```
tests/
├── Unit/                      # Unit Tests (100% coverage)
│   ├── Controllers/           # Controller tests
│   ├── Services/             # Service tests
│   ├── Models/               # Model tests
│   ├── Core/                 # Core component tests
│   └── Utils/                # Utility tests
├── Integration/              # Integration Tests
│   ├── Database/             # Database integration
│   ├── API/                  # API endpoint tests
│   └── Services/             # Service integration
├── E2E/                      # End-to-End Tests
│   ├── UserJourney/          # Complete user workflows
│   ├── Payment/              # Payment workflows
│   └── Admin/                # Admin workflows
├── Performance/              # Performance Tests
│   ├── Load/                 # Load testing
│   └── Stress/               # Stress testing
└── Security/                 # Security Tests
    ├── Validation/           # Input validation
    └── Authentication/       # Auth security
```

## 🔧 Unit Testing

### Writing Unit Tests

```php
<?php

namespace WeBot\Tests\Unit\Services;

use WeBot\Tests\Unit\BaseTestCase;
use WeBot\Services\UserService;
use WeBot\Models\User;

class UserServiceTest extends BaseTestCase
{
    private UserService $userService;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->userService = $this->container->make(UserService::class);
    }
    
    public function testCreateUser(): void
    {
        // Arrange
        $userData = [
            'telegram_id' => 123456789,
            'first_name' => 'Test User',
            'username' => 'testuser'
        ];
        
        // Act
        $user = $this->userService->createUser($userData);
        
        // Assert
        $this->assertInstanceOf(User::class, $user);
        $this->assertEquals(123456789, $user->telegram_id);
        $this->assertEquals('Test User', $user->first_name);
    }
    
    public function testCreateUserWithInvalidData(): void
    {
        // Arrange
        $invalidData = ['invalid' => 'data'];
        
        // Act & Assert
        $this->expectException(\WeBot\Exceptions\ValidationException::class);
        $this->userService->createUser($invalidData);
    }
}
```

### Test Utilities

```php
<?php

namespace WeBot\Tests\Unit;

use PHPUnit\Framework\TestCase;
use WeBot\Core\DIContainer;

abstract class BaseTestCase extends TestCase
{
    protected DIContainer $container;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Setup test container
        $this->container = new DIContainer();
        $this->setupTestBindings();
    }
    
    protected function setupTestBindings(): void
    {
        // Mock database
        $this->container->bind(\WeBot\Core\Database::class, function() {
            return $this->createMock(\WeBot\Core\Database::class);
        });
        
        // Mock services
        $this->container->bind('telegram', function() {
            $telegram = $this->createMock(\WeBot\Services\TelegramService::class);
            $telegram->method('sendMessage')->willReturn(['ok' => true]);
            return $telegram;
        });
    }
    
    protected function createTestMessage(array $data = []): array
    {
        return array_merge([
            'message_id' => 1,
            'from' => [
                'id' => 123456789,
                'first_name' => 'Test',
                'username' => 'testuser'
            ],
            'chat' => [
                'id' => 123456789,
                'type' => 'private'
            ],
            'date' => time(),
            'text' => '/start'
        ], $data);
    }
}
```

## 🔗 Integration Testing

### Database Integration
```php
<?php

namespace WeBot\Tests\Integration\Database;

use WeBot\Tests\Integration\BaseIntegrationTest;

class UserRepositoryTest extends BaseIntegrationTest
{
    public function testCreateAndRetrieveUser(): void
    {
        // Arrange
        $userData = [
            'telegram_id' => 123456789,
            'first_name' => 'Integration Test',
            'username' => 'integrationtest'
        ];
        
        // Act
        $userId = $this->userRepository->create($userData);
        $user = $this->userRepository->findById($userId);
        
        // Assert
        $this->assertNotNull($user);
        $this->assertEquals(123456789, $user['telegram_id']);
    }
}
```

### API Integration
```php
<?php

namespace WeBot\Tests\Integration\API;

use WeBot\Tests\Integration\BaseIntegrationTest;

class WebhookTest extends BaseIntegrationTest
{
    public function testTelegramWebhook(): void
    {
        // Arrange
        $webhookData = [
            'update_id' => 123,
            'message' => $this->createTestMessage()
        ];
        
        // Act
        $response = $this->post('/webhook.php', $webhookData);
        
        // Assert
        $this->assertEquals(200, $response->getStatusCode());
    }
}
```

## 🌐 End-to-End Testing

### User Journey Tests
```php
<?php

namespace WeBot\Tests\E2E\UserJourney;

use WeBot\Tests\E2E\BaseE2ETest;

class NewUserRegistrationTest extends BaseE2ETest
{
    public function testCompleteUserRegistration(): void
    {
        // 1. User starts bot
        $this->sendMessage('/start');
        $this->assertResponseContains('خوش آمدید');
        
        // 2. User provides phone number
        $this->sendMessage('+989123456789');
        $this->assertResponseContains('تایید شد');
        
        // 3. User selects service
        $this->sendCallback('select_service_1');
        $this->assertResponseContains('سرویس انتخاب شد');
        
        // 4. User makes payment
        $this->sendCallback('payment_zarinpal');
        $this->assertResponseContains('پرداخت');
        
        // 5. Verify user created
        $user = $this->getUserFromDatabase(123456789);
        $this->assertNotNull($user);
        $this->assertEquals('verified', $user['status']);
    }
}
```

## ⚡ Performance Testing

### Load Testing
```php
<?php

namespace WeBot\Tests\Performance\Load;

use WeBot\Tests\Performance\BasePerformanceTest;

class WebhookLoadTest extends BasePerformanceTest
{
    public function testWebhookUnderLoad(): void
    {
        $startTime = microtime(true);
        $requests = 1000;
        $concurrent = 50;
        
        // Simulate concurrent requests
        $this->runConcurrentRequests($requests, $concurrent, function() {
            return $this->post('/webhook.php', $this->createTestMessage());
        });
        
        $endTime = microtime(true);
        $duration = $endTime - $startTime;
        $rps = $requests / $duration;
        
        // Assert performance requirements
        $this->assertLessThan(10, $duration); // Max 10 seconds
        $this->assertGreaterThan(100, $rps);  // Min 100 RPS
    }
}
```

### Memory Testing
```php
public function testMemoryUsage(): void
{
    $initialMemory = memory_get_usage(true);
    
    // Perform operations
    for ($i = 0; $i < 1000; $i++) {
        $this->userService->createUser($this->generateUserData());
    }
    
    $finalMemory = memory_get_usage(true);
    $memoryIncrease = $finalMemory - $initialMemory;
    
    // Assert memory usage is reasonable
    $this->assertLessThan(50 * 1024 * 1024, $memoryIncrease); // Max 50MB
}
```

## 🔒 Security Testing

### Input Validation Tests
```php
<?php

namespace WeBot\Tests\Security\Validation;

use WeBot\Tests\Security\BaseSecurityTest;

class InputValidationTest extends BaseSecurityTest
{
    public function testSQLInjectionPrevention(): void
    {
        $maliciousInputs = [
            "'; DROP TABLE users; --",
            "1' OR '1'='1",
            "admin'/*",
            "1; DELETE FROM users WHERE 1=1; --"
        ];
        
        foreach ($maliciousInputs as $input) {
            $response = $this->sendMessage($input);
            $this->assertResponseSafe($response);
            $this->assertDatabaseIntact();
        }
    }
    
    public function testXSSPrevention(): void
    {
        $xssPayloads = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>",
            "';alert('xss');//"
        ];
        
        foreach ($xssPayloads as $payload) {
            $response = $this->sendMessage($payload);
            $this->assertNotContains('<script>', $response);
            $this->assertNotContains('javascript:', $response);
        }
    }
}
```

## 🐛 Debugging

### Debug Configuration
```php
// .env.testing
APP_DEBUG=true
LOG_LEVEL=debug
DB_LOG_QUERIES=true

// Enable Xdebug
XDEBUG_MODE=debug
XDEBUG_START_WITH_REQUEST=yes
```

### Debug Utilities
```php
<?php

// Debug helper functions
function dd($data) {
    var_dump($data);
    die();
}

function dump_sql() {
    $queries = \WeBot\Core\Database::getQueryLog();
    foreach ($queries as $query) {
        echo $query['sql'] . "\n";
        echo "Time: " . $query['time'] . "ms\n\n";
    }
}

function debug_container() {
    $container = app();
    echo "Registered services:\n";
    foreach ($container->getBindings() as $key => $binding) {
        echo "- $key\n";
    }
}
```

### Test Debugging
```bash
# Run single test with debug
php vendor/bin/phpunit --filter testSpecificMethod --debug

# Run with verbose output
php vendor/bin/phpunit --verbose

# Run with coverage and debug
php vendor/bin/phpunit --coverage-text --debug
```

## 📊 Test Metrics

### Coverage Requirements
- **Unit Tests**: 100% line coverage
- **Integration Tests**: 90% feature coverage
- **E2E Tests**: 80% user journey coverage

### Performance Benchmarks
- **Response Time**: < 100ms average
- **Memory Usage**: < 128MB per request
- **Database Queries**: < 10 per request
- **Cache Hit Rate**: > 90%

## 🔄 Continuous Testing

### GitHub Actions
```yaml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.1
          extensions: mysql, redis
          
      - name: Install dependencies
        run: composer install
        
      - name: Run tests
        run: php vendor/bin/phpunit --coverage-clover coverage.xml
        
      - name: Upload coverage
        uses: codecov/codecov-action@v1
```

## 📞 Support

- **Test Issues**: Check test logs in `storage/logs/test.log`
- **Performance Issues**: Use profiling tools
- **Coverage Issues**: Review coverage reports

---

**Next**: [Development Guide](../development/README.md)
