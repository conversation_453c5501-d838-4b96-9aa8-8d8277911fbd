#!/bin/bash
# WeBot Environment Setup Script

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
ENVIRONMENT=${1:-production}
INTERACTIVE=${2:-true}

echo -e "${BLUE}🔧 WeBot Environment Setup${NC}"
echo -e "${BLUE}Environment: ${ENVIRONMENT}${NC}"
echo "=================================="

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

prompt_input() {
    local prompt="$1"
    local default="$2"
    local secret="$3"
    
    if [ "$INTERACTIVE" = "false" ]; then
        echo "$default"
        return
    fi
    
    if [ "$secret" = "true" ]; then
        echo -n "$prompt: "
        read -s value
        echo
    else
        echo -n "$prompt [$default]: "
        read value
    fi
    
    echo "${value:-$default}"
}

generate_random_key() {
    openssl rand -hex 32
}

generate_password() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-25
}

# Check requirements
check_requirements() {
    log_info "Checking requirements..."
    
    local missing=()
    
    command -v docker >/dev/null 2>&1 || missing+=("docker")
    command -v docker-compose >/dev/null 2>&1 || missing+=("docker-compose")
    command -v openssl >/dev/null 2>&1 || missing+=("openssl")
    command -v curl >/dev/null 2>&1 || missing+=("curl")
    
    if [ ${#missing[@]} -ne 0 ]; then
        log_error "Missing required tools: ${missing[*]}"
        exit 1
    fi
    
    log_info "✅ All requirements satisfied"
}

# Setup environment file
setup_env_file() {
    log_info "Setting up environment file..."
    
    local env_file=".env"
    local template_file=".env.${ENVIRONMENT}"
    
    if [ ! -f "$template_file" ]; then
        log_error "Template file $template_file not found"
        exit 1
    fi
    
    # Copy template
    cp "$template_file" "$env_file"
    
    # Generate secure values
    local app_key=$(generate_random_key)
    local db_password=$(generate_password)
    local redis_password=$(generate_password)
    local webhook_secret=$(generate_random_key)
    
    # Replace placeholders
    sed -i "s/CHANGE_THIS_TO_32_CHARACTER_RANDOM_STRING/$app_key/g" "$env_file"
    sed -i "s/CHANGE_THIS_TO_SECURE_PASSWORD/$db_password/g" "$env_file"
    
    # Interactive configuration
    if [ "$INTERACTIVE" = "true" ]; then
        configure_interactive
    fi
    
    # Set secure permissions
    chmod 600 "$env_file"
    
    log_info "✅ Environment file created: $env_file"
}

# Interactive configuration
configure_interactive() {
    log_info "Interactive configuration..."
    
    # Application settings
    echo -e "\n${BLUE}Application Settings:${NC}"
    local app_url=$(prompt_input "Application URL" "https://your-domain.com")
    sed -i "s|APP_URL=.*|APP_URL=$app_url|g" .env
    
    # Database settings
    echo -e "\n${BLUE}Database Settings:${NC}"
    local db_name=$(prompt_input "Database name" "webot")
    local db_user=$(prompt_input "Database username" "webot")
    local db_pass=$(prompt_input "Database password" "$(generate_password)" true)
    
    sed -i "s/DB_DATABASE=.*/DB_DATABASE=$db_name/g" .env
    sed -i "s/DB_USERNAME=.*/DB_USERNAME=$db_user/g" .env
    sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=$db_pass/g" .env
    
    # Telegram settings
    echo -e "\n${BLUE}Telegram Bot Settings:${NC}"
    local bot_token=$(prompt_input "Bot token" "" true)
    local webhook_url="$app_url/webhook"
    
    sed -i "s/TELEGRAM_BOT_TOKEN=.*/TELEGRAM_BOT_TOKEN=$bot_token/g" .env
    sed -i "s|TELEGRAM_WEBHOOK_URL=.*|TELEGRAM_WEBHOOK_URL=$webhook_url|g" .env
    
    # Admin settings
    echo -e "\n${BLUE}Admin Settings:${NC}"
    local admin_ids=$(prompt_input "Admin user IDs (comma-separated)" "123456789")
    sed -i "s/ADMIN_IDS=.*/ADMIN_IDS=$admin_ids/g" .env
    
    # Payment gateways
    echo -e "\n${BLUE}Payment Gateways:${NC}"
    local zarinpal_merchant=$(prompt_input "ZarinPal Merchant ID" "")
    local nowpayments_key=$(prompt_input "NowPayments API Key" "" true)
    
    if [ -n "$zarinpal_merchant" ]; then
        sed -i "s/ZARINPAL_MERCHANT_ID=.*/ZARINPAL_MERCHANT_ID=$zarinpal_merchant/g" .env
    fi
    
    if [ -n "$nowpayments_key" ]; then
        sed -i "s/NOWPAYMENTS_API_KEY=.*/NOWPAYMENTS_API_KEY=$nowpayments_key/g" .env
    fi
}

# Setup SSL certificates
setup_ssl() {
    log_info "Setting up SSL certificates..."
    
    local ssl_dir="docker/ssl"
    mkdir -p "$ssl_dir"
    
    if [ "$INTERACTIVE" = "true" ]; then
        echo -e "\n${BLUE}SSL Certificate Setup:${NC}"
        echo "1. Use existing certificates"
        echo "2. Generate self-signed certificates"
        echo "3. Skip SSL setup"
        
        echo -n "Choose option [1]: "
        read ssl_option
        ssl_option=${ssl_option:-1}
        
        case $ssl_option in
            1)
                echo -n "Path to certificate file: "
                read cert_path
                echo -n "Path to private key file: "
                read key_path
                
                if [ -f "$cert_path" ] && [ -f "$key_path" ]; then
                    cp "$cert_path" "$ssl_dir/cert.pem"
                    cp "$key_path" "$ssl_dir/private.key"
                    chmod 600 "$ssl_dir/private.key"
                    log_info "✅ SSL certificates copied"
                else
                    log_error "Certificate files not found"
                fi
                ;;
            2)
                openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
                    -keyout "$ssl_dir/private.key" \
                    -out "$ssl_dir/cert.pem" \
                    -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
                chmod 600 "$ssl_dir/private.key"
                log_info "✅ Self-signed certificates generated"
                ;;
            3)
                log_warn "SSL setup skipped"
                ;;
        esac
    fi
}

# Setup directories
setup_directories() {
    log_info "Setting up directories..."
    
    local dirs=(
        "storage/logs"
        "storage/cache"
        "storage/sessions"
        "storage/uploads"
        "storage/secrets"
        "public/uploads"
        "docker/mysql/data"
        "docker/redis/data"
        "logs"
    )
    
    for dir in "${dirs[@]}"; do
        mkdir -p "$dir"
        chmod 755 "$dir"
    done
    
    # Set special permissions
    chmod 700 storage/secrets
    chmod 755 storage/logs
    
    log_info "✅ Directories created"
}

# Setup database
setup_database() {
    log_info "Setting up database..."
    
    # Start database container
    docker-compose up -d mysql
    
    # Wait for database to be ready
    log_info "Waiting for database to be ready..."
    local attempts=0
    while ! docker-compose exec mysql mysqladmin ping -h localhost --silent; do
        sleep 2
        attempts=$((attempts + 1))
        if [ $attempts -gt 30 ]; then
            log_error "Database failed to start"
            exit 1
        fi
    done
    
    # Run migrations
    log_info "Running database migrations..."
    if [ -f "migrations/migrate.php" ]; then
        docker-compose exec webot php migrations/migrate.php
        log_info "✅ Database migrations completed"
    fi
}

# Validate configuration
validate_config() {
    log_info "Validating configuration..."
    
    # Check required environment variables
    source .env
    
    local required_vars=(
        "APP_URL"
        "DB_PASSWORD"
        "TELEGRAM_BOT_TOKEN"
    )
    
    local missing=()
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing+=("$var")
        fi
    done
    
    if [ ${#missing[@]} -ne 0 ]; then
        log_error "Missing required variables: ${missing[*]}"
        exit 1
    fi
    
    log_info "✅ Configuration validated"
}

# Main setup process
main() {
    check_requirements
    setup_directories
    setup_env_file
    setup_ssl
    validate_config
    
    if [ "$ENVIRONMENT" = "production" ]; then
        setup_database
    fi
    
    echo -e "\n${GREEN}🎉 Environment setup completed!${NC}"
    echo -e "${BLUE}Next steps:${NC}"
    echo "1. Review .env file and adjust settings"
    echo "2. Start services: docker-compose up -d"
    echo "3. Check health: ./scripts/health-check.sh"
    echo "4. Set up monitoring and backups"
}

# Handle arguments
case "${1:-setup}" in
    "setup"|"production"|"staging"|"development")
        main
        ;;
    "validate")
        validate_config
        ;;
    *)
        echo "Usage: $0 {setup|production|staging|development|validate}"
        exit 1
        ;;
esac
