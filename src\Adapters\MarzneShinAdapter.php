<?php

declare(strict_types=1);

namespace WeBot\Adapters;

use WeBot\Contracts\PanelAdapterInterface;
use WeBot\Exceptions\PanelException;
use WeBot\Utils\HttpClientWrapper;

/**
 * Marzneshin Panel Adapter
 *
 * Enhanced adapter for Marzneshin panel with advanced features
 * including OAuth2 authentication, templates, user groups,
 * and advanced monitoring capabilities.
 *
 * @package WeBot\Adapters
 * @version 2.0
 */
class MarzneShinAdapter implements PanelAdapterInterface
{
    private $httpClient;
    private array $config;
    private ?string $accessToken = null;
    private ?string $refreshToken = null;
    private ?int $tokenExpiry = null;
    private string $compatibilityMode = 'native'; // 'native' or 'marzban'

    public function __construct(array $config)
    {
        $this->config = $config;

        // Use HttpClientWrapper for compatibility
        $this->httpClient = new HttpClientWrapper([
            'base_uri' => rtrim($config['url'], '/') . '/api/v1/',
            'timeout' => $config['timeout'] ?? 30,
            'verify' => $config['verify_ssl'] ?? true,
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'User-Agent' => 'WeBot/2.0 MarzneShin-Adapter'
            ]
        ]);

        // Set compatibility mode based on config
        $this->compatibilityMode = $config['compatibility_mode'] ?? 'native';
    }

    /**
     * OAuth2 Authentication with Marzneshin (Enhanced)
     */
    public function authenticate(): array
    {
        try {
            // Prepare authentication data based on botmirzapanel approach
            $authData = [
                'username' => $this->config['username'],
                'password' => $this->config['password']
            ];

            // Check if using OAuth2 or basic auth
            if (isset($this->config['auth_type']) && $this->config['auth_type'] === 'oauth2') {
                $authData['grant_type'] = 'password';
                $authData['scope'] = $this->config['scope'] ?? 'admin';

                if (isset($this->config['client_id'])) {
                    $authData['client_id'] = $this->config['client_id'];
                }
                if (isset($this->config['client_secret'])) {
                    $authData['client_secret'] = $this->config['client_secret'];
                }
            }

            $response = $this->httpClient->post('auth/token', [
                'json' => $authData,
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'User-Agent' => 'WeBot/2.0 Marzneshin-Client'
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            if (isset($data['access_token'])) {
                $this->accessToken = $data['access_token'];
                $this->refreshToken = $data['refresh_token'] ?? null;
                $this->tokenExpiry = time() + ($data['expires_in'] ?? 3600);

                // Store token info for debugging
                $this->logTokenInfo($data);

                return [
                    'success' => true,
                    'access_token' => $this->accessToken,
                    'refresh_token' => $this->refreshToken,
                    'expires_in' => $data['expires_in'] ?? 3600,
                    'token_type' => $data['token_type'] ?? 'Bearer',
                    'scope' => $data['scope'] ?? 'admin'
                ];
            }

            return [
                'success' => false,
                'error' => 'Invalid response format',
                'response' => $data
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'OAuth2 authentication failed: ' . $e->getMessage(),
                'status_code' => 0,
                'response_body' => ''
            ];
        }
    }

    /**
     * Validate OAuth2 token
     */
    public function validateToken(?string $token = null): bool
    {
        $token ??= $this->accessToken;

        if (!$token || ($this->tokenExpiry && time() >= $this->tokenExpiry)) {
            return false;
        }

        try {
            $response = $this->makeAuthenticatedRequest('GET', 'admin/me');
            return $response['success'];
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Refresh OAuth2 token
     */
    public function refreshToken(): array
    {
        if (!$this->refreshToken) {
            return $this->authenticate();
        }

        try {
            $response = $this->httpClient->post('auth/token', [
                'json' => [
                    'grant_type' => 'refresh_token',
                    'refresh_token' => $this->refreshToken
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            if (isset($data['access_token'])) {
                $this->accessToken = $data['access_token'];
                $this->refreshToken = $data['refresh_token'] ?? $this->refreshToken;
                $this->tokenExpiry = time() + ($data['expires_in'] ?? 3600);

                return [
                    'success' => true,
                    'access_token' => $this->accessToken,
                    'refresh_token' => $this->refreshToken
                ];
            }

            return [
                'success' => false,
                'error' => 'Token refresh failed'
            ];
        } catch (\Exception $e) {
            // If refresh fails, try full authentication
            return $this->authenticate();
        }
    }

    /**
     * Create user with enhanced features
     */
    public function createUser(array $userData): array
    {
        $this->ensureAuthenticated();

        $marzneShinUser = $this->mapToMarzneShinUser($userData);

        try {
            $response = $this->makeAuthenticatedRequest('POST', 'users', $marzneShinUser);

            if ($response['success']) {
                $user = $response['data'];
                return [
                    'success' => true,
                    'username' => $user['username'],
                    'subscription_url' => $user['subscription_url'] ?? null,
                    'proxies' => $user['proxies'] ?? [],
                    'data_limit' => $user['data_limit'],
                    'expire' => $user['expire'],
                    'status' => $user['status'],
                    'note' => $user['note'] ?? null,
                    'sub_updated_at' => $user['sub_updated_at'] ?? null,
                    'on_hold_expire_duration' => $user['on_hold_expire_duration'] ?? 0,
                    'auto_delete_in_days' => $user['auto_delete_in_days'] ?? 0
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to create user: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get user with enhanced information
     */
    public function getUser(string $username): array
    {
        $this->ensureAuthenticated();

        try {
            $response = $this->makeAuthenticatedRequest('GET', "users/{$username}");

            if ($response['success']) {
                $user = $response['data'];
                return [
                    'success' => true,
                    'username' => $user['username'],
                    'proxies' => $user['proxies'] ?? [],
                    'data_limit' => $user['data_limit'],
                    'data_limit_reset_strategy' => $user['data_limit_reset_strategy'] ?? 'no_reset',
                    'expire' => $user['expire'],
                    'status' => $user['status'],
                    'used_traffic' => $user['used_traffic'] ?? 0,
                    'lifetime_used_traffic' => $user['lifetime_used_traffic'] ?? 0,
                    'created_at' => $user['created_at'] ?? null,
                    'links' => $user['links'] ?? [],
                    'subscription_url' => $user['subscription_url'] ?? null,
                    'note' => $user['note'] ?? null,
                    'sub_updated_at' => $user['sub_updated_at'] ?? null,
                    'sub_last_user_agent' => $user['sub_last_user_agent'] ?? null,
                    'online_at' => $user['online_at'] ?? null,
                    'on_hold_expire_duration' => $user['on_hold_expire_duration'] ?? 0,
                    'on_hold_timeout' => $user['on_hold_timeout'] ?? null,
                    'auto_delete_in_days' => $user['auto_delete_in_days'] ?? 0,
                    'group_id' => $user['group_id'] ?? null
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to get user: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Update user with enhanced features
     */
    public function updateUser(string $username, array $updateData): array
    {
        $this->ensureAuthenticated();

        $marzneShinData = $this->mapToMarzneShinUser($updateData);

        try {
            $response = $this->makeAuthenticatedRequest('PUT', "users/{$username}", $marzneShinData);

            if ($response['success']) {
                return [
                    'success' => true,
                    'username' => $username,
                    'updated_fields' => array_keys($updateData)
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to update user: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Delete user
     */
    public function deleteUser(string $username): array
    {
        $this->ensureAuthenticated();

        try {
            $response = $this->makeAuthenticatedRequest('DELETE', "users/{$username}");

            if ($response['success']) {
                return [
                    'success' => true,
                    'username' => $username,
                    'message' => 'User deleted successfully'
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to delete user: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Suspend user
     */
    public function suspendUser(string $username): array
    {
        return $this->updateUser($username, ['status' => 'disabled']);
    }

    /**
     * Reactivate user
     */
    public function reactivateUser(string $username): array
    {
        return $this->updateUser($username, ['status' => 'active']);
    }

    /**
     * Get user statistics with enhanced metrics
     */
    public function getUserStats(string $username): array
    {
        $userInfo = $this->getUser($username);

        if (!$userInfo['success']) {
            return $userInfo;
        }

        $user = $userInfo;
        $totalTraffic = $user['data_limit'] ?? 0;
        $usedTraffic = $user['used_traffic'] ?? 0;
        $remainingTraffic = max(0, $totalTraffic - $usedTraffic);

        return [
            'success' => true,
            'username' => $username,
            'used_traffic' => $usedTraffic,
            'total_traffic' => $totalTraffic,
            'remaining_traffic' => $remainingTraffic,
            'usage_percentage' => $totalTraffic > 0 ? ($usedTraffic / $totalTraffic) * 100 : 0,
            'expire_date' => $user['expire'],
            'status' => $user['status'],
            'lifetime_used_traffic' => $user['lifetime_used_traffic'] ?? 0,
            'online_at' => $user['online_at'],
            'last_user_agent' => $user['sub_last_user_agent'],
            'on_hold_expire_duration' => $user['on_hold_expire_duration'],
            'auto_delete_in_days' => $user['auto_delete_in_days']
        ];
    }

    /**
     * Generate configuration with enhanced options
     */
    public function generateConfig(string $username, string $clientType = 'v2ray'): array
    {
        $userInfo = $this->getUser($username);

        if (!$userInfo['success']) {
            return $userInfo;
        }

        $subscriptionUrl = $userInfo['subscription_url'];

        if (!$subscriptionUrl) {
            return [
                'success' => false,
                'error' => 'No subscription URL available for user'
            ];
        }

        try {
            // Get subscription content with user agent tracking
            $response = $this->httpClient->get($subscriptionUrl, [
                'headers' => [
                    'User-Agent' => $this->getClientUserAgent($clientType)
                ]
            ]);
            $configContent = $response->getBody()->getContents();

            switch (strtolower($clientType)) {
                case 'v2ray':
                case 'v2rayn':
                    $config = $this->formatV2RayConfig($configContent);
                    break;
                case 'clash':
                    $config = $this->formatClashConfig($configContent);
                    break;
                case 'sing-box':
                    $config = $this->formatSingBoxConfig($configContent);
                    break;
                case 'json':
                    $config = $this->formatJSONConfig($configContent);
                    break;
                case 'uri':
                    $config = $configContent; // Raw URI format
                    break;
                default:
                    $config = $configContent;
            }

            return [
                'success' => true,
                'config' => $config,
                'client_type' => $clientType,
                'subscription_url' => $subscriptionUrl,
                'format' => $clientType
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to generate config: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get system information with enhanced metrics
     */
    public function getSystemInfo(): array
    {
        $this->ensureAuthenticated();

        try {
            $response = $this->makeAuthenticatedRequest('GET', 'system');

            if ($response['success']) {
                return [
                    'success' => true,
                    'version' => $response['data']['version'] ?? 'unknown',
                    'memory_usage' => $response['data']['mem_used'] ?? 0,
                    'memory_total' => $response['data']['mem_total'] ?? 0,
                    'cpu_usage' => $response['data']['cpu_percent'] ?? 0,
                    'uptime' => $response['data']['uptime'] ?? 0,
                    'users_count' => $response['data']['users_count'] ?? 0,
                    'incoming_bandwidth' => $response['data']['incoming_bandwidth'] ?? 0,
                    'outgoing_bandwidth' => $response['data']['outgoing_bandwidth'] ?? 0,
                    'total_user_usage' => $response['data']['total_user_usage'] ?? 0
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to get system info: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Health check
     */
    public function healthCheck(): array
    {
        try {
            $response = $this->httpClient->get('health');

            return [
                'success' => $response->getStatusCode() === 200,
                'status_code' => $response->getStatusCode(),
                'response_time' => 0 // Would need to measure this
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Bulk operations with enhanced features
     */
    public function bulkUpdateUsers(array $usernames, array $updateData): array
    {
        $results = [];
        $successCount = 0;
        $failureCount = 0;

        foreach ($usernames as $username) {
            $result = $this->updateUser($username, $updateData);
            $results[$username] = $result;

            if ($result['success']) {
                $successCount++;
            } else {
                $failureCount++;
            }
        }

        return [
            'success' => $failureCount === 0,
            'total' => count($usernames),
            'successful' => $successCount,
            'failed' => $failureCount,
            'results' => $results
        ];
    }

    public function getBulkUserStats(array $usernames): array
    {
        $results = [];
        $successCount = 0;

        foreach ($usernames as $username) {
            $stats = $this->getUserStats($username);
            if ($stats['success']) {
                $results[$username] = $stats;
                $successCount++;
            }
        }

        return [
            'success' => true,
            'total' => count($usernames),
            'successful' => $successCount,
            'users' => $results
        ];
    }

    /**
     * Set compatibility mode
     */
    public function setCompatibilityMode(string $mode): void
    {
        $this->compatibilityMode = $mode;
    }

    /**
     * Get user templates (Marzneshin specific)
     */
    public function getUserTemplates(): array
    {
        $this->ensureAuthenticated();

        try {
            $response = $this->makeAuthenticatedRequest('GET', 'user-templates');

            if ($response['success']) {
                return [
                    'success' => true,
                    'templates' => $response['data'] ?? []
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to get user templates: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Create user from template (Marzneshin specific)
     */
    public function createUserFromTemplate(string $templateId, array $userData): array
    {
        $this->ensureAuthenticated();

        $requestData = array_merge($userData, [
            'template_id' => $templateId
        ]);

        try {
            $response = $this->makeAuthenticatedRequest('POST', 'users/from-template', $requestData);

            if ($response['success']) {
                $user = $response['data'];
                return [
                    'success' => true,
                    'username' => $user['username'],
                    'subscription_url' => $user['subscription_url'] ?? null,
                    'proxies' => $user['proxies'] ?? [],
                    'data_limit' => $user['data_limit'],
                    'expire' => $user['expire'],
                    'status' => $user['status'],
                    'template_id' => $templateId
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to create user from template: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get user groups (Marzneshin specific)
     */
    public function getUserGroups(): array
    {
        $this->ensureAuthenticated();

        try {
            $response = $this->makeAuthenticatedRequest('GET', 'user-groups');

            if ($response['success']) {
                return [
                    'success' => true,
                    'groups' => $response['data'] ?? []
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to get user groups: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Assign user to group (Marzneshin specific)
     */
    public function assignUserToGroup(string $username, int $groupId): array
    {
        return $this->updateUser($username, ['group_id' => $groupId]);
    }

    /**
     * Get subscription info with user agent tracking
     */
    public function getSubscriptionInfo(string $username): array
    {
        $userInfo = $this->getUser($username);

        if (!$userInfo['success']) {
            return $userInfo;
        }

        return [
            'success' => true,
            'username' => $username,
            'subscription_url' => $userInfo['subscription_url'],
            'sub_updated_at' => $userInfo['sub_updated_at'],
            'sub_last_user_agent' => $userInfo['sub_last_user_agent'],
            'online_at' => $userInfo['online_at']
        ];
    }

    /**
     * Reset user subscription (force update)
     */
    public function resetUserSubscription(string $username): array
    {
        $this->ensureAuthenticated();

        try {
            $response = $this->makeAuthenticatedRequest('POST', "users/{$username}/reset-subscription");

            if ($response['success']) {
                return [
                    'success' => true,
                    'username' => $username,
                    'message' => 'Subscription reset successfully'
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to reset subscription: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Private helper methods
     */
    private function ensureAuthenticated(): void
    {
        if (!$this->validateToken()) {
            $authResult = $this->authenticate();
            if (!$authResult['success']) {
                throw new PanelException('Authentication failed: ' . $authResult['error']);
            }
        }
    }

    private function makeAuthenticatedRequest(string $method, string $endpoint, array $data = []): array
    {
        try {
            $options = [
                'headers' => [
                    'Authorization' => "Bearer {$this->accessToken}"
                ]
            ];

            if (!empty($data)) {
                $options['json'] = $data;
            }

            $response = $this->httpClient->request($method, $endpoint, $options);
            $responseData = json_decode($response->getBody()->getContents(), true);

            return [
                'success' => true,
                'data' => $responseData,
                'status_code' => $response->getStatusCode()
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'status_code' => 0,
                'response' => ''
            ];
        }
    }

    private function mapToMarzneShinUser(array $userData): array
    {
        $marzneShinUser = [];

        if (isset($userData['username'])) {
            $marzneShinUser['username'] = $userData['username'];
        }

        if (isset($userData['proxies'])) {
            $marzneShinUser['proxies'] = $userData['proxies'];
        }

        if (isset($userData['data_limit'])) {
            $marzneShinUser['data_limit'] = (int) $userData['data_limit'];
        }

        if (isset($userData['expire'])) {
            $marzneShinUser['expire'] = is_numeric($userData['expire']) ? (int) $userData['expire'] : strtotime($userData['expire']);
        }

        if (isset($userData['expire_days'])) {
            $marzneShinUser['expire'] = strtotime("+{$userData['expire_days']} days");
        }

        if (isset($userData['status'])) {
            $marzneShinUser['status'] = $userData['status'];
        }

        if (isset($userData['note'])) {
            $marzneShinUser['note'] = $userData['note'];
        }

        if (isset($userData['on_hold_expire_duration'])) {
            $marzneShinUser['on_hold_expire_duration'] = (int) $userData['on_hold_expire_duration'];
        }

        if (isset($userData['on_hold_timeout'])) {
            $marzneShinUser['on_hold_timeout'] = is_numeric($userData['on_hold_timeout']) ? (int) $userData['on_hold_timeout'] : strtotime($userData['on_hold_timeout']);
        }

        if (isset($userData['auto_delete_in_days'])) {
            $marzneShinUser['auto_delete_in_days'] = (int) $userData['auto_delete_in_days'];
        }

        if (isset($userData['group_id'])) {
            $marzneShinUser['group_id'] = (int) $userData['group_id'];
        }

        return $marzneShinUser;
    }

    private function getClientUserAgent(string $clientType): string
    {
        $userAgents = [
            'v2ray' => 'v2rayN/6.23',
            'v2rayn' => 'v2rayN/6.23',
            'clash' => 'ClashX/1.118.0',
            'sing-box' => 'sing-box/1.8.0',
            'json' => 'WeBot/2.0',
            'uri' => 'WeBot/2.0'
        ];

        return $userAgents[$clientType] ?? 'WeBot/2.0';
    }

    private function formatV2RayConfig(string $content): string
    {
        return $content; // V2Ray config is usually base64 encoded
    }

    private function formatClashConfig(string $content): string
    {
        $configs = explode("\n", base64_decode($content));
        $clashConfig = "proxies:\n";

        foreach ($configs as $config) {
            if (!empty(trim($config))) {
                $clashConfig .= "  - " . $this->convertToClashProxy($config) . "\n";
            }
        }

        return $clashConfig;
    }

    private function formatSingBoxConfig(string $content): string
    {
        $configs = explode("\n", base64_decode($content));
        $outbounds = [];

        foreach ($configs as $config) {
            if (!empty(trim($config))) {
                $outbounds[] = $this->convertToSingBoxOutbound($config);
            }
        }

        return json_encode(['outbounds' => $outbounds], JSON_PRETTY_PRINT);
    }

    private function formatJSONConfig(string $content): string
    {
        $configs = explode("\n", base64_decode($content));
        $jsonConfigs = [];

        foreach ($configs as $config) {
            if (!empty(trim($config))) {
                $jsonConfigs[] = $this->parseConfigToJSON($config);
            }
        }

        return json_encode($jsonConfigs, JSON_PRETTY_PRINT);
    }

    private function convertToClashProxy(string $config): string
    {
        unset($config); // Suppress unused parameter warning
        // Simplified conversion - would need full implementation
        return "name: proxy, type: vmess, server: example.com, port: 443";
    }

    private function convertToSingBoxOutbound(string $config): array
    {
        unset($config); // Suppress unused parameter warning
        // Simplified conversion - would need full implementation
        return [
            'type' => 'vmess',
            'tag' => 'proxy',
            'server' => 'example.com',
            'server_port' => 443
        ];
    }

    private function parseConfigToJSON(string $config): array
    {
        unset($config); // Suppress unused parameter warning
        // Simplified parsing - would need full implementation
        return [
            'protocol' => 'vmess',
            'settings' => [
                'vnext' => [
                    [
                        'address' => 'example.com',
                        'port' => 443,
                        'users' => []
                    ]
                ]
            ]
        ];
    }

    /**
     * Log token information for debugging
     */
    private function logTokenInfo(array $tokenData): void
    {
        if ($this->config['debug_mode'] ?? false) {
            error_log('Marzneshin Token Info: ' . json_encode([
                'expires_in' => $tokenData['expires_in'] ?? 'unknown',
                'token_type' => $tokenData['token_type'] ?? 'unknown',
                'scope' => $tokenData['scope'] ?? 'unknown',
                'timestamp' => time()
            ]));
        }
    }

    /**
     * Enhanced user creation with Marzneshin features
     */
    public function createUserAdvanced(array $userData): array
    {
        $this->ensureAuthenticated();

        // Enhanced user data mapping for Marzneshin
        $marzneShinUser = $this->mapToMarzneShinUserAdvanced($userData);

        try {
            $response = $this->makeAuthenticatedRequest('POST', 'users', $marzneShinUser);

            if ($response['success']) {
                $user = $response['data'];
                return [
                    'success' => true,
                    'username' => $user['username'],
                    'subscription_url' => $user['subscription_url'] ?? null,
                    'proxies' => $user['proxies'] ?? [],
                    'data_limit' => $user['data_limit'],
                    'expire' => $user['expire'],
                    'status' => $user['status'],
                    'note' => $user['note'] ?? null,
                    'sub_updated_at' => $user['sub_updated_at'] ?? null,
                    'on_hold_expire_duration' => $user['on_hold_expire_duration'] ?? 0,
                    'auto_delete_in_days' => $user['auto_delete_in_days'] ?? 0,
                    'group_id' => $user['group_id'] ?? null,
                    'template_id' => $user['template_id'] ?? null
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to create advanced user: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Enhanced user data mapping for Marzneshin
     */
    private function mapToMarzneShinUserAdvanced(array $userData): array
    {
        $marzneShinUser = $this->mapToMarzneShinUser($userData);

        // Add Marzneshin-specific fields
        if (isset($userData['template_id'])) {
            $marzneShinUser['template_id'] = (int) $userData['template_id'];
        }

        if (isset($userData['sub_revoked_at'])) {
            $marzneShinUser['sub_revoked_at'] = $userData['sub_revoked_at'];
        }

        if (isset($userData['sub_last_user_agent'])) {
            $marzneShinUser['sub_last_user_agent'] = $userData['sub_last_user_agent'];
        }

        if (isset($userData['online_at'])) {
            $marzneShinUser['online_at'] = $userData['online_at'];
        }

        // Enhanced data limit reset strategy
        if (isset($userData['data_limit_reset_strategy'])) {
            $marzneShinUser['data_limit_reset_strategy'] = $userData['data_limit_reset_strategy'];
        }

        return $marzneShinUser;
    }

    /**
     * Get node information (Marzneshin multi-node support)
     */
    public function getNodes(): array
    {
        $this->ensureAuthenticated();

        try {
            $response = $this->makeAuthenticatedRequest('GET', 'nodes');

            if ($response['success']) {
                return [
                    'success' => true,
                    'nodes' => $response['data'] ?? []
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to get nodes: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get node usage statistics
     */
    public function getNodeUsage(int $nodeId): array
    {
        $this->ensureAuthenticated();

        try {
            $response = $this->makeAuthenticatedRequest('GET', "nodes/{$nodeId}/usage");

            if ($response['success']) {
                return [
                    'success' => true,
                    'node_id' => $nodeId,
                    'usage' => $response['data'] ?? []
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to get node usage: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Enhanced system information with node details
     */
    public function getSystemInfoAdvanced(): array
    {
        $systemInfo = $this->getSystemInfo();

        if (!$systemInfo['success']) {
            return $systemInfo;
        }

        // Get additional Marzneshin-specific info
        $nodes = $this->getNodes();

        $systemInfo['nodes'] = $nodes['success'] ? $nodes['nodes'] : [];
        $systemInfo['node_count'] = count($systemInfo['nodes']);

        return $systemInfo;
    }
}
