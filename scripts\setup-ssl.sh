#!/bin/bash

# WeBot SSL Setup Script
# Automated SSL certificate setup with Let's Encrypt

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOMAIN=""
EMAIL=""
WEBROOT="/var/www/html/public"
SSL_DIR="./docker/nginx/ssl"
NGINX_CONF_DIR="./docker/nginx"
CERTBOT_DIR="./certbot"

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}"
    echo "🔒 WeBot SSL Certificate Setup"
    echo "=============================="
    echo -e "${NC}"
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--domain)
                DOMAIN="$2"
                shift 2
                ;;
            -e|--email)
                EMAIL="$2"
                shift 2
                ;;
            -w|--webroot)
                WEBROOT="$2"
                shift 2
                ;;
            --staging)
                STAGING=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -d, --domain DOMAIN     Domain name for SSL certificate"
    echo "  -e, --email EMAIL       Email address for Let's Encrypt"
    echo "  -w, --webroot PATH      Webroot path (default: /var/www/html/public)"
    echo "  --staging               Use Let's Encrypt staging environment"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 -d example.com -e <EMAIL>"
    echo "  $0 -d bot.example.com -e <EMAIL> --staging"
}

# Validate inputs
validate_inputs() {
    if [[ -z "$DOMAIN" ]]; then
        print_error "Domain is required. Use -d or --domain option."
        exit 1
    fi

    if [[ -z "$EMAIL" ]]; then
        print_error "Email is required. Use -e or --email option."
        exit 1
    fi

    # Validate domain format
    if ! [[ "$DOMAIN" =~ ^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$ ]]; then
        print_error "Invalid domain format: $DOMAIN"
        exit 1
    fi

    # Validate email format
    if ! [[ "$EMAIL" =~ ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
        print_error "Invalid email format: $EMAIL"
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."

    # Check if running as root or with sudo
    if [[ $EUID -ne 0 ]] && ! sudo -n true 2>/dev/null; then
        print_error "This script requires root privileges or sudo access"
        exit 1
    fi

    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi

    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed"
        exit 1
    fi

    print_success "Prerequisites check passed"
}

# Create necessary directories
create_directories() {
    print_status "Creating SSL directories..."

    mkdir -p "$SSL_DIR"
    mkdir -p "$CERTBOT_DIR/conf"
    mkdir -p "$CERTBOT_DIR/www"
    mkdir -p "$CERTBOT_DIR/logs"

    print_success "Directories created"
}

# Generate DH parameters
generate_dhparam() {
    print_status "Generating DH parameters (this may take a while)..."

    if [[ ! -f "$SSL_DIR/dhparam.pem" ]]; then
        openssl dhparam -out "$SSL_DIR/dhparam.pem" 2048
        print_success "DH parameters generated"
    else
        print_warning "DH parameters already exist"
    fi
}

# Create temporary self-signed certificate
create_temp_cert() {
    print_status "Creating temporary self-signed certificate..."

    openssl req -x509 -nodes -newkey rsa:2048 \
        -days 1 \
        -keyout "$SSL_DIR/private.key" \
        -out "$SSL_DIR/cert.pem" \
        -subj "/CN=$DOMAIN"

    print_success "Temporary certificate created"
}

# Setup Nginx configuration for ACME challenge
setup_nginx_acme() {
    print_status "Setting up Nginx for ACME challenge..."

    cat > "$NGINX_CONF_DIR/acme.conf" << EOF
# ACME Challenge Configuration
location /.well-known/acme-challenge/ {
    root /var/www/certbot;
    try_files \$uri =404;
}

# Redirect all HTTP to HTTPS except ACME challenge
location / {
    return 301 https://\$server_name\$request_uri;
}
EOF

    print_success "Nginx ACME configuration created"
}

# Obtain Let's Encrypt certificate
obtain_certificate() {
    print_status "Obtaining Let's Encrypt certificate..."

    local staging_flag=""
    if [[ "$STAGING" == "true" ]]; then
        staging_flag="--staging"
        print_warning "Using Let's Encrypt staging environment"
    fi

    # Run certbot
    docker run --rm \
        -v "$PWD/$CERTBOT_DIR/conf:/etc/letsencrypt" \
        -v "$PWD/$CERTBOT_DIR/www:/var/www/certbot" \
        -v "$PWD/$CERTBOT_DIR/logs:/var/log/letsencrypt" \
        certbot/certbot certonly \
        --webroot \
        --webroot-path=/var/www/certbot \
        --email "$EMAIL" \
        --agree-tos \
        --no-eff-email \
        $staging_flag \
        -d "$DOMAIN"

    if [[ $? -eq 0 ]]; then
        print_success "Certificate obtained successfully"
    else
        print_error "Failed to obtain certificate"
        exit 1
    fi
}

# Install certificate
install_certificate() {
    print_status "Installing certificate..."

    # Copy certificate files
    cp "$CERTBOT_DIR/conf/live/$DOMAIN/fullchain.pem" "$SSL_DIR/cert.pem"
    cp "$CERTBOT_DIR/conf/live/$DOMAIN/privkey.pem" "$SSL_DIR/private.key"
    cp "$CERTBOT_DIR/conf/live/$DOMAIN/chain.pem" "$SSL_DIR/ca.pem"

    # Set proper permissions
    chmod 644 "$SSL_DIR/cert.pem"
    chmod 600 "$SSL_DIR/private.key"
    chmod 644 "$SSL_DIR/ca.pem"

    print_success "Certificate installed"
}

# Setup certificate renewal
setup_renewal() {
    print_status "Setting up certificate renewal..."

    # Create renewal script
    cat > "scripts/renew-ssl.sh" << 'EOF'
#!/bin/bash

# WeBot SSL Certificate Renewal Script

CERTBOT_DIR="./certbot"
SSL_DIR="./docker/nginx/ssl"
DOMAIN="$1"

if [[ -z "$DOMAIN" ]]; then
    echo "Usage: $0 <domain>"
    exit 1
fi

echo "Renewing certificate for $DOMAIN..."

# Renew certificate
docker run --rm \
    -v "$PWD/$CERTBOT_DIR/conf:/etc/letsencrypt" \
    -v "$PWD/$CERTBOT_DIR/www:/var/www/certbot" \
    -v "$PWD/$CERTBOT_DIR/logs:/var/log/letsencrypt" \
    certbot/certbot renew \
    --webroot \
    --webroot-path=/var/www/certbot

# Copy renewed certificates
if [[ -f "$CERTBOT_DIR/conf/live/$DOMAIN/fullchain.pem" ]]; then
    cp "$CERTBOT_DIR/conf/live/$DOMAIN/fullchain.pem" "$SSL_DIR/cert.pem"
    cp "$CERTBOT_DIR/conf/live/$DOMAIN/privkey.pem" "$SSL_DIR/private.key"
    cp "$CERTBOT_DIR/conf/live/$DOMAIN/chain.pem" "$SSL_DIR/ca.pem"
    
    # Reload Nginx
    docker-compose exec nginx nginx -s reload
    
    echo "Certificate renewed and Nginx reloaded"
else
    echo "Certificate renewal failed"
    exit 1
fi
EOF

    chmod +x "scripts/renew-ssl.sh"

    # Add to crontab (optional)
    print_status "To setup automatic renewal, add this to your crontab:"
    echo "0 12 * * * $PWD/scripts/renew-ssl.sh $DOMAIN"

    print_success "Renewal script created"
}

# Main execution
main() {
    print_header

    parse_args "$@"
    validate_inputs
    check_prerequisites
    create_directories
    generate_dhparam
    create_temp_cert
    setup_nginx_acme

    print_status "Starting WeBot services for certificate validation..."
    docker-compose up -d nginx

    # Wait for Nginx to start
    sleep 10

    obtain_certificate
    install_certificate
    setup_renewal

    print_status "Restarting Nginx with new certificate..."
    docker-compose restart nginx

    print_success "SSL setup completed successfully!"
    print_status "Your WeBot instance is now secured with SSL/TLS"
    print_status "Certificate will expire in 90 days"
    print_status "Use scripts/renew-ssl.sh $DOMAIN to renew manually"
}

# Run main function with all arguments
main "$@"
