# 🚀 WeBot 2.0 Quick Start Guide

Get WeBot up and running in minutes with this step-by-step guide.

## ⚡ Quick Setup (5 minutes)

### 1. Prerequisites Check
```bash
# Check PHP version (8.1+ required)
php --version

# Check required extensions
php -m | grep -E "(mysql|curl|json|mbstring)"

# Check Composer
composer --version
```

### 2. Download & Install
```bash
# Clone repository
git clone https://github.com/your-repo/webot.git
cd webot

# Install dependencies
composer install

# Copy environment file
cp .env.example .env
```

### 3. Basic Configuration
Edit `.env` file with your settings:
```env
# Database
DB_HOST=localhost
DB_DATABASE=webot
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_bot_token_from_botfather

# Application
APP_URL=https://yourdomain.com
```

### 4. Database Setup
```bash
# Create database
mysql -u root -p -e "CREATE DATABASE webot"

# Run migrations
php migrate.php migrate

# Verify setup
php scripts/validate-env.php
```

### 5. Test Installation
```bash
# Run basic tests
php vendor/bin/phpunit tests/Unit/Core/

# Check health
curl http://localhost/health
```

## 🤖 Telegram Bot Setup

### 1. Create Your Bot
1. Message [@BotFather](https://t.me/botfather) on Telegram
2. Send `/newbot`
3. Choose a name and username
4. Copy the bot token

### 2. Configure Bot
```bash
# Set bot token in .env
TELEGRAM_BOT_TOKEN=**********:ABCdefGHIjklMNOpqrsTUVwxyz

# Set webhook (replace with your domain)
curl -X POST "https://api.telegram.org/bot{YOUR_BOT_TOKEN}/setWebhook" \
     -H "Content-Type: application/json" \
     -d '{"url": "https://yourdomain.com/webhook.php"}'
```

### 3. Test Bot
1. Find your bot on Telegram
2. Send `/start`
3. You should receive a welcome message

## 🎯 First Steps

### 1. Admin Setup
```bash
# Create admin user
php scripts/create-admin.php --telegram-id=YOUR_TELEGRAM_ID

# Set admin permissions
php scripts/set-permissions.php --user-id=1 --role=admin
```

### 2. Panel Configuration
Add your VPN panel details to `.env`:
```env
# Marzban Panel
MARZBAN_URL=https://your-panel.com
MARZBAN_USERNAME=admin
MARZBAN_PASSWORD=your_password

# X-UI Panel (alternative)
XUI_URL=https://your-xui.com
XUI_USERNAME=admin
XUI_PASSWORD=your_password
```

### 3. Payment Gateway
Configure payment gateway in `.env`:
```env
# ZarinPal (Iranian gateway)
ZARINPAL_MERCHANT_ID=your_merchant_id
ZARINPAL_SANDBOX=false

# PayPal (International)
PAYPAL_CLIENT_ID=your_client_id
PAYPAL_CLIENT_SECRET=your_secret
PAYPAL_SANDBOX=false
```

## 📱 Basic Usage

### User Commands
- `/start` - Start the bot and register
- `/menu` - Show main menu
- `/profile` - View user profile
- `/services` - Browse available services
- `/support` - Contact support

### Admin Commands
- `/admin` - Admin panel
- `/stats` - View statistics
- `/users` - Manage users
- `/services` - Manage services
- `/payments` - View payments

## 🔧 Configuration

### Environment Variables
```env
# Application
APP_NAME="WeBot"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=webot
DB_USERNAME=webot
DB_PASSWORD=secure_password

# Telegram
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_WEBHOOK_URL=https://yourdomain.com/webhook.php

# Cache (Redis)
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Security
APP_KEY=your_32_character_secret_key
JWT_SECRET=your_jwt_secret

# Logging
LOG_CHANNEL=daily
LOG_LEVEL=info
```

### Panel Configuration
```php
// config/panels/marzban.php
return [
    'url' => env('MARZBAN_URL'),
    'username' => env('MARZBAN_USERNAME'),
    'password' => env('MARZBAN_PASSWORD'),
    'timeout' => 30,
    'verify_ssl' => true,
];
```

### Payment Configuration
```php
// config/payments/zarinpal.php
return [
    'merchant_id' => env('ZARINPAL_MERCHANT_ID'),
    'sandbox' => env('ZARINPAL_SANDBOX', false),
    'callback_url' => env('APP_URL') . '/payment/callback',
    'currency' => 'IRR',
];
```

## 🧪 Testing Your Setup

### 1. Health Check
```bash
# Check application health
curl https://yourdomain.com/health

# Expected response:
{
    "status": "ok",
    "database": "connected",
    "redis": "connected",
    "telegram": "configured"
}
```

### 2. Bot Testing
```bash
# Test webhook
curl -X POST "https://yourdomain.com/webhook.php" \
     -H "Content-Type: application/json" \
     -d '{
         "update_id": 123,
         "message": {
             "message_id": 1,
             "from": {"id": 123456789, "first_name": "Test"},
             "chat": {"id": 123456789, "type": "private"},
             "date": **********,
             "text": "/start"
         }
     }'
```

### 3. Database Testing
```bash
# Test database connection
php -r "
try {
    \$pdo = new PDO('mysql:host=localhost;dbname=webot', 'webot', 'password');
    echo 'Database: Connected ✅\n';
} catch (Exception \$e) {
    echo 'Database: Failed ❌ - ' . \$e->getMessage() . '\n';
}
"
```

## 🚨 Troubleshooting

### Common Issues

**Bot not responding**
```bash
# Check webhook status
curl "https://api.telegram.org/bot{BOT_TOKEN}/getWebhookInfo"

# Check logs
tail -f storage/logs/webot.log
```

**Database connection failed**
```bash
# Test MySQL connection
mysql -h localhost -u webot -p webot

# Check PHP MySQL extension
php -m | grep mysql
```

**Permission denied**
```bash
# Fix file permissions
sudo chown -R www-data:www-data /var/www/webot
sudo chmod -R 755 /var/www/webot
sudo chmod -R 777 storage/ public/uploads/
```

**Composer issues**
```bash
# Clear composer cache
composer clear-cache

# Update dependencies
composer update

# Install with verbose output
composer install -v
```

### Debug Mode
Enable debug mode for troubleshooting:
```env
APP_DEBUG=true
LOG_LEVEL=debug
```

View logs:
```bash
tail -f storage/logs/webot.log
```

## 📚 Next Steps

### 1. Customize Your Bot
- Edit message templates in `resources/templates/`
- Customize keyboards in `src/Utils/KeyboardBuilder.php`
- Add custom commands in controllers

### 2. Add Services
- Configure VPN services in admin panel
- Set pricing and limits
- Test service creation

### 3. Setup Monitoring
- Configure log monitoring
- Set up performance monitoring
- Enable error alerting

### 4. Go Live
- Configure SSL certificate
- Set up backup system
- Monitor performance

## 📞 Support

### Documentation
- **[Installation Guide](../installation/README.md)** - Detailed installation
- **[API Documentation](../api/README.md)** - API reference
- **[Testing Guide](../testing/README.md)** - Testing and debugging

### Community
- **GitHub Issues** - Bug reports and feature requests
- **Telegram Group** - Community support
- **Documentation** - This guide

### Professional Support
- **Custom Development** - Feature development
- **Deployment Services** - Professional setup
- **Maintenance** - Ongoing support

---

🎉 **Congratulations!** Your WeBot is now ready to serve users.

**Next**: [Configuration Guide](../configuration/README.md)
