<?php

declare(strict_types=1);

namespace WeBot\Core;

use Exception;
use ReflectionClass;
use ReflectionException;
use ReflectionParameter;

/**
 * Dependency Injection Container
 *
 * Simple DI container for managing dependencies and automatic injection.
 *
 * @package WeBot\Core
 * @version 2.0
 */
class DIContainer
{
    /**
     * Container bindings
     */
    private array $bindings = [];

    /**
     * Singleton instances
     */
    private array $instances = [];

    /**
     * Aliases for interface to implementation mapping
     */
    private array $aliases = [];

    /**
     * Bind a class or interface to a concrete implementation
     */
    public function bind(string $abstract, $concrete = null, bool $singleton = false): void
    {
        if ($concrete === null) {
            $concrete = $abstract;
        }

        $this->bindings[$abstract] = [
            'concrete' => $concrete,
            'singleton' => $singleton
        ];
    }

    /**
     * Bind as singleton
     */
    public function singleton(string $abstract, $concrete = null): void
    {
        $this->bind($abstract, $concrete, true);
    }

    /**
     * Register an alias
     */
    public function alias(string $alias, string $abstract): void
    {
        $this->aliases[$alias] = $abstract;
    }

    /**
     * Register an existing instance
     */
    public function instance(string $abstract, object $instance): void
    {
        $this->instances[$abstract] = $instance;
    }

    /**
     * Resolve a class from the container
     */
    public function make(string $abstract, array $parameters = []): object
    {
        // Check if we have an alias
        if (isset($this->aliases[$abstract])) {
            $abstract = $this->aliases[$abstract];
        }

        // Check if we already have an instance
        if (isset($this->instances[$abstract])) {
            return $this->instances[$abstract];
        }

        // Check if we have a binding
        if (isset($this->bindings[$abstract])) {
            $binding = $this->bindings[$abstract];
            $concrete = $binding['concrete'];

            if (is_callable($concrete)) {
                $instance = $concrete($this, $parameters);
            } else {
                $instance = $this->build($concrete, $parameters);
            }

            // Store as singleton if needed
            if ($binding['singleton']) {
                $this->instances[$abstract] = $instance;
            }

            return $instance;
        }

        // Try to build the class directly
        return $this->build($abstract, $parameters);
    }

    /**
     * Build a class instance with dependency injection
     */
    private function build(string $concrete, array $parameters = []): object
    {
        try {
            $reflection = new ReflectionClass($concrete);
        } catch (ReflectionException $e) {
            throw new Exception("Class {$concrete} not found: " . $e->getMessage());
        }

        if (!$reflection->isInstantiable()) {
            throw new Exception("Class {$concrete} is not instantiable");
        }

        $constructor = $reflection->getConstructor();

        if ($constructor === null) {
            return new $concrete();
        }

        $dependencies = $this->resolveDependencies($constructor->getParameters(), $parameters);

        return $reflection->newInstanceArgs($dependencies);
    }

    /**
     * Resolve method dependencies
     */
    private function resolveDependencies(array $parameters, array $primitives = []): array
    {
        $dependencies = [];

        foreach ($parameters as $parameter) {
            $dependency = $this->resolveDependency($parameter, $primitives);
            $dependencies[] = $dependency;
        }

        return $dependencies;
    }

    /**
     * Resolve a single dependency
     */
    private function resolveDependency(ReflectionParameter $parameter, array $primitives = [])
    {
        $name = $parameter->getName();

        // Check if we have a primitive value
        if (array_key_exists($name, $primitives)) {
            return $primitives[$name];
        }

        // Get parameter type
        $type = $parameter->getType();

        if ($type === null) {
            if ($parameter->isDefaultValueAvailable()) {
                return $parameter->getDefaultValue();
            }

            throw new Exception("Cannot resolve parameter {$name} without type hint");
        }

        $typeName = $type->getName();

        // Handle built-in types
        if ($type->isBuiltin()) {
            // Try to resolve from bindings first
            if (isset($this->bindings[$name])) {
                return $this->resolve($this->bindings[$name]);
            }

            // Special handling for common parameter names
            if ($name === 'config' && $typeName === 'array') {
                return $this->bindings['config'] ?? [];
            }

            if ($parameter->isDefaultValueAvailable()) {
                return $parameter->getDefaultValue();
            }

            throw new Exception("Cannot resolve primitive parameter {$name} of type {$typeName}");
        }

        // Resolve class dependency
        try {
            return $this->make($typeName);
        } catch (Exception $e) {
            if ($parameter->isDefaultValueAvailable()) {
                return $parameter->getDefaultValue();
            }

            throw new Exception("Cannot resolve dependency {$typeName} for parameter {$name}: " . $e->getMessage());
        }
    }

    /**
     * Check if a binding exists
     */
    public function bound(string $abstract): bool
    {
        return isset($this->bindings[$abstract]) ||
               isset($this->instances[$abstract]) ||
               isset($this->aliases[$abstract]);
    }

    /**
     * Remove a binding
     */
    public function forget(string $abstract): void
    {
        unset($this->bindings[$abstract], $this->instances[$abstract], $this->aliases[$abstract]);
    }

    /**
     * Get all bindings
     */
    public function getBindings(): array
    {
        return $this->bindings;
    }

    /**
     * Call a method with dependency injection
     */
    public function call(callable $callback, array $parameters = [])
    {
        if (is_array($callback)) {
            [$object, $method] = $callback;

            if (is_string($object)) {
                $object = $this->make($object);
            }

            $reflection = new ReflectionClass($object);
            $methodReflection = $reflection->getMethod($method);
            $dependencies = $this->resolveDependencies($methodReflection->getParameters(), $parameters);

            return $methodReflection->invokeArgs($object, $dependencies);
        }

        // For closures and other callables
        $reflection = new \ReflectionFunction($callback);
        $dependencies = $this->resolveDependencies($reflection->getParameters(), $parameters);

        return $reflection->invokeArgs($dependencies);
    }

    /**
     * Setup default bindings for WeBot
     */
    public function setupDefaults(): void
    {
        // Bind container itself
        $this->singleton(DIContainer::class, function () {
            return $this;
        });

        // Config
        $this->singleton(Config::class, function () {
            return new Config();
        });

        // Database
        $this->singleton(Database::class, function (DIContainer $container) {
            $config = $container->make(Config::class);
            return new Database($config->get('database', []));
        });

        // Cache Manager
        $this->singleton(CacheManager::class, function (DIContainer $container) {
            $config = $container->make(Config::class);
            return new CacheManager($config->get('cache', ['driver' => 'array']));
        });

        // Security Manager
        $this->singleton(SecurityManager::class, function (DIContainer $container) {
            $config = $container->make(Config::class);
            return new SecurityManager($config->get('security', []));
        });

        // Input Validator
        $this->singleton(InputValidator::class, function () {
            return new InputValidator();
        });

        // Logger
        $this->singleton('WeBot\Utils\Logger', function () {
            return \WeBot\Utils\Logger::getInstance();
        });
    }

    /**
     * Check if a binding exists
     */
    public function hasBinding(string $abstract): bool
    {
        return isset($this->bindings[$abstract]);
    }

    /**
     * Get all binding keys
     */
    public function getBindingKeys(): array
    {
        return array_keys($this->bindings);
    }

    /**
     * Get a service from container (alias for make)
     */
    public function get(string $abstract)
    {
        return $this->make($abstract);
    }
}
