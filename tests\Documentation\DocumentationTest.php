<?php

declare(strict_types=1);

namespace WeBot\Tests\Documentation;

use WeBot\Tests\Unit\BaseTestCase;
use WeBot\Documentation\OpenApiGenerator;
use WeBot\Documentation\ApiDocumentationGenerator;

/**
 * Documentation Generation Tests
 * 
 * Tests for API documentation generation functionality
 * 
 * @package WeBot\Tests\Documentation
 * @version 2.0
 */
class DocumentationTest extends BaseTestCase
{
    private OpenApiGenerator $openApiGenerator;
    private ApiDocumentationGenerator $docGenerator;
    private string $testOutputDir;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->testOutputDir = sys_get_temp_dir() . '/webot_docs_test_' . uniqid();
        mkdir($this->testOutputDir, 0755, true);
        
        $this->openApiGenerator = new OpenApiGenerator('2.0.0-test', 'https://test.webot.com');
        $this->docGenerator = new ApiDocumentationGenerator($this->config, $this->testOutputDir);
    }
    
    protected function tearDown(): void
    {
        // Clean up test directory
        if (is_dir($this->testOutputDir)) {
            $this->removeDirectory($this->testOutputDir);
        }
        
        parent::tearDown();
    }
    
    /**
     * Test OpenAPI specification generation
     */
    public function testOpenApiSpecificationGeneration(): void
    {
        $spec = $this->openApiGenerator->generate();
        
        // Test basic structure
        $this->assertArrayHasKey('openapi', $spec);
        $this->assertArrayHasKey('info', $spec);
        $this->assertArrayHasKey('paths', $spec);
        $this->assertArrayHasKey('components', $spec);
        
        // Test OpenAPI version
        $this->assertEquals('3.0.3', $spec['openapi']);
        
        // Test info section
        $this->assertArrayHasKey('title', $spec['info']);
        $this->assertArrayHasKey('version', $spec['info']);
        $this->assertEquals('WeBot API', $spec['info']['title']);
        $this->assertEquals('2.0.0-test', $spec['info']['version']);
        
        // Test components
        $this->assertArrayHasKey('schemas', $spec['components']);
        $this->assertArrayHasKey('securitySchemes', $spec['components']);
        $this->assertArrayHasKey('responses', $spec['components']);
        
        // Test schemas
        $schemas = $spec['components']['schemas'];
        $this->assertArrayHasKey('User', $schemas);
        $this->assertArrayHasKey('Service', $schemas);
        $this->assertArrayHasKey('Payment', $schemas);
        
        // Test User schema structure
        $userSchema = $schemas['User'];
        $this->assertArrayHasKey('type', $userSchema);
        $this->assertArrayHasKey('properties', $userSchema);
        $this->assertArrayHasKey('required', $userSchema);
        $this->assertEquals('object', $userSchema['type']);
        
        // Test required fields
        $this->assertContains('id', $userSchema['required']);
        $this->assertContains('telegram_id', $userSchema['required']);
        $this->assertContains('status', $userSchema['required']);
    }
    
    /**
     * Test security schemes
     */
    public function testSecuritySchemes(): void
    {
        $spec = $this->openApiGenerator->generate();
        $securitySchemes = $spec['components']['securitySchemes'];
        
        // Test Bearer Auth
        $this->assertArrayHasKey('BearerAuth', $securitySchemes);
        $bearerAuth = $securitySchemes['BearerAuth'];
        $this->assertEquals('http', $bearerAuth['type']);
        $this->assertEquals('bearer', $bearerAuth['scheme']);
        $this->assertEquals('JWT', $bearerAuth['bearerFormat']);
        
        // Test Telegram Auth
        $this->assertArrayHasKey('TelegramAuth', $securitySchemes);
        $telegramAuth = $securitySchemes['TelegramAuth'];
        $this->assertEquals('apiKey', $telegramAuth['type']);
        $this->assertEquals('header', $telegramAuth['in']);
        $this->assertEquals('X-Telegram-Bot-Api-Secret-Token', $telegramAuth['name']);
    }
    
    /**
     * Test API paths generation
     */
    public function testApiPathsGeneration(): void
    {
        $spec = $this->openApiGenerator->generate();
        $paths = $spec['paths'];
        
        // Test webhook endpoints
        $this->assertArrayHasKey('/webhook/telegram', $paths);
        $webhookPath = $paths['/webhook/telegram'];
        $this->assertArrayHasKey('post', $webhookPath);
        
        // Test auth endpoints
        $this->assertArrayHasKey('/api/auth/login', $paths);
        $this->assertArrayHasKey('/api/auth/refresh', $paths);
        
        // Test user endpoints
        $this->assertArrayHasKey('/api/users/profile', $paths);
        $profilePath = $paths['/api/users/profile'];
        $this->assertArrayHasKey('get', $profilePath);
        $this->assertArrayHasKey('put', $profilePath);
        
        // Test service endpoints
        $this->assertArrayHasKey('/api/services', $paths);
        $this->assertArrayHasKey('/api/services/{service_id}', $paths);
        $this->assertArrayHasKey('/api/services/{service_id}/config', $paths);
        
        // Test payment endpoints
        $this->assertArrayHasKey('/api/payments', $paths);
        $this->assertArrayHasKey('/api/payments/{payment_id}', $paths);
        
        // Test health endpoint
        $this->assertArrayHasKey('/api/health', $paths);
    }
    
    /**
     * Test OpenAPI JSON export
     */
    public function testOpenApiJsonExport(): void
    {
        $filename = $this->testOutputDir . '/test_openapi.json';
        $result = $this->openApiGenerator->exportToFile($filename);
        
        $this->assertTrue($result);
        $this->assertFileExists($filename);
        
        // Test JSON validity
        $content = file_get_contents($filename);
        $decoded = json_decode($content, true);
        $this->assertNotNull($decoded);
        $this->assertArrayHasKey('openapi', $decoded);
    }
    
    /**
     * Test OpenAPI YAML export
     */
    public function testOpenApiYamlExport(): void
    {
        $filename = $this->testOutputDir . '/test_openapi.yaml';
        $result = $this->openApiGenerator->exportToYaml($filename);
        
        $this->assertTrue($result);
        $this->assertFileExists($filename);
        
        // Test YAML content
        $content = file_get_contents($filename);
        $this->assertStringContains('openapi:', $content);
        $this->assertStringContains('info:', $content);
        $this->assertStringContains('paths:', $content);
    }
    
    /**
     * Test complete documentation generation
     */
    public function testCompleteDocumentationGeneration(): void
    {
        $result = $this->docGenerator->generateAll();
        
        $this->assertArrayHasKey('success', $result);
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('files', $result);
        
        $files = $result['files'];
        
        // Test that all expected files are generated
        $expectedFiles = [
            'openapi_json',
            'openapi_yaml',
            'markdown',
            'postman',
            'examples',
            'auth_guide',
            'error_codes',
            'rate_limiting'
        ];
        
        foreach ($expectedFiles as $fileType) {
            $this->assertArrayHasKey($fileType, $files);
            $this->assertFileExists($files[$fileType]);
        }
    }
    
    /**
     * Test Markdown documentation generation
     */
    public function testMarkdownDocumentationGeneration(): void
    {
        $filename = $this->docGenerator->generateMarkdownDocs();
        
        $this->assertFileExists($filename);
        
        $content = file_get_contents($filename);
        
        // Test content structure
        $this->assertStringContains('# WeBot API Documentation', $content);
        $this->assertStringContains('## Authentication', $content);
        $this->assertStringContains('## Endpoints', $content);
        $this->assertStringContains('## Error Codes', $content);
        $this->assertStringContains('## Code Examples', $content);
        
        // Test specific sections
        $this->assertStringContains('POST /api/auth/login', $content);
        $this->assertStringContains('GET /api/users/profile', $content);
        $this->assertStringContains('Bearer Token', $content);
        $this->assertStringContains('Rate Limiting', $content);
    }
    
    /**
     * Test Postman collection generation
     */
    public function testPostmanCollectionGeneration(): void
    {
        $filename = $this->docGenerator->generatePostmanCollection();
        
        $this->assertFileExists($filename);
        
        $content = file_get_contents($filename);
        $collection = json_decode($content, true);
        
        $this->assertNotNull($collection);
        $this->assertArrayHasKey('info', $collection);
        $this->assertArrayHasKey('item', $collection);
        $this->assertArrayHasKey('variable', $collection);
        
        // Test collection info
        $this->assertEquals('WeBot API', $collection['info']['name']);
        $this->assertEquals('2.0.0', $collection['info']['version']);
        
        // Test variables
        $variables = $collection['variable'];
        $variableNames = array_column($variables, 'key');
        $this->assertContains('base_url', $variableNames);
        $this->assertContains('jwt_token', $variableNames);
        
        // Test items structure
        $items = $collection['item'];
        $this->assertGreaterThan(0, count($items));
        
        // Find Authentication folder
        $authFolder = null;
        foreach ($items as $item) {
            if ($item['name'] === 'Authentication') {
                $authFolder = $item;
                break;
            }
        }
        
        $this->assertNotNull($authFolder);
        $this->assertArrayHasKey('item', $authFolder);
        $this->assertGreaterThan(0, count($authFolder['item']));
    }
    
    /**
     * Test code examples generation
     */
    public function testCodeExamplesGeneration(): void
    {
        $filename = $this->docGenerator->generateCodeExamples();
        
        $this->assertFileExists($filename);
        
        $content = file_get_contents($filename);
        
        // Test content structure
        $this->assertStringContains('# WeBot API Code Examples', $content);
        $this->assertStringContains('## JavaScript', $content);
        $this->assertStringContains('## PHP', $content);
        $this->assertStringContains('## Python', $content);
        
        // Test specific examples
        $this->assertStringContains('class WeBotAPI', $content);
        $this->assertStringContains('async function', $content);
        $this->assertStringContains('curl_init()', $content);
        $this->assertStringContains('requests.Session()', $content);
    }
    
    /**
     * Test authentication guide generation
     */
    public function testAuthenticationGuideGeneration(): void
    {
        $filename = $this->docGenerator->generateAuthenticationGuide();
        
        $this->assertFileExists($filename);
        
        $content = file_get_contents($filename);
        
        // Test content structure
        $this->assertStringContains('# WeBot API Authentication Guide', $content);
        $this->assertStringContains('## JWT Bearer Token', $content);
        $this->assertStringContains('## Telegram Bot API Secret Token', $content);
        $this->assertStringContains('## Session-based Authentication', $content);
        $this->assertStringContains('## Security Best Practices', $content);
    }
    
    /**
     * Test error codes documentation generation
     */
    public function testErrorCodesDocumentationGeneration(): void
    {
        $filename = $this->docGenerator->generateErrorCodesDoc();
        
        $this->assertFileExists($filename);
        
        $content = file_get_contents($filename);
        
        // Test content structure
        $this->assertStringContains('# WeBot API Error Codes', $content);
        $this->assertStringContains('## HTTP Status Codes', $content);
        $this->assertStringContains('## Authentication Errors', $content);
        $this->assertStringContains('## Validation Errors', $content);
        $this->assertStringContains('UNAUTHORIZED', $content);
        $this->assertStringContains('VALIDATION_ERROR', $content);
    }
    
    /**
     * Test rate limiting documentation generation
     */
    public function testRateLimitingDocumentationGeneration(): void
    {
        $filename = $this->docGenerator->generateRateLimitingDoc();
        
        $this->assertFileExists($filename);
        
        $content = file_get_contents($filename);
        
        // Test content structure
        $this->assertStringContains('# WeBot API Rate Limiting', $content);
        $this->assertStringContains('## Rate Limits', $content);
        $this->assertStringContains('## Rate Limit Headers', $content);
        $this->assertStringContains('X-RateLimit-Limit', $content);
        $this->assertStringContains('X-RateLimit-Remaining', $content);
    }
    
    /**
     * Helper method to remove directory recursively
     */
    private function removeDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }
        
        $files = array_diff(scandir($dir), ['.', '..']);
        
        foreach ($files as $file) {
            $path = $dir . DIRECTORY_SEPARATOR . $file;
            if (is_dir($path)) {
                $this->removeDirectory($path);
            } else {
                unlink($path);
            }
        }
        
        rmdir($dir);
    }
}
