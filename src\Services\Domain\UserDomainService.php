<?php

declare(strict_types=1);

namespace WeBot\Services\Domain;

use WeBot\DTOs\UserRegistrationDTO;
use WeBot\Events\EventDispatcher;
use WeBot\Events\UserRegisteredEvent;
use WeBot\Events\UserUpdatedEvent;
use WeBot\Events\UserBlockedEvent;
use WeBot\Events\UserUnblockedEvent;
use WeBot\Services\DatabaseService;
use WeBot\Utils\Logger;
use WeBot\Exceptions\WeBotException;

/**
 * User Domain Service
 *
 * Handles business logic for user operations.
 * Encapsulates domain rules and coordinates with other services.
 *
 * @package WeBot\Services\Domain
 * @version 2.0
 */
class UserDomainService
{
    private DatabaseService $database;
    private EventDispatcher $eventDispatcher;
    private Logger $logger;

    public function __construct(
        DatabaseService $database,
        EventDispatcher $eventDispatcher
    ) {
        $this->database = $database;
        $this->eventDispatcher = $eventDispatcher;
        $this->logger = Logger::getInstance();
    }

    /**
     * Register new user
     */
    public function registerUser(UserRegistrationDTO $userDTO): array
    {
        // Validate DTO
        if (!$userDTO->isValid()) {
            throw new WeBotException(
                'User registration validation failed: ' . $userDTO->getFirstError(),
                400,
                null,
                ['errors' => $userDTO->getErrors()],
                'اطلاعات وارد شده صحیح نیست.'
            );
        }

        $telegramId = $userDTO->getTelegramId();

        // Check if user already exists
        if ($this->userExists($telegramId)) {
            throw new WeBotException(
                "User with Telegram ID {$telegramId} already exists",
                409,
                null,
                ['telegram_id' => $telegramId],
                'این کاربر قبلاً ثبت‌نام کرده است.'
            );
        }

        try {
            // Begin transaction
            $this->database->beginTransaction();

            // Insert user
            $userData = $userDTO->toDatabaseArray();
            $userId = $this->database->insert('users', $userData);

            if (!$userId) {
                throw new WeBotException(
                    'Failed to insert user into database',
                    500,
                    null,
                    ['user_data' => $userData],
                    'خطا در ثبت‌نام کاربر.'
                );
            }

            // Get complete user data
            $user = $this->getUserById($userId);

            // Commit transaction
            $this->database->commit();

            // Dispatch event
            $event = new UserRegisteredEvent($userId, $user);
            $this->eventDispatcher->dispatch($event);

            $this->logger->info('User registered successfully', [
                'user_id' => $userId,
                'telegram_id' => $telegramId,
                'first_name' => $userDTO->getFirstName()
            ]);

            return $user;
        } catch (\Exception $e) {
            $this->database->rollback();

            $this->logger->error('User registration failed', [
                'telegram_id' => $telegramId,
                'error' => $e->getMessage()
            ]);

            throw new WeBotException(
                'User registration failed: ' . $e->getMessage(),
                500,
                $e,
                ['telegram_id' => $telegramId],
                'خطا در ثبت‌نام کاربر. لطفاً مجدداً تلاش کنید.'
            );
        }
    }

    /**
     * Update user information
     */
    public function updateUser(int $userId, array $updateData): array
    {
        // Get current user data
        $oldUser = $this->getUserById($userId);
        if (!$oldUser) {
            throw new WeBotException(
                "User with ID {$userId} not found",
                404,
                null,
                ['user_id' => $userId],
                'کاربر یافت نشد.'
            );
        }

        try {
            // Begin transaction
            $this->database->beginTransaction();

            // Update user
            $updateData['updated_at'] = date('Y-m-d H:i:s');
            $updated = $this->database->update('users', $updateData, ['id' => $userId]);

            if (!$updated) {
                throw new WeBotException(
                    'Failed to update user in database',
                    500,
                    null,
                    ['user_id' => $userId, 'update_data' => $updateData],
                    'خطا در بروزرسانی اطلاعات کاربر.'
                );
            }

            // Get updated user data
            $newUser = $this->getUserById($userId);

            // Commit transaction
            $this->database->commit();

            // Dispatch event
            $event = new UserUpdatedEvent($userId, $oldUser, $newUser);
            $this->eventDispatcher->dispatch($event);

            $this->logger->info('User updated successfully', [
                'user_id' => $userId,
                'changes' => array_keys($updateData)
            ]);

            return $newUser;
        } catch (\Exception $e) {
            $this->database->rollback();

            $this->logger->error('User update failed', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            throw new WeBotException(
                'User update failed: ' . $e->getMessage(),
                500,
                $e,
                ['user_id' => $userId],
                'خطا در بروزرسانی اطلاعات کاربر.'
            );
        }
    }

    /**
     * Block user
     */
    public function blockUser(int $userId, string $reason, int $blockedBy = null): bool
    {
        $user = $this->getUserById($userId);
        if (!$user) {
            throw new WeBotException(
                "User with ID {$userId} not found",
                404,
                null,
                ['user_id' => $userId],
                'کاربر یافت نشد.'
            );
        }

        if ($user['status'] === 'blocked') {
            throw new WeBotException(
                "User {$userId} is already blocked",
                409,
                null,
                ['user_id' => $userId],
                'این کاربر قبلاً مسدود شده است.'
            );
        }

        try {
            // Update user status
            $updated = $this->database->update('users', [
                'status' => 'blocked',
                'blocked_at' => date('Y-m-d H:i:s'),
                'block_reason' => $reason,
                'updated_at' => date('Y-m-d H:i:s')
            ], ['id' => $userId]);

            if ($updated) {
                // Dispatch event
                $event = new UserBlockedEvent($userId, $reason, $blockedBy);
                $this->eventDispatcher->dispatch($event);

                $this->logger->info('User blocked successfully', [
                    'user_id' => $userId,
                    'reason' => $reason,
                    'blocked_by' => $blockedBy
                ]);

                return true;
            }

            return false;
        } catch (\Exception $e) {
            $this->logger->error('User blocking failed', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            throw new WeBotException(
                'User blocking failed: ' . $e->getMessage(),
                500,
                $e,
                ['user_id' => $userId],
                'خطا در مسدود کردن کاربر.'
            );
        }
    }

    /**
     * Unblock user
     */
    public function unblockUser(int $userId, int $unblockedBy = null): bool
    {
        $user = $this->getUserById($userId);
        if (!$user) {
            throw new WeBotException(
                "User with ID {$userId} not found",
                404,
                null,
                ['user_id' => $userId],
                'کاربر یافت نشد.'
            );
        }

        if ($user['status'] !== 'blocked') {
            throw new WeBotException(
                "User {$userId} is not blocked",
                409,
                null,
                ['user_id' => $userId],
                'این کاربر مسدود نیست.'
            );
        }

        try {
            // Update user status
            $updated = $this->database->update('users', [
                'status' => 'active',
                'blocked_at' => null,
                'block_reason' => null,
                'updated_at' => date('Y-m-d H:i:s')
            ], ['id' => $userId]);

            if ($updated) {
                // Dispatch event
                $event = new UserUnblockedEvent($userId, $unblockedBy);
                $this->eventDispatcher->dispatch($event);

                $this->logger->info('User unblocked successfully', [
                    'user_id' => $userId,
                    'unblocked_by' => $unblockedBy
                ]);

                return true;
            }

            return false;
        } catch (\Exception $e) {
            $this->logger->error('User unblocking failed', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            throw new WeBotException(
                'User unblocking failed: ' . $e->getMessage(),
                500,
                $e,
                ['user_id' => $userId],
                'خطا در رفع مسدودیت کاربر.'
            );
        }
    }

    /**
     * Check if user exists by Telegram ID
     */
    public function userExists(int $telegramId): bool
    {
        $result = $this->database->select('users', ['id'], ['telegram_id' => $telegramId]);
        return !empty($result);
    }

    /**
     * Get user by ID
     */
    public function getUserById(int $userId): ?array
    {
        $users = $this->database->select('users', ['*'], ['id' => $userId]);
        return $users[0] ?? null;
    }

    /**
     * Get user by Telegram ID
     */
    public function getUserByTelegramId(int $telegramId): ?array
    {
        $users = $this->database->select('users', ['*'], ['telegram_id' => $telegramId]);
        return $users[0] ?? null;
    }

    /**
     * Get user statistics
     */
    public function getUserStatistics(int $userId): array
    {
        $user = $this->getUserById($userId);
        if (!$user) {
            return [];
        }

        // Get service count
        $services = $this->database->select('services', ['COUNT(*) as count'], ['user_id' => $userId]);
        $serviceCount = $services[0]['count'] ?? 0;

        // Get payment count and total
        $payments = $this->database->query(
            "SELECT COUNT(*) as count, COALESCE(SUM(amount), 0) as total
             FROM payments
             WHERE user_id = {$userId} AND status = 'paid'"
        );
        $paymentData = $payments[0] ?? ['count' => 0, 'total' => 0];

        return [
            'user_id' => $userId,
            'registration_date' => $user['created_at'],
            'last_activity' => $user['updated_at'],
            'status' => $user['status'],
            'service_count' => (int)$serviceCount,
            'payment_count' => (int)$paymentData['count'],
            'total_payments' => (int)$paymentData['total'],
            'is_premium' => (bool)$user['is_premium']
        ];
    }
}
