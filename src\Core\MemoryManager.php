<?php

declare(strict_types=1);

namespace WeBot\Core;

/**
 * Memory Manager
 *
 * Advanced memory management with monitoring,
 * optimization, and automatic cleanup.
 *
 * @package WeBot\Core
 * @version 2.0
 */
class MemoryManager
{
    private int $memoryLimit;
    private int $warningThreshold;
    private int $criticalThreshold;
    private array $allocations = [];
    private array $metrics = [];
    private bool $monitoringEnabled;
    private int $gcThreshold;

    public function __construct(array $config = [])
    {
        $this->memoryLimit = $this->parseMemoryLimit($config['memory_limit'] ?? ini_get('memory_limit'));
        $this->warningThreshold = (int) ($this->memoryLimit * ($config['warning_threshold'] ?? 0.8));
        $this->criticalThreshold = (int) ($this->memoryLimit * ($config['critical_threshold'] ?? 0.9));
        $this->monitoringEnabled = $config['monitoring_enabled'] ?? true;
        $this->gcThreshold = $config['gc_threshold'] ?? 10000;

        $this->initializeMonitoring();
    }

    /**
     * Get current memory usage
     */
    public function getCurrentUsage(): array
    {
        return [
            'current' => memory_get_usage(true),
            'peak' => memory_get_peak_usage(true),
            'current_real' => memory_get_usage(false),
            'peak_real' => memory_get_peak_usage(false),
            'limit' => $this->memoryLimit,
            'percentage' => $this->getUsagePercentage()
        ];
    }

    /**
     * Get memory usage percentage
     */
    public function getUsagePercentage(): float
    {
        $current = memory_get_usage(true);
        return ($current / $this->memoryLimit) * 100;
    }

    /**
     * Check if memory usage is critical
     */
    public function isCritical(): bool
    {
        return memory_get_usage(true) >= $this->criticalThreshold;
    }

    /**
     * Check if memory usage is at warning level
     */
    public function isWarning(): bool
    {
        return memory_get_usage(true) >= $this->warningThreshold;
    }

    /**
     * Force garbage collection
     */
    public function forceGarbageCollection(): array
    {
        $beforeMemory = memory_get_usage(true);
        $beforeCycles = gc_collect_cycles();

        // Disable garbage collection temporarily
        $gcEnabled = gc_enabled();
        if (!$gcEnabled) {
            gc_enable();
        }

        // Force collection
        $collected = gc_collect_cycles();

        // Restore previous state
        if (!$gcEnabled) {
            gc_disable();
        }

        $afterMemory = memory_get_usage(true);
        $freed = $beforeMemory - $afterMemory;

        $result = [
            'cycles_collected' => $collected,
            'memory_before' => $beforeMemory,
            'memory_after' => $afterMemory,
            'memory_freed' => $freed,
            'percentage_freed' => $beforeMemory > 0 ? ($freed / $beforeMemory) * 100 : 0
        ];

        $this->logMetric('garbage_collection', $result);

        return $result;
    }

    /**
     * Optimize memory usage
     */
    public function optimize(): array
    {
        $optimizations = [];

        // Force garbage collection
        $gcResult = $this->forceGarbageCollection();
        $optimizations['garbage_collection'] = $gcResult;

        // Clear internal caches
        $this->clearInternalCaches();
        $optimizations['cache_cleared'] = true;

        // Optimize arrays
        $this->optimizeArrays();
        $optimizations['arrays_optimized'] = true;

        // Clean up allocations tracking
        $this->cleanupAllocations();
        $optimizations['allocations_cleaned'] = true;

        return $optimizations;
    }

    /**
     * Monitor memory allocation
     */
    public function trackAllocation(string $identifier, $data): void
    {
        if (!$this->monitoringEnabled) {
            return;
        }

        $size = $this->calculateSize($data);
        $this->allocations[$identifier] = [
            'size' => $size,
            'timestamp' => microtime(true),
            'memory_before' => memory_get_usage(true),
            'type' => gettype($data)
        ];

        // Auto cleanup if too many allocations
        if (count($this->allocations) > $this->gcThreshold) {
            $this->cleanupAllocations();
        }
    }

    /**
     * Release tracked allocation
     */
    public function releaseAllocation(string $identifier): void
    {
        if (isset($this->allocations[$identifier])) {
            unset($this->allocations[$identifier]);
        }
    }

    /**
     * Get allocation statistics
     */
    public function getAllocationStats(): array
    {
        $totalSize = 0;
        $typeStats = [];
        $oldestAllocation = null;
        $largestAllocation = null;

        foreach ($this->allocations as $id => $allocation) {
            $totalSize += $allocation['size'];

            $type = $allocation['type'];
            if (!isset($typeStats[$type])) {
                $typeStats[$type] = ['count' => 0, 'size' => 0];
            }
            $typeStats[$type]['count']++;
            $typeStats[$type]['size'] += $allocation['size'];

            if ($oldestAllocation === null || $allocation['timestamp'] < $oldestAllocation['timestamp']) {
                $oldestAllocation = $allocation;
                $oldestAllocation['id'] = $id;
            }

            if ($largestAllocation === null || $allocation['size'] > $largestAllocation['size']) {
                $largestAllocation = $allocation;
                $largestAllocation['id'] = $id;
            }
        }

        return [
            'total_allocations' => count($this->allocations),
            'total_size' => $totalSize,
            'average_size' => count($this->allocations) > 0 ? $totalSize / count($this->allocations) : 0,
            'type_stats' => $typeStats,
            'oldest_allocation' => $oldestAllocation,
            'largest_allocation' => $largestAllocation
        ];
    }

    /**
     * Set memory limit
     */
    public function setMemoryLimit(string $limit): bool
    {
        $bytes = $this->parseMemoryLimit($limit);

        if (ini_set('memory_limit', $limit) !== false) {
            $this->memoryLimit = $bytes;
            $this->warningThreshold = (int) ($bytes * 0.8);
            $this->criticalThreshold = (int) ($bytes * 0.9);
            return true;
        }

        return false;
    }

    /**
     * Get memory recommendations
     */
    public function getRecommendations(): array
    {
        $recommendations = [];
        $usage = $this->getCurrentUsage();
        $percentage = $usage['percentage'];

        if ($percentage > 90) {
            $recommendations[] = [
                'level' => 'critical',
                'message' => 'Memory usage is critical. Consider increasing memory limit or optimizing code.',
                'action' => 'immediate'
            ];
        } elseif ($percentage > 80) {
            $recommendations[] = [
                'level' => 'warning',
                'message' => 'Memory usage is high. Monitor closely and consider optimization.',
                'action' => 'soon'
            ];
        }

        $allocStats = $this->getAllocationStats();
        if ($allocStats['total_allocations'] > $this->gcThreshold) {
            $recommendations[] = [
                'level' => 'info',
                'message' => 'High number of tracked allocations. Consider cleanup.',
                'action' => 'cleanup'
            ];
        }

        if (isset($allocStats['largest_allocation']) && $allocStats['largest_allocation']['size'] > 50 * 1024 * 1024) {
            $recommendations[] = [
                'level' => 'warning',
                'message' => 'Large allocation detected. Review memory usage patterns.',
                'action' => 'review'
            ];
        }

        return $recommendations;
    }

    /**
     * Generate memory report
     */
    public function generateReport(): array
    {
        return [
            'timestamp' => date('Y-m-d H:i:s'),
            'usage' => $this->getCurrentUsage(),
            'allocations' => $this->getAllocationStats(),
            'metrics' => $this->getMetrics(),
            'recommendations' => $this->getRecommendations(),
            'gc_stats' => [
                'enabled' => gc_enabled(),
                'runs' => gc_status()
            ]
        ];
    }

    /**
     * Monitor memory in real-time
     */
    public function startMonitoring(callable $callback = null): void
    {
        if (!$this->monitoringEnabled) {
            return;
        }

        register_tick_function(function () use ($callback) {
            $usage = $this->getCurrentUsage();

            if ($this->isCritical()) {
                $this->handleCriticalMemory();
            } elseif ($this->isWarning()) {
                $this->handleWarningMemory();
            }

            if ($callback) {
                $callback($usage);
            }
        });

        declare(ticks=1000);
    }

    /**
     * Handle critical memory situation
     */
    private function handleCriticalMemory(): void
    {
        error_log('CRITICAL: Memory usage is critical - ' . $this->getUsagePercentage() . '%');

        // Force aggressive cleanup
        $this->optimize();

        // Log critical event
        $this->logMetric('critical_memory', [
            'usage' => $this->getCurrentUsage(),
            'timestamp' => time()
        ]);
    }

    /**
     * Handle warning memory situation
     */
    private function handleWarningMemory(): void
    {
        error_log('WARNING: Memory usage is high - ' . $this->getUsagePercentage() . '%');

        // Gentle cleanup
        $this->forceGarbageCollection();

        // Log warning event
        $this->logMetric('warning_memory', [
            'usage' => $this->getCurrentUsage(),
            'timestamp' => time()
        ]);
    }

    /**
     * Calculate size of data
     */
    private function calculateSize($data): int
    {
        if (is_string($data)) {
            return strlen($data);
        } elseif (is_array($data)) {
            return strlen(serialize($data));
        } elseif (is_object($data)) {
            return strlen(serialize($data));
        } else {
            return 8; // Approximate size for primitives
        }
    }

    /**
     * Parse memory limit string
     */
    private function parseMemoryLimit(string $limit): int
    {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit) - 1]);
        $value = (int) $limit;

        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }

    /**
     * Initialize monitoring
     */
    private function initializeMonitoring(): void
    {
        if (!$this->monitoringEnabled) {
            return;
        }

        // Enable garbage collection if not enabled
        if (!gc_enabled()) {
            gc_enable();
        }

        // Set up shutdown handler
        register_shutdown_function([$this, 'onShutdown']);
    }

    /**
     * Clear internal caches
     */
    private function clearInternalCaches(): void
    {
        // Clear realpath cache
        clearstatcache();

        // Clear opcache if available
        if (function_exists('opcache_reset')) {
            opcache_reset();
        }
    }

    /**
     * Optimize arrays
     */
    private function optimizeArrays(): void
    {
        // Clean up metrics array if too large
        if (count($this->metrics) > 1000) {
            $this->metrics = array_slice($this->metrics, -500, null, true);
        }
    }

    /**
     * Cleanup allocations
     */
    private function cleanupAllocations(): void
    {
        $now = microtime(true);
        $maxAge = 3600; // 1 hour

        foreach ($this->allocations as $id => $allocation) {
            if ($now - $allocation['timestamp'] > $maxAge) {
                unset($this->allocations[$id]);
            }
        }
    }

    /**
     * Log metric
     */
    private function logMetric(string $type, array $data): void
    {
        $this->metrics[] = [
            'type' => $type,
            'data' => $data,
            'timestamp' => microtime(true)
        ];
    }

    /**
     * Get metrics
     */
    private function getMetrics(): array
    {
        return $this->metrics;
    }

    /**
     * Shutdown handler
     */
    public function onShutdown(): void
    {
        if ($this->monitoringEnabled) {
            $finalReport = $this->generateReport();
            error_log('Memory Manager Final Report: ' . json_encode($finalReport));
        }
    }
}
