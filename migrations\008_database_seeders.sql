-- Migration: Database seeders
-- Version: 008
-- Description: Insert sample data for development and testing
-- Author: WeBot Team
-- Date: 2024-01-01

BEGIN;

-- Insert sample admin user
INSERT IGNORE INTO `users` (
    `userid`, `first_name`, `last_name`, `username`, `isAdmin`, `wallet`, `created_at`
) VALUES 
(
    123456789, 
    'Admin', 
    'User', 
    'admin_user', 
    1, 
    0, 
    NOW()
);

-- Insert sample regular users
INSERT IGNORE INTO `users` (
    `userid`, `first_name`, `last_name`, `username`, `isAdmin`, `wallet`, `created_at`
) VALUES 
(
    *********, 
    '<PERSON>', 
    'Doe', 
    'john_doe', 
    0, 
    50000, 
    DATE_SUB(NOW(), INTERVAL 30 DAY)
),
(
    *********, 
    'علی', 
    'احمدی', 
    'ali_ahmadi', 
    0, 
    25000, 
    DATE_SUB(NOW(), INTERVAL 15 DAY)
),
(
    *********, 
    '<PERSON>', 
    '<PERSON>', 
    'sarah_smith', 
    0, 
    75000, 
    DATE_SUB(NOW(), INTERVAL 7 DAY)
);

-- Insert sample panels
INSERT IGNORE INTO `panels` (
    `name`, `type`, `url`, `username`, `password`, `status`, `health_status`, `is_active`, `priority`, `max_users`, `current_users`
) VALUES 
(
    'Main Marzban Server',
    'marzban',
    'https://panel1.example.com',
    'admin',
    'encrypted_password_123',
    'online',
    'healthy',
    TRUE,
    1,
    1000,
    150
),
(
    'Marzneshin Premium',
    'marzneshin',
    'https://premium.example.com',
    'admin',
    'encrypted_password_456',
    'online',
    'healthy',
    TRUE,
    2,
    500,
    75
),
(
    'X-UI Backup Server',
    'x-ui',
    'https://backup.example.com',
    'admin',
    'encrypted_password_789',
    'offline',
    'warning',
    FALSE,
    3,
    200,
    0
),
(
    'Marzban Secondary',
    'marzban',
    'https://panel2.example.com',
    'admin',
    'encrypted_password_abc',
    'online',
    'healthy',
    TRUE,
    4,
    800,
    120
);

-- Insert sample services
INSERT IGNORE INTO `services` (
    `userid`, `username`, `server_id`, `panel_id`, `data_limit`, `expire_date`, `status`, `created_at`
) VALUES 
(
    *********,
    'john_doe_service1',
    1,
    1,
    53687091200, -- 50GB
    DATE_ADD(NOW(), INTERVAL 30 DAY),
    'active',
    DATE_SUB(NOW(), INTERVAL 25 DAY)
),
(
    *********,
    'ali_ahmadi_service1',
    2,
    2,
    107374182400, -- 100GB
    DATE_ADD(NOW(), INTERVAL 15 DAY),
    'active',
    DATE_SUB(NOW(), INTERVAL 10 DAY)
),
(
    *********,
    'sarah_smith_service1',
    1,
    1,
    161061273600, -- 150GB
    DATE_ADD(NOW(), INTERVAL 45 DAY),
    'active',
    DATE_SUB(NOW(), INTERVAL 5 DAY)
),
(
    *********,
    'john_doe_service2',
    3,
    4,
    32212254720, -- 30GB
    DATE_SUB(NOW(), INTERVAL 5 DAY),
    'expired',
    DATE_SUB(NOW(), INTERVAL 35 DAY)
);

-- Insert sample payments
INSERT IGNORE INTO `payments` (
    `userid`, `amount`, `status`, `gateway`, `transaction_id`, `description`, `created_at`
) VALUES 
(
    *********,
    25.00,
    'completed',
    'stripe',
    'txn_1234567890',
    'Payment for 50GB VPN service',
    DATE_SUB(NOW(), INTERVAL 25 DAY)
),
(
    *********,
    45.00,
    'completed',
    'paypal',
    'PAY-*********0',
    'Payment for 100GB VPN service',
    DATE_SUB(NOW(), INTERVAL 10 DAY)
),
(
    *********,
    65.00,
    'completed',
    'crypto',
    'btc_abcdef123456',
    'Payment for 150GB VPN service',
    DATE_SUB(NOW(), INTERVAL 5 DAY)
),
(
    *********,
    20.00,
    'failed',
    'stripe',
    'txn_failed_001',
    'Failed payment attempt',
    DATE_SUB(NOW(), INTERVAL 2 DAY)
);

-- Insert sample tickets
INSERT IGNORE INTO `tickets` (
    `user_id`, `subject`, `description`, `category`, `priority`, `status`, `assigned_to`, `created_at`
) VALUES 
(
    *********,
    'Connection Issues with VPN',
    'I am experiencing frequent disconnections with my VPN service. The connection drops every few minutes and I have to reconnect manually.',
    'connection',
    'high',
    'in_progress',
    123456789,
    DATE_SUB(NOW(), INTERVAL 2 DAY)
),
(
    *********,
    'Payment Not Processed',
    'سلام، پرداخت من انجام شده ولی سرویس هنوز فعال نشده. لطفاً بررسی کنید.',
    'payment',
    'urgent',
    'open',
    NULL,
    DATE_SUB(NOW(), INTERVAL 1 DAY)
),
(
    *********,
    'Request for Additional Bandwidth',
    'Hello, I would like to upgrade my current plan to get more bandwidth. Please let me know the available options.',
    'other',
    'medium',
    'resolved',
    123456789,
    DATE_SUB(NOW(), INTERVAL 5 DAY)
),
(
    *********,
    'Configuration Help Needed',
    'I need help setting up the VPN on my iPhone. The configuration file is not working properly.',
    'technical',
    'medium',
    'waiting',
    123456789,
    DATE_SUB(NOW(), INTERVAL 3 DAY)
);

-- Insert sample ticket replies
INSERT IGNORE INTO `ticket_replies` (
    `ticket_id`, `user_id`, `message`, `is_staff`, `created_at`
) VALUES 
(
    1,
    123456789,
    'Thank you for reporting this issue. We are investigating the connection problems on our servers. Please try connecting to a different server location in the meantime.',
    TRUE,
    DATE_SUB(NOW(), INTERVAL 1 DAY)
),
(
    1,
    *********,
    'I tried different servers but the problem persists. It happens on both my phone and laptop.',
    FALSE,
    DATE_SUB(NOW(), INTERVAL 12 HOUR)
),
(
    3,
    123456789,
    'We have several upgrade options available. I will send you the details via private message.',
    TRUE,
    DATE_SUB(NOW(), INTERVAL 4 DAY)
),
(
    3,
    *********,
    'Perfect, thank you for the quick response!',
    FALSE,
    DATE_SUB(NOW(), INTERVAL 4 DAY)
),
(
    4,
    123456789,
    'For iPhone setup, please follow these steps:\n1. Download the configuration file\n2. Open it with your VPN app\n3. Import the settings\n\nLet me know if you need further assistance.',
    TRUE,
    DATE_SUB(NOW(), INTERVAL 2 DAY)
);

-- Insert sample message queue entries
INSERT IGNORE INTO `message_queue` (
    `user_id`, `message`, `status`, `priority`, `scheduled_at`, `created_at`
) VALUES 
(
    *********,
    '🔔 Your VPN service will expire in 5 days. Please renew to avoid interruption.',
    'pending',
    2,
    DATE_ADD(NOW(), INTERVAL 1 HOUR),
    NOW()
),
(
    *********,
    '✅ Your payment has been processed successfully. Your service is now active.',
    'sent',
    1,
    DATE_SUB(NOW(), INTERVAL 2 HOUR),
    DATE_SUB(NOW(), INTERVAL 2 HOUR)
),
(
    *********,
    '🎉 Welcome to our premium VPN service! Your account is ready to use.',
    'sent',
    3,
    DATE_SUB(NOW(), INTERVAL 1 DAY),
    DATE_SUB(NOW(), INTERVAL 1 DAY)
);

-- Insert sample notification logs
INSERT IGNORE INTO `notification_logs` (
    `user_id`, `type`, `title`, `message`, `status`, `sent_at`, `created_at`
) VALUES 
(
    *********,
    'service_expiring',
    '⚠️ Service Expiring Soon',
    'Your VPN service will expire in 5 days. Please renew to continue using the service.',
    'sent',
    DATE_SUB(NOW(), INTERVAL 1 HOUR),
    DATE_SUB(NOW(), INTERVAL 1 HOUR)
),
(
    *********,
    'payment_successful',
    '✅ Payment Successful',
    'Your payment of $45.00 has been processed successfully.',
    'sent',
    DATE_SUB(NOW(), INTERVAL 10 DAY),
    DATE_SUB(NOW(), INTERVAL 10 DAY)
),
(
    *********,
    'service_created',
    '🎉 Service Activated',
    'Your new VPN service has been activated and is ready to use.',
    'read',
    DATE_SUB(NOW(), INTERVAL 5 DAY),
    DATE_SUB(NOW(), INTERVAL 5 DAY)
);

-- Insert sample panel health logs
INSERT IGNORE INTO `panel_health_log` (
    `panel_id`, `status`, `health_status`, `response_time_ms`, `checked_at`
) VALUES 
(1, 'online', 'healthy', 150, DATE_SUB(NOW(), INTERVAL 5 MINUTE)),
(1, 'online', 'healthy', 145, DATE_SUB(NOW(), INTERVAL 10 MINUTE)),
(1, 'online', 'healthy', 160, DATE_SUB(NOW(), INTERVAL 15 MINUTE)),
(2, 'online', 'healthy', 120, DATE_SUB(NOW(), INTERVAL 5 MINUTE)),
(2, 'online', 'healthy', 125, DATE_SUB(NOW(), INTERVAL 10 MINUTE)),
(3, 'offline', 'critical', NULL, DATE_SUB(NOW(), INTERVAL 5 MINUTE)),
(3, 'offline', 'critical', NULL, DATE_SUB(NOW(), INTERVAL 10 MINUTE)),
(4, 'online', 'healthy', 180, DATE_SUB(NOW(), INTERVAL 5 MINUTE));

-- Update panel statistics with sample data
UPDATE `panels` SET 
    `statistics` = JSON_OBJECT(
        'users_count', `current_users`,
        'active_users', FLOOR(`current_users` * 0.8),
        'total_traffic', FLOOR(RAND() * 1000000000000),
        'today_traffic', FLOOR(RAND() * 10000000000),
        'cpu_usage', FLOOR(RAND() * 80) + 10,
        'memory_usage', FLOOR(RAND() * 70) + 20,
        'disk_usage', FLOOR(RAND() * 60) + 15,
        'uptime', FLOOR(RAND() * 2592000) + 86400
    ),
    `last_sync_at` = DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 60) MINUTE),
    `last_health_check` = DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 10) MINUTE)
WHERE `id` IN (1, 2, 4);

-- Create sample user sessions
INSERT IGNORE INTO `user_sessions` (
    `user_id`, `session_id`, `session_key`, `session_value`, `expires_at`
) VALUES 
(
    *********,
    SHA2(CONCAT('session_', *********, '_', UNIX_TIMESTAMP()), 256),
    'last_action',
    'main_menu',
    DATE_ADD(NOW(), INTERVAL 24 HOUR)
),
(
    *********,
    SHA2(CONCAT('session_', *********, '_', UNIX_TIMESTAMP()), 256),
    'language',
    'fa',
    DATE_ADD(NOW(), INTERVAL 24 HOUR)
),
(
    *********,
    SHA2(CONCAT('session_', *********, '_', UNIX_TIMESTAMP()), 256),
    'step',
    'service_selection',
    DATE_ADD(NOW(), INTERVAL 24 HOUR)
);

-- Update user statistics
UPDATE `users` u SET 
    `wallet` = (
        SELECT COALESCE(SUM(p.amount), 0) 
        FROM `payments` p 
        WHERE p.userid = u.userid 
        AND p.status = 'completed'
    ) - (
        SELECT COALESCE(SUM(25), 0) 
        FROM `services` s 
        WHERE s.userid = u.userid 
        AND s.status = 'active'
    )
WHERE u.userid IN (*********, *********, *********);

-- Create sample configuration data for panels
UPDATE `panels` SET 
    `config_data` = CASE 
        WHEN `type` = 'marzban' THEN JSON_OBJECT(
            'oauth2_enabled', true,
            'auto_backup', true,
            'max_connections_per_user', 3,
            'default_protocols', JSON_ARRAY('vmess', 'vless'),
            'webhook_url', CONCAT(`url`, '/webhook')
        )
        WHEN `type` = 'marzneshin' THEN JSON_OBJECT(
            'templates_enabled', true,
            'user_groups_enabled', true,
            'multi_node_support', true,
            'subscription_tracking', true,
            'default_protocols', JSON_ARRAY('vmess', 'vless', 'trojan')
        )
        WHEN `type` = 'x-ui' THEN JSON_OBJECT(
            'inbound_management', true,
            'traffic_monitoring', true,
            'user_management', 'basic',
            'default_protocols', JSON_ARRAY('vmess', 'vless')
        )
    END
WHERE `id` IN (1, 2, 3, 4);

-- Record migration
INSERT INTO schema_versions (version, applied_at, description) 
VALUES (8, NOW(), 'Insert sample data for development and testing')
ON DUPLICATE KEY UPDATE applied_at = NOW();

COMMIT;

-- Verification queries
SELECT 'Sample data inserted successfully' as message;
SELECT 'Users:' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT 'Panels:', COUNT(*) FROM panels
UNION ALL
SELECT 'Services:', COUNT(*) FROM services
UNION ALL
SELECT 'Payments:', COUNT(*) FROM payments
UNION ALL
SELECT 'Tickets:', COUNT(*) FROM tickets
UNION ALL
SELECT 'Ticket Replies:', COUNT(*) FROM ticket_replies
UNION ALL
SELECT 'Message Queue:', COUNT(*) FROM message_queue
UNION ALL
SELECT 'Message Templates:', COUNT(*) FROM message_templates
UNION ALL
SELECT 'Notification Logs:', COUNT(*) FROM notification_logs
UNION ALL
SELECT 'Panel Health Logs:', COUNT(*) FROM panel_health_log
UNION ALL
SELECT 'User Sessions:', COUNT(*) FROM user_sessions;
