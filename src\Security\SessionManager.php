<?php

declare(strict_types=1);

namespace WeBot\Security;

use WeBot\Utils\Logger;
use Monolog\Logger as MonologLogger;
use WeBot\Exceptions\SecurityException;

/**
 * Secure Session Manager
 *
 * Provides secure session handling with protection against:
 * - Session fixation
 * - Session hijacking
 * - CSRF attacks
 * - Session timeout
 *
 * @package WeBot\Security
 * @version 2.0
 */
class SessionManager
{
    private array $config;
    private MonologLogger $logger;
    private static ?self $instance = null;

    private function __construct(array $config = [])
    {
        $this->config = array_merge([
            'name' => 'WEBOT_SESSION',
            'lifetime' => 1800, // 30 minutes
            'path' => '/',
            'domain' => '',
            'secure' => true,
            'httponly' => true,
            'samesite' => 'Strict',
            'regenerate_interval' => 300, // 5 minutes
            'max_lifetime' => 7200, // 2 hours
            'ip_check' => true,
            'user_agent_check' => true,
            'fingerprint_check' => true,
        ], $config);

        $this->logger = Logger::getInstance();
        $this->initializeSession();
    }

    public static function getInstance(array $config = []): self
    {
        if (self::$instance === null) {
            self::$instance = new self($config);
        }
        return self::$instance;
    }

    /**
     * Initialize secure session
     */
    private function initializeSession(): void
    {
        // Configure session settings
        ini_set('session.name', $this->config['name']);
        ini_set('session.cookie_lifetime', (string) $this->config['lifetime']);
        ini_set('session.cookie_path', $this->config['path']);
        ini_set('session.cookie_domain', $this->config['domain']);
        ini_set('session.cookie_secure', $this->config['secure'] ? '1' : '0');
        ini_set('session.cookie_httponly', $this->config['httponly'] ? '1' : '0');
        ini_set('session.cookie_samesite', $this->config['samesite']);
        ini_set('session.use_strict_mode', '1');
        ini_set('session.use_only_cookies', '1');
        ini_set('session.use_trans_sid', '0');

        // Start session if not already started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }

    /**
     * Start secure session
     */
    public function start(): bool
    {
        if (session_status() === PHP_SESSION_ACTIVE) {
            return true;
        }

        $started = session_start();

        if ($started) {
            $this->validateSession();
            $this->updateSessionActivity();
        }

        return $started;
    }

    /**
     * Validate session security
     */
    private function validateSession(): void
    {
        // Check session timeout
        if ($this->isSessionExpired()) {
            $this->destroy();
            throw new SecurityException('Session expired');
        }

        // Check IP address (if enabled)
        if ($this->config['ip_check'] && !$this->validateIpAddress()) {
            $this->destroy();
            $this->logger->warning('Session IP mismatch detected');
            throw new SecurityException('Session security violation');
        }

        // Check User Agent (if enabled)
        if ($this->config['user_agent_check'] && !$this->validateUserAgent()) {
            $this->destroy();
            $this->logger->warning('Session User-Agent mismatch detected');
            throw new SecurityException('Session security violation');
        }

        // Check browser fingerprint (if enabled)
        if ($this->config['fingerprint_check'] && !$this->validateFingerprint()) {
            $this->destroy();
            $this->logger->warning('Session fingerprint mismatch detected');
            throw new SecurityException('Session security violation');
        }

        // Regenerate session ID periodically
        if ($this->shouldRegenerateId()) {
            $this->regenerateId();
        }
    }

    /**
     * Check if session is expired
     */
    private function isSessionExpired(): bool
    {
        $lastActivity = $_SESSION['_last_activity'] ?? 0;
        $createdAt = $_SESSION['_created_at'] ?? 0;
        $now = time();

        // Check activity timeout
        if ($now - $lastActivity > $this->config['lifetime']) {
            return true;
        }

        // Check maximum lifetime
        if ($now - $createdAt > $this->config['max_lifetime']) {
            return true;
        }

        return false;
    }

    /**
     * Validate IP address
     */
    private function validateIpAddress(): bool
    {
        $currentIp = $_SERVER['REMOTE_ADDR'] ?? '';
        $sessionIp = $_SESSION['_ip_address'] ?? '';

        if (empty($sessionIp)) {
            $_SESSION['_ip_address'] = $currentIp;
            return true;
        }

        return $currentIp === $sessionIp;
    }

    /**
     * Validate User Agent
     */
    private function validateUserAgent(): bool
    {
        $currentUserAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $sessionUserAgent = $_SESSION['_user_agent'] ?? '';

        if (empty($sessionUserAgent)) {
            $_SESSION['_user_agent'] = $currentUserAgent;
            return true;
        }

        return $currentUserAgent === $sessionUserAgent;
    }

    /**
     * Validate browser fingerprint
     */
    private function validateFingerprint(): bool
    {
        $currentFingerprint = $this->generateFingerprint();
        $sessionFingerprint = $_SESSION['_fingerprint'] ?? '';

        if (empty($sessionFingerprint)) {
            $_SESSION['_fingerprint'] = $currentFingerprint;
            return true;
        }

        return $currentFingerprint === $sessionFingerprint;
    }

    /**
     * Generate browser fingerprint
     */
    private function generateFingerprint(): string
    {
        $components = [
            $_SERVER['HTTP_USER_AGENT'] ?? '',
            $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '',
            $_SERVER['HTTP_ACCEPT_ENCODING'] ?? '',
            $_SERVER['HTTP_ACCEPT'] ?? '',
        ];

        return hash('sha256', implode('|', $components));
    }

    /**
     * Check if session ID should be regenerated
     */
    private function shouldRegenerateId(): bool
    {
        $lastRegeneration = $_SESSION['_last_regeneration'] ?? 0;
        return (time() - $lastRegeneration) > $this->config['regenerate_interval'];
    }

    /**
     * Regenerate session ID
     */
    public function regenerateId(): bool
    {
        $regenerated = session_regenerate_id(true);

        if ($regenerated) {
            $_SESSION['_last_regeneration'] = time();
            $this->logger->info('Session ID regenerated');
        }

        return $regenerated;
    }

    /**
     * Update session activity
     */
    private function updateSessionActivity(): void
    {
        $now = time();

        if (!isset($_SESSION['_created_at'])) {
            $_SESSION['_created_at'] = $now;
        }

        $_SESSION['_last_activity'] = $now;
    }

    /**
     * Set session value
     */
    public function set(string $key, $value): void
    {
        $_SESSION[$key] = $value;
    }

    /**
     * Get session value
     */
    public function get(string $key, $default = null)
    {
        return $_SESSION[$key] ?? $default;
    }

    /**
     * Check if session has key
     */
    public function has(string $key): bool
    {
        return isset($_SESSION[$key]);
    }

    /**
     * Remove session value
     */
    public function remove(string $key): void
    {
        unset($_SESSION[$key]);
    }

    /**
     * Get all session data
     */
    public function all(): array
    {
        return $_SESSION;
    }

    /**
     * Clear all session data
     */
    public function clear(): void
    {
        $_SESSION = [];
    }

    /**
     * Destroy session
     */
    public function destroy(): bool
    {
        $_SESSION = [];

        if (ini_get('session.use_cookies')) {
            $params = session_get_cookie_params();
            setcookie(
                session_name(),
                '',
                time() - 42000,
                $params['path'],
                $params['domain'],
                $params['secure'],
                $params['httponly']
            );
        }

        return session_destroy();
    }

    /**
     * Get session ID
     */
    public function getId(): string
    {
        return session_id();
    }

    /**
     * Get session status
     */
    public function getStatus(): int
    {
        return session_status();
    }

    /**
     * Check if session is active
     */
    public function isActive(): bool
    {
        return session_status() === PHP_SESSION_ACTIVE;
    }

    /**
     * Generate CSRF token
     */
    public function generateCsrfToken(): string
    {
        if (!$this->has('_csrf_token')) {
            $this->set('_csrf_token', bin2hex(random_bytes(32)));
        }

        return $this->get('_csrf_token');
    }

    /**
     * Validate CSRF token
     */
    public function validateCsrfToken(string $token): bool
    {
        $sessionToken = $this->get('_csrf_token');

        if (empty($sessionToken) || empty($token)) {
            return false;
        }

        return hash_equals($sessionToken, $token);
    }
}
