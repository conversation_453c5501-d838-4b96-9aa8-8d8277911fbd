{"name": "webot/telegram-vpn-bot", "description": "Professional Telegram VPN Bot - WeBot", "type": "project", "license": "GPL-3.0", "authors": [{"name": "WeBot Team", "email": "<EMAIL>"}], "require": {"php": ">=8.1", "ext-curl": "*", "ext-json": "*", "ext-pdo": "*", "ext-mbstring": "*", "ext-gd": "*", "ext-redis": "*", "endroid/qr-code": "^4.8", "monolog/monolog": "^3.5", "vlucas/phpdotenv": "^5.6", "guzzlehttp/guzzle": "^7.8", "symfony/validator": "^6.4", "league/container": "^4.2", "ramsey/uuid": "^4.7", "firebase/php-jwt": "^6.10", "psr/log": "^3.0", "psr/container": "^2.0"}, "require-dev": {"phpunit/phpunit": "^10.5", "phpstan/phpstan": "^1.10", "squizlabs/php_codesniffer": "^3.8", "phpmetrics/phpmetrics": "^2.8", "infection/infection": "^0.27", "slevomat/coding-standard": "^8.14"}, "autoload": {"psr-4": {"WeBot\\": "src/"}, "files": ["src/Utils/helpers.php"], "classmap": ["src/Legacy/"], "exclude-from-classmap": ["src/Legacy/phpqrcode/"]}, "autoload-dev": {"psr-4": {"WeBot\\Tests\\": "tests/"}}, "scripts": {"analyze": ["@cs-check", "@phpstan", "@phpmetrics"], "cs-check": "vendor/bin/phpcs --standard=phpcs.xml", "cs-fix": "vendor/bin/phpcbf --standard=phpcs.xml", "phpstan": "vendor/bin/phpstan analyse --configuration=phpstan.neon --memory-limit=1G", "phpstan-baseline": "vendor/bin/phpstan analyse --configuration=phpstan.neon --generate-baseline", "phpmetrics": "vendor/bin/phpmetrics --report-html=reports/metrics src/", "test": "vendor/bin/phpunit", "test-coverage": "vendor/bin/phpunit --coverage-html reports/coverage", "test-unit": "vendor/bin/phpunit --testsuite=Unit", "test-integration": "vendor/bin/phpunit --testsuite=Integration", "test-e2e": "vendor/bin/phpunit --testsuite=E2E", "security": "composer audit", "quality": ["@cs-check", "@phpstan", "@test"], "fix": ["@cs-fix"], "install-dev": "composer install", "install-prod": "composer install --no-dev --optimize-autoloader", "dump-autoload": "composer dump-autoload --optimize", "post-install-cmd": ["@dump-autoload"], "post-update-cmd": ["@dump-autoload"]}, "config": {"optimize-autoloader": true, "sort-packages": true, "classmap-authoritative": false, "apcu-autoloader": false, "preferred-install": {"*": "dist"}, "allow-plugins": {"infection/extension-installer": true, "dealerdirect/phpcodesniffer-composer-installer": true}, "exclude-from-classmap": ["/src/Legacy/"], "platform": {"php": "8.1"}, "cache-dir": "storage/cache/composer"}, "minimum-stability": "stable", "prefer-stable": true}