# WeBot v2.0 Docker Compose Configuration
version: '3.8'

services:
  # WeBot Application
  webot:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: webot_app
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_DATABASE=webot
      - DB_USERNAME=webot
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - WEBHOOK_URL=${WEBHOOK_URL}
    volumes:
      - ./storage:/var/www/html/storage
      - ./public/uploads:/var/www/html/public/uploads
      - ./logs:/var/www/html/logs
    depends_on:
      - mysql
      - redis
    networks:
      - webot_network
    healthcheck:
      test: ["C<PERSON>", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: webot_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: webot
      MYSQL_USER: webot
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
      - ./docker/mysql/conf.d:/etc/mysql/conf.d
    ports:
      - "3307:3306"
    networks:
      - webot_network
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: webot_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - webot_network
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Load Balancer (for scaling)
  nginx:
    image: nginx:alpine
    container_name: webot_nginx
    restart: unless-stopped
    ports:
      - "8080:80"
      - "8443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - ./docker/ssl:/etc/nginx/ssl
      - ./public:/var/www/html/public
    depends_on:
      webot:
        condition: service_healthy
    networks:
      - webot_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  # Monitoring with Prometheus
  # prometheus:
  #   image: prom/prometheus:latest
  #   container_name: webot_prometheus
  #   restart: unless-stopped
  #   ports:
  #     - "9090:9090"
  #   volumes:
  #     - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
  #     - prometheus_data:/prometheus
  #   networks:
  #     - webot_network
  #   command:
  #     - '--config.file=/etc/prometheus/prometheus.yml'
  #     - '--storage.tsdb.path=/prometheus'
  #     - '--web.console.libraries=/etc/prometheus/console_libraries'
  #     - '--web.console.templates=/etc/prometheus/consoles'
  #   healthcheck:
  #     test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 30s

  # Grafana Dashboard
  # grafana:
  #   image: grafana/grafana:latest
  #   container_name: webot_grafana
  #   restart: unless-stopped
  #   ports:
  #     - "3000:3000"
  #   environment:
  #     - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
  #   volumes:
  #     - grafana_data:/var/lib/grafana
  #     - ./docker/grafana/provisioning:/etc/grafana/provisioning
  #   depends_on:
  #     prometheus:
  #       condition: service_healthy
  #   networks:
  #     - webot_network
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 60s

  # Log aggregation with ELK Stack
  # elasticsearch:
  #   image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
  #   container_name: webot_elasticsearch
  #   restart: unless-stopped
  #   environment:
  #     - discovery.type=single-node
  #     - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
  #     - xpack.security.enabled=false
  #   volumes:
  #     - elasticsearch_data:/usr/share/elasticsearch/data
  #   ports:
  #     - "9200:9200"
  #   networks:
  #     - webot_network

  # logstash:
  #   image: docker.elastic.co/logstash/logstash:8.11.0
  #   container_name: webot_logstash
  #   restart: unless-stopped
  #   volumes:
  #     - ./docker/logstash/pipeline:/usr/share/logstash/pipeline
  #     - ./logs:/var/log/webot
  #   ports:
  #     - "5044:5044"
  #   depends_on:
  #     - elasticsearch
  #   networks:
  #     - webot_network

  # kibana:
  #   image: docker.elastic.co/kibana/kibana:8.11.0
  #   container_name: webot_kibana
  #   restart: unless-stopped
  #   ports:
  #     - "5601:5601"
  #   environment:
  #     - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
  #   depends_on:
  #     - elasticsearch
  #   networks:
  #     - webot_network

# Volumes
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  # prometheus_data:
  #   driver: local
  # grafana_data:
  #   driver: local
  # elasticsearch_data:
  #   driver: local

# Networks
networks:
  webot_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
