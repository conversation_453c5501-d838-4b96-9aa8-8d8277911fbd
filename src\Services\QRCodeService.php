<?php

declare(strict_types=1);

namespace WeBot\Services;

use WeBot\Core\CacheManager;
use WeBot\Exceptions\WeBotException;

/**
 * QR Code Service
 *
 * Handles QR code generation for VPN configurations,
 * subscription URLs, and other data with caching support.
 *
 * @package WeBot\Services
 * @version 2.0
 */
class QRCodeService
{
    private CacheManager $cache;
    private array $config;
    private string $tempPath;

    public function __construct(CacheManager $cache, array $config = [])
    {
        $this->cache = $cache;
        $this->config = array_merge([
            'size' => 300,
            'margin' => 10,
            'error_correction' => 'M', // L, M, Q, H
            'format' => 'png',
            'cache_ttl' => 3600, // 1 hour
            'temp_path' => 'storage/temp/qr',
            'max_data_length' => 2953, // QR code data limit
        ], $config);

        $this->tempPath = $this->config['temp_path'];
        $this->ensureTempDirectory();
    }

    /**
     * Generate QR code for VPN configuration
     */
    public function generateVPNConfig(string $configData, array $options = []): array
    {
        try {
            // Validate config data
            if (empty($configData)) {
                throw new WeBotException('Configuration data cannot be empty');
            }

            if (strlen($configData) > $this->config['max_data_length']) {
                throw new WeBotException('Configuration data too long for QR code');
            }

            $cacheKey = 'qr_vpn_' . md5($configData);

            // Check cache first
            if ($cached = $this->cache->get($cacheKey)) {
                return [
                    'success' => true,
                    'qr_code' => $cached['qr_code'],
                    'file_path' => $cached['file_path'],
                    'cached' => true
                ];
            }

            // Generate QR code
            $result = $this->generateQRCode($configData, array_merge([
                'type' => 'vpn_config',
                'size' => $options['size'] ?? 400,
                'error_correction' => 'M'
            ], $options));

            if ($result['success']) {
                // Cache the result
                $this->cache->set($cacheKey, [
                    'qr_code' => $result['qr_code'],
                    'file_path' => $result['file_path']
                ], $this->config['cache_ttl']);
            }

            return $result;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to generate VPN QR code: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Generate QR code for subscription URL
     */
    public function generateSubscriptionQR(string $subscriptionUrl, array $options = []): array
    {
        try {
            if (empty($subscriptionUrl) || !filter_var($subscriptionUrl, FILTER_VALIDATE_URL)) {
                throw new WeBotException('Invalid subscription URL');
            }

            $cacheKey = 'qr_sub_' . md5($subscriptionUrl);

            // Check cache first
            if ($cached = $this->cache->get($cacheKey)) {
                return [
                    'success' => true,
                    'qr_code' => $cached['qr_code'],
                    'file_path' => $cached['file_path'],
                    'cached' => true
                ];
            }

            // Generate QR code
            $result = $this->generateQRCode($subscriptionUrl, array_merge([
                'type' => 'subscription',
                'size' => $options['size'] ?? 300,
                'error_correction' => 'L'
            ], $options));

            if ($result['success']) {
                // Cache the result
                $this->cache->set($cacheKey, [
                    'qr_code' => $result['qr_code'],
                    'file_path' => $result['file_path']
                ], $this->config['cache_ttl']);
            }

            return $result;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to generate subscription QR code: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Generate QR code for text data
     */
    public function generateTextQR(string $text, array $options = []): array
    {
        try {
            if (empty($text)) {
                throw new WeBotException('Text cannot be empty');
            }

            if (strlen($text) > $this->config['max_data_length']) {
                throw new WeBotException('Text too long for QR code');
            }

            return $this->generateQRCode($text, array_merge([
                'type' => 'text',
                'size' => $options['size'] ?? $this->config['size'],
                'error_correction' => $this->config['error_correction']
            ], $options));
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to generate text QR code: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Generate QR code using different methods
     */
    private function generateQRCode(string $data, array $options = []): array
    {
        $size = $options['size'] ?? $this->config['size'];
        $margin = $options['margin'] ?? $this->config['margin'];
        $errorCorrection = $options['error_correction'] ?? $this->config['error_correction'];
        $format = $options['format'] ?? $this->config['format'];
        $type = $options['type'] ?? 'generic';

        // Try different QR code generation methods
        $methods = [
            'google_charts' => [$this, 'generateWithGoogleCharts'],
            'qr_server' => [$this, 'generateWithQRServer'],
            'local_library' => [$this, 'generateWithLocalLibrary']
        ];

        foreach ($methods as $methodName => $method) {
            try {
                $result = call_user_func($method, $data, $size, $margin, $errorCorrection, $format, $type);
                if ($result['success']) {
                    $result['method'] = $methodName;
                    return $result;
                }
            } catch (\Exception $e) {
                // Continue to next method
                continue;
            }
        }

        return [
            'success' => false,
            'error' => 'All QR code generation methods failed'
        ];
    }

    /**
     * Generate QR code using Google Charts API
     */
    private function generateWithGoogleCharts(string $data, int $size, int $margin, string $errorCorrection, string $format, string $type): array
    {
        try {
            $encodedData = urlencode($data);
            $url = "https://chart.googleapis.com/chart?chs={$size}x{$size}&cht=qr&chl={$encodedData}&choe=UTF-8&chld={$errorCorrection}|{$margin}";

            $context = stream_context_create([
                'http' => [
                    'timeout' => 10,
                    'user_agent' => 'WeBot/2.0 QRCodeService'
                ]
            ]);

            $qrData = file_get_contents($url, false, $context);

            if ($qrData === false) {
                throw new \Exception('Failed to fetch QR code from Google Charts');
            }

            // Save to file
            $filename = $this->generateFilename($type, $format);
            $filePath = $this->tempPath . '/' . $filename;

            if (file_put_contents($filePath, $qrData) === false) {
                throw new \Exception('Failed to save QR code file');
            }

            return [
                'success' => true,
                'qr_code' => base64_encode($qrData),
                'file_path' => $filePath,
                'filename' => $filename,
                'size' => strlen($qrData),
                'dimensions' => "{$size}x{$size}"
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Generate QR code using QR Server API
     */
    private function generateWithQRServer(string $data, int $size, int $margin, string $errorCorrection, string $format, string $type): array
    {
        try {
            $encodedData = urlencode($data);
            $url = "https://api.qrserver.com/v1/create-qr-code/?size={$size}x{$size}&data={$encodedData}&format={$format}&margin={$margin}&ecc={$errorCorrection}";

            $context = stream_context_create([
                'http' => [
                    'timeout' => 10,
                    'user_agent' => 'WeBot/2.0 QRCodeService'
                ]
            ]);

            $qrData = file_get_contents($url, false, $context);

            if ($qrData === false) {
                throw new \Exception('Failed to fetch QR code from QR Server');
            }

            // Save to file
            $filename = $this->generateFilename($type, $format);
            $filePath = $this->tempPath . '/' . $filename;

            if (file_put_contents($filePath, $qrData) === false) {
                throw new \Exception('Failed to save QR code file');
            }

            return [
                'success' => true,
                'qr_code' => base64_encode($qrData),
                'file_path' => $filePath,
                'filename' => $filename,
                'size' => strlen($qrData),
                'dimensions' => "{$size}x{$size}"
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Generate QR code using local library (placeholder)
     */
    private function generateWithLocalLibrary(string $data, int $size, int $margin, string $errorCorrection, string $format, string $type): array
    {
        // This would use a local PHP QR code library like endroid/qr-code
        // For now, return failure to fall back to online services
        return [
            'success' => false,
            'error' => 'Local QR code library not implemented'
        ];
    }

    /**
     * Generate unique filename
     */
    private function generateFilename(string $type, string $format): string
    {
        $timestamp = time();
        $random = bin2hex(random_bytes(4));
        return "qr_{$type}_{$timestamp}_{$random}.{$format}";
    }

    /**
     * Ensure temp directory exists
     */
    private function ensureTempDirectory(): void
    {
        if (!is_dir($this->tempPath)) {
            if (!mkdir($this->tempPath, 0755, true)) {
                throw new WeBotException("Failed to create temp directory: {$this->tempPath}");
            }
        }

        if (!is_writable($this->tempPath)) {
            throw new WeBotException("Temp directory is not writable: {$this->tempPath}");
        }
    }

    /**
     * Clean up old QR code files
     */
    public function cleanup(int $maxAge = 3600): int
    {
        $deletedCount = 0;
        $cutoffTime = time() - $maxAge;

        try {
            $files = glob($this->tempPath . '/qr_*');

            foreach ($files as $file) {
                if (is_file($file) && filemtime($file) < $cutoffTime) {
                    if (unlink($file)) {
                        $deletedCount++;
                    }
                }
            }
        } catch (\Exception $e) {
            // Log error but don't throw
            error_log("QR code cleanup failed: " . $e->getMessage());
        }

        return $deletedCount;
    }

    /**
     * Get QR code info
     */
    public function getQRInfo(string $filePath): array
    {
        if (!file_exists($filePath)) {
            return [
                'success' => false,
                'error' => 'File not found'
            ];
        }

        $fileInfo = [
            'success' => true,
            'file_path' => $filePath,
            'filename' => basename($filePath),
            'size' => filesize($filePath),
            'created_at' => filemtime($filePath),
            'mime_type' => mime_content_type($filePath)
        ];

        // Get image dimensions if it's an image
        if (strpos($fileInfo['mime_type'], 'image/') === 0) {
            $imageInfo = getimagesize($filePath);
            if ($imageInfo) {
                $fileInfo['width'] = $imageInfo[0];
                $fileInfo['height'] = $imageInfo[1];
                $fileInfo['dimensions'] = "{$imageInfo[0]}x{$imageInfo[1]}";
            }
        }

        return $fileInfo;
    }

    /**
     * Validate QR code data
     */
    public function validateData(string $data, string $type = 'generic'): array
    {
        $errors = [];

        if (empty($data)) {
            $errors[] = 'Data cannot be empty';
        }

        if (strlen($data) > $this->config['max_data_length']) {
            $errors[] = "Data too long (max {$this->config['max_data_length']} characters)";
        }

        // Type-specific validation
        switch ($type) {
            case 'subscription':
                if (!filter_var($data, FILTER_VALIDATE_URL)) {
                    $errors[] = 'Invalid URL format';
                }
                break;
            case 'vpn_config':
                if (!$this->isValidVPNConfig($data)) {
                    $errors[] = 'Invalid VPN configuration format';
                }
                break;
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Check if data is valid VPN configuration
     */
    private function isValidVPNConfig(string $data): bool
    {
        // Check for common VPN config patterns
        $patterns = [
            '/^vmess:\/\//',
            '/^vless:\/\//',
            '/^trojan:\/\//',
            '/^ss:\/\//',
            '/^ssr:\/\//',
            '/^\{.*"v".*"ps".*\}$/' // JSON format
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $data)) {
                return true;
            }
        }

        return false;
    }
}
