<?php
/**
 * Database Connection Test for WeBot
 * 
 * This script tests the database connectivity and verifies
 * that the database service is working properly.
 */

declare(strict_types=1);

echo "=== WeBot Database Connection Test ===\n\n";

// Load the application
try {
    require_once __DIR__ . '/autoload.php';
    echo "✅ Application loaded successfully\n\n";
} catch (Exception $e) {
    echo "❌ Failed to load application: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 1: Database service availability
echo "1. Database Service Availability Test:\n";
try {
    $dbService = db();
    echo "   ✅ Database service is available\n";
    
    if (method_exists($dbService, 'getConnection')) {
        echo "   ✅ Database service has getConnection() method\n";
    } else {
        echo "   ❌ Database service missing getConnection() method\n";
        exit(1);
    }
} catch (Exception $e) {
    echo "   ❌ Database service not available: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 2: Database configuration
echo "\n2. Database Configuration Test:\n";
$configOk = true;

$dbHost = env('DB_HOST');
$dbDatabase = env('DB_DATABASE');
$dbUsername = env('DB_USERNAME');
$dbPassword = env('DB_PASSWORD', '');

if (!empty($dbHost)) {
    echo "   ✅ DB_HOST: {$dbHost}\n";
} else {
    echo "   ❌ DB_HOST: Not configured\n";
    $configOk = false;
}

if (!empty($dbDatabase)) {
    echo "   ✅ DB_DATABASE: {$dbDatabase}\n";
} else {
    echo "   ❌ DB_DATABASE: Not configured\n";
    $configOk = false;
}

if (!empty($dbUsername)) {
    echo "   ✅ DB_USERNAME: {$dbUsername}\n";
} else {
    echo "   ❌ DB_USERNAME: Not configured\n";
    $configOk = false;
}

echo "   ℹ️  DB_PASSWORD: " . (empty($dbPassword) ? "Not set" : "Set") . "\n";

// Test 3: Database connection attempt
echo "\n3. Database Connection Attempt Test:\n";
$connectionOk = false;

try {
    $connection = $dbService->getConnection();
    if ($connection !== null) {
        echo "   ✅ Database connection established\n";
        $connectionOk = true;
        
        // Test connection type
        if ($connection instanceof PDO) {
            echo "   ✅ Connection is PDO instance\n";
        } else {
            echo "   ⚠️  Connection is not PDO instance\n";
        }
    } else {
        echo "   ❌ Database connection returned null\n";
    }
} catch (Exception $e) {
    echo "   ❌ Database connection failed: " . $e->getMessage() . "\n";
    echo "   ℹ️  This is expected if database is not set up yet\n";
}

// Test 4: Database driver and version (if connected)
if ($connectionOk) {
    echo "\n4. Database Driver and Version Test:\n";
    try {
        $connection = $dbService->getConnection();
        
        // Get database driver
        $driver = $connection->getAttribute(PDO::ATTR_DRIVER_NAME);
        echo "   ✅ Database driver: {$driver}\n";
        
        // Get database version
        $version = $connection->getAttribute(PDO::ATTR_SERVER_VERSION);
        echo "   ✅ Database version: {$version}\n";
        
        // Test a simple query
        $stmt = $connection->query('SELECT 1 as test');
        if ($stmt) {
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($result && $result['test'] == 1) {
                echo "   ✅ Simple query test passed\n";
            } else {
                echo "   ❌ Simple query test failed\n";
            }
        } else {
            echo "   ❌ Failed to execute simple query\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Database driver/version test failed: " . $e->getMessage() . "\n";
    }
} else {
    echo "\n4. Database Driver and Version Test: Skipped (no connection)\n";
}

// Test 5: Database class instantiation
echo "\n5. Database Class Instantiation Test:\n";
$classInstantiationOk = true;

try {
    $database = new WeBot\Core\Database();
    echo "   ✅ Database class can be instantiated\n";
    
    if (method_exists($database, 'connect')) {
        echo "   ✅ Database class has connect() method\n";
    } else {
        echo "   ❌ Database class missing connect() method\n";
        $classInstantiationOk = false;
    }
} catch (Exception $e) {
    echo "   ❌ Failed to instantiate Database class: " . $e->getMessage() . "\n";
    $classInstantiationOk = false;
}

// Test 6: Database configuration loading
echo "\n6. Database Configuration Loading Test:\n";
$dbConfigOk = true;

try {
    $dbConfig = config('database');
    if (is_array($dbConfig)) {
        echo "   ✅ Database configuration loaded\n";
        
        if (isset($dbConfig['default'])) {
            echo "   ✅ Default connection configured: " . $dbConfig['default'] . "\n";
        } else {
            echo "   ❌ Default connection not configured\n";
            $dbConfigOk = false;
        }
        
        if (isset($dbConfig['connections'])) {
            $connectionCount = count($dbConfig['connections']);
            echo "   ✅ {$connectionCount} database connection(s) configured\n";
        } else {
            echo "   ❌ Database connections not configured\n";
            $dbConfigOk = false;
        }
    } else {
        echo "   ❌ Database configuration not loaded properly\n";
        $dbConfigOk = false;
    }
} catch (Exception $e) {
    echo "   ❌ Failed to load database configuration: " . $e->getMessage() . "\n";
    $dbConfigOk = false;
}

echo "\n=== Overall Status ===\n";
if ($configOk && $classInstantiationOk && $dbConfigOk) {
    if ($connectionOk) {
        echo "✅ Database system is fully functional!\n";
        echo "ℹ️  Database connection is working and ready for use\n";
    } else {
        echo "⚠️  Database system is configured but connection failed\n";
        echo "ℹ️  This is normal if database server is not running or not set up\n";
        echo "🔧 To fix database connection:\n";
        echo "   1. Ensure MySQL/PostgreSQL server is running\n";
        echo "   2. Create the database: {$dbDatabase}\n";
        echo "   3. Verify database credentials in .env file\n";
        echo "   4. Check firewall and network connectivity\n";
    }
    exit(0);
} else {
    echo "❌ Database system has configuration issues.\n";
    echo "\n🔧 To fix database issues:\n";
    echo "   1. Configure database settings in .env file\n";
    echo "   2. Check database configuration in config/database.php\n";
    echo "   3. Verify Database class implementation\n";
    echo "   4. Install required PDO extensions\n";
    exit(1);
}
