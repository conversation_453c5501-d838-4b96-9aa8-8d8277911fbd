<?php

declare(strict_types=1);

/**
 * WeBot Helper Functions
 *
 * Global helper functions for WeBot application.
 *
 * @package WeBot\Utils
 * @version 2.0
 */

if (!function_exists('storage_path')) {
    /**
     * Get the path to the storage folder
     */
    function storage_path(string $path = ''): string
    {
        $storagePath = WEBOT_ROOT . '/storage';
        return $path ? $storagePath . '/' . ltrim($path, '/') : $storagePath;
    }
}

if (!function_exists('config_path')) {
    /**
     * Get the path to the config folder
     */
    function config_path(string $path = ''): string
    {
        $configPath = WEBOT_ROOT . '/config';
        return $path ? $configPath . '/' . ltrim($path, '/') : $configPath;
    }
}

if (!function_exists('public_path')) {
    /**
     * Get the path to the public folder
     */
    function public_path(string $path = ''): string
    {
        $publicPath = WEBOT_ROOT . '/public';
        return $path ? $publicPath . '/' . ltrim($path, '/') : $publicPath;
    }
}

if (!function_exists('base_path')) {
    /**
     * Get the path to the application root
     */
    function base_path(string $path = ''): string
    {
        return $path ? WEBOT_ROOT . '/' . ltrim($path, '/') : WEBOT_ROOT;
    }
}

if (!function_exists('env')) {
    /**
     * Get environment variable with default value
     */
    function env(string $key, mixed $default = null): mixed
    {
        $value = $_ENV[$key] ?? getenv($key);

        if ($value === false) {
            return $default;
        }

        // Convert string booleans
        if (is_string($value)) {
            switch (strtolower($value)) {
                case 'true':
                case '(true)':
                    return true;
                case 'false':
                case '(false)':
                    return false;
                case 'null':
                case '(null)':
                    return null;
                case 'empty':
                case '(empty)':
                    return '';
            }

            // Handle quoted strings
            if (strlen($value) > 1 && $value[0] === '"' && $value[-1] === '"') {
                return substr($value, 1, -1);
            }
        }

        return $value;
    }
}

if (!function_exists('config')) {
    /**
     * Get configuration value
     */
    function config(string $key = null, mixed $default = null): mixed
    {
        static $config = null;

        if ($config === null) {
            // Load app config
            $appConfigFile = config_path('app.php');
            if (file_exists($appConfigFile)) {
                $config = require $appConfigFile;
            } else {
                $config = [];
            }
        }

        if ($key === null) {
            return $config;
        }

        // Support dot notation
        $keys = explode('.', $key);
        $value = $config;

        foreach ($keys as $segment) {
            if (is_array($value) && array_key_exists($segment, $value)) {
                $value = $value[$segment];
            } else {
                return $default;
            }
        }

        return $value;
    }
}

if (!function_exists('app_path')) {
    /**
     * Get the path to the app folder
     */
    function app_path(string $path = ''): string
    {
        $appPath = WEBOT_ROOT . '/src';
        return $path ? $appPath . '/' . ltrim($path, '/') : $appPath;
    }
}

if (!function_exists('database_path')) {
    /**
     * Get the path to the database folder
     */
    function database_path(string $path = ''): string
    {
        $databasePath = WEBOT_ROOT . '/database';
        return $path ? $databasePath . '/' . ltrim($path, '/') : $databasePath;
    }
}

if (!function_exists('resource_path')) {
    /**
     * Get the path to the resources folder
     */
    function resource_path(string $path = ''): string
    {
        $resourcePath = WEBOT_ROOT . '/resources';
        return $path ? $resourcePath . '/' . ltrim($path, '/') : $resourcePath;
    }
}

if (!function_exists('now')) {
    /**
     * Get current timestamp
     */
    function now(): DateTime
    {
        return new DateTime();
    }
}

if (!function_exists('today')) {
    /**
     * Get today's date
     */
    function today(): DateTime
    {
        return new DateTime('today');
    }
}

if (!function_exists('logger')) {
    /**
     * Get logger instance
     */
    function logger(): WeBot\Utils\Logger
    {
        return WeBot\Utils\Logger::getInstance();
    }
}

if (!function_exists('dd')) {
    /**
     * Dump and die (for debugging)
     */
    function dd(...$vars): void
    {
        foreach ($vars as $var) {
            var_dump($var);
        }
        die(1);
    }
}

if (!function_exists('dump')) {
    /**
     * Dump variable (for debugging)
     */
    function dump(...$vars): void
    {
        foreach ($vars as $var) {
            var_dump($var);
        }
    }
}

if (!function_exists('abort')) {
    /**
     * Abort execution with error
     */
    function abort(int $code = 500, string $message = ''): void
    {
        http_response_code($code);
        if ($message) {
            echo $message;
        }
        exit($code);
    }
}

if (!function_exists('redirect')) {
    /**
     * Redirect to URL
     */
    function redirect(string $url, int $code = 302): void
    {
        header("Location: {$url}", true, $code);
        exit;
    }
}

if (!function_exists('old')) {
    /**
     * Get old input value
     */
    function old(string $key, mixed $default = null): mixed
    {
        return $_SESSION['_old_input'][$key] ?? $default;
    }
}

if (!function_exists('csrf_token')) {
    /**
     * Get CSRF token
     */
    function csrf_token(): string
    {
        if (!isset($_SESSION['_token'])) {
            $_SESSION['_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['_token'];
    }
}

if (!function_exists('asset')) {
    /**
     * Get asset URL
     */
    function asset(string $path): string
    {
        $baseUrl = env('APP_URL', 'http://localhost');
        return rtrim($baseUrl, '/') . '/' . ltrim($path, '/');
    }
}

if (!function_exists('url')) {
    /**
     * Generate URL
     */
    function url(string $path = ''): string
    {
        $baseUrl = env('APP_URL', 'http://localhost');
        return $path ? rtrim($baseUrl, '/') . '/' . ltrim($path, '/') : $baseUrl;
    }
}
