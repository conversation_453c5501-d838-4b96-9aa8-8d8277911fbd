<?php

declare(strict_types=1);

namespace WeBot\Middleware;

use WeBot\Core\InputValidator;
use WeBot\Exceptions\ValidationException;
use WeBot\Utils\Logger;

/**
 * Input Validation Middleware
 *
 * Validates and sanitizes all incoming requests before processing.
 *
 * @package WeBot\Middleware
 * @version 2.0
 */
class InputValidationMiddleware
{
    private InputValidator $validator;
    private Logger $logger;
    private array $config;

    public function __construct(InputValidator $validator, Logger $logger, array $config = [])
    {
        $this->validator = $validator;
        $this->logger = $logger;
        $this->config = array_merge([
            'strict_mode' => true,
            'log_violations' => true,
            'sanitize_html' => true,
            'max_input_length' => 10000,
            'allowed_tags' => [],
            'blocked_patterns' => [
                '/script/i',
                '/javascript:/i',
                '/vbscript:/i',
                '/onload/i',
                '/onerror/i',
                '/<iframe/i',
                '/<object/i',
                '/<embed/i'
            ]
        ], $config);
    }

    /**
     * Process incoming request
     */
    public function handle(array $request): array
    {
        try {
            // Basic security checks
            $this->performSecurityChecks($request);

            // Validate and sanitize input
            $sanitizedRequest = $this->validateAndSanitize($request);

            // Log if needed
            if ($this->config['log_violations']) {
                $this->logValidationAttempt($request, $sanitizedRequest);
            }

            return $sanitizedRequest;
        } catch (ValidationException $e) {
            $this->logger->warning('Input validation failed', [
                'error' => $e->getMessage(),
                'request' => $this->sanitizeForLogging($request)
            ]);

            throw $e;
        }
    }

    /**
     * Perform basic security checks
     */
    private function performSecurityChecks(array $request): void
    {
        // Check for blocked patterns
        $this->checkBlockedPatterns($request);

        // Check input length
        $this->checkInputLength($request);

        // Check for suspicious content
        $this->checkSuspiciousContent($request);
    }

    /**
     * Check for blocked patterns
     */
    private function checkBlockedPatterns(array $request): void
    {
        $content = json_encode($request);

        foreach ($this->config['blocked_patterns'] as $pattern) {
            if (preg_match($pattern, $content)) {
                throw new ValidationException('Blocked content pattern detected');
            }
        }
    }

    /**
     * Check input length
     */
    private function checkInputLength(array $request): void
    {
        $content = json_encode($request);

        if (strlen($content) > $this->config['max_input_length']) {
            throw new ValidationException('Input too large');
        }
    }

    /**
     * Check for suspicious content
     */
    private function checkSuspiciousContent(array $request): void
    {
        // Check for SQL injection patterns
        $sqlPatterns = [
            '/union\s+select/i',
            '/drop\s+table/i',
            '/delete\s+from/i',
            '/insert\s+into/i',
            '/update\s+set/i',
            '/exec\s*\(/i',
            '/script\s*:/i'
        ];

        $content = json_encode($request);

        foreach ($sqlPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                throw new ValidationException('Suspicious SQL pattern detected');
            }
        }
    }

    /**
     * Validate and sanitize request
     */
    private function validateAndSanitize(array $request): array
    {
        $sanitized = [];

        foreach ($request as $key => $value) {
            $sanitized[$key] = $this->sanitizeValue($value);
        }

        // Apply specific validation rules based on request type
        if (isset($request['message'])) {
            $sanitized = $this->validateTelegramMessage($sanitized);
        } elseif (isset($request['callback_query'])) {
            $sanitized = $this->validateCallbackQuery($sanitized);
        }

        return $sanitized;
    }

    /**
     * Sanitize individual value
     */
    private function sanitizeValue($value)
    {
        if (is_array($value)) {
            return array_map([$this, 'sanitizeValue'], $value);
        }

        if (!is_string($value)) {
            return $value;
        }

        // Remove null bytes
        $value = str_replace("\0", '', $value);

        // Sanitize HTML if enabled
        if ($this->config['sanitize_html']) {
            $value = $this->sanitizeHtml($value);
        }

        // Trim whitespace
        $value = trim($value);

        return $value;
    }

    /**
     * Sanitize HTML content
     */
    private function sanitizeHtml(string $value): string
    {
        // Remove dangerous tags
        $value = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $value);
        $value = preg_replace('/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/mi', '', $value);

        // Remove dangerous attributes
        $value = preg_replace('/\s*on\w+\s*=\s*["\'][^"\']*["\']/i', '', $value);
        $value = preg_replace('/javascript:/i', '', $value);

        // Allow only specific tags if configured
        if (!empty($this->config['allowed_tags'])) {
            $value = strip_tags($value, implode('', $this->config['allowed_tags']));
        }

        return $value;
    }

    /**
     * Validate Telegram message
     */
    private function validateTelegramMessage(array $request): array
    {
        $rules = [
            'message.message_id' => 'required|integer|min:1',
            'message.from.id' => 'required|integer|min:1',
            'message.chat.id' => 'required|integer',
            'message.date' => 'required|integer|min:1',
            'message.text' => 'string|max:4096',
            'message.from.first_name' => 'string|max:64',
            'message.from.username' => 'string|max:32|regex:/^[a-zA-Z0-9_]+$/',
        ];

        return $this->applyValidationRules($request, $rules);
    }

    /**
     * Validate callback query
     */
    private function validateCallbackQuery(array $request): array
    {
        $rules = [
            'callback_query.id' => 'required|string',
            'callback_query.from.id' => 'required|integer|min:1',
            'callback_query.data' => 'string|max:64',
            'callback_query.message.message_id' => 'integer|min:1',
        ];

        return $this->applyValidationRules($request, $rules);
    }

    /**
     * Apply validation rules
     */
    private function applyValidationRules(array $data, array $rules): array
    {
        $result = $this->validator->validate($data, $rules);

        if (!empty($result['errors'])) {
            throw new ValidationException('Validation failed: ' . implode(', ', $result['errors']));
        }

        return $result['data'];
    }

    /**
     * Log validation attempt
     */
    private function logValidationAttempt(array $original, array $sanitized): void
    {
        $changes = $this->detectChanges($original, $sanitized);

        if (!empty($changes)) {
            $this->logger->info('Input sanitized', [
                'changes' => $changes,
                'original_size' => strlen(json_encode($original)),
                'sanitized_size' => strlen(json_encode($sanitized))
            ]);
        }
    }

    /**
     * Detect changes between original and sanitized data
     */
    private function detectChanges(array $original, array $sanitized): array
    {
        $changes = [];

        // Simple comparison - in production, you might want more sophisticated diff
        if (json_encode($original) !== json_encode($sanitized)) {
            $changes[] = 'Data was sanitized';
        }

        return $changes;
    }

    /**
     * Sanitize data for logging (remove sensitive information)
     */
    private function sanitizeForLogging(array $data): array
    {
        $sanitized = $data;

        // Remove or mask sensitive fields
        $sensitiveFields = ['password', 'token', 'key', 'secret'];

        array_walk_recursive($sanitized, function (&$value, $key) use ($sensitiveFields) {
            if (in_array(strtolower($key), $sensitiveFields)) {
                $value = '***MASKED***';
            }
        });

        return $sanitized;
    }
}
