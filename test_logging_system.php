<?php
/**
 * Logging System Test for WeBot
 * 
 * This script tests the logging functionality and verifies
 * that logs can be written properly to different channels.
 */

declare(strict_types=1);

echo "=== WeBot Logging System Test ===\n\n";

// Load the application
try {
    require_once __DIR__ . '/autoload.php';
    echo "✅ Application loaded successfully\n\n";
} catch (Exception $e) {
    echo "❌ Failed to load application: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 1: Logger service availability
echo "1. Logger Service Availability Test:\n";
try {
    $logger = logger();
    echo "   ✅ Logger service is available\n";
    
    if (method_exists($logger, 'info') && method_exists($logger, 'error')) {
        echo "   ✅ Logger has required methods (info, error)\n";
    } else {
        echo "   ❌ Logger missing required methods\n";
        exit(1);
    }
} catch (Exception $e) {
    echo "   ❌ Logger service not available: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 2: Log directory permissions
echo "\n2. Log Directory Permissions Test:\n";
$logDirectories = ['storage/logs', 'logs'];
$logDirOk = true;

foreach ($logDirectories as $dir) {
    if (file_exists($dir)) {
        if (is_writable($dir)) {
            echo "   ✅ {$dir}: Writable\n";
        } else {
            echo "   ❌ {$dir}: Not writable\n";
            $logDirOk = false;
        }
    } else {
        echo "   ⚠️  {$dir}: Does not exist\n";
    }
}

// Test 3: Basic logging functionality
echo "\n3. Basic Logging Functionality Test:\n";
$basicLoggingOk = true;

try {
    $testMessage = "Test log message - " . date('Y-m-d H:i:s');
    $logger->info($testMessage);
    echo "   ✅ Info level logging works\n";
} catch (Exception $e) {
    echo "   ❌ Info level logging failed: " . $e->getMessage() . "\n";
    $basicLoggingOk = false;
}

try {
    $testMessage = "Test error message - " . date('Y-m-d H:i:s');
    $logger->error($testMessage);
    echo "   ✅ Error level logging works\n";
} catch (Exception $e) {
    echo "   ❌ Error level logging failed: " . $e->getMessage() . "\n";
    $basicLoggingOk = false;
}

try {
    $testMessage = "Test warning message - " . date('Y-m-d H:i:s');
    $logger->warning($testMessage);
    echo "   ✅ Warning level logging works\n";
} catch (Exception $e) {
    echo "   ❌ Warning level logging failed: " . $e->getMessage() . "\n";
    $basicLoggingOk = false;
}

try {
    $testMessage = "Test debug message - " . date('Y-m-d H:i:s');
    $logger->debug($testMessage);
    echo "   ✅ Debug level logging works\n";
} catch (Exception $e) {
    echo "   ❌ Debug level logging failed: " . $e->getMessage() . "\n";
    $basicLoggingOk = false;
}

// Test 4: Contextual logging
echo "\n4. Contextual Logging Test:\n";
$contextualOk = true;

try {
    $context = [
        'user_id' => 12345,
        'action' => 'test_action',
        'ip' => '127.0.0.1'
    ];
    $logger->info('Test contextual logging', $context);
    echo "   ✅ Contextual logging works\n";
} catch (Exception $e) {
    echo "   ❌ Contextual logging failed: " . $e->getMessage() . "\n";
    $contextualOk = false;
}

// Test 5: Log file creation
echo "\n5. Log File Creation Test:\n";
$fileCreationOk = true;

// Check if log files are created
$logFiles = glob('storage/logs/*.log');
if (!empty($logFiles)) {
    echo "   ✅ Log files are being created\n";
    
    // Check if we can read the latest log file
    $latestLogFile = end($logFiles);
    if (is_readable($latestLogFile)) {
        echo "   ✅ Log files are readable\n";
        
        // Check if our test messages are in the log
        $logContent = file_get_contents($latestLogFile);
        if (strpos($logContent, 'Test log message') !== false) {
            echo "   ✅ Test messages found in log file\n";
        } else {
            echo "   ⚠️  Test messages not found in log file (may be in different file)\n";
        }
    } else {
        echo "   ❌ Log files are not readable\n";
        $fileCreationOk = false;
    }
} else {
    echo "   ⚠️  No log files found in storage/logs/\n";
}

// Test 6: Logger configuration
echo "\n6. Logger Configuration Test:\n";
$configOk = true;

try {
    $loggingConfig = config('logging');
    if (is_array($loggingConfig)) {
        echo "   ✅ Logging configuration loaded\n";
        
        if (isset($loggingConfig['default'])) {
            echo "   ✅ Default log channel configured: " . $loggingConfig['default'] . "\n";
        } else {
            echo "   ❌ Default log channel not configured\n";
            $configOk = false;
        }
        
        if (isset($loggingConfig['channels'])) {
            $channelCount = count($loggingConfig['channels']);
            echo "   ✅ {$channelCount} log channels configured\n";
        } else {
            echo "   ❌ Log channels not configured\n";
            $configOk = false;
        }
    } else {
        echo "   ❌ Logging configuration not loaded properly\n";
        $configOk = false;
    }
} catch (Exception $e) {
    echo "   ❌ Failed to load logging configuration: " . $e->getMessage() . "\n";
    $configOk = false;
}

// Test 7: Error handling in logging
echo "\n7. Error Handling in Logging Test:\n";
$errorHandlingOk = true;

try {
    // Test logging with invalid context
    $logger->info('Test with null context', null);
    echo "   ✅ Handles null context gracefully\n";
} catch (Exception $e) {
    echo "   ❌ Failed to handle null context: " . $e->getMessage() . "\n";
    $errorHandlingOk = false;
}

echo "\n=== Overall Status ===\n";
if ($logDirOk && $basicLoggingOk && $contextualOk && $fileCreationOk && $configOk && $errorHandlingOk) {
    echo "✅ Logging system is working perfectly!\n";
    echo "ℹ️  All log levels work and files are being created\n";
    exit(0);
} else {
    echo "❌ Logging system has some issues.\n";
    echo "\n🔧 To fix logging issues:\n";
    echo "   1. Ensure log directories are writable\n";
    echo "   2. Check logging configuration in config/logging.php\n";
    echo "   3. Verify Monolog is properly installed\n";
    echo "   4. Check file permissions on storage/logs/\n";
    exit(1);
}
