<?php

declare(strict_types=1);

namespace WeBot\DTOs;

/**
 * Service Creation DTO
 *
 * Data transfer object for VPN service creation.
 * Handles validation and data transformation for service setup.
 *
 * @package WeBot\DTOs
 * @version 2.0
 */
class ServiceCreationDTO extends BaseDTO
{
    /**
     * Validate service creation data
     */
    protected function validate(): void
    {
        $this->validateRequired(['user_id', 'server_id', 'plan_id', 'volume', 'days']);
        $this->validateNumeric('user_id', 1);
        $this->validateNumeric('server_id', 1);
        $this->validateNumeric('plan_id', 1);
        $this->validateNumeric('volume', 1);
        $this->validateNumeric('days', 1, 365);
        $this->validateLength('remark', 0, 100);

        if ($this->has('status')) {
            $this->validateIn('status', ['active', 'expired', 'suspended', 'cancelled']);
        }
    }

    /**
     * Get validation rules
     */
    protected function rules(): array
    {
        return [
            'user_id' => 'required|integer|min:1',
            'server_id' => 'required|integer|min:1',
            'plan_id' => 'required|integer|min:1',
            'remark' => 'string|max:100',
            'volume' => 'required|integer|min:1',
            'days' => 'required|integer|min:1|max:365',
            'status' => 'in:active,expired,suspended,cancelled'
        ];
    }

    /**
     * Get field labels
     */
    protected function labels(): array
    {
        return [
            'user_id' => 'شناسه کاربر',
            'server_id' => 'شناسه سرور',
            'plan_id' => 'شناسه پلن',
            'remark' => 'توضیحات',
            'volume' => 'حجم',
            'days' => 'تعداد روز',
            'status' => 'وضعیت'
        ];
    }

    /**
     * Get user ID
     */
    public function getUserId(): int
    {
        return (int)$this->get('user_id');
    }

    /**
     * Get server ID
     */
    public function getServerId(): int
    {
        return (int)$this->get('server_id');
    }

    /**
     * Get plan ID
     */
    public function getPlanId(): int
    {
        return (int)$this->get('plan_id');
    }

    /**
     * Get remark
     */
    public function getRemark(): string
    {
        return $this->get('remark', '');
    }

    /**
     * Get volume in bytes
     */
    public function getVolume(): int
    {
        return (int)$this->get('volume');
    }

    /**
     * Get volume in GB
     */
    public function getVolumeGB(): float
    {
        return $this->getVolume() / (1024 * 1024 * 1024);
    }

    /**
     * Get days
     */
    public function getDays(): int
    {
        return (int)$this->get('days');
    }

    /**
     * Get status
     */
    public function getStatus(): string
    {
        return $this->get('status', 'active');
    }

    /**
     * Get expiration date
     */
    public function getExpirationDate(): string
    {
        $days = $this->getDays();
        return date('Y-m-d H:i:s', strtotime("+{$days} days"));
    }

    /**
     * Generate unique remark
     */
    public function generateRemark(): string
    {
        $remark = $this->getRemark();
        if (empty($remark)) {
            $userId = $this->getUserId();
            $timestamp = time();
            $remark = "user_{$userId}_{$timestamp}";
        }
        return $remark;
    }

    /**
     * Generate UUID for service
     */
    public function generateUUID(): string
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff)
        );
    }

    /**
     * Convert to database array
     */
    public function toDatabaseArray(): array
    {
        return [
            'user_id' => $this->getUserId(),
            'server_id' => $this->getServerId(),
            'plan_id' => $this->getPlanId(),
            'remark' => $this->generateRemark(),
            'uuid' => $this->generateUUID(),
            'volume' => $this->getVolume(),
            'used_volume' => 0,
            'days' => $this->getDays(),
            'status' => $this->getStatus(),
            'expires_at' => $this->getExpirationDate(),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Convert to panel user data
     */
    public function toPanelUserData(): array
    {
        return [
            'username' => $this->generateRemark(),
            'proxies' => [
                'vless' => [
                    'id' => $this->generateUUID()
                ]
            ],
            'data_limit' => $this->getVolume(),
            'expire' => strtotime($this->getExpirationDate()),
            'data_limit_reset_strategy' => 'no_reset',
            'status' => $this->getStatus() === 'active' ? 'active' : 'disabled'
        ];
    }

    /**
     * Create from plan and user data
     */
    public static function fromPlanAndUser(array $plan, int $userId, int $serverId): self
    {
        return new self([
            'user_id' => $userId,
            'server_id' => $serverId,
            'plan_id' => $plan['id'] ?? 0,
            'volume' => ($plan['volume_gb'] ?? 0) * 1024 * 1024 * 1024, // Convert GB to bytes
            'days' => $plan['days'] ?? 30,
            'status' => 'active'
        ]);
    }
}
