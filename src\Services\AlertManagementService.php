<?php

declare(strict_types=1);

namespace WeBot\Services;

use WeBot\Core\CacheManager;
use WeBot\Services\DatabaseService;
use WeBot\Utils\Logger;
use WeBot\Events\EventDispatcher;
use WeBot\Exceptions\WeBotException;

/**
 * Alert Management Service
 *
 * Manages system alerts, notifications, and escalation policies
 * for monitoring and operational awareness.
 *
 * @package WeBot\Services
 * @version 2.0
 */
class AlertManagementService
{
    private DatabaseService $database;
    private CacheManager $cache;
    private Logger $logger;
    private EventDispatcher $eventDispatcher;
    private array $config;
    private array $activeAlerts = [];
    private array $alertRules = [];

    public function __construct(
        DatabaseService $database,
        CacheManager $cache,
        EventDispatcher $eventDispatcher,
        array $config = []
    ) {
        $this->database = $database;
        $this->cache = $cache;
        $this->logger = Logger::getInstance();
        $this->eventDispatcher = $eventDispatcher;
        $this->config = array_merge($this->getDefaultConfig(), $config);

        $this->loadAlertRules();
        $this->loadActiveAlerts();
    }

    /**
     * Create new alert
     */
    public function createAlert(array $alertData): array
    {
        $alert = [
            'id' => uniqid('alert_', true),
            'type' => $alertData['type'],
            'severity' => $alertData['severity'] ?? 'medium',
            'title' => $alertData['title'],
            'message' => $alertData['message'],
            'source' => $alertData['source'] ?? 'system',
            'data' => $alertData['data'] ?? [],
            'tags' => $alertData['tags'] ?? [],
            'status' => 'active',
            'created_at' => time(),
            'updated_at' => time(),
            'acknowledged_at' => null,
            'acknowledged_by' => null,
            'resolved_at' => null,
            'resolved_by' => null,
            'escalation_level' => 0,
            'notification_count' => 0,
            'last_notification_at' => null
        ];

        // Check for duplicate alerts
        if ($this->isDuplicateAlert($alert)) {
            $this->logger->debug('Duplicate alert suppressed', ['alert' => $alert]);
            return $this->updateExistingAlert($alert);
        }

        // Store alert
        $this->storeAlert($alert);

        // Add to active alerts
        $this->activeAlerts[$alert['id']] = $alert;

        // Process alert rules
        $this->processAlertRules($alert);

        // Send notifications
        $this->sendAlertNotifications($alert);

        // Dispatch event
        $this->eventDispatcher->dispatchByName('alert.created', ['alert' => $alert]);

        $this->logger->info('Alert created', [
            'alert_id' => $alert['id'],
            'type' => $alert['type'],
            'severity' => $alert['severity']
        ]);

        return $alert;
    }

    /**
     * Acknowledge alert
     */
    public function acknowledgeAlert(string $alertId, string $acknowledgedBy, string $note = ''): bool
    {
        $alert = $this->getAlert($alertId);
        if (!$alert) {
            throw new WeBotException("Alert not found: {$alertId}");
        }

        if ($alert['status'] !== 'active') {
            throw new WeBotException("Alert is not active: {$alertId}");
        }

        $alert['status'] = 'acknowledged';
        $alert['acknowledged_at'] = time();
        $alert['acknowledged_by'] = $acknowledgedBy;
        $alert['updated_at'] = time();

        if ($note) {
            $alert['data']['acknowledgment_note'] = $note;
        }

        $this->updateAlert($alert);

        // Dispatch event
        $this->eventDispatcher->dispatchByName('alert.acknowledged', [
            'alert' => $alert,
            'acknowledged_by' => $acknowledgedBy,
            'note' => $note
        ]);

        $this->logger->info('Alert acknowledged', [
            'alert_id' => $alertId,
            'acknowledged_by' => $acknowledgedBy
        ]);

        return true;
    }

    /**
     * Resolve alert
     */
    public function resolveAlert(string $alertId, string $resolvedBy, string $resolution = ''): bool
    {
        $alert = $this->getAlert($alertId);
        if (!$alert) {
            throw new WeBotException("Alert not found: {$alertId}");
        }

        $alert['status'] = 'resolved';
        $alert['resolved_at'] = time();
        $alert['resolved_by'] = $resolvedBy;
        $alert['updated_at'] = time();

        if ($resolution) {
            $alert['data']['resolution'] = $resolution;
        }

        $this->updateAlert($alert);

        // Remove from active alerts
        unset($this->activeAlerts[$alertId]);

        // Dispatch event
        $this->eventDispatcher->dispatchByName('alert.resolved', [
            'alert' => $alert,
            'resolved_by' => $resolvedBy,
            'resolution' => $resolution
        ]);

        $this->logger->info('Alert resolved', [
            'alert_id' => $alertId,
            'resolved_by' => $resolvedBy
        ]);

        return true;
    }

    /**
     * Get active alerts
     */
    public function getActiveAlerts(array $filters = []): array
    {
        $alerts = $this->activeAlerts;

        // Apply filters
        if (!empty($filters)) {
            $alerts = array_filter($alerts, function ($alert) use ($filters) {
                return $this->matchesFilters($alert, $filters);
            });
        }

        // Sort by severity and creation time
        uasort($alerts, function ($a, $b) {
            $severityOrder = ['critical' => 4, 'high' => 3, 'medium' => 2, 'low' => 1];

            $aSeverity = $severityOrder[$a['severity']] ?? 0;
            $bSeverity = $severityOrder[$b['severity']] ?? 0;

            if ($aSeverity !== $bSeverity) {
                return $bSeverity <=> $aSeverity; // Higher severity first
            }

            return $b['created_at'] <=> $a['created_at']; // Newer first
        });

        return array_values($alerts);
    }

    /**
     * Get alert statistics
     */
    public function getAlertStatistics(int $days = 7): array
    {
        $endTime = time();
        $startTime = $endTime - ($days * 86400);

        $alerts = $this->getAlertsInPeriod($startTime, $endTime);

        $stats = [
            'period' => ['start' => $startTime, 'end' => $endTime, 'days' => $days],
            'total_alerts' => count($alerts),
            'by_severity' => [],
            'by_type' => [],
            'by_status' => [],
            'by_day' => [],
            'avg_resolution_time' => 0,
            'escalation_rate' => 0,
            'top_alert_sources' => []
        ];

        $resolutionTimes = [];
        $escalatedCount = 0;
        $sourceCounts = [];

        foreach ($alerts as $alert) {
            // By severity
            $severity = $alert['severity'];
            $stats['by_severity'][$severity] = ($stats['by_severity'][$severity] ?? 0) + 1;

            // By type
            $type = $alert['type'];
            $stats['by_type'][$type] = ($stats['by_type'][$type] ?? 0) + 1;

            // By status
            $status = $alert['status'];
            $stats['by_status'][$status] = ($stats['by_status'][$status] ?? 0) + 1;

            // By day
            $day = date('Y-m-d', $alert['created_at']);
            $stats['by_day'][$day] = ($stats['by_day'][$day] ?? 0) + 1;

            // Resolution time
            if ($alert['resolved_at']) {
                $resolutionTimes[] = $alert['resolved_at'] - $alert['created_at'];
            }

            // Escalation
            if ($alert['escalation_level'] > 0) {
                $escalatedCount++;
            }

            // Sources
            $source = $alert['source'];
            $sourceCounts[$source] = ($sourceCounts[$source] ?? 0) + 1;
        }

        // Calculate averages
        if (!empty($resolutionTimes)) {
            $stats['avg_resolution_time'] = array_sum($resolutionTimes) / count($resolutionTimes);
        }

        if (count($alerts) > 0) {
            $stats['escalation_rate'] = ($escalatedCount / count($alerts)) * 100;
        }

        // Top sources
        arsort($sourceCounts);
        $stats['top_alert_sources'] = array_slice($sourceCounts, 0, 5, true);

        return $stats;
    }

    /**
     * Process alert escalation
     */
    public function processEscalation(): void
    {
        foreach ($this->activeAlerts as $alert) {
            if ($this->shouldEscalateAlert($alert)) {
                $this->escalateAlert($alert);
            }
        }
    }

    /**
     * Add alert rule
     */
    public function addAlertRule(array $rule): void
    {
        $rule['id'] = $rule['id'] ?? uniqid('rule_', true);
        $rule['created_at'] = time();
        $rule['enabled'] = $rule['enabled'] ?? true;

        $this->alertRules[$rule['id']] = $rule;
        $this->storeAlertRule($rule);

        $this->logger->info('Alert rule added', ['rule_id' => $rule['id']]);
    }

    /**
     * Get alert dashboard data
     */
    public function getDashboardData(): array
    {
        $activeAlerts = $this->getActiveAlerts();
        $stats = $this->getAlertStatistics(7);

        return [
            'summary' => [
                'total_active' => count($activeAlerts),
                'critical_count' => count(array_filter($activeAlerts, fn($a) => $a['severity'] === 'critical')),
                'unacknowledged_count' => count(array_filter($activeAlerts, fn($a) => $a['status'] === 'active')),
                'avg_resolution_time' => $stats['avg_resolution_time'],
                'escalation_rate' => $stats['escalation_rate']
            ],
            'active_alerts' => array_slice($activeAlerts, 0, 10),
            'recent_statistics' => $stats,
            'alert_trends' => $this->getAlertTrends(30),
            'top_alert_types' => array_slice($stats['by_type'], 0, 5, true)
        ];
    }

    /**
     * Check if alert is duplicate
     */
    private function isDuplicateAlert(array $alert): bool
    {
        $threshold = time() - $this->config['duplicate_threshold'];

        foreach ($this->activeAlerts as $existingAlert) {
            if (
                $existingAlert['type'] === $alert['type'] &&
                $existingAlert['source'] === $alert['source'] &&
                $existingAlert['created_at'] > $threshold
            ) {
                // Check if data is similar
                if ($this->isSimilarAlertData($existingAlert['data'], $alert['data'])) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Get alerts in period
     */
    private function getAlertsInPeriod(int $startTime, int $endTime): array
    {
        try {
            $startDate = date('Y-m-d H:i:s', $startTime);
            $endDate = date('Y-m-d H:i:s', $endTime);

            $alerts = $this->database->fetchAll(
                "SELECT * FROM alerts WHERE created_at BETWEEN ? AND ? ORDER BY created_at DESC",
                [$startDate, $endDate]
            );

            // Process alerts
            foreach ($alerts as &$alert) {
                $alert['data'] = json_decode($alert['data'], true) ?? [];
                $alert['tags'] = json_decode($alert['tags'], true) ?? [];
                $alert['created_at'] = strtotime($alert['created_at']);
                $alert['updated_at'] = strtotime($alert['updated_at']);
            }

            return $alerts;
        } catch (\Exception $e) {
            $this->logger->error('Failed to get alerts in period', [
                'start_time' => $startTime,
                'end_time' => $endTime,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get alert trends
     */
    private function getAlertTrends(int $days): array
    {
        try {
            $endTime = time();
            $startTime = $endTime - ($days * 86400);

            // Get daily alert counts
            $result = $this->database->fetchAll(
                "SELECT
                    DATE(created_at) as date,
                    COUNT(*) as total_alerts,
                    COUNT(CASE WHEN severity = 'critical' THEN 1 END) as critical_alerts,
                    COUNT(CASE WHEN severity = 'high' THEN 1 END) as high_alerts,
                    COUNT(CASE WHEN status = 'resolved' THEN 1 END) as resolved_alerts
                 FROM alerts
                 WHERE created_at BETWEEN ? AND ?
                 GROUP BY DATE(created_at)
                 ORDER BY date",
                [date('Y-m-d H:i:s', $startTime), date('Y-m-d H:i:s', $endTime)]
            );

            return $result;
        } catch (\Exception $e) {
            $this->logger->error('Failed to get alert trends', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * Store alert in database and cache
     */
    private function storeAlert(array $alert): void
    {
        // Store in cache for quick access
        $cacheKey = "alert:{$alert['id']}";
        $this->cache->set($cacheKey, $alert, $this->config['cache_ttl']);

        // Store in database for persistence
        $this->database->insert('alerts', [
            'id' => $alert['id'],
            'type' => $alert['type'],
            'severity' => $alert['severity'],
            'title' => $alert['title'],
            'message' => $alert['message'],
            'source' => $alert['source'],
            'data' => json_encode($alert['data']),
            'tags' => json_encode($alert['tags']),
            'status' => $alert['status'],
            'created_at' => date('Y-m-d H:i:s', $alert['created_at']),
            'updated_at' => date('Y-m-d H:i:s', $alert['updated_at'])
        ]);
    }

    /**
     * Load alert rules from database
     */
    private function loadAlertRules(): void
    {
        try {
            $rules = $this->database->query("SELECT * FROM alert_rules WHERE enabled = 1");
            foreach ($rules as $rule) {
                $this->alertRules[$rule['id']] = $rule;
            }
        } catch (\Exception $e) {
            $this->logger->error('Failed to load alert rules', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Load active alerts from cache and database
     */
    private function loadActiveAlerts(): void
    {
        try {
            $alerts = $this->database->query("SELECT * FROM alerts WHERE status IN ('active', 'acknowledged')");
            foreach ($alerts as $alert) {
                $alert['data'] = json_decode($alert['data'], true) ?? [];
                $alert['tags'] = json_decode($alert['tags'], true) ?? [];
                $alert['created_at'] = strtotime($alert['created_at']);
                $alert['updated_at'] = strtotime($alert['updated_at']);
                $this->activeAlerts[$alert['id']] = $alert;
            }
        } catch (\Exception $e) {
            $this->logger->error('Failed to load active alerts', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Update existing alert
     */
    private function updateExistingAlert(array $alert): array
    {
        // Find existing similar alert and update its count
        foreach ($this->activeAlerts as $existingAlert) {
            if (
                $existingAlert['type'] === $alert['type'] &&
                $existingAlert['source'] === $alert['source']
            ) {
                $existingAlert['notification_count']++;
                $existingAlert['updated_at'] = time();
                $this->updateAlert($existingAlert);

                return $existingAlert;
            }
        }

        return $alert;
    }

    /**
     * Update alert in database and cache
     */
    private function updateAlert(array $alert): void
    {
        // Update in database
        $this->database->update('alerts', [
            'status' => $alert['status'],
            'acknowledged_at' => $alert['acknowledged_at'] ? date('Y-m-d H:i:s', $alert['acknowledged_at']) : null,
            'acknowledged_by' => $alert['acknowledged_by'],
            'resolved_at' => $alert['resolved_at'] ? date('Y-m-d H:i:s', $alert['resolved_at']) : null,
            'resolved_by' => $alert['resolved_by'],
            'escalation_level' => $alert['escalation_level'],
            'notification_count' => $alert['notification_count'],
            'updated_at' => date('Y-m-d H:i:s', $alert['updated_at']),
            'data' => json_encode($alert['data'])
        ], ['id' => $alert['id']]);

        // Update in cache
        $cacheKey = "alert:{$alert['id']}";
        $this->cache->set($cacheKey, $alert, $this->config['cache_ttl']);

        // Update active alerts
        $this->activeAlerts[$alert['id']] = $alert;
    }

    /**
     * Get alert by ID
     */
    private function getAlert(string $alertId): ?array
    {
        // Try cache first
        $cacheKey = "alert:{$alertId}";
        $alert = $this->cache->get($cacheKey);

        if ($alert) {
            return $alert;
        }

        // Try active alerts
        if (isset($this->activeAlerts[$alertId])) {
            return $this->activeAlerts[$alertId];
        }

        // Try database
        try {
            $result = $this->database->fetchRow("SELECT * FROM alerts WHERE id = ?", [$alertId]);
            if ($result) {
                $result['data'] = json_decode($result['data'], true) ?? [];
                $result['tags'] = json_decode($result['tags'], true) ?? [];
                $result['created_at'] = strtotime($result['created_at']);
                $result['updated_at'] = strtotime($result['updated_at']);
                return $result;
            }
        } catch (\Exception $e) {
            $this->logger->error('Failed to get alert', ['alert_id' => $alertId, 'error' => $e->getMessage()]);
        }

        return null;
    }

    /**
     * Process alert rules
     */
    private function processAlertRules(array $alert): void
    {
        foreach ($this->alertRules as $rule) {
            if ($this->alertMatchesRule($alert, $rule)) {
                $this->applyAlertRule($alert, $rule);
            }
        }
    }

    /**
     * Check if alert matches rule
     */
    private function alertMatchesRule(array $alert, array $rule): bool
    {
        // Simple rule matching - can be extended
        if (isset($rule['alert_type']) && $rule['alert_type'] !== $alert['type']) {
            return false;
        }

        if (isset($rule['severity']) && $rule['severity'] !== $alert['severity']) {
            return false;
        }

        if (isset($rule['source']) && $rule['source'] !== $alert['source']) {
            return false;
        }

        return true;
    }

    /**
     * Apply alert rule
     */
    private function applyAlertRule(array &$alert, array $rule): void
    {
        // Apply rule actions
        if (isset($rule['auto_escalate']) && $rule['auto_escalate']) {
            $alert['escalation_level']++;
        }

        if (isset($rule['notification_channels'])) {
            $alert['data']['notification_channels'] = $rule['notification_channels'];
        }

        if (isset($rule['assignee'])) {
            $alert['data']['assignee'] = $rule['assignee'];
        }
    }

    /**
     * Send alert notifications
     */
    private function sendAlertNotifications(array $alert): void
    {
        $channels = $alert['data']['notification_channels'] ?? $this->config['notification_channels'];

        foreach ($channels as $channel) {
            try {
                $this->sendNotificationToChannel($alert, $channel);
            } catch (\Exception $e) {
                $this->logger->error('Failed to send alert notification', [
                    'alert_id' => $alert['id'],
                    'channel' => $channel,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Update notification count
        $alert['notification_count']++;
        $alert['last_notification_at'] = time();
    }

    /**
     * Send notification to specific channel
     */
    private function sendNotificationToChannel(array $alert, string $channel): void
    {
        // This would integrate with actual notification services
        $this->logger->info("Alert notification sent", [
            'alert_id' => $alert['id'],
            'channel' => $channel,
            'severity' => $alert['severity']
        ]);
    }

    /**
     * Check if alert matches filters
     */
    private function matchesFilters(array $alert, array $filters): bool
    {
        foreach ($filters as $key => $value) {
            if (!isset($alert[$key]) || $alert[$key] !== $value) {
                return false;
            }
        }
        return true;
    }

    /**
     * Check if alert should be escalated
     */
    private function shouldEscalateAlert(array $alert): bool
    {
        if (!$this->config['escalation_enabled'] || $alert['status'] !== 'active') {
            return false;
        }

        $intervals = $this->config['escalation_intervals'];
        $currentLevel = $alert['escalation_level'];

        if ($currentLevel >= count($intervals)) {
            return false; // Max escalation reached
        }

        $escalationTime = $alert['created_at'] + $intervals[$currentLevel];
        return time() >= $escalationTime;
    }

    /**
     * Escalate alert
     */
    private function escalateAlert(array &$alert): void
    {
        $alert['escalation_level']++;
        $alert['updated_at'] = time();

        $this->updateAlert($alert);
        $this->sendAlertNotifications($alert);

        $this->logger->warning('Alert escalated', [
            'alert_id' => $alert['id'],
            'escalation_level' => $alert['escalation_level']
        ]);
    }

    /**
     * Store alert rule
     */
    private function storeAlertRule(array $rule): void
    {
        $this->database->insert('alert_rules', [
            'id' => $rule['id'],
            'name' => $rule['name'] ?? '',
            'conditions' => json_encode($rule['conditions'] ?? []),
            'actions' => json_encode($rule['actions'] ?? []),
            'enabled' => $rule['enabled'] ? 1 : 0,
            'created_at' => date('Y-m-d H:i:s', $rule['created_at'])
        ]);
    }

    /**
     * Check if alert data is similar
     */
    private function isSimilarAlertData(array $data1, array $data2): bool
    {
        // Simple similarity check - can be enhanced
        $similarity = 0;
        $totalKeys = count(array_unique(array_merge(array_keys($data1), array_keys($data2))));

        if ($totalKeys === 0) {
            return true;
        }

        foreach ($data1 as $key => $value) {
            if (isset($data2[$key]) && $data2[$key] === $value) {
                $similarity++;
            }
        }

        return ($similarity / $totalKeys) > 0.7; // 70% similarity threshold
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'cache_ttl' => 3600,
            'duplicate_threshold' => 300, // 5 minutes
            'escalation_enabled' => true,
            'escalation_intervals' => [1800, 3600, 7200], // 30min, 1h, 2h
            'notification_channels' => ['email', 'slack'],
            'max_notifications_per_alert' => 5,
            'auto_resolve_timeout' => 86400 // 24 hours
        ];
    }
}
