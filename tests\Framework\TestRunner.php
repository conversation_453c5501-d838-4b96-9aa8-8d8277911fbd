<?php

declare(strict_types=1);

namespace WeBot\Tests\Framework;

/**
 * Test Runner
 * 
 * Discovers and runs all tests in the WeBot test suite
 * with proper reporting and coverage analysis.
 * 
 * @package WeBot\Tests\Framework
 * @version 2.0
 */
class TestRunner
{
    private array $testSuites = [];
    private array $results = [];
    private float $startTime;
    private float $endTime;
    private bool $verbose = false;
    
    public function __construct(bool $verbose = false)
    {
        $this->verbose = $verbose;
        $this->startTime = microtime(true);
    }
    
    /**
     * Discover and register test suites
     */
    public function discoverTests(string $testDirectory = null): void
    {
        $testDirectory = $testDirectory ?: __DIR__ . '/../';
        
        $this->log("🔍 Discovering tests in: {$testDirectory}");
        
        // Discover unit tests
        $this->discoverTestsInDirectory($testDirectory . 'Unit', 'Unit');
        
        // Discover integration tests
        $this->discoverTestsInDirectory($testDirectory . 'Integration', 'Integration');
        
        // Discover feature tests
        $this->discoverTestsInDirectory($testDirectory . 'Feature', 'Feature');
        
        // Discover E2E tests
        $this->discoverTestsInDirectory($testDirectory . 'E2E', 'E2E');
        
        $this->log("📋 Found " . count($this->testSuites) . " test suites");
    }
    
    /**
     * Discover tests in specific directory
     */
    private function discoverTestsInDirectory(string $directory, string $type): void
    {
        if (!is_dir($directory)) {
            return;
        }
        
        $files = glob($directory . '/*Test.php');
        
        foreach ($files as $file) {
            $className = $this->getClassNameFromFile($file);
            
            if ($className && class_exists($className)) {
                $this->testSuites[] = [
                    'type' => $type,
                    'class' => $className,
                    'file' => $file
                ];
            }
        }
    }
    
    /**
     * Extract class name from file
     */
    private function getClassNameFromFile(string $file): ?string
    {
        $content = file_get_contents($file);
        
        // Extract namespace
        if (preg_match('/namespace\s+([^;]+);/', $content, $namespaceMatches)) {
            $namespace = $namespaceMatches[1];
        } else {
            return null;
        }
        
        // Extract class name
        if (preg_match('/class\s+(\w+)/', $content, $classMatches)) {
            $className = $classMatches[1];
            return $namespace . '\\' . $className;
        }
        
        return null;
    }
    
    /**
     * Run all discovered tests
     */
    public function runAllTests(): array
    {
        $this->log("🚀 Starting test execution...\n");
        
        TestCase::resetStats();
        
        foreach ($this->testSuites as $suite) {
            $this->runTestSuite($suite);
        }
        
        $this->endTime = microtime(true);
        $this->generateReport();
        
        return $this->results;
    }
    
    /**
     * Run specific test suite
     */
    private function runTestSuite(array $suite): void
    {
        $this->log("📦 Running {$suite['type']} tests: {$suite['class']}");
        
        try {
            $testInstance = new $suite['class']();
            $methods = $this->getTestMethods($testInstance);
            
            $suiteResults = [
                'suite' => $suite['class'],
                'type' => $suite['type'],
                'tests' => [],
                'passed' => 0,
                'failed' => 0,
                'errors' => []
            ];
            
            foreach ($methods as $method) {
                $testResult = $this->runTestMethod($testInstance, $method);
                $suiteResults['tests'][] = $testResult;
                
                if ($testResult['status'] === 'passed') {
                    $suiteResults['passed']++;
                } else {
                    $suiteResults['failed']++;
                    if (isset($testResult['error'])) {
                        $suiteResults['errors'][] = $testResult['error'];
                    }
                }
            }
            
            $this->results[] = $suiteResults;
            
        } catch (\Throwable $e) {
            $this->log("❌ Error running test suite: " . $e->getMessage());
            
            $this->results[] = [
                'suite' => $suite['class'],
                'type' => $suite['type'],
                'tests' => [],
                'passed' => 0,
                'failed' => 1,
                'errors' => [$e->getMessage()]
            ];
        }
    }
    
    /**
     * Get test methods from test instance
     */
    private function getTestMethods(object $testInstance): array
    {
        $reflection = new \ReflectionClass($testInstance);
        $methods = $reflection->getMethods(\ReflectionMethod::IS_PUBLIC);
        
        $testMethods = [];
        
        foreach ($methods as $method) {
            if (strpos($method->getName(), 'test') === 0) {
                $testMethods[] = $method->getName();
            }
        }
        
        return $testMethods;
    }
    
    /**
     * Run individual test method
     */
    private function runTestMethod(object $testInstance, string $methodName): array
    {
        $this->log("  🧪 {$methodName}");
        
        $startTime = microtime(true);
        
        try {
            // Setup
            if (method_exists($testInstance, 'setUp')) {
                $testInstance->setUp();
            }
            
            // Run test
            $testInstance->$methodName();
            
            // Teardown
            if (method_exists($testInstance, 'tearDown')) {
                $testInstance->tearDown();
            }
            
            $endTime = microtime(true);
            
            return [
                'method' => $methodName,
                'status' => 'passed',
                'duration' => $endTime - $startTime
            ];
            
        } catch (\Throwable $e) {
            $endTime = microtime(true);
            
            $this->log("    ❌ Failed: " . $e->getMessage());
            
            return [
                'method' => $methodName,
                'status' => 'failed',
                'duration' => $endTime - $startTime,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ];
        }
    }
    
    /**
     * Generate test report
     */
    private function generateReport(): void
    {
        $stats = TestCase::getTestStats();
        $duration = $this->endTime - $this->startTime;
        
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "📊 TEST RESULTS SUMMARY\n";
        echo str_repeat("=", 60) . "\n";
        
        echo "⏱️  Duration: " . number_format($duration, 2) . " seconds\n";
        echo "📦 Test Suites: " . count($this->testSuites) . "\n";
        echo "🧪 Total Tests: " . $stats['total'] . "\n";
        echo "✅ Passed: " . $stats['passed'] . "\n";
        echo "❌ Failed: " . $stats['failed'] . "\n";
        echo "📈 Success Rate: " . number_format($stats['success_rate'], 1) . "%\n";
        
        if (!empty($stats['failures'])) {
            echo "\n❌ FAILURES:\n";
            foreach ($stats['failures'] as $failure) {
                echo "  • {$failure}\n";
            }
        }
        
        // Suite breakdown
        echo "\n📋 SUITE BREAKDOWN:\n";
        foreach ($this->results as $result) {
            $total = $result['passed'] + $result['failed'];
            $rate = $total > 0 ? ($result['passed'] / $total) * 100 : 0;
            
            echo sprintf(
                "  %s %s: %d/%d (%.1f%%)\n",
                $rate === 100 ? "✅" : "❌",
                $result['type'],
                $result['passed'],
                $total,
                $rate
            );
        }
        
        echo "\n" . str_repeat("=", 60) . "\n";
        
        // Exit with appropriate code
        if ($stats['failed'] > 0) {
            echo "❌ Tests failed!\n";
            exit(1);
        } else {
            echo "✅ All tests passed!\n";
            exit(0);
        }
    }
    
    /**
     * Log message if verbose mode is enabled
     */
    private function log(string $message): void
    {
        if ($this->verbose) {
            echo $message . "\n";
        }
    }
    
    /**
     * Run specific test file
     */
    public function runTestFile(string $filePath): array
    {
        $className = $this->getClassNameFromFile($filePath);
        
        if (!$className || !class_exists($className)) {
            throw new \InvalidArgumentException("Cannot find test class in file: {$filePath}");
        }
        
        $suite = [
            'type' => 'Manual',
            'class' => $className,
            'file' => $filePath
        ];
        
        TestCase::resetStats();
        $this->runTestSuite($suite);
        $this->endTime = microtime(true);
        $this->generateReport();
        
        return $this->results;
    }
    
    /**
     * Run tests by type
     */
    public function runTestsByType(string $type): array
    {
        $this->log("🎯 Running {$type} tests only...\n");
        
        TestCase::resetStats();
        
        foreach ($this->testSuites as $suite) {
            if ($suite['type'] === $type) {
                $this->runTestSuite($suite);
            }
        }
        
        $this->endTime = microtime(true);
        $this->generateReport();
        
        return $this->results;
    }
}
