<?php
/**
 * Error Handling System Test for WeBot
 * 
 * This script tests the error handling and exception management
 * functionality of the WeBot application.
 */

declare(strict_types=1);

echo "=== WeBot Error Handling Test ===\n\n";

// Load the application
try {
    require_once __DIR__ . '/autoload.php';
    echo "✅ Application loaded successfully\n\n";
} catch (Exception $e) {
    echo "❌ Failed to load application: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 1: Exception classes availability
echo "1. Exception Classes Availability Test:\n";
$exceptionClasses = [
    'WeBot\Exceptions\WeBotException' => 'Base WeBot exception',
    'WeBot\Exceptions\DatabaseException' => 'Database exception',
    'WeBot\Exceptions\ValidationException' => 'Validation exception',
    'WeBot\Exceptions\AuthenticationException' => 'Authentication exception',
    'WeBot\Exceptions\PanelException' => 'Panel exception',
    'WeBot\Exceptions\PaymentException' => 'Payment exception'
];

$exceptionsOk = true;
foreach ($exceptionClasses as $className => $description) {
    if (class_exists($className)) {
        echo "   ✅ {$className} - {$description}\n";
    } else {
        echo "   ❌ {$className} - {$description}\n";
        $exceptionsOk = false;
    }
}

// Test 2: Error handlers registration
echo "\n2. Error Handlers Registration Test:\n";
$handlersOk = true;

// Check if error handler is set
$errorHandler = set_error_handler(null);
if ($errorHandler !== null) {
    echo "   ✅ Custom error handler is registered\n";
    set_error_handler($errorHandler); // Restore the handler
} else {
    echo "   ❌ No custom error handler registered\n";
    $handlersOk = false;
}

// Check if exception handler is set
$exceptionHandler = set_exception_handler(null);
if ($exceptionHandler !== null) {
    echo "   ✅ Custom exception handler is registered\n";
    set_exception_handler($exceptionHandler); // Restore the handler
} else {
    echo "   ❌ No custom exception handler registered\n";
    $handlersOk = false;
}

// Test 3: WeBot exception instantiation
echo "\n3. WeBot Exception Instantiation Test:\n";
$instantiationOk = true;

try {
    $exception = new WeBot\Exceptions\WeBotException('Test exception message', 1001);
    echo "   ✅ WeBotException can be instantiated\n";
    
    if ($exception->getMessage() === 'Test exception message') {
        echo "   ✅ Exception message is correct\n";
    } else {
        echo "   ❌ Exception message is incorrect\n";
        $instantiationOk = false;
    }
    
    if ($exception->getCode() === 1001) {
        echo "   ✅ Exception code is correct\n";
    } else {
        echo "   ❌ Exception code is incorrect\n";
        $instantiationOk = false;
    }
} catch (Exception $e) {
    echo "   ❌ Failed to instantiate WeBotException: " . $e->getMessage() . "\n";
    $instantiationOk = false;
}

// Test 4: Exception inheritance
echo "\n4. Exception Inheritance Test:\n";
$inheritanceOk = true;

try {
    $dbException = new WeBot\Exceptions\DatabaseException('Database error');
    if ($dbException instanceof WeBot\Exceptions\WeBotException) {
        echo "   ✅ DatabaseException extends WeBotException\n";
    } else {
        echo "   ❌ DatabaseException does not extend WeBotException\n";
        $inheritanceOk = false;
    }
    
    if ($dbException instanceof Exception) {
        echo "   ✅ DatabaseException extends base Exception\n";
    } else {
        echo "   ❌ DatabaseException does not extend base Exception\n";
        $inheritanceOk = false;
    }
} catch (Exception $e) {
    echo "   ❌ Failed to test exception inheritance: " . $e->getMessage() . "\n";
    $inheritanceOk = false;
}

// Test 5: Error logging integration
echo "\n5. Error Logging Integration Test:\n";
$loggingIntegrationOk = true;

try {
    // Trigger a controlled error to test logging
    $originalErrorReporting = error_reporting();
    error_reporting(E_ALL);
    
    // Create a test scenario that should be logged
    $testException = new WeBot\Exceptions\ValidationException('Test validation error for logging');
    
    // Log the exception manually to test integration
    logger()->error('Test exception logging', [
        'exception' => get_class($testException),
        'message' => $testException->getMessage(),
        'code' => $testException->getCode()
    ]);
    
    echo "   ✅ Exception logging integration works\n";
    
    error_reporting($originalErrorReporting);
} catch (Exception $e) {
    echo "   ❌ Exception logging integration failed: " . $e->getMessage() . "\n";
    $loggingIntegrationOk = false;
}

// Test 6: Error recovery mechanisms
echo "\n6. Error Recovery Mechanisms Test:\n";
$recoveryOk = true;

try {
    // Test graceful handling of missing services
    try {
        $nonExistentService = service('non_existent_service');
        echo "   ❌ Should have thrown exception for non-existent service\n";
        $recoveryOk = false;
    } catch (Exception $e) {
        echo "   ✅ Properly handles non-existent service requests\n";
    }
} catch (Exception $e) {
    echo "   ❌ Error recovery test failed: " . $e->getMessage() . "\n";
    $recoveryOk = false;
}

// Test 7: Debug mode handling
echo "\n7. Debug Mode Handling Test:\n";
$debugModeOk = true;

$appDebug = env('APP_DEBUG', 'false');
if ($appDebug === 'false') {
    echo "   ✅ Debug mode is disabled (production setting)\n";
} else {
    echo "   ⚠️  Debug mode is enabled (development setting)\n";
}

// Test 8: Error response formatting
echo "\n8. Error Response Formatting Test:\n";
$responseFormattingOk = true;

try {
    // Test that we can format error responses properly
    $testError = [
        'error' => 'Test error',
        'message' => 'This is a test error message',
        'code' => 500
    ];
    
    $jsonResponse = json_encode($testError);
    if ($jsonResponse !== false) {
        echo "   ✅ Error responses can be formatted as JSON\n";
    } else {
        echo "   ❌ Failed to format error response as JSON\n";
        $responseFormattingOk = false;
    }
} catch (Exception $e) {
    echo "   ❌ Error response formatting test failed: " . $e->getMessage() . "\n";
    $responseFormattingOk = false;
}

echo "\n=== Overall Status ===\n";
if ($exceptionsOk && $handlersOk && $instantiationOk && $inheritanceOk && $loggingIntegrationOk && $recoveryOk && $responseFormattingOk) {
    echo "✅ Error handling system is working perfectly!\n";
    echo "ℹ️  All exception classes are available and handlers are registered\n";
    exit(0);
} else {
    echo "❌ Error handling system has some issues.\n";
    echo "\n🔧 To fix error handling issues:\n";
    echo "   1. Ensure all exception classes are properly defined\n";
    echo "   2. Check error and exception handler registration in autoload.php\n";
    echo "   3. Verify exception inheritance hierarchy\n";
    echo "   4. Test error logging integration\n";
    exit(1);
}
