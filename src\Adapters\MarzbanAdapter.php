<?php

declare(strict_types=1);

namespace WeBot\Adapters;

use WeBot\Contracts\PanelAdapterInterface;
use WeBot\Exceptions\PanelException;
use WeBot\Utils\HttpClientWrapper;

/**
 * Marzban Panel Adapter
 *
 * Handles all interactions with Marzban panel API
 * including user management, configuration generation,
 * and statistics retrieval.
 *
 * @package WeBot\Adapters
 * @version 2.0
 */
class MarzbanAdapter implements PanelAdapterInterface
{
    private $httpClient;
    private array $config;
    private ?string $accessToken = null;
    private ?int $tokenExpiry = null;

    public function __construct(array $config)
    {
        $this->config = $config;

        // Use HttpClientWrapper for compatibility
        $this->httpClient = new HttpClientWrapper([
            'base_uri' => rtrim($config['url'], '/') . '/api/v1/',
            'timeout' => $config['timeout'] ?? 30,
            'verify' => $config['verify_ssl'] ?? true,
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            ]
        ]);
    }

    /**
     * Authenticate with Marzban panel
     */
    public function authenticate(): array
    {
        try {
            $response = $this->httpClient->post('admin/token', [
                'form_params' => [
                    'username' => $this->config['username'],
                    'password' => $this->config['password']
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            if (isset($data['access_token'])) {
                $this->accessToken = $data['access_token'];
                $this->tokenExpiry = time() + ($data['expires_in'] ?? 3600);

                return [
                    'success' => true,
                    'token' => $this->accessToken,
                    'expires_in' => $data['expires_in'] ?? 3600
                ];
            }

            return [
                'success' => false,
                'error' => 'Invalid response format'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Authentication failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Validate authentication token
     */
    public function validateToken(?string $token = null): bool
    {
        $token ??= $this->accessToken;

        if (!$token || ($this->tokenExpiry && time() >= $this->tokenExpiry)) {
            return false;
        }

        try {
            $response = $this->makeAuthenticatedRequest('GET', 'admin');
            return $response['success'];
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Refresh authentication token
     */
    public function refreshToken(): array
    {
        return $this->authenticate();
    }

    /**
     * Create new user
     */
    public function createUser(array $userData): array
    {
        $this->ensureAuthenticated();

        $marzbanUser = $this->mapToMarzbanUser($userData);

        try {
            $response = $this->makeAuthenticatedRequest('POST', 'user', $marzbanUser);

            if ($response['success']) {
                $user = $response['data'];
                return [
                    'success' => true,
                    'username' => $user['username'],
                    'subscription_url' => $user['subscription_url'] ?? null,
                    'proxies' => $user['proxies'] ?? [],
                    'data_limit' => $user['data_limit'],
                    'expire' => $user['expire'],
                    'status' => $user['status']
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to create user: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get user information
     */
    public function getUser(string $username): array
    {
        $this->ensureAuthenticated();

        try {
            $response = $this->makeAuthenticatedRequest('GET', "user/{$username}");

            if ($response['success']) {
                $user = $response['data'];
                return [
                    'success' => true,
                    'username' => $user['username'],
                    'proxies' => $user['proxies'] ?? [],
                    'data_limit' => $user['data_limit'],
                    'data_limit_reset_strategy' => $user['data_limit_reset_strategy'] ?? 'no_reset',
                    'expire' => $user['expire'],
                    'status' => $user['status'],
                    'used_traffic' => $user['used_traffic'] ?? 0,
                    'lifetime_used_traffic' => $user['lifetime_used_traffic'] ?? 0,
                    'created_at' => $user['created_at'] ?? null,
                    'links' => $user['links'] ?? [],
                    'subscription_url' => $user['subscription_url'] ?? null
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to get user: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Update user
     */
    public function updateUser(string $username, array $updateData): array
    {
        $this->ensureAuthenticated();

        $marzbanData = $this->mapToMarzbanUser($updateData);

        try {
            $response = $this->makeAuthenticatedRequest('PUT', "user/{$username}", $marzbanData);

            if ($response['success']) {
                return [
                    'success' => true,
                    'username' => $username,
                    'updated_fields' => array_keys($updateData)
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to update user: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Delete user
     */
    public function deleteUser(string $username): array
    {
        $this->ensureAuthenticated();

        try {
            $response = $this->makeAuthenticatedRequest('DELETE', "user/{$username}");

            if ($response['success']) {
                return [
                    'success' => true,
                    'username' => $username,
                    'message' => 'User deleted successfully'
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to delete user: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Suspend user
     */
    public function suspendUser(string $username): array
    {
        return $this->updateUser($username, ['status' => 'disabled']);
    }

    /**
     * Reactivate user
     */
    public function reactivateUser(string $username): array
    {
        return $this->updateUser($username, ['status' => 'active']);
    }

    /**
     * Reset user traffic
     */
    public function resetUserTraffic(string $username): array
    {
        $this->ensureAuthenticated();

        try {
            $response = $this->makeAuthenticatedRequest('POST', "user/{$username}/reset");

            if ($response['success']) {
                return [
                    'success' => true,
                    'username' => $username,
                    'message' => 'User traffic reset successfully'
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to reset user traffic: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get user statistics
     */
    public function getUserStats(string $username): array
    {
        $userInfo = $this->getUser($username);

        if (!$userInfo['success']) {
            return $userInfo;
        }

        $user = $userInfo;
        $totalTraffic = $user['data_limit'] ?? 0;
        $usedTraffic = $user['used_traffic'] ?? 0;
        $remainingTraffic = max(0, $totalTraffic - $usedTraffic);

        return [
            'success' => true,
            'username' => $username,
            'used_traffic' => $usedTraffic,
            'total_traffic' => $totalTraffic,
            'remaining_traffic' => $remainingTraffic,
            'usage_percentage' => $totalTraffic > 0 ? ($usedTraffic / $totalTraffic) * 100 : 0,
            'expire_date' => $user['expire'],
            'status' => $user['status'],
            'lifetime_used_traffic' => $user['lifetime_used_traffic'] ?? 0
        ];
    }

    /**
     * Generate configuration for different clients
     */
    public function generateConfig(string $username, string $clientType = 'v2ray'): array
    {
        $userInfo = $this->getUser($username);

        if (!$userInfo['success']) {
            return $userInfo;
        }

        $subscriptionUrl = $userInfo['subscription_url'];

        if (!$subscriptionUrl) {
            return [
                'success' => false,
                'error' => 'No subscription URL available for user'
            ];
        }

        try {
            // Get subscription content
            $response = $this->httpClient->get($subscriptionUrl);
            $configContent = $response->getBody()->getContents();

            switch (strtolower($clientType)) {
                case 'v2ray':
                case 'v2rayn':
                    $config = $this->formatV2RayConfig($configContent);
                    break;
                case 'clash':
                    $config = $this->formatClashConfig($configContent);
                    break;
                case 'sing-box':
                    $config = $this->formatSingBoxConfig($configContent);
                    break;
                default:
                    $config = $configContent;
            }

            return [
                'success' => true,
                'config' => $config,
                'client_type' => $clientType,
                'subscription_url' => $subscriptionUrl
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to generate config: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get system information
     */
    public function getSystemInfo(): array
    {
        $this->ensureAuthenticated();

        try {
            $response = $this->makeAuthenticatedRequest('GET', 'system');

            if ($response['success']) {
                return [
                    'success' => true,
                    'version' => $response['data']['version'] ?? 'unknown',
                    'memory_usage' => $response['data']['mem_used'] ?? 0,
                    'memory_total' => $response['data']['mem_total'] ?? 0,
                    'cpu_usage' => $response['data']['cpu_percent'] ?? 0,
                    'uptime' => $response['data']['uptime'] ?? 0,
                    'users_count' => $response['data']['users_count'] ?? 0,
                    'incoming_bandwidth' => $response['data']['incoming_bandwidth'] ?? 0,
                    'outgoing_bandwidth' => $response['data']['outgoing_bandwidth'] ?? 0
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to get system info: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Health check
     */
    public function healthCheck(): array
    {
        try {
            $response = $this->httpClient->get('docs');

            return [
                'success' => $response->getStatusCode() === 200,
                'status_code' => $response->getStatusCode(),
                'response_time' => 0 // Would need to measure this
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Bulk operations
     */
    public function bulkUpdateUsers(array $usernames, array $updateData): array
    {
        $results = [];
        $successCount = 0;
        $failureCount = 0;

        foreach ($usernames as $username) {
            $result = $this->updateUser($username, $updateData);
            $results[$username] = $result;

            if ($result['success']) {
                $successCount++;
            } else {
                $failureCount++;
            }
        }

        return [
            'success' => $failureCount === 0,
            'total' => count($usernames),
            'successful' => $successCount,
            'failed' => $failureCount,
            'results' => $results
        ];
    }

    public function getBulkUserStats(array $usernames): array
    {
        $results = [];
        $successCount = 0;

        foreach ($usernames as $username) {
            $stats = $this->getUserStats($username);
            if ($stats['success']) {
                $results[$username] = $stats;
                $successCount++;
            }
        }

        return [
            'success' => true,
            'total' => count($usernames),
            'successful' => $successCount,
            'users' => $results
        ];
    }

    /**
     * Private helper methods
     */
    private function ensureAuthenticated(): void
    {
        if (!$this->validateToken()) {
            $authResult = $this->authenticate();
            if (!$authResult['success']) {
                throw new PanelException('Authentication failed: ' . $authResult['error']);
            }
        }
    }

    private function makeAuthenticatedRequest(string $method, string $endpoint, array $data = []): array
    {
        try {
            $options = [
                'headers' => [
                    'Authorization' => "Bearer {$this->accessToken}"
                ]
            ];

            if (!empty($data)) {
                $options['json'] = $data;
            }

            $response = $this->httpClient->request($method, $endpoint, $options);
            $responseData = json_decode($response->getBody()->getContents(), true);

            return [
                'success' => true,
                'data' => $responseData,
                'status_code' => $response->getStatusCode()
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'status_code' => 0,
                'response' => ''
            ];
        }
    }

    private function mapToMarzbanUser(array $userData): array
    {
        $marzbanUser = [];

        if (isset($userData['username'])) {
            $marzbanUser['username'] = $userData['username'];
        }

        if (isset($userData['proxies'])) {
            $marzbanUser['proxies'] = $userData['proxies'];
        }

        if (isset($userData['data_limit'])) {
            $marzbanUser['data_limit'] = (int) $userData['data_limit'];
        }

        if (isset($userData['expire'])) {
            $marzbanUser['expire'] = is_numeric($userData['expire']) ? (int) $userData['expire'] : strtotime($userData['expire']);
        }

        if (isset($userData['expire_days'])) {
            $marzbanUser['expire'] = strtotime("+{$userData['expire_days']} days");
        }

        if (isset($userData['status'])) {
            $marzbanUser['status'] = $userData['status'];
        }

        if (isset($userData['data_limit_reset_strategy'])) {
            $marzbanUser['data_limit_reset_strategy'] = $userData['data_limit_reset_strategy'];
        }

        return $marzbanUser;
    }

    private function formatV2RayConfig(string $content): string
    {
        // V2Ray config is usually base64 encoded list of configs
        return $content;
    }

    private function formatClashConfig(string $content): string
    {
        // Convert to Clash YAML format
        $configs = explode("\n", base64_decode($content));
        $clashConfig = "proxies:\n";

        foreach ($configs as $config) {
            if (!empty(trim($config))) {
                // Parse and convert each config to Clash format
                $clashConfig .= "  - " . $this->convertToClashProxy($config) . "\n";
            }
        }

        return $clashConfig;
    }

    private function formatSingBoxConfig(string $content): string
    {
        // Convert to Sing-box JSON format
        $configs = explode("\n", base64_decode($content));
        $outbounds = [];

        foreach ($configs as $config) {
            if (!empty(trim($config))) {
                $outbounds[] = $this->convertToSingBoxOutbound($config);
            }
        }

        return json_encode(['outbounds' => $outbounds], JSON_PRETTY_PRINT);
    }

    private function convertToClashProxy(string $config): string
    {
        unset($config); // Suppress unused parameter warning
        // Simplified conversion - would need full implementation
        return "name: proxy, type: vmess, server: example.com, port: 443";
    }

    private function convertToSingBoxOutbound(string $config): array
    {
        unset($config); // Suppress unused parameter warning
        // Simplified conversion - would need full implementation
        return [
            'type' => 'vmess',
            'tag' => 'proxy',
            'server' => 'example.com',
            'server_port' => 443
        ];
    }
}
