<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#357ABD;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#50C878;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#228B22;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="100" cy="100" r="95" fill="url(#gradient1)" stroke="#2C5282" stroke-width="3"/>
  
  <!-- Robot Head -->
  <rect x="60" y="50" width="80" height="60" rx="15" ry="15" fill="#FFFFFF" stroke="#2C5282" stroke-width="2"/>
  
  <!-- Robot Eyes -->
  <circle cx="80" cy="75" r="8" fill="url(#gradient2)"/>
  <circle cx="120" cy="75" r="8" fill="url(#gradient2)"/>
  <circle cx="80" cy="75" r="3" fill="#FFFFFF"/>
  <circle cx="120" cy="75" r="3" fill="#FFFFFF"/>
  
  <!-- Robot Mouth -->
  <rect x="90" y="90" width="20" height="8" rx="4" ry="4" fill="#2C5282"/>
  
  <!-- Robot Antennas -->
  <line x1="75" y1="50" x2="75" y2="35" stroke="#2C5282" stroke-width="3" stroke-linecap="round"/>
  <line x1="125" y1="50" x2="125" y2="35" stroke="#2C5282" stroke-width="3" stroke-linecap="round"/>
  <circle cx="75" cy="32" r="4" fill="url(#gradient2)"/>
  <circle cx="125" cy="32" r="4" fill="url(#gradient2)"/>
  
  <!-- Robot Body -->
  <rect x="70" y="110" width="60" height="50" rx="10" ry="10" fill="#FFFFFF" stroke="#2C5282" stroke-width="2"/>
  
  <!-- Robot Chest Panel -->
  <rect x="80" y="120" width="40" height="30" rx="5" ry="5" fill="#F7FAFC" stroke="#2C5282" stroke-width="1"/>
  
  <!-- Robot Arms -->
  <rect x="45" y="115" width="20" height="35" rx="10" ry="10" fill="#FFFFFF" stroke="#2C5282" stroke-width="2"/>
  <rect x="135" y="115" width="20" height="35" rx="10" ry="10" fill="#FFFFFF" stroke="#2C5282" stroke-width="2"/>
  
  <!-- Robot Hands -->
  <circle cx="55" cy="155" r="8" fill="url(#gradient2)"/>
  <circle cx="145" cy="155" r="8" fill="url(#gradient2)"/>
  
  <!-- Robot Legs -->
  <rect x="80" y="160" width="15" height="25" rx="7" ry="7" fill="#FFFFFF" stroke="#2C5282" stroke-width="2"/>
  <rect x="105" y="160" width="15" height="25" rx="7" ry="7" fill="#FFFFFF" stroke="#2C5282" stroke-width="2"/>
  
  <!-- Robot Feet -->
  <ellipse cx="87.5" cy="190" rx="12" ry="6" fill="url(#gradient2)"/>
  <ellipse cx="112.5" cy="190" rx="12" ry="6" fill="url(#gradient2)"/>
  
  <!-- WeBot Text -->
  <text x="100" y="25" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#FFFFFF">WeBot</text>
  
  <!-- VPN Text -->
  <text x="100" y="210" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#2C5282">VPN Bot</text>
</svg>
