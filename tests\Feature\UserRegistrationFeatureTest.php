<?php

declare(strict_types=1);

namespace WeBot\Tests\Feature;

use WeBot\Tests\Unit\BaseTestCase;
use WeBot\Controllers\UserController;

/**
 * User Registration Feature Tests
 * 
 * End-to-end tests for complete user registration workflow
 * including Telegram interactions and database persistence.
 * 
 * @package WeBot\Tests\Feature
 * @version 2.0
 */
class UserRegistrationFeatureTest extends BaseTestCase
{
    private UserController $userController;
    private object $mockContainer;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->setupMockContainer();
        $this->userController = new UserController($this->mockContainer);
    }
    
    /**
     * Setup mock container for feature testing
     */
    private function setupMockContainer(): void
    {
        $this->mockContainer = new class($this->database, $this->config) {
            private $database;
            private $config;
            
            public function __construct($database, $config) {
                $this->database = $database;
                $this->config = $config;
            }
            
            public function get(string $service) {
                switch ($service) {
                    case 'database':
                        return $this->database;
                    case 'config':
                        return $this->config;
                    case 'telegram':
                        return $this->createMockTelegramService();
                    case 'logger':
                        return $this->createMockLogger();
                    default:
                        return null;
                }
            }
            
            private function createMockTelegramService() {
                return new class {
                    public function sendMessage(array $params): array {
                        return [
                            'ok' => true,
                            'result' => [
                                'message_id' => rand(1000, 9999),
                                'chat' => ['id' => $params['chat_id']],
                                'text' => $params['text']
                            ]
                        ];
                    }
                    
                    public function editMessageText(array $params): array {
                        return [
                            'ok' => true,
                            'result' => [
                                'message_id' => $params['message_id'],
                                'chat' => ['id' => $params['chat_id']],
                                'text' => $params['text']
                            ]
                        ];
                    }
                };
            }
            
            private function createMockLogger() {
                return new class {
                    public function info(string $message, array $context = []): void {}
                    public function error(string $message, array $context = []): void {}
                    public function warning(string $message, array $context = []): void {}
                };
            }
        };
    }
    
    /**
     * Test complete new user registration flow
     */
    public function testCompleteNewUserRegistrationFlow(): void
    {
        $telegramId = 111222333444;
        
        // Step 1: User sends /start command
        $startMessage = [
            'message_id' => 1001,
            'from' => [
                'id' => $telegramId,
                'username' => 'newfeatureuser',
                'first_name' => 'New Feature User'
            ],
            'chat' => [
                'id' => $telegramId,
                'type' => 'private'
            ],
            'text' => '/start',
            'date' => time()
        ];
        
        $response1 = $this->userController->handleStart($startMessage);
        
        // Verify welcome message is sent
        $this->assertEquals('sendMessage', $response1['method']);
        $this->assertStringContains('خوش آمدید', $response1['text']);
        
        // Verify user is created in database with pending status
        $users = $this->database->query(
            "SELECT * FROM users WHERE telegram_id = ?",
            [$telegramId]
        );
        
        $this->assertEquals(1, count($users));
        $this->assertEquals('pending', $users[0]['status']);
        $this->assertEquals('newfeatureuser', $users[0]['username']);
        
        // Step 2: User sends phone number
        $phoneMessage = [
            'message_id' => 1002,
            'from' => [
                'id' => $telegramId,
                'username' => 'newfeatureuser',
                'first_name' => 'New Feature User'
            ],
            'chat' => [
                'id' => $telegramId,
                'type' => 'private'
            ],
            'text' => '+1234567890',
            'date' => time()
        ];
        
        $response2 = $this->userController->handleMessage($phoneMessage);
        
        // Verify phone number is accepted and user is activated
        $this->assertEquals('sendMessage', $response2['method']);
        $this->assertStringContains('تایید', $response2['text']);
        
        // Verify user status is updated to active
        $updatedUsers = $this->database->query(
            "SELECT * FROM users WHERE telegram_id = ?",
            [$telegramId]
        );
        
        $this->assertEquals('active', $updatedUsers[0]['status']);
        $this->assertEquals('+1234567890', $updatedUsers[0]['phone']);
        
        // Step 3: User requests main menu
        $menuMessage = [
            'message_id' => 1003,
            'from' => [
                'id' => $telegramId,
                'username' => 'newfeatureuser',
                'first_name' => 'New Feature User'
            ],
            'chat' => [
                'id' => $telegramId,
                'type' => 'private'
            ],
            'text' => '/menu',
            'date' => time()
        ];
        
        $response3 = $this->userController->handleMessage($menuMessage);
        
        // Verify main menu is displayed
        $this->assertEquals('sendMessage', $response3['method']);
        $this->assertArrayHasKey('reply_markup', $response3);
        $this->assertStringContains('منوی اصلی', $response3['text']);
    }
    
    /**
     * Test user registration with invalid phone number
     */
    public function testRegistrationWithInvalidPhoneNumber(): void
    {
        $telegramId = 222333444555;
        
        // Step 1: Create pending user
        $this->database->execute(
            "INSERT INTO users (telegram_id, username, first_name, status) VALUES (?, ?, ?, ?)",
            [$telegramId, 'invalidphoneuser', 'Invalid Phone User', 'pending']
        );
        
        // Step 2: User sends invalid phone number
        $invalidPhoneMessage = [
            'message_id' => 2001,
            'from' => [
                'id' => $telegramId,
                'username' => 'invalidphoneuser',
                'first_name' => 'Invalid Phone User'
            ],
            'chat' => [
                'id' => $telegramId,
                'type' => 'private'
            ],
            'text' => 'invalid_phone_123',
            'date' => time()
        ];
        
        $response = $this->userController->handleMessage($invalidPhoneMessage);
        
        // Verify error message is sent
        $this->assertEquals('sendMessage', $response['method']);
        $this->assertStringContains('نامعتبر', $response['text']);
        
        // Verify user status remains pending
        $users = $this->database->query(
            "SELECT * FROM users WHERE telegram_id = ?",
            [$telegramId]
        );
        
        $this->assertEquals('pending', $users[0]['status']);
        $this->assertNull($users[0]['phone']);
    }
    
    /**
     * Test existing user login flow
     */
    public function testExistingUserLoginFlow(): void
    {
        $telegramId = 123456789; // This user exists in test data
        
        // User sends /start command
        $startMessage = [
            'message_id' => 3001,
            'from' => [
                'id' => $telegramId,
                'username' => 'testuser1',
                'first_name' => 'Test User 1'
            ],
            'chat' => [
                'id' => $telegramId,
                'type' => 'private'
            ],
            'text' => '/start',
            'date' => time()
        ];
        
        $response = $this->userController->handleStart($startMessage);
        
        // Verify welcome back message is sent
        $this->assertEquals('sendMessage', $response['method']);
        $this->assertStringContains('خوش آمدید', $response['text']);
        
        // Verify no new user is created
        $users = $this->database->query(
            "SELECT COUNT(*) as count FROM users WHERE telegram_id = ?",
            [$telegramId]
        );
        
        $this->assertEquals(1, $users[0]['count']);
    }
    
    /**
     * Test banned user access attempt
     */
    public function testBannedUserAccessAttempt(): void
    {
        $telegramId = 555666777; // This user is banned in test data
        
        // User sends /start command
        $startMessage = [
            'message_id' => 4001,
            'from' => [
                'id' => $telegramId,
                'username' => 'banneduser',
                'first_name' => 'Banned User'
            ],
            'chat' => [
                'id' => $telegramId,
                'type' => 'private'
            ],
            'text' => '/start',
            'date' => time()
        ];
        
        $response = $this->userController->handleStart($startMessage);
        
        // Verify banned message is sent
        $this->assertEquals('sendMessage', $response['method']);
        $this->assertStringContains('مسدود', $response['text']);
        
        // Try to send another command
        $menuMessage = [
            'message_id' => 4002,
            'from' => [
                'id' => $telegramId,
                'username' => 'banneduser',
                'first_name' => 'Banned User'
            ],
            'chat' => [
                'id' => $telegramId,
                'type' => 'private'
            ],
            'text' => '/menu',
            'date' => time()
        ];
        
        $response2 = $this->userController->handleMessage($menuMessage);
        
        // Verify banned user cannot access menu
        $this->assertStringContains('مسدود', $response2['text']);
    }
    
    /**
     * Test user profile management flow
     */
    public function testUserProfileManagementFlow(): void
    {
        $telegramId = 123456789; // Active user from test data
        
        // Step 1: User requests profile
        $profileCallback = [
            'id' => 'callback_profile_5001',
            'from' => [
                'id' => $telegramId,
                'username' => 'testuser1',
                'first_name' => 'Test User 1'
            ],
            'message' => [
                'message_id' => 5001,
                'chat' => ['id' => $telegramId]
            ],
            'data' => 'profile'
        ];
        
        $response1 = $this->userController->handleCallback($profileCallback);
        
        // Verify profile is displayed
        $this->assertEquals('editMessageText', $response1['method']);
        $this->assertStringContains('پروفایل', $response1['text']);
        $this->assertStringContains('testuser1', $response1['text']);
        
        // Step 2: User requests to edit profile
        $editProfileCallback = [
            'id' => 'callback_edit_profile_5002',
            'from' => [
                'id' => $telegramId,
                'username' => 'testuser1',
                'first_name' => 'Test User 1'
            ],
            'message' => [
                'message_id' => 5001,
                'chat' => ['id' => $telegramId]
            ],
            'data' => 'edit_profile'
        ];
        
        $response2 = $this->userController->handleCallback($editProfileCallback);
        
        // Verify edit options are shown
        $this->assertEquals('editMessageText', $response2['method']);
        $this->assertStringContains('ویرایش', $response2['text']);
    }
    
    /**
     * Test user registration rate limiting
     */
    public function testUserRegistrationRateLimiting(): void
    {
        $baseId = 666777888;
        
        // Attempt to register multiple users rapidly
        for ($i = 0; $i < 12; $i++) {
            $telegramId = $baseId + $i;
            
            $startMessage = [
                'message_id' => 6000 + $i,
                'from' => [
                    'id' => $telegramId,
                    'username' => "ratetest{$i}",
                    'first_name' => "Rate Test {$i}"
                ],
                'chat' => [
                    'id' => $telegramId,
                    'type' => 'private'
                ],
                'text' => '/start',
                'date' => time()
            ];
            
            $response = $this->userController->handleStart($startMessage);
            
            // After 10 requests, rate limiting should kick in
            if ($i >= 10) {
                $this->assertStringContains('تعداد درخواست', $response['text']);
            }
        }
    }
}
