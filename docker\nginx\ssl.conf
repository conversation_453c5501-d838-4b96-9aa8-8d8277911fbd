# WeBot SSL/TLS Configuration
# Production-ready SSL configuration with security best practices

# SSL Certificate and Key
ssl_certificate /etc/nginx/ssl/cert.pem;
ssl_certificate_key /etc/nginx/ssl/private.key;

# SSL Protocols - Only TLS 1.2 and 1.3
ssl_protocols TLSv1.2 TLSv1.3;

# Cipher Suites - Modern and secure ciphers only
ssl_ciphers ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256;
ssl_prefer_server_ciphers on;

# SSL Session Configuration
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;
ssl_session_tickets off;

# OCSP Stapling
ssl_stapling on;
ssl_stapling_verify on;
ssl_trusted_certificate /etc/nginx/ssl/ca.pem;
resolver ******* ******* ******* valid=300s;
resolver_timeout 5s;

# Perfect Forward Secrecy
ssl_dhparam /etc/nginx/ssl/dhparam.pem;

# Security Headers
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
add_header X-Frame-Options DENY always;
add_header X-Content-Type-Options nosniff always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self'; frame-ancestors 'none';" always;
add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;

# Additional Security Headers
add_header X-Permitted-Cross-Domain-Policies none always;
add_header X-Download-Options noopen always;
add_header Cross-Origin-Embedder-Policy require-corp always;
add_header Cross-Origin-Opener-Policy same-origin always;
add_header Cross-Origin-Resource-Policy cross-origin always;

# SSL Buffer Size
ssl_buffer_size 4k;

# SSL Early Data (TLS 1.3)
ssl_early_data on;

# Custom SSL Error Pages
error_page 495 496 497 /ssl_error.html;

# SSL Verification
ssl_verify_client off;
ssl_verify_depth 1;

# SSL Client Certificate (if needed)
# ssl_client_certificate /etc/nginx/ssl/client_ca.pem;
# ssl_verify_client optional;

# Disable SSL for specific locations if needed
# location /insecure {
#     ssl_verify_client off;
# }
