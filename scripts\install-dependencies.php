#!/usr/bin/env php
<?php
/**
 * WeBot Dependencies Installation Script
 * 
 * This script installs required dependencies and sets up the environment.
 * 
 * Usage:
 *   php scripts/install-dependencies.php
 * 
 * @package WeBot
 * @version 2.0
 */

declare(strict_types=1);

echo "🚀 WeBot Dependencies Installation\n";
echo "==================================\n\n";

// Check if composer is available
function checkComposer(): bool
{
    $output = [];
    $returnCode = 0;
    exec('composer --version 2>&1', $output, $returnCode);
    return $returnCode === 0;
}

// Install composer dependencies
function installComposerDependencies(): bool
{
    echo "📦 Installing Composer dependencies...\n";
    
    if (!file_exists('composer.json')) {
        echo "❌ composer.json not found!\n";
        return false;
    }
    
    $output = [];
    $returnCode = 0;
    
    // Run composer install
    exec('composer install --no-dev --optimize-autoloader 2>&1', $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "✅ Composer dependencies installed successfully!\n";
        return true;
    } else {
        echo "❌ Failed to install composer dependencies:\n";
        foreach ($output as $line) {
            echo "   {$line}\n";
        }
        return false;
    }
}

// Create required directories
function createDirectories(): void
{
    echo "📁 Creating required directories...\n";
    
    $directories = [
        'storage/logs',
        'storage/cache',
        'storage/sessions',
        'storage/backups',
        'storage/uploads',
        'public/uploads',
        'public/temp',
        'public/assets'
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
            echo "   ✅ Created: {$dir}\n";
        } else {
            echo "   ✓ Exists: {$dir}\n";
        }
        
        // Create .gitkeep file
        $gitkeepFile = $dir . '/.gitkeep';
        if (!file_exists($gitkeepFile)) {
            file_put_contents($gitkeepFile, '');
        }
    }
}

// Set file permissions
function setPermissions(): void
{
    echo "🔐 Setting file permissions...\n";
    
    $writableDirectories = [
        'storage',
        'public/uploads',
        'public/temp'
    ];
    
    foreach ($writableDirectories as $dir) {
        if (is_dir($dir)) {
            chmod($dir, 0755);
            echo "   ✅ Set permissions for: {$dir}\n";
        }
    }
}

// Check environment file
function checkEnvironmentFile(): void
{
    echo "🔧 Checking environment configuration...\n";
    
    if (!file_exists('.env')) {
        if (file_exists('.env.example')) {
            copy('.env.example', '.env');
            echo "   ✅ Created .env from .env.example\n";
            echo "   ⚠️  Please edit .env file with your configuration!\n";
        } else {
            echo "   ❌ No .env.example file found!\n";
        }
    } else {
        echo "   ✓ .env file exists\n";
    }
}

// Validate installation
function validateInstallation(): bool
{
    echo "🔍 Validating installation...\n";
    
    $checks = [
        'vendor/autoload.php' => 'Composer autoloader',
        '.env' => 'Environment file',
        'storage/logs' => 'Logs directory',
        'storage/cache' => 'Cache directory'
    ];
    
    $allValid = true;
    
    foreach ($checks as $path => $description) {
        if (file_exists($path) || is_dir($path)) {
            echo "   ✅ {$description}\n";
        } else {
            echo "   ❌ {$description} - Missing: {$path}\n";
            $allValid = false;
        }
    }
    
    return $allValid;
}

// Main installation process
function main(): int
{
    try {
        // Check composer
        if (!checkComposer()) {
            echo "❌ Composer is not installed or not in PATH!\n";
            echo "   Please install Composer: https://getcomposer.org/\n";
            return 1;
        }
        
        echo "✅ Composer is available\n\n";
        
        // Install dependencies
        if (!installComposerDependencies()) {
            return 1;
        }
        
        echo "\n";
        
        // Create directories
        createDirectories();
        echo "\n";
        
        // Set permissions
        setPermissions();
        echo "\n";
        
        // Check environment
        checkEnvironmentFile();
        echo "\n";
        
        // Validate installation
        if (!validateInstallation()) {
            echo "\n❌ Installation validation failed!\n";
            return 1;
        }
        
        echo "\n🎉 WeBot installation completed successfully!\n";
        echo "\nNext steps:\n";
        echo "1. Edit .env file with your configuration\n";
        echo "2. Run: php scripts/validate-env.php\n";
        echo "3. Set up your database\n";
        echo "4. Configure your Telegram bot\n";
        
        return 0;
        
    } catch (Exception $e) {
        echo "❌ Installation failed: " . $e->getMessage() . "\n";
        return 1;
    }
}

// Run the installation
exit(main());
