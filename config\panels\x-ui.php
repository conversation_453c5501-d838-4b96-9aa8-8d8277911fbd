<?php

declare(strict_types=1);

/**
 * X-UI Panel Configuration
 * 
 * Configuration settings for X-UI panel integration
 * including inbound management and client operations.
 * 
 * @package WeBot\Config\Panels
 * @version 2.0
 */

return [
    /*
    |--------------------------------------------------------------------------
    | X-UI Panel Settings
    |--------------------------------------------------------------------------
    */
    'enabled' => (bool)($_ENV['XUI_ENABLED'] ?? false),
    'name' => 'X-UI',
    'description' => 'X-UI Panel Integration with Inbound Management',
    'version' => '1.8.0',

    /*
    |--------------------------------------------------------------------------
    | Connection Settings
    |--------------------------------------------------------------------------
    */
    'connection' => [
        'url' => $_ENV['XUI_URL'] ?? 'https://panel.example.com',
        'port' => (int)($_ENV['XUI_PORT'] ?? 54321),
        'path' => $_ENV['XUI_PATH'] ?? '/',
        'timeout' => (int)($_ENV['XUI_TIMEOUT'] ?? 30),
        'verify_ssl' => (bool)($_ENV['XUI_VERIFY_SSL'] ?? true),
        'retry_attempts' => (int)($_ENV['XUI_RETRY_ATTEMPTS'] ?? 3),
        'retry_delay' => (int)($_ENV['XUI_RETRY_DELAY'] ?? 1000),
    ],

    /*
    |--------------------------------------------------------------------------
    | Authentication
    |--------------------------------------------------------------------------
    */
    'auth' => [
        'username' => $_ENV['XUI_USERNAME'] ?? '',
        'password' => $_ENV['XUI_PASSWORD'] ?? '',
        'session_lifetime' => (int)($_ENV['XUI_SESSION_LIFETIME'] ?? 3600),
        'auto_refresh' => (bool)($_ENV['XUI_AUTO_REFRESH'] ?? true),
        'login_path' => '/login',
        'logout_path' => '/logout',
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Client Settings
    |--------------------------------------------------------------------------
    */
    'defaults' => [
        'data_limit' => (int)($_ENV['XUI_DEFAULT_DATA_LIMIT'] ?? 50 * 1024 * 1024 * 1024), // 50GB
        'expire_days' => (int)($_ENV['XUI_DEFAULT_EXPIRE_DAYS'] ?? 30),
        'connection_limit' => (int)($_ENV['XUI_DEFAULT_CONNECTION_LIMIT'] ?? 1),
        'enable' => (bool)($_ENV['XUI_DEFAULT_ENABLE'] ?? true),
        'protocol' => $_ENV['XUI_DEFAULT_PROTOCOL'] ?? 'vmess',
        'network' => $_ENV['XUI_DEFAULT_NETWORK'] ?? 'ws',
        'security' => $_ENV['XUI_DEFAULT_SECURITY'] ?? 'tls',
    ],

    /*
    |--------------------------------------------------------------------------
    | Inbound Configuration
    |--------------------------------------------------------------------------
    */
    'inbounds' => [
        'auto_create' => (bool)($_ENV['XUI_AUTO_CREATE_INBOUND'] ?? true),
        'default_port' => (int)($_ENV['XUI_DEFAULT_PORT'] ?? 443),
        'default_protocol' => $_ENV['XUI_DEFAULT_INBOUND_PROTOCOL'] ?? 'vmess',
        'default_network' => $_ENV['XUI_DEFAULT_INBOUND_NETWORK'] ?? 'ws',
        'default_security' => $_ENV['XUI_DEFAULT_INBOUND_SECURITY'] ?? 'tls',
        'default_domain' => $_ENV['XUI_DEFAULT_DOMAIN'] ?? 'example.com',
        'default_path' => $_ENV['XUI_DEFAULT_PATH'] ?? '/ws',
        'remark_prefix' => $_ENV['XUI_REMARK_PREFIX'] ?? 'WeBot-',
        'enable_sniffing' => (bool)($_ENV['XUI_ENABLE_SNIFFING'] ?? true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Supported Protocols
    |--------------------------------------------------------------------------
    */
    'protocols' => [
        'vmess' => [
            'enabled' => true,
            'alter_id' => 0,
            'security' => 'auto',
        ],
        'vless' => [
            'enabled' => true,
            'flow' => '',
            'encryption' => 'none',
        ],
        'trojan' => [
            'enabled' => true,
            'password_length' => 32,
        ],
        'shadowsocks' => [
            'enabled' => false,
            'method' => 'chacha20-ietf-poly1305',
            'password_length' => 32,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Network Settings
    |--------------------------------------------------------------------------
    */
    'networks' => [
        'tcp' => [
            'enabled' => true,
            'header_type' => 'none',
        ],
        'ws' => [
            'enabled' => true,
            'path' => '/ws',
            'headers' => [],
        ],
        'grpc' => [
            'enabled' => false,
            'service_name' => 'grpc',
        ],
        'h2' => [
            'enabled' => false,
            'path' => '/h2',
            'host' => [],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    */
    'security_types' => [
        'none' => [
            'enabled' => true,
        ],
        'tls' => [
            'enabled' => true,
            'server_name' => $_ENV['XUI_TLS_SERVER_NAME'] ?? 'example.com',
            'alpn' => ['h2', 'http/1.1'],
            'allow_insecure' => false,
        ],
        'reality' => [
            'enabled' => false,
            'dest' => 'www.google.com:443',
            'server_names' => ['www.google.com'],
            'private_key' => '',
            'short_ids' => [],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Client Configuration
    |--------------------------------------------------------------------------
    */
    'clients' => [
        'supported_types' => [
            'v2ray' => 'V2Ray/V2RayN',
            'clash' => 'Clash/ClashX',
            'sing-box' => 'Sing-box',
            'json' => 'JSON Config',
            'uri' => 'URI Format',
        ],
        'default_type' => 'v2ray',
        'subscription_format' => 'base64',
        'include_stats' => true,
        'generate_qr_code' => (bool)($_ENV['XUI_GENERATE_QR_CODE'] ?? true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Features Configuration
    |--------------------------------------------------------------------------
    */
    'features' => [
        'client_management' => true,
        'inbound_management' => true,
        'traffic_monitoring' => true,
        'subscription_urls' => true,
        'config_generation' => true,
        'bulk_operations' => true,
        'client_statistics' => true,
        'traffic_reset' => true,
        'client_suspension' => true,
        'expiry_management' => true,
        'system_monitoring' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    */
    'rate_limiting' => [
        'enabled' => (bool)($_ENV['XUI_RATE_LIMITING'] ?? true),
        'requests_per_minute' => (int)($_ENV['XUI_REQUESTS_PER_MINUTE'] ?? 30),
        'burst_limit' => (int)($_ENV['XUI_BURST_LIMIT'] ?? 5),
        'cooldown_period' => (int)($_ENV['XUI_COOLDOWN_PERIOD'] ?? 60),
    ],

    /*
    |--------------------------------------------------------------------------
    | Caching
    |--------------------------------------------------------------------------
    */
    'caching' => [
        'enabled' => (bool)($_ENV['XUI_CACHING'] ?? true),
        'client_info_ttl' => (int)($_ENV['XUI_CLIENT_CACHE_TTL'] ?? 300),
        'inbound_info_ttl' => (int)($_ENV['XUI_INBOUND_CACHE_TTL'] ?? 600),
        'system_info_ttl' => (int)($_ENV['XUI_SYSTEM_CACHE_TTL'] ?? 60),
        'config_cache_ttl' => (int)($_ENV['XUI_CONFIG_CACHE_TTL'] ?? 3600),
        'cache_prefix' => 'xui:',
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring & Health Checks
    |--------------------------------------------------------------------------
    */
    'monitoring' => [
        'health_check_interval' => (int)($_ENV['XUI_HEALTH_CHECK_INTERVAL'] ?? 300),
        'health_check_timeout' => (int)($_ENV['XUI_HEALTH_CHECK_TIMEOUT'] ?? 10),
        'alert_on_failure' => (bool)($_ENV['XUI_ALERT_ON_FAILURE'] ?? true),
        'max_consecutive_failures' => (int)($_ENV['XUI_MAX_FAILURES'] ?? 3),
        'metrics_collection' => (bool)($_ENV['XUI_METRICS'] ?? true),
        'xray_status_monitoring' => (bool)($_ENV['XUI_XRAY_MONITORING'] ?? true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Error Handling
    |--------------------------------------------------------------------------
    */
    'error_handling' => [
        'log_errors' => true,
        'retry_on_failure' => true,
        'fallback_enabled' => (bool)($_ENV['XUI_FALLBACK'] ?? false),
        'circuit_breaker' => [
            'enabled' => (bool)($_ENV['XUI_CIRCUIT_BREAKER'] ?? true),
            'failure_threshold' => 5,
            'recovery_timeout' => 60,
            'half_open_max_calls' => 3,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    */
    'security' => [
        'encrypt_credentials' => (bool)($_ENV['XUI_ENCRYPT_CREDENTIALS'] ?? true),
        'validate_ssl_cert' => (bool)($_ENV['XUI_VALIDATE_SSL'] ?? true),
        'allowed_ips' => explode(',', $_ENV['XUI_ALLOWED_IPS'] ?? ''),
        'session_security' => (bool)($_ENV['XUI_SESSION_SECURITY'] ?? true),
        'audit_logging' => (bool)($_ENV['XUI_AUDIT_LOGGING'] ?? true),
        'csrf_protection' => (bool)($_ENV['XUI_CSRF_PROTECTION'] ?? true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup & Sync
    |--------------------------------------------------------------------------
    */
    'backup' => [
        'enabled' => (bool)($_ENV['XUI_BACKUP_ENABLED'] ?? true),
        'sync_interval' => (int)($_ENV['XUI_SYNC_INTERVAL'] ?? 3600),
        'backup_clients' => true,
        'backup_inbounds' => true,
        'backup_settings' => true,
        'retention_days' => (int)($_ENV['XUI_BACKUP_RETENTION'] ?? 7),
    ],

    /*
    |--------------------------------------------------------------------------
    | Advanced Settings
    |--------------------------------------------------------------------------
    */
    'advanced' => [
        'custom_headers' => [
            'User-Agent' => 'WeBot/2.0 X-UI-Adapter',
            'X-Client-Version' => '2.0.0',
        ],
        'cookie_management' => (bool)($_ENV['XUI_COOKIE_MANAGEMENT'] ?? true),
        'session_persistence' => (bool)($_ENV['XUI_SESSION_PERSISTENCE'] ?? true),
        'custom_endpoints' => [
            'login' => '/login',
            'panel' => '/panel',
            'api' => '/panel/api',
        ],
        'xray_integration' => (bool)($_ENV['XUI_XRAY_INTEGRATION'] ?? true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Development & Testing
    |--------------------------------------------------------------------------
    */
    'development' => [
        'debug_mode' => (bool)($_ENV['XUI_DEBUG'] ?? false),
        'log_requests' => (bool)($_ENV['XUI_LOG_REQUESTS'] ?? false),
        'log_responses' => (bool)($_ENV['XUI_LOG_RESPONSES'] ?? false),
        'mock_responses' => (bool)($_ENV['XUI_MOCK'] ?? false),
        'test_credentials' => [
            'username' => $_ENV['XUI_TEST_USERNAME'] ?? 'admin',
            'password' => $_ENV['XUI_TEST_PASSWORD'] ?? 'admin',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Integration Settings
    |--------------------------------------------------------------------------
    */
    'integration' => [
        'auto_create_clients' => (bool)($_ENV['XUI_AUTO_CREATE_CLIENTS'] ?? true),
        'auto_suspend_expired' => (bool)($_ENV['XUI_AUTO_SUSPEND_EXPIRED'] ?? true),
        'auto_delete_expired' => (bool)($_ENV['XUI_AUTO_DELETE_EXPIRED'] ?? false),
        'sync_client_status' => (bool)($_ENV['XUI_SYNC_CLIENT_STATUS'] ?? true),
        'notification_events' => [
            'client_created' => true,
            'client_suspended' => true,
            'client_expired' => true,
            'quota_exceeded' => true,
            'panel_offline' => true,
            'xray_offline' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Tuning
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'connection_pooling' => (bool)($_ENV['XUI_CONNECTION_POOLING'] ?? true),
        'max_connections' => (int)($_ENV['XUI_MAX_CONNECTIONS'] ?? 5),
        'keep_alive' => (bool)($_ENV['XUI_KEEP_ALIVE'] ?? true),
        'compression' => (bool)($_ENV['XUI_COMPRESSION'] ?? false),
        'batch_operations' => (bool)($_ENV['XUI_BATCH_OPERATIONS'] ?? true),
        'async_operations' => (bool)($_ENV['XUI_ASYNC_OPERATIONS'] ?? false),
        'lazy_loading' => (bool)($_ENV['XUI_LAZY_LOADING'] ?? true),
    ],

    /*
    |--------------------------------------------------------------------------
    | UUID Generation
    |--------------------------------------------------------------------------
    */
    'uuid' => [
        'version' => 4, // UUID version 4 (random)
        'format' => 'standard', // 'standard' or 'short'
        'uppercase' => false,
        'use_crypto_random' => true,
    ],
];
