<?php

declare(strict_types=1);

namespace WeBot\Core;

/**
 * Resource Manager
 *
 * Manages system resources including file handles,
 * database connections, and external API connections.
 *
 * @package WeBot\Core
 * @version 2.0
 */
class ResourceManager
{
    private array $resources = [];
    private array $pools = [];
    private array $limits = [];
    private array $metrics = [];
    private bool $autoCleanup;

    public function __construct(array $config = [])
    {
        $this->autoCleanup = $config['auto_cleanup'] ?? true;
        $this->limits = array_merge([
            'max_file_handles' => 1000,
            'max_db_connections' => 50,
            'max_api_connections' => 100,
            'max_memory_per_resource' => 50 * 1024 * 1024, // 50MB
            'resource_timeout' => 3600 // 1 hour
        ], $config['limits'] ?? []);

        $this->initializePools();

        if ($this->autoCleanup) {
            register_shutdown_function([$this, 'cleanup']);
        }
    }

    /**
     * Register a resource
     */
    public function register(string $id, $resource, string $type, array $metadata = []): bool
    {
        if (isset($this->resources[$id])) {
            return false; // Resource already exists
        }

        // Check limits
        if (!$this->checkLimits($type)) {
            throw new \RuntimeException("Resource limit exceeded for type: {$type}");
        }

        $this->resources[$id] = [
            'resource' => $resource,
            'type' => $type,
            'created_at' => microtime(true),
            'last_used' => microtime(true),
            'use_count' => 0,
            'metadata' => $metadata,
            'memory_usage' => $this->estimateMemoryUsage($resource)
        ];

        $this->updateMetrics($type, 'created');
        return true;
    }

    /**
     * Get a resource
     */
    public function get(string $id)
    {
        if (!isset($this->resources[$id])) {
            return null;
        }

        $this->resources[$id]['last_used'] = microtime(true);
        $this->resources[$id]['use_count']++;

        $this->updateMetrics($this->resources[$id]['type'], 'accessed');

        return $this->resources[$id]['resource'];
    }

    /**
     * Release a resource
     */
    public function release(string $id): bool
    {
        if (!isset($this->resources[$id])) {
            return false;
        }

        $resource = $this->resources[$id];
        $this->closeResource($resource['resource'], $resource['type']);

        unset($this->resources[$id]);
        $this->updateMetrics($resource['type'], 'released');

        return true;
    }

    /**
     * Get resource from pool or create new one
     */
    public function getFromPool(string $type, callable $factory, array $config = [])
    {
        $poolKey = $type . '_pool';

        if (!isset($this->pools[$poolKey])) {
            $this->pools[$poolKey] = [];
        }

        // Try to get from pool
        if (!empty($this->pools[$poolKey])) {
            $resourceId = array_pop($this->pools[$poolKey]);
            if (isset($this->resources[$resourceId])) {
                return $this->get($resourceId);
            }
        }

        // Create new resource
        $resource = $factory($config);
        $resourceId = uniqid($type . '_', true);

        if ($this->register($resourceId, $resource, $type, $config)) {
            return $resource;
        }

        return null;
    }

    /**
     * Return resource to pool
     */
    public function returnToPool(string $id): bool
    {
        if (!isset($this->resources[$id])) {
            return false;
        }

        $resource = $this->resources[$id];
        $poolKey = $resource['type'] . '_pool';

        if (!isset($this->pools[$poolKey])) {
            $this->pools[$poolKey] = [];
        }

        // Check if resource is still valid
        if ($this->isResourceValid($resource)) {
            $this->pools[$poolKey][] = $id;
            return true;
        } else {
            return $this->release($id);
        }
    }

    /**
     * Get resource statistics
     */
    public function getStats(): array
    {
        $stats = [
            'total_resources' => count($this->resources),
            'by_type' => [],
            'memory_usage' => 0,
            'pools' => []
        ];

        foreach ($this->resources as $id => $resource) {
            $type = $resource['type'];

            if (!isset($stats['by_type'][$type])) {
                $stats['by_type'][$type] = [
                    'count' => 0,
                    'memory_usage' => 0,
                    'total_uses' => 0
                ];
            }

            $stats['by_type'][$type]['count']++;
            $stats['by_type'][$type]['memory_usage'] += $resource['memory_usage'];
            $stats['by_type'][$type]['total_uses'] += $resource['use_count'];
            $stats['memory_usage'] += $resource['memory_usage'];
        }

        foreach ($this->pools as $poolKey => $pool) {
            $stats['pools'][$poolKey] = count($pool);
        }

        $stats['metrics'] = $this->metrics;

        return $stats;
    }

    /**
     * Cleanup expired resources
     */
    public function cleanupExpired(): int
    {
        $cleaned = 0;
        $now = microtime(true);
        $timeout = $this->limits['resource_timeout'];

        foreach ($this->resources as $id => $resource) {
            if ($now - $resource['last_used'] > $timeout) {
                $this->release($id);
                $cleaned++;
            }
        }

        return $cleaned;
    }

    /**
     * Cleanup all resources
     */
    public function cleanup(): void
    {
        foreach (array_keys($this->resources) as $id) {
            $this->release($id);
        }

        $this->pools = [];
        $this->resources = [];
    }

    /**
     * Monitor resource usage
     */
    public function monitor(): array
    {
        $warnings = [];
        $stats = $this->getStats();

        // Check memory usage
        if ($stats['memory_usage'] > $this->limits['max_memory_per_resource'] * count($this->resources)) {
            $warnings[] = [
                'type' => 'memory',
                'message' => 'High memory usage detected',
                'current' => $stats['memory_usage'],
                'limit' => $this->limits['max_memory_per_resource'] * count($this->resources)
            ];
        }

        // Check resource counts
        foreach ($stats['by_type'] as $type => $typeStats) {
            $limitKey = "max_{$type}_connections";
            if (isset($this->limits[$limitKey]) && $typeStats['count'] > $this->limits[$limitKey]) {
                $warnings[] = [
                    'type' => 'count',
                    'resource_type' => $type,
                    'message' => "Too many {$type} resources",
                    'current' => $typeStats['count'],
                    'limit' => $this->limits[$limitKey]
                ];
            }
        }

        return [
            'stats' => $stats,
            'warnings' => $warnings,
            'recommendations' => $this->getRecommendations($stats, $warnings)
        ];
    }

    /**
     * Optimize resource usage
     */
    public function optimize(): array
    {
        $optimizations = [];

        // Cleanup expired resources
        $cleaned = $this->cleanupExpired();
        if ($cleaned > 0) {
            $optimizations['expired_cleaned'] = $cleaned;
        }

        // Optimize pools
        $poolOptimizations = $this->optimizePools();
        if (!empty($poolOptimizations)) {
            $optimizations['pools'] = $poolOptimizations;
        }

        // Defragment resource arrays
        $this->defragmentArrays();
        $optimizations['arrays_defragmented'] = true;

        return $optimizations;
    }

    /**
     * Check resource limits
     */
    private function checkLimits(string $type): bool
    {
        $limitKey = "max_{$type}_connections";

        if (!isset($this->limits[$limitKey])) {
            return true; // No limit set
        }

        $currentCount = 0;
        foreach ($this->resources as $resource) {
            if ($resource['type'] === $type) {
                $currentCount++;
            }
        }

        return $currentCount < $this->limits[$limitKey];
    }

    /**
     * Estimate memory usage of resource
     */
    private function estimateMemoryUsage($resource): int
    {
        if (is_string($resource)) {
            return strlen($resource);
        } elseif (is_array($resource)) {
            return strlen(serialize($resource));
        } elseif (is_object($resource)) {
            return 1024; // Estimate for objects
        } else {
            return 64; // Default estimate
        }
    }

    /**
     * Close resource properly
     */
    private function closeResource($resource, string $type): void
    {
        try {
            switch ($type) {
                case 'file':
                    if (is_resource($resource)) {
                        fclose($resource);
                    }
                    break;

                case 'db_connection':
                    if (method_exists($resource, 'close')) {
                        $resource->close();
                    }
                    break;

                case 'curl':
                    if (is_resource($resource)) {
                        curl_close($resource);
                    }
                    break;

                default:
                    // Try generic close methods
                    if (is_object($resource)) {
                        if (method_exists($resource, 'close')) {
                            $resource->close();
                        } elseif (method_exists($resource, 'disconnect')) {
                            $resource->disconnect();
                        }
                    }
                    break;
            }
        } catch (\Exception $e) {
            error_log("Error closing resource: " . $e->getMessage());
        }
    }

    /**
     * Check if resource is still valid
     */
    private function isResourceValid(array $resourceData): bool
    {
        $resource = $resourceData['resource'];
        $type = $resourceData['type'];

        switch ($type) {
            case 'file':
                return is_resource($resource);

            case 'db_connection':
                if (method_exists($resource, 'ping')) {
                    return $resource->ping();
                }
                return true;

            case 'curl':
                return is_resource($resource);

            default:
                return true;
        }
    }

    /**
     * Initialize resource pools
     */
    private function initializePools(): void
    {
        $this->pools = [
            'db_connection_pool' => [],
            'file_pool' => [],
            'curl_pool' => [],
            'api_connection_pool' => []
        ];
    }

    /**
     * Update metrics
     */
    private function updateMetrics(string $type, string $action): void
    {
        $key = "{$type}_{$action}";

        if (!isset($this->metrics[$key])) {
            $this->metrics[$key] = 0;
        }

        $this->metrics[$key]++;
    }

    /**
     * Get recommendations
     */
    private function getRecommendations(array $stats, array $warnings): array
    {
        $recommendations = [];

        if (!empty($warnings)) {
            $recommendations[] = 'Consider increasing resource limits or optimizing usage';
        }

        if ($stats['total_resources'] > 100) {
            $recommendations[] = 'High number of resources - consider implementing resource pooling';
        }

        foreach ($stats['by_type'] as $type => $typeStats) {
            if ($typeStats['total_uses'] / max($typeStats['count'], 1) < 2) {
                $recommendations[] = "Low usage efficiency for {$type} resources";
            }
        }

        return $recommendations;
    }

    /**
     * Optimize pools
     */
    private function optimizePools(): array
    {
        $optimizations = [];

        foreach ($this->pools as $poolKey => $pool) {
            $before = count($pool);

            // Remove invalid resources from pool
            $this->pools[$poolKey] = array_filter($pool, function ($id) {
                return isset($this->resources[$id]) &&
                       $this->isResourceValid($this->resources[$id]);
            });

            $after = count($this->pools[$poolKey]);

            if ($before !== $after) {
                $optimizations[$poolKey] = [
                    'before' => $before,
                    'after' => $after,
                    'removed' => $before - $after
                ];
            }
        }

        return $optimizations;
    }

    /**
     * Defragment arrays
     */
    private function defragmentArrays(): void
    {
        // Reindex arrays to remove gaps
        $this->resources = array_values($this->resources);

        foreach ($this->pools as $key => $pool) {
            $this->pools[$key] = array_values($pool);
        }
    }
}
