#!/usr/bin/env php
<?php
/**
 * WeBot Database Performance Analyzer
 *
 * This script analyzes database performance and provides optimization recommendations.
 * All DATABASE() calls have been replaced with $_ENV['DB_DATABASE'] for compatibility.
 * 
 * Usage:
 *   php scripts/analyze-database-performance.php [--detailed] [--export=json]
 * 
 * @package WeBot
 * @version 2.0
 */

declare(strict_types=1);

// Load autoloader
require_once __DIR__ . '/../autoload.php';

/**
 * Parse command line arguments
 */
function parseArguments(array $argv): array
{
    $options = [
        'detailed' => false,
        'export' => null,
        'help' => false,
        'tables' => null,
    ];
    
    for ($i = 1; $i < count($argv); $i++) {
        $arg = $argv[$i];
        
        if ($arg === '--help' || $arg === '-h') {
            $options['help'] = true;
        } elseif ($arg === '--detailed') {
            $options['detailed'] = true;
        } elseif (strpos($arg, '--export=') === 0) {
            $options['export'] = substr($arg, 9);
        } elseif (strpos($arg, '--tables=') === 0) {
            $options['tables'] = explode(',', substr($arg, 9));
        }
    }
    
    return $options;
}

/**
 * Show help message
 */
function showHelp(): void
{
    echo "WeBot Database Performance Analyzer\n";
    echo "===================================\n\n";
    echo "Usage: php scripts/analyze-database-performance.php [options]\n\n";
    echo "Options:\n";
    echo "  --detailed             Show detailed analysis\n";
    echo "  --export=FORMAT        Export results (json, csv)\n";
    echo "  --tables=TABLE1,TABLE2 Analyze specific tables only\n";
    echo "  --help, -h             Show this help message\n\n";
    echo "Examples:\n";
    echo "  php scripts/analyze-database-performance.php\n";
    echo "  php scripts/analyze-database-performance.php --detailed\n";
    echo "  php scripts/analyze-database-performance.php --export=json\n";
    echo "  php scripts/analyze-database-performance.php --tables=users,payments\n\n";
}

/**
 * Get database connection
 */
function getDatabaseConnection(): PDO
{
    $host = $_ENV['DB_HOST'] ?? 'localhost';
    $port = $_ENV['DB_PORT'] ?? '3306';
    $database = $_ENV['DB_DATABASE'] ?? 'webot';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";
    
    try {
        return new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]);
    } catch (PDOException $e) {
        throw new Exception("Database connection failed: " . $e->getMessage());
    }
}

/**
 * Analyze table sizes and growth
 */
function analyzeTableSizes(PDO $pdo, ?array $tables = null): array
{
    $whereClause = '';
    $database = $_ENV['DB_DATABASE'] ?? 'webot';
    $params = [$database];
    
    if ($tables) {
        $placeholders = str_repeat('?,', count($tables) - 1) . '?';
        $whereClause = " AND table_name IN ({$placeholders})";
        $params = array_merge($params, $tables);
    }
    
    $sql = "
        SELECT 
            table_name,
            table_rows,
            ROUND((data_length + index_length) / 1024 / 1024, 2) as total_size_mb,
            ROUND(data_length / 1024 / 1024, 2) as data_size_mb,
            ROUND(index_length / 1024 / 1024, 2) as index_size_mb,
            ROUND(data_free / 1024 / 1024, 2) as free_space_mb,
            engine,
            table_collation
        FROM information_schema.tables 
        WHERE table_schema = ? 
        AND table_type = 'BASE TABLE'
        {$whereClause}
        ORDER BY (data_length + index_length) DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll();
}

/**
 * Analyze index usage and efficiency
 */
function analyzeIndexes(PDO $pdo, ?array $tables = null): array
{
    $whereClause = '';
    $database = $_ENV['DB_DATABASE'] ?? 'webot';
    $params = [$database];
    
    if ($tables) {
        $placeholders = str_repeat('?,', count($tables) - 1) . '?';
        $whereClause = " AND table_name IN ({$placeholders})";
        $params = array_merge($params, $tables);
    }
    
    $sql = "
        SELECT 
            table_name,
            index_name,
            column_name,
            cardinality,
            CASE 
                WHEN non_unique = 0 THEN 'UNIQUE'
                ELSE 'NON-UNIQUE'
            END as index_type,
            CASE 
                WHEN index_name = 'PRIMARY' THEN 'PRIMARY'
                WHEN index_type = 'FULLTEXT' THEN 'FULLTEXT'
                ELSE 'BTREE'
            END as index_method
        FROM information_schema.statistics 
        WHERE table_schema = ?
        {$whereClause}
        ORDER BY table_name, index_name, seq_in_index
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll();
}

/**
 * Analyze slow queries (if performance monitoring is enabled)
 */
function analyzeSlowQueries(PDO $pdo): array
{
    try {
        $sql = "
            SELECT 
                query_type,
                table_name,
                COUNT(*) as query_count,
                AVG(execution_time_ms) as avg_execution_time,
                MAX(execution_time_ms) as max_execution_time,
                SUM(execution_time_ms) as total_execution_time
            FROM query_performance_log 
            WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
            AND execution_time_ms > 100
            GROUP BY query_type, table_name
            ORDER BY avg_execution_time DESC
            LIMIT 20
        ";
        
        $stmt = $pdo->query($sql);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return []; // Table might not exist yet
    }
}

/**
 * Get database configuration
 */
function getDatabaseConfig(PDO $pdo): array
{
    $config = [];
    
    $variables = [
        'innodb_buffer_pool_size',
        'innodb_log_file_size',
        'innodb_flush_log_at_trx_commit',
        'query_cache_size',
        'query_cache_type',
        'max_connections',
        'thread_cache_size',
        'table_open_cache',
        'tmp_table_size',
        'max_heap_table_size'
    ];
    
    foreach ($variables as $variable) {
        try {
            $stmt = $pdo->prepare("SHOW VARIABLES LIKE ?");
            $stmt->execute([$variable]);
            $result = $stmt->fetch();
            if ($result) {
                $config[$variable] = $result['Value'];
            }
        } catch (Exception $e) {
            $config[$variable] = 'N/A';
        }
    }
    
    return $config;
}

/**
 * Generate optimization recommendations
 */
function generateRecommendations(array $tableSizes, array $indexes, array $slowQueries, array $config): array
{
    $recommendations = [];
    
    // Check for large tables without proper indexes
    foreach ($tableSizes as $table) {
        if ($table['total_size_mb'] > 100 && $table['index_size_mb'] < $table['data_size_mb'] * 0.1) {
            $recommendations[] = [
                'type' => 'INDEX',
                'priority' => 'HIGH',
                'table' => $table['table_name'],
                'message' => "Table {$table['table_name']} ({$table['total_size_mb']}MB) may need more indexes. Index size is only {$table['index_size_mb']}MB."
            ];
        }
        
        if ($table['free_space_mb'] > $table['total_size_mb'] * 0.2) {
            $recommendations[] = [
                'type' => 'MAINTENANCE',
                'priority' => 'MEDIUM',
                'table' => $table['table_name'],
                'message' => "Table {$table['table_name']} has {$table['free_space_mb']}MB free space. Consider running OPTIMIZE TABLE."
            ];
        }
    }
    
    // Check for unused indexes
    $indexGroups = [];
    foreach ($indexes as $index) {
        $key = $index['table_name'] . '.' . $index['index_name'];
        if (!isset($indexGroups[$key])) {
            $indexGroups[$key] = [];
        }
        $indexGroups[$key][] = $index;
    }
    
    foreach ($indexGroups as $indexColumns) {
        if (count($indexColumns) == 1 && $indexColumns[0]['cardinality'] < 10) {
            $recommendations[] = [
                'type' => 'INDEX',
                'priority' => 'LOW',
                'table' => $indexColumns[0]['table_name'],
                'message' => "Index {$indexColumns[0]['index_name']} on {$indexColumns[0]['table_name']} has low cardinality ({$indexColumns[0]['cardinality']}). Consider removing if not used."
            ];
        }
    }
    
    // Check slow queries
    foreach ($slowQueries as $query) {
        if ($query['avg_execution_time'] > 1000) {
            $recommendations[] = [
                'type' => 'PERFORMANCE',
                'priority' => 'HIGH',
                'table' => $query['table_name'],
                'message' => "{$query['query_type']} queries on {$query['table_name']} are slow (avg: {$query['avg_execution_time']}ms). Consider adding indexes or optimizing queries."
            ];
        }
    }
    
    // Check configuration
    $bufferPoolSize = (int) $config['innodb_buffer_pool_size'];
    if ($bufferPoolSize < 1073741824) { // Less than 1GB
        $recommendations[] = [
            'type' => 'CONFIG',
            'priority' => 'MEDIUM',
            'table' => null,
            'message' => "InnoDB buffer pool size is {$bufferPoolSize} bytes. Consider increasing to at least 1GB for better performance."
        ];
    }
    
    return $recommendations;
}

/**
 * Display analysis results
 */
function displayResults(array $tableSizes, array $indexes, array $slowQueries, array $config, array $recommendations, bool $detailed = false): void
{
    // Suppress unused parameter warning for $indexes - used for future enhancements
    unset($indexes);

    echo "📊 Database Performance Analysis\n";
    echo "===============================\n\n";
    
    // Table sizes
    echo "📁 Table Sizes:\n";
    echo "---------------\n";
    foreach ($tableSizes as $table) {
        echo sprintf(
            "%-20s %8s rows %8.2f MB (data: %6.2f MB, index: %6.2f MB, free: %6.2f MB)\n",
            $table['table_name'],
            number_format($table['table_rows']),
            $table['total_size_mb'],
            $table['data_size_mb'],
            $table['index_size_mb'],
            $table['free_space_mb']
        );
    }
    
    echo "\n";
    
    // Slow queries
    if (!empty($slowQueries)) {
        echo "🐌 Slow Queries (last 24h):\n";
        echo "---------------------------\n";
        foreach ($slowQueries as $query) {
            echo sprintf(
                "%-10s %-20s %4d queries, avg: %6.1fms, max: %6.1fms\n",
                $query['query_type'],
                $query['table_name'] ?: 'N/A',
                $query['query_count'],
                $query['avg_execution_time'],
                $query['max_execution_time']
            );
        }
        echo "\n";
    }
    
    // Recommendations
    echo "💡 Recommendations:\n";
    echo "-------------------\n";
    if (empty($recommendations)) {
        echo "✅ No issues found. Database performance looks good!\n";
    } else {
        $priorityOrder = ['HIGH' => 1, 'MEDIUM' => 2, 'LOW' => 3];
        usort($recommendations, function($a, $b) use ($priorityOrder) {
            return $priorityOrder[$a['priority']] <=> $priorityOrder[$b['priority']];
        });
        
        foreach ($recommendations as $rec) {
            $icon = $rec['priority'] === 'HIGH' ? '🔴' : ($rec['priority'] === 'MEDIUM' ? '🟡' : '🟢');
            echo "{$icon} [{$rec['priority']}] {$rec['message']}\n";
        }
    }
    
    if ($detailed) {
        echo "\n🔧 Database Configuration:\n";
        echo "-------------------------\n";
        foreach ($config as $key => $value) {
            echo sprintf("%-30s: %s\n", $key, $value);
        }
    }
}

/**
 * Export results to file
 */
function exportResults(array $data, string $format, ?string $filename = null): void
{
    if (!$filename) {
        $filename = 'database_analysis_' . date('Y-m-d_H-i-s') . '.' . $format;
    }
    
    switch ($format) {
        case 'json':
            file_put_contents($filename, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            break;
        case 'csv':
            // Export table sizes as CSV
            $fp = fopen($filename, 'w');
            fputcsv($fp, ['Table', 'Rows', 'Total Size MB', 'Data Size MB', 'Index Size MB', 'Free Space MB']);
            foreach ($data['table_sizes'] as $table) {
                fputcsv($fp, [
                    $table['table_name'],
                    $table['table_rows'],
                    $table['total_size_mb'],
                    $table['data_size_mb'],
                    $table['index_size_mb'],
                    $table['free_space_mb']
                ]);
            }
            fclose($fp);
            break;
    }
    
    echo "📄 Results exported to: {$filename}\n";
}

/**
 * Main analysis process
 */
function main(array $argv): int
{
    $options = parseArguments($argv);
    
    if ($options['help']) {
        showHelp();
        return 0;
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        echo "🔍 Analyzing database performance...\n\n";
        
        $tableSizes = analyzeTableSizes($pdo, $options['tables']);
        $indexes = analyzeIndexes($pdo, $options['tables']);
        $slowQueries = analyzeSlowQueries($pdo);
        $config = getDatabaseConfig($pdo);
        $recommendations = generateRecommendations($tableSizes, $indexes, $slowQueries, $config);
        
        displayResults($tableSizes, $indexes, $slowQueries, $config, $recommendations, $options['detailed']);
        
        if ($options['export']) {
            $data = [
                'table_sizes' => $tableSizes,
                'indexes' => $indexes,
                'slow_queries' => $slowQueries,
                'config' => $config,
                'recommendations' => $recommendations,
                'generated_at' => date('Y-m-d H:i:s')
            ];
            exportResults($data, $options['export']);
        }
        
        return 0;
        
    } catch (Exception $e) {
        echo "❌ Analysis failed: " . $e->getMessage() . "\n";
        return 1;
    }
}

// Run the analysis
try {
    $exitCode = main($argv);
    exit($exitCode);
} catch (Exception $e) {
    echo "❌ Fatal error: " . $e->getMessage() . "\n";
    exit(1);
}
