<?php

declare(strict_types=1);

namespace WeBot\Exceptions;

use Exception;

/**
 * Cache Exception
 *
 * Exception thrown for cache-related errors.
 *
 * @package WeBot\Exceptions
 * @version 2.0
 */
class CacheException extends Exception
{
    private ?string $cacheKey = null;
    private ?string $operation = null;

    public function __construct(
        string $message = "",
        int $code = 0,
        ?Exception $previous = null,
        ?string $cacheKey = null,
        ?string $operation = null
    ) {
        parent::__construct($message, $code, $previous);
        $this->cacheKey = $cacheKey;
        $this->operation = $operation;
    }

    /**
     * Get cache key that caused the exception
     */
    public function getCacheKey(): ?string
    {
        return $this->cacheKey;
    }

    /**
     * Get cache operation that failed
     */
    public function getOperation(): ?string
    {
        return $this->operation;
    }

    /**
     * Get detailed error information
     */
    public function getDetailedInfo(): array
    {
        return [
            'message' => $this->getMessage(),
            'code' => $this->getCode(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'cache_key' => $this->cacheKey,
            'operation' => $this->operation,
            'trace' => $this->getTraceAsString()
        ];
    }
}
