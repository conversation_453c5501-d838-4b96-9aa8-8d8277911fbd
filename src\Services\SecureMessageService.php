<?php

declare(strict_types=1);

namespace WeBot\Services;

use WeBot\Core\Database;
use WeBot\Utils\Logger;
use WeBot\Exceptions\WeBotException;

/**
 * Secure Message Service
 *
 * Secure replacement for legacy settings/messagewebot.php
 * Handles currency rates and message queue with proper security
 *
 * @package WeBot\Services
 * @version 2.0
 */
class SecureMessageService
{
    private Database $database;
    private Logger $logger;
    private array $config;

    public function __construct(Database $database, array $config = [])
    {
        $this->database = $database;
        $this->logger = Logger::getInstance();
        $this->config = array_merge([
            'rate_limit_interval' => 3600, // 1 hour
            'currency_api_url' => 'https://api.pooleno.ir/v1/currency/short-name/trx?type=buy',
            'currency_api_timeout' => 10,
            'max_send_batch' => 100,
            'retry_attempts' => 3,
        ], $config);
    }

    /**
     * Secure currency rate update (replaces legacy curl_get_file_contents)
     */
    public function updateCurrencyRates(): array
    {
        try {
            $botState = $this->getBotState();
            $rateLimit = $botState['rateLimit'] ?? 0;

            // Check rate limit
            if (time() <= $rateLimit) {
                return [
                    'success' => false,
                    'message' => 'Rate limit active',
                    'next_update' => $rateLimit
                ];
            }

            // Fetch rates with security measures
            $rates = $this->fetchCurrencyRatesSecurely();

            if ($rates === null) {
                return [
                    'success' => false,
                    'message' => 'Failed to fetch currency rates'
                ];
            }

            // Validate and sanitize rates
            $usdRate = $this->validateRate($rates['priceUsdt'] ?? 0);
            $trxRate = $this->validateRate(($rates['priceFiat'] ?? 0) / 10);

            // Update bot state securely
            $botState['USDRate'] = $usdRate;
            $botState['TRXRate'] = $trxRate;
            $botState['rateLimit'] = time() + $this->config['rate_limit_interval'];
            $botState['last_update'] = date('Y-m-d H:i:s');

            $this->saveBotStateSecurely($botState);

            $this->logger->info('Currency rates updated securely', [
                'usd_rate' => $usdRate,
                'trx_rate' => $trxRate
            ]);

            return [
                'success' => true,
                'rates' => [
                    'usd' => $usdRate,
                    'trx' => $trxRate
                ],
                'next_update' => $botState['rateLimit']
            ];
        } catch (\Exception $e) {
            $this->logger->error('Currency rate update failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Internal error occurred'
            ];
        }
    }

    /**
     * Secure API call with proper validation
     */
    private function fetchCurrencyRatesSecurely(): ?array
    {
        // Validate URL
        if (!filter_var($this->config['currency_api_url'], FILTER_VALIDATE_URL)) {
            $this->logger->error('Invalid currency API URL');
            return null;
        }

        // Create secure context
        $context = stream_context_create([
            'http' => [
                'timeout' => $this->config['currency_api_timeout'],
                'user_agent' => 'WeBot/2.0 (Secure)',
                'method' => 'GET',
                'header' => [
                    'Accept: application/json',
                    'Content-Type: application/json',
                    'Cache-Control: no-cache'
                ],
                'ignore_errors' => true
            ],
            'ssl' => [
                'verify_peer' => true,
                'verify_peer_name' => true,
                'allow_self_signed' => false
            ]
        ]);

        $response = @file_get_contents($this->config['currency_api_url'], false, $context);

        if ($response === false) {
            $this->logger->warning('Failed to fetch currency rates from API');
            return null;
        }

        // Validate response size
        if (strlen($response) > 10240) { // 10KB max
            $this->logger->warning('Currency API response too large');
            return null;
        }

        $data = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->logger->warning('Invalid JSON response from currency API', [
                'json_error' => json_last_error_msg()
            ]);
            return null;
        }

        // Validate response structure and data types
        if (
            !is_array($data) ||
            !isset($data['priceUsdt']) ||
            !isset($data['priceFiat']) ||
            !is_numeric($data['priceUsdt']) ||
            !is_numeric($data['priceFiat'])
        ) {
            $this->logger->warning('Invalid currency API response structure');
            return null;
        }

        return $data;
    }

    /**
     * Validate and sanitize rate values
     */
    private function validateRate(float $rate): float
    {
        // Ensure rate is positive and within reasonable bounds
        if ($rate <= 0 || $rate > 1000000) {
            throw new WeBotException('Invalid currency rate value');
        }

        return round($rate, 2);
    }

    /**
     * Get bot state with proper error handling
     */
    private function getBotState(): array
    {
        try {
            $result = $this->database->query(
                "SELECT `value` FROM `setting` WHERE `type` = ? LIMIT 1",
                ['BOT_STATES']
            );

            if (empty($result)) {
                return $this->getDefaultBotState();
            }

            $state = json_decode($result[0]['value'], true);

            if (json_last_error() !== JSON_ERROR_NONE || !is_array($state)) {
                $this->logger->warning('Invalid bot state JSON, using defaults');
                return $this->getDefaultBotState();
            }

            // Validate state structure
            return $this->validateBotState($state);
        } catch (\Exception $e) {
            $this->logger->error('Failed to get bot state', [
                'error' => $e->getMessage()
            ]);

            return $this->getDefaultBotState();
        }
    }

    /**
     * Save bot state with transaction safety
     */
    private function saveBotStateSecurely(array $state): void
    {
        // Validate state before saving
        $validatedState = $this->validateBotState($state);

        $stateJson = json_encode($validatedState, JSON_UNESCAPED_UNICODE);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new WeBotException('Failed to encode bot state to JSON: ' . json_last_error_msg());
        }

        // Use transaction for atomic operation
        $this->database->beginTransaction();

        try {
            // Check if record exists
            $exists = $this->database->query(
                "SELECT COUNT(*) as count FROM `setting` WHERE `type` = ?",
                ['BOT_STATES']
            );

            if ($exists[0]['count'] > 0) {
                // Update existing record
                $this->database->execute(
                    "UPDATE `setting` SET `value` = ?, `updated_at` = NOW() WHERE `type` = ?",
                    [$stateJson, 'BOT_STATES']
                );
            } else {
                // Insert new record
                $this->database->execute(
                    "INSERT INTO `setting` (`type`, `value`, `created_at`, `updated_at`) VALUES (?, ?, NOW(), NOW())",
                    ['BOT_STATES', $stateJson]
                );
            }

            $this->database->commit();
        } catch (\Exception $e) {
            $this->database->rollback();
            throw new WeBotException('Failed to save bot state: ' . $e->getMessage());
        }
    }

    /**
     * Validate bot state structure
     */
    private function validateBotState(array $state): array
    {
        $defaults = $this->getDefaultBotState();

        // Ensure all required fields exist with proper types
        $validated = [];

        foreach ($defaults as $key => $defaultValue) {
            if (isset($state[$key])) {
                $validated[$key] = $state[$key];
            } else {
                $validated[$key] = $defaultValue;
            }
        }

        // Validate specific fields
        if (!is_numeric($validated['USDRate']) || $validated['USDRate'] <= 0) {
            $validated['USDRate'] = $defaults['USDRate'];
        }

        if (!is_numeric($validated['TRXRate']) || $validated['TRXRate'] <= 0) {
            $validated['TRXRate'] = $defaults['TRXRate'];
        }

        if (!is_numeric($validated['rateLimit']) || $validated['rateLimit'] < 0) {
            $validated['rateLimit'] = $defaults['rateLimit'];
        }

        return $validated;
    }

    /**
     * Get default bot state
     */
    private function getDefaultBotState(): array
    {
        return [
            'USDRate' => 1.0,
            'TRXRate' => 0.1,
            'rateLimit' => 0,
            'last_update' => null,
            'version' => '2.0',
            'security_version' => '1.0'
        ];
    }

    /**
     * Secure message queue processing (replaces legacy send_list logic)
     */
    public function processPendingMessages(int $limit = null): array
    {
        $limit = $limit ?? $this->config['max_send_batch'];

        try {
            $messages = $this->database->query(
                "SELECT * FROM `send_list` WHERE `state` = 1 ORDER BY `priority` DESC, `created_at` ASC LIMIT ?",
                [$limit]
            );

            $processed = [];

            foreach ($messages as $message) {
                $result = $this->processMessage($message);
                $processed[] = $result;
            }

            return $processed;
        } catch (\Exception $e) {
            $this->logger->error('Failed to process pending messages', [
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }

    /**
     * Process individual message with security validation
     */
    private function processMessage(array $message): array
    {
        try {
            // Validate message data
            if (!isset($message['id'], $message['chat_id'], $message['message'])) {
                throw new WeBotException('Invalid message structure');
            }

            // Sanitize message content
            $sanitizedMessage = $this->sanitizeMessage($message['message']);

            // Mark as processed
            $this->database->execute(
                "UPDATE `send_list` SET `state` = 2, `processed_at` = NOW() WHERE `id` = ?",
                [$message['id']]
            );

            return [
                'id' => $message['id'],
                'chat_id' => $message['chat_id'],
                'message' => $sanitizedMessage,
                'status' => 'processed'
            ];
        } catch (\Exception $e) {
            $this->logger->error('Failed to process message', [
                'message_id' => $message['id'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);

            // Mark as failed
            if (isset($message['id'])) {
                $this->database->execute(
                    "UPDATE `send_list` SET `state` = 3, `error` = ?, `failed_at` = NOW() WHERE `id` = ?",
                    [$e->getMessage(), $message['id']]
                );
            }

            return [
                'id' => $message['id'] ?? null,
                'status' => 'failed',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Sanitize message content
     */
    private function sanitizeMessage(string $message): string
    {
        // Remove potential XSS and injection attempts
        $message = strip_tags($message);
        $message = htmlspecialchars($message, ENT_QUOTES, 'UTF-8');

        // Limit message length
        if (strlen($message) > 4096) {
            $message = substr($message, 0, 4093) . '...';
        }

        return $message;
    }
}
