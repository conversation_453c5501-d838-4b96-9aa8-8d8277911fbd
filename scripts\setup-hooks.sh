#!/bin/bash

# WeBot Git Hooks Setup Script
# This script sets up git hooks for code quality checks

set -e

echo "🔧 Setting up WeBot Git Hooks..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in a git repository
if [ ! -d ".git" ]; then
    print_error "This is not a git repository. Please run this script from the project root."
    exit 1
fi

# Create .git/hooks directory if it doesn't exist
if [ ! -d ".git/hooks" ]; then
    mkdir -p .git/hooks
    print_status "Created .git/hooks directory"
fi

# Copy pre-commit hook
if [ -f ".githooks/pre-commit" ]; then
    cp .githooks/pre-commit .git/hooks/pre-commit
    chmod +x .git/hooks/pre-commit
    print_success "Pre-commit hook installed"
else
    print_error "Pre-commit hook source file not found at .githooks/pre-commit"
    exit 1
fi

# Create commit-msg hook for conventional commits
cat > .git/hooks/commit-msg << 'EOF'
#!/bin/bash

# WeBot Commit Message Hook
# Enforces conventional commit format

commit_regex='^(feat|fix|docs|style|refactor|test|chore|perf|ci|build|revert)(\(.+\))?: .{1,50}'

error_msg="Invalid commit message format!

Commit message should follow conventional commits format:
<type>[optional scope]: <description>

Types:
- feat: A new feature
- fix: A bug fix
- docs: Documentation only changes
- style: Changes that do not affect the meaning of the code
- refactor: A code change that neither fixes a bug nor adds a feature
- test: Adding missing tests or correcting existing tests
- chore: Changes to the build process or auxiliary tools
- perf: A code change that improves performance
- ci: Changes to CI configuration files and scripts
- build: Changes that affect the build system or external dependencies
- revert: Reverts a previous commit

Examples:
- feat: add user authentication
- fix(auth): resolve login validation issue
- docs: update API documentation
- style: format code according to PSR-12
- refactor(payment): simplify payment processing logic"

if ! grep -qE "$commit_regex" "$1"; then
    echo "$error_msg" >&2
    exit 1
fi
EOF

chmod +x .git/hooks/commit-msg
print_success "Commit message hook installed"

# Create pre-push hook
cat > .git/hooks/pre-push << 'EOF'
#!/bin/bash

# WeBot Pre-push Hook
# Runs comprehensive tests before pushing

echo "🚀 Running pre-push checks..."

# Run full test suite
if [ -f "vendor/bin/phpunit" ]; then
    echo "Running full test suite..."
    vendor/bin/phpunit
    if [ $? -ne 0 ]; then
        echo "❌ Tests failed. Push aborted."
        exit 1
    fi
    echo "✅ All tests passed"
fi

# Run static analysis on entire codebase
if [ -f "vendor/bin/phpstan" ]; then
    echo "Running static analysis..."
    vendor/bin/phpstan analyse --level=8 src/
    if [ $? -ne 0 ]; then
        echo "❌ Static analysis failed. Push aborted."
        exit 1
    fi
    echo "✅ Static analysis passed"
fi

echo "✅ All pre-push checks passed!"
EOF

chmod +x .git/hooks/pre-push
print_success "Pre-push hook installed"

# Set up git config for hooks
git config core.hooksPath .git/hooks

print_success "Git hooks setup completed! 🎉"
print_status "Hooks installed:"
print_status "  - pre-commit: Code quality checks"
print_status "  - commit-msg: Conventional commit format validation"
print_status "  - pre-push: Full test suite and static analysis"

print_warning "Note: Make sure to run 'composer install' to have all required tools available."

echo ""
echo "🔍 Testing pre-commit hook..."
if .git/hooks/pre-commit; then
    print_success "Pre-commit hook test passed!"
else
    print_warning "Pre-commit hook test failed. Please check your setup."
fi

echo ""
print_status "Setup complete! Your commits will now be automatically checked for quality."
print_status "To bypass hooks temporarily, use: git commit --no-verify"
