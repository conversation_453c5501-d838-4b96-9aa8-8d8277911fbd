<?php

declare(strict_types=1);

namespace WeBot\Events;

/**
 * Service Events
 *
 * Domain events related to VPN service operations.
 *
 * @package WeBot\Events
 * @version 2.0
 */

/**
 * Service Created Event
 */
class ServiceCreatedEvent extends BaseEvent
{
    public function __construct(int $serviceId, int $userId, array $serviceData)
    {
        parent::__construct([
            'service_id' => $serviceId,
            'user_id' => $userId,
            'service_data' => $serviceData,
            'server_id' => $serviceData['server_id'] ?? null,
            'plan_id' => $serviceData['plan_id'] ?? null,
            'volume' => $serviceData['volume'] ?? null,
            'expires_at' => $serviceData['expires_at'] ?? null
        ]);
    }

    public function getServiceId(): int
    {
        return $this->get('service_id');
    }

    public function getUserId(): int
    {
        return $this->get('user_id');
    }

    public function getServiceData(): array
    {
        return $this->get('service_data', []);
    }

    public function getServerId(): ?int
    {
        return $this->get('server_id');
    }

    public function getPlanId(): ?int
    {
        return $this->get('plan_id');
    }

    public function getVolume(): ?int
    {
        return $this->get('volume');
    }

    public function getExpiresAt(): ?string
    {
        return $this->get('expires_at');
    }
}

/**
 * Service Renewed Event
 */
class ServiceRenewedEvent extends BaseEvent
{
    public function __construct(int $serviceId, int $userId, int $days, string $oldExpiresAt, string $newExpiresAt)
    {
        parent::__construct([
            'service_id' => $serviceId,
            'user_id' => $userId,
            'days' => $days,
            'old_expires_at' => $oldExpiresAt,
            'new_expires_at' => $newExpiresAt,
            'renewed_at' => date('Y-m-d H:i:s')
        ]);
    }

    public function getServiceId(): int
    {
        return $this->get('service_id');
    }

    public function getUserId(): int
    {
        return $this->get('user_id');
    }

    public function getDays(): int
    {
        return $this->get('days');
    }

    public function getOldExpiresAt(): string
    {
        return $this->get('old_expires_at');
    }

    public function getNewExpiresAt(): string
    {
        return $this->get('new_expires_at');
    }

    public function getRenewedAt(): string
    {
        return $this->get('renewed_at');
    }
}

/**
 * Service Suspended Event
 */
class ServiceSuspendedEvent extends BaseEvent
{
    public function __construct(int $serviceId, int $userId, string $reason)
    {
        parent::__construct([
            'service_id' => $serviceId,
            'user_id' => $userId,
            'reason' => $reason,
            'suspended_at' => date('Y-m-d H:i:s')
        ]);
    }

    public function getServiceId(): int
    {
        return $this->get('service_id');
    }

    public function getUserId(): int
    {
        return $this->get('user_id');
    }

    public function getReason(): string
    {
        return $this->get('reason');
    }

    public function getSuspendedAt(): string
    {
        return $this->get('suspended_at');
    }
}

/**
 * Service Activated Event
 */
class ServiceActivatedEvent extends BaseEvent
{
    public function __construct(int $serviceId, int $userId)
    {
        parent::__construct([
            'service_id' => $serviceId,
            'user_id' => $userId,
            'activated_at' => date('Y-m-d H:i:s')
        ]);
    }

    public function getServiceId(): int
    {
        return $this->get('service_id');
    }

    public function getUserId(): int
    {
        return $this->get('user_id');
    }

    public function getActivatedAt(): string
    {
        return $this->get('activated_at');
    }
}

/**
 * Service Expired Event
 */
class ServiceExpiredEvent extends BaseEvent
{
    public function __construct(int $serviceId, int $userId, string $expiredAt)
    {
        parent::__construct([
            'service_id' => $serviceId,
            'user_id' => $userId,
            'expired_at' => $expiredAt,
            'detected_at' => date('Y-m-d H:i:s')
        ]);
    }

    public function getServiceId(): int
    {
        return $this->get('service_id');
    }

    public function getUserId(): int
    {
        return $this->get('user_id');
    }

    public function getExpiredAt(): string
    {
        return $this->get('expired_at');
    }

    public function getDetectedAt(): string
    {
        return $this->get('detected_at');
    }
}

/**
 * Service Volume Exceeded Event
 */
class ServiceVolumeExceededEvent extends BaseEvent
{
    public function __construct(int $serviceId, int $userId, int $totalVolume, int $usedVolume)
    {
        parent::__construct([
            'service_id' => $serviceId,
            'user_id' => $userId,
            'total_volume' => $totalVolume,
            'used_volume' => $usedVolume,
            'exceeded_at' => date('Y-m-d H:i:s')
        ]);
    }

    public function getServiceId(): int
    {
        return $this->get('service_id');
    }

    public function getUserId(): int
    {
        return $this->get('user_id');
    }

    public function getTotalVolume(): int
    {
        return $this->get('total_volume');
    }

    public function getUsedVolume(): int
    {
        return $this->get('used_volume');
    }

    public function getExceededAt(): string
    {
        return $this->get('exceeded_at');
    }

    public function getUsagePercentage(): float
    {
        $total = $this->getTotalVolume();
        $used = $this->getUsedVolume();

        return $total > 0 ? ($used / $total) * 100 : 0;
    }
}

/**
 * Service Deleted Event
 */
class ServiceDeletedEvent extends BaseEvent
{
    public function __construct(int $serviceId, int $userId, array $serviceData, string $reason = '')
    {
        parent::__construct([
            'service_id' => $serviceId,
            'user_id' => $userId,
            'service_data' => $serviceData,
            'reason' => $reason,
            'deleted_at' => date('Y-m-d H:i:s')
        ]);
    }

    public function getServiceId(): int
    {
        return $this->get('service_id');
    }

    public function getUserId(): int
    {
        return $this->get('user_id');
    }

    public function getServiceData(): array
    {
        return $this->get('service_data', []);
    }

    public function getReason(): string
    {
        return $this->get('reason', '');
    }

    public function getDeletedAt(): string
    {
        return $this->get('deleted_at');
    }
}
