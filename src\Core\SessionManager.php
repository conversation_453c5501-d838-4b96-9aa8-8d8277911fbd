<?php

declare(strict_types=1);

namespace WeBot\Core;

/**
 * Session Manager
 *
 * Advanced session management with Redis backend,
 * security features, and user tracking.
 *
 * @package WeBot\Core
 * @version 2.0
 */
class SessionManager
{
    private CacheManager $cache;
    private string $sessionId;
    private array $data = [];
    private bool $started = false;
    private int $lifetime;
    private string $cookieName;
    private array $cookieParams;

    public function __construct(CacheManager $cache, array $config = [])
    {
        $this->cache = $cache;
        $this->lifetime = $config['lifetime'] ?? 7200; // 2 hours
        $this->cookieName = $config['cookie_name'] ?? 'webot_session';
        $this->cookieParams = [
            'lifetime' => $config['cookie_lifetime'] ?? 0,
            'path' => $config['cookie_path'] ?? '/',
            'domain' => $config['cookie_domain'] ?? '',
            'secure' => $config['cookie_secure'] ?? true,
            'httponly' => $config['cookie_httponly'] ?? true,
            'samesite' => $config['cookie_samesite'] ?? 'Strict'
        ];
    }

    /**
     * Start session
     */
    public function start(): bool
    {
        if ($this->started) {
            return true;
        }

        // Get session ID from cookie or generate new one
        $this->sessionId = $_COOKIE[$this->cookieName] ?? $this->generateSessionId();

        // Load session data
        $this->loadSessionData();

        // Set session cookie
        $this->setSessionCookie();

        // Update last activity
        $this->updateLastActivity();

        $this->started = true;
        return true;
    }

    /**
     * Get session value
     */
    public function get(string $key, $default = null)
    {
        $this->ensureStarted();
        return $this->data[$key] ?? $default;
    }

    /**
     * Set session value
     */
    public function set(string $key, $value): void
    {
        $this->ensureStarted();
        $this->data[$key] = $value;
        $this->save();
    }

    /**
     * Check if session has key
     */
    public function has(string $key): bool
    {
        $this->ensureStarted();
        return isset($this->data[$key]);
    }

    /**
     * Remove session value
     */
    public function remove(string $key): void
    {
        $this->ensureStarted();
        unset($this->data[$key]);
        $this->save();
    }

    /**
     * Get all session data
     */
    public function all(): array
    {
        $this->ensureStarted();
        return $this->data;
    }

    /**
     * Clear all session data
     */
    public function clear(): void
    {
        $this->ensureStarted();
        $this->data = [];
        $this->save();
    }

    /**
     * Destroy session
     */
    public function destroy(): bool
    {
        $this->ensureStarted();

        // Remove from cache
        $this->cache->deleteSession($this->sessionId);

        // Clear cookie
        setcookie(
            $this->cookieName,
            '',
            time() - 3600,
            $this->cookieParams['path'],
            $this->cookieParams['domain'],
            $this->cookieParams['secure'],
            $this->cookieParams['httponly']
        );

        $this->data = [];
        $this->started = false;

        return true;
    }

    /**
     * Regenerate session ID
     */
    public function regenerate(bool $deleteOld = true): bool
    {
        $this->ensureStarted();

        $oldSessionId = $this->sessionId;
        $this->sessionId = $this->generateSessionId();

        // Save data with new session ID
        $this->save();

        // Delete old session
        if ($deleteOld) {
            $this->cache->deleteSession($oldSessionId);
        }

        // Update cookie
        $this->setSessionCookie();

        return true;
    }

    /**
     * Get session ID
     */
    public function getId(): string
    {
        $this->ensureStarted();
        return $this->sessionId;
    }

    /**
     * Set user ID in session
     */
    public function setUserId(int $userId): void
    {
        $this->set('user_id', $userId);
        $this->set('login_time', time());
    }

    /**
     * Get user ID from session
     */
    public function getUserId(): ?int
    {
        return $this->get('user_id');
    }

    /**
     * Check if user is logged in
     */
    public function isLoggedIn(): bool
    {
        return $this->getUserId() !== null;
    }

    /**
     * Logout user
     */
    public function logout(): void
    {
        $this->remove('user_id');
        $this->remove('login_time');
        $this->regenerate();
    }

    /**
     * Set flash message
     */
    public function flash(string $key, $value): void
    {
        $flash = $this->get('_flash', []);
        $flash[$key] = $value;
        $this->set('_flash', $flash);
    }

    /**
     * Get flash message
     */
    public function getFlash(string $key, $default = null)
    {
        $flash = $this->get('_flash', []);
        $value = $flash[$key] ?? $default;

        // Remove flash message after reading
        unset($flash[$key]);
        $this->set('_flash', $flash);

        return $value;
    }

    /**
     * Get all flash messages
     */
    public function getAllFlash(): array
    {
        $flash = $this->get('_flash', []);
        $this->remove('_flash');
        return $flash;
    }

    /**
     * Set CSRF token
     */
    public function setCsrfToken(): string
    {
        $token = bin2hex(random_bytes(32));
        $this->set('_csrf_token', $token);
        return $token;
    }

    /**
     * Get CSRF token
     */
    public function getCsrfToken(): ?string
    {
        return $this->get('_csrf_token');
    }

    /**
     * Verify CSRF token
     */
    public function verifyCsrfToken(string $token): bool
    {
        $sessionToken = $this->getCsrfToken();
        return $sessionToken && hash_equals($sessionToken, $token);
    }

    /**
     * Track user activity
     */
    public function trackActivity(string $action, array $data = []): void
    {
        $activity = [
            'action' => $action,
            'data' => $data,
            'timestamp' => time(),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ];

        $activities = $this->get('_activities', []);
        $activities[] = $activity;

        // Keep only last 10 activities
        if (count($activities) > 10) {
            $activities = array_slice($activities, -10);
        }

        $this->set('_activities', $activities);
    }

    /**
     * Get user activities
     */
    public function getActivities(): array
    {
        return $this->get('_activities', []);
    }

    /**
     * Check session security
     */
    public function validateSecurity(): bool
    {
        $this->ensureStarted();

        // Check IP address (if enabled)
        if ($this->has('_ip_address')) {
            $sessionIp = $this->get('_ip_address');
            $currentIp = $_SERVER['REMOTE_ADDR'] ?? '';

            if ($sessionIp !== $currentIp) {
                $this->destroy();
                return false;
            }
        }

        // Check user agent (if enabled)
        if ($this->has('_user_agent')) {
            $sessionAgent = $this->get('_user_agent');
            $currentAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

            if ($sessionAgent !== $currentAgent) {
                $this->destroy();
                return false;
            }
        }

        // Check session expiry
        $lastActivity = $this->get('_last_activity', 0);
        if (time() - $lastActivity > $this->lifetime) {
            $this->destroy();
            return false;
        }

        return true;
    }

    /**
     * Enable security features
     */
    public function enableSecurity(): void
    {
        $this->set('_ip_address', $_SERVER['REMOTE_ADDR'] ?? '');
        $this->set('_user_agent', $_SERVER['HTTP_USER_AGENT'] ?? '');
    }

    /**
     * Get session statistics
     */
    public function getStats(): array
    {
        $this->ensureStarted();

        return [
            'session_id' => $this->sessionId,
            'started' => $this->started,
            'user_id' => $this->getUserId(),
            'logged_in' => $this->isLoggedIn(),
            'last_activity' => $this->get('_last_activity'),
            'login_time' => $this->get('login_time'),
            'data_size' => strlen(serialize($this->data)),
            'activities_count' => count($this->getActivities())
        ];
    }

    /**
     * Save session data
     */
    private function save(): void
    {
        if (!$this->started) {
            return;
        }

        $this->cache->setSession($this->sessionId, $this->data, $this->lifetime);
    }

    /**
     * Load session data
     */
    private function loadSessionData(): void
    {
        $data = $this->cache->getSession($this->sessionId);
        $this->data = $data ?? [];
    }

    /**
     * Generate session ID
     */
    private function generateSessionId(): string
    {
        return bin2hex(random_bytes(32));
    }

    /**
     * Set session cookie
     */
    private function setSessionCookie(): void
    {
        setcookie(
            $this->cookieName,
            $this->sessionId,
            [
                'expires' => $this->cookieParams['lifetime'] > 0 ?
                    time() + $this->cookieParams['lifetime'] : 0,
                'path' => $this->cookieParams['path'],
                'domain' => $this->cookieParams['domain'],
                'secure' => $this->cookieParams['secure'],
                'httponly' => $this->cookieParams['httponly'],
                'samesite' => $this->cookieParams['samesite']
            ]
        );
    }

    /**
     * Update last activity
     */
    private function updateLastActivity(): void
    {
        $this->data['_last_activity'] = time();
        $this->save();
    }

    /**
     * Ensure session is started
     */
    private function ensureStarted(): void
    {
        if (!$this->started) {
            $this->start();
        }
    }

    /**
     * Get session by ID (for AuthenticationMiddleware compatibility)
     */
    public function getSession(string $sessionId): ?array
    {
        $data = $this->cache->getSession($sessionId);
        return $data;
    }

    /**
     * Check if session is expired (for AuthenticationMiddleware compatibility)
     */
    public function isExpired(array $session): bool
    {
        $lastActivity = $session['_last_activity'] ?? 0;
        return (time() - $lastActivity) > $this->lifetime;
    }

    /**
     * Destroy session by ID (for AuthenticationMiddleware compatibility)
     */
    public function destroySession(string $sessionId): bool
    {
        return $this->cache->deleteSession($sessionId);
    }

    /**
     * Refresh session (for AuthenticationMiddleware compatibility)
     */
    public function refreshSession(string $sessionId): bool
    {
        $data = $this->cache->getSession($sessionId);
        if ($data) {
            $data['_last_activity'] = time();
            return $this->cache->setSession($sessionId, $data, $this->lifetime);
        }
        return false;
    }

    /**
     * Create session with data (for AuthenticationMiddleware compatibility)
     */
    public function createSession(array $sessionData): string
    {
        $sessionId = $this->generateSessionId();
        $sessionData['_last_activity'] = time();
        $sessionData['_created_at'] = time();

        $this->cache->setSession($sessionId, $sessionData, $this->lifetime);
        return $sessionId;
    }
}
