<?php

declare(strict_types=1);

namespace WeBot\Services;

use WeBot\Core\Config;
use WeBot\Utils\Logger;
use Monolog\Logger as MonologLogger;
use WeBot\Utils\Helper;
use WeBot\Exceptions\PaymentException;

/**
 * Payment Service
 *
 * Handles payment processing, gateway integration,
 * wallet management, and transaction verification.
 *
 * @package WeBot\Services
 * @version 2.0
 */
class PaymentService
{
    private Config $config;
    private DatabaseService $database;
    private MonologLogger $logger;
    private array $gateways;

    public function __construct(Config $config, DatabaseService $database)
    {
        $this->config = $config;
        $this->database = $database;
        $this->logger = Logger::getInstance();

        $this->loadGateways();
    }

    /**
     * Create payment request
     */
    public function createPayment(int $userId, int $amount, string $gateway, array $metadata = []): array
    {
        // Validate amount
        if ($amount < 1000 || $amount > 10000000) {
            throw new PaymentException('مبلغ نامعتبر', PaymentException::INVALID_AMOUNT);
        }

        // Validate gateway
        if (!$this->isGatewayAvailable($gateway)) {
            throw new PaymentException('درگاه پرداخت در دسترس نیست', PaymentException::GATEWAY_UNAVAILABLE);
        }

        // Generate unique hash
        $hashId = $this->generatePaymentHash();

        // Create payment record
        $paymentData = [
            'user_id' => $userId,
            'amount' => $amount,
            'gateway' => $gateway,
            'hash_id' => $hashId,
            'status' => 'pending',
            'metadata' => json_encode($metadata),
            'created_at' => date('Y-m-d H:i:s')
        ];

        $paymentId = $this->database->insert('payments', $paymentData);

        $this->logger->info('Payment created', [
            'payment_id' => $paymentId,
            'user_id' => $userId,
            'amount' => $amount,
            'gateway' => $gateway
        ]);

        return [
            'payment_id' => $paymentId,
            'hash_id' => $hashId,
            'amount' => $amount,
            'gateway' => $gateway,
            'payment_url' => $this->generatePaymentUrl($paymentId, $gateway)
        ];
    }

    /**
     * Verify payment
     */
    public function verifyPayment(int $paymentId, array $gatewayData = []): bool
    {
        $payment = $this->getPaymentById($paymentId);

        if (!$payment) {
            throw new PaymentException('پرداخت یافت نشد');
        }

        if ($payment['status'] === 'completed') {
            return true;
        }

        if ($payment['status'] === 'failed' || $payment['status'] === 'cancelled') {
            throw new PaymentException('پرداخت لغو شده یا ناموفق');
        }

        // Verify with gateway
        $verified = $this->verifyWithGateway($payment, $gatewayData);

        if ($verified) {
            // Update payment status
            $this->updatePaymentStatus($paymentId, 'completed');

            // Add to user wallet
            $this->addToUserWallet($payment['user_id'], $payment['amount']);

            // Create transaction record
            $this->createTransaction($payment['user_id'], $payment['amount'], 'deposit', 'payment_verified', $paymentId);

            $this->logger->info('Payment verified successfully', [
                'payment_id' => $paymentId,
                'user_id' => $payment['user_id'],
                'amount' => $payment['amount']
            ]);

            return true;
        } else {
            $this->updatePaymentStatus($paymentId, 'failed');

            $this->logger->warning('Payment verification failed', [
                'payment_id' => $paymentId,
                'gateway' => $payment['gateway']
            ]);

            return false;
        }
    }

    /**
     * Cancel payment
     */
    public function cancelPayment(int $paymentId): bool
    {
        $payment = $this->getPaymentById($paymentId);

        if (!$payment) {
            throw new PaymentException('پرداخت یافت نشد');
        }

        if ($payment['status'] === 'completed') {
            throw new PaymentException('پرداخت تکمیل شده قابل لغو نیست');
        }

        $this->updatePaymentStatus($paymentId, 'cancelled');

        $this->logger->info('Payment cancelled', [
            'payment_id' => $paymentId,
            'user_id' => $payment['user_id']
        ]);

        return true;
    }

    /**
     * Add balance to user wallet
     */
    public function addToUserWallet(int $userId, int $amount): bool
    {
        $sql = "UPDATE `users` SET `wallet` = `wallet` + ? WHERE `userid` = ?";
        $this->database->execute($sql, [$amount, $userId], 'ii');

        $this->logger->info('Wallet balance added', [
            'user_id' => $userId,
            'amount' => $amount
        ]);

        return true;
    }

    /**
     * Deduct from user wallet
     */
    public function deductFromUserWallet(int $userId, int $amount): bool
    {
        // Check current balance
        $currentBalance = $this->getUserWalletBalance($userId);

        if ($currentBalance < $amount) {
            throw new PaymentException('موجودی کافی نیست', PaymentException::INSUFFICIENT_FUNDS);
        }

        $sql = "UPDATE `users` SET `wallet` = `wallet` - ? WHERE `userid` = ? AND `wallet` >= ?";
        $this->database->execute($sql, [$amount, $userId, $amount], 'iii');

        if ($this->database->getAffectedRows() === 0) {
            throw new PaymentException('خطا در کسر از کیف پول');
        }

        // Create transaction record
        $this->createTransaction($userId, $amount, 'withdraw', 'wallet_deduction');

        $this->logger->info('Wallet balance deducted', [
            'user_id' => $userId,
            'amount' => $amount
        ]);

        return true;
    }

    /**
     * Get user wallet balance
     */
    public function getUserWalletBalance(int $userId): int
    {
        $sql = "SELECT `wallet` FROM `users` WHERE `userid` = ?";
        $balance = $this->database->fetchValue($sql, [$userId], 'i');

        return (int) ($balance ?? 0);
    }

    /**
     * Process payment (for testing)
     */
    public function processPayment(array $paymentData): array
    {
        try {
            $userId = $paymentData['user_id'];
            $amount = $paymentData['amount'];
            $gateway = $paymentData['gateway'] ?? 'wallet';

            // Create payment record
            $paymentResult = $this->createPayment($userId, $amount, $gateway, $paymentData);
            $paymentId = $paymentResult['payment_id'] ?? 0;

            // For testing, automatically verify the payment
            $verified = $this->verifyPayment($paymentId, $paymentData);

            return [
                'success' => $verified,
                'payment_id' => $paymentId,
                'message' => $verified ? 'Payment processed successfully' : 'Payment verification failed'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Get latest payment for user
     */
    public function getLatestPayment(int $userId): ?array
    {
        $sql = "SELECT * FROM `payments` WHERE `user_id` = ? ORDER BY `created_at` DESC LIMIT 1";
        return $this->database->fetchRow($sql, [$userId], 'i');
    }

    /**
     * Get user transactions
     */
    public function getUserTransactions(int $userId, int $limit = 20, int $offset = 0): array
    {
        $sql = "SELECT * FROM `transactions` WHERE `user_id` = ? ORDER BY `created_at` DESC LIMIT ? OFFSET ?";
        return $this->database->fetchAll($sql, [$userId, $limit, $offset], 'iii');
    }

    /**
     * Create transaction record
     */
    public function createTransaction(int $userId, int $amount, string $type, string $description, ?int $referenceId = null): int
    {
        $transactionData = [
            'user_id' => $userId,
            'amount' => $amount,
            'type' => $type, // deposit, withdraw
            'description' => $description,
            'reference_id' => $referenceId,
            'status' => 'completed',
            'created_at' => date('Y-m-d H:i:s')
        ];

        return $this->database->insert('transactions', $transactionData);
    }

    /**
     * Get payment by ID
     */
    public function getPaymentById(int $paymentId): ?array
    {
        $sql = "SELECT * FROM `payments` WHERE `id` = ?";
        return $this->database->fetchRow($sql, [$paymentId], 'i');
    }

    /**
     * Update payment status
     */
    private function updatePaymentStatus(int $paymentId, string $status): bool
    {
        $sql = "UPDATE `payments` SET `status` = ?, `updated_at` = NOW() WHERE `id` = ?";
        $this->database->execute($sql, [$status, $paymentId], 'si');

        return $this->database->getAffectedRows() > 0;
    }

    /**
     * Generate payment URL
     */
    private function generatePaymentUrl(int $paymentId, string $gateway): string
    {
        $payment = $this->getPaymentById($paymentId);

        return match ($gateway) {
            'zarinpal' => $this->generateZarinpalUrl($payment),
            'nowpayments' => $this->generateNowpaymentsUrl($payment),
            default => throw new PaymentException('درگاه پشتیبانی نمی‌شود')
        };
    }

    /**
     * Generate Zarinpal payment URL
     */
    private function generateZarinpalUrl(array $payment): string
    {
        if (env('DISABLE_EXTERNAL_APIS', false)) {
            return "https://sandbox.zarinpal.com/pg/payment/{$payment['hash_id']}";
        }

        // TODO: Real Zarinpal integration would go here
        // This is a simplified example - real implementation would use:
        // - $config['merchant_id']
        // - $payment['amount']
        // - callback URL and description
        return "https://www.zarinpal.com/pg/StartPay/{$payment['hash_id']}";
    }

    /**
     * Generate NOWPayments URL
     */
    private function generateNowpaymentsUrl(array $payment): string
    {
        if (env('DISABLE_EXTERNAL_APIS', false)) {
            return "https://nowpayments.io/payment/{$payment['hash_id']}";
        }

        // Real NOWPayments integration would go here
        return "https://nowpayments.io/payment/{$payment['hash_id']}";
    }

    /**
     * Verify with gateway
     */
    private function verifyWithGateway(array $payment, array $gatewayData): bool
    {
        if (env('DISABLE_EXTERNAL_APIS', false)) {
            // Mock verification for testing
            return true;
        }

        return match ($payment['gateway']) {
            'zarinpal' => $this->verifyZarinpal($payment, $gatewayData),
            'nowpayments' => $this->verifyNowpayments($payment, $gatewayData),
            default => false
        };
    }

    /**
     * Verify Zarinpal payment
     */
    private function verifyZarinpal(array $_payment, array $_gatewayData): bool
    {
        // TODO: Implement actual Zarinpal verification
        // Real verification would use $_payment and $_gatewayData
        // This is a placeholder implementation

        return isset($_gatewayData['Status']) && $_gatewayData['Status'] === 'OK';
    }

    /**
     * Verify NOWPayments
     */
    private function verifyNowpayments(array $_payment, array $_gatewayData): bool
    {
        // TODO: Implement actual NOWPayments verification
        // Real verification would use $_payment and $_gatewayData
        // This is a placeholder implementation

        return isset($_gatewayData['payment_status']) && $_gatewayData['payment_status'] === 'finished';
    }

    /**
     * Check if gateway is available
     */
    private function isGatewayAvailable(string $gateway): bool
    {
        return isset($this->gateways[$gateway]) && ($this->gateways[$gateway]['active'] ?? false);
    }

    /**
     * Generate payment hash
     */
    private function generatePaymentHash(): string
    {
        return Helper::randomString(32);
    }

    /**
     * Load payment gateways configuration
     */
    private function loadGateways(): void
    {
        $this->gateways = $this->config->get('payments', []);

        // Set default active status
        foreach ($this->gateways as &$gateway) {
            if (!isset($gateway['active'])) {
                $gateway['active'] = !empty($gateway['merchant_id'] ?? $gateway['api_key'] ?? '');
            }
        }
    }

    /**
     * Get available gateways
     */
    public function getAvailableGateways(): array
    {
        $available = [];

        foreach ($this->gateways as $name => $config) {
            if ($config['active'] ?? false) {
                $available[] = [
                    'name' => $name,
                    'title' => $config['title'] ?? ucfirst($name),
                    'description' => $config['description'] ?? '',
                    'min_amount' => $config['min_amount'] ?? 1000,
                    'max_amount' => $config['max_amount'] ?? 10000000
                ];
            }
        }

        return $available;
    }

    /**
     * Get payment statistics
     */
    public function getPaymentStats(int $userId = null): array
    {
        $whereClause = $userId ? "WHERE user_id = ?" : "";
        $params = $userId ? [$userId] : [];
        $types = $userId ? 'i' : '';

        $stats = [
            'total_payments' => 0,
            'successful_payments' => 0,
            'failed_payments' => 0,
            'pending_payments' => 0,
            'total_amount' => 0,
            'successful_amount' => 0
        ];

        // Total payments
        $sql = "SELECT COUNT(*) FROM `payments` {$whereClause}";
        $stats['total_payments'] = (int) $this->database->fetchValue($sql, $params, $types);

        // Successful payments
        $sql = "SELECT COUNT(*), COALESCE(SUM(amount), 0) FROM `payments` {$whereClause} AND status = 'completed'";
        if ($userId) {
            $params[] = $userId;
            $types .= 'i';
        }
        $result = $this->database->fetchRow($sql, $params, $types);
        $stats['successful_payments'] = (int) $result['COUNT(*)'];
        $stats['successful_amount'] = (int) $result['COALESCE(SUM(amount), 0)'];

        // Failed payments
        $sql = "SELECT COUNT(*) FROM `payments` {$whereClause} AND status = 'failed'";
        $stats['failed_payments'] = (int) $this->database->fetchValue($sql, $params, $types);

        // Pending payments
        $sql = "SELECT COUNT(*) FROM `payments` {$whereClause} AND status = 'pending'";
        $stats['pending_payments'] = (int) $this->database->fetchValue($sql, $params, $types);

        // Total amount
        $sql = "SELECT COALESCE(SUM(amount), 0) FROM `payments` {$whereClause}";
        $stats['total_amount'] = (int) $this->database->fetchValue($sql, $params, $types);

        return $stats;
    }

    /**
     * Process refund
     */
    public function processRefund(int $paymentId, int $amount = null, string $reason = ''): array
    {
        $payment = $this->getPaymentById($paymentId);

        if (!$payment) {
            throw new PaymentException('پرداخت یافت نشد');
        }

        if ($payment['status'] !== 'completed') {
            throw new PaymentException('فقط پرداخت‌های تکمیل شده قابل بازگشت هستند');
        }

        $refundAmount = $amount ?? $payment['amount'];

        if ($refundAmount > $payment['amount']) {
            throw new PaymentException('مبلغ بازگشت نمی‌تواند بیشتر از مبلغ پرداخت باشد');
        }

        // Check user wallet balance
        $userBalance = $this->getUserWalletBalance($payment['user_id']);
        if ($userBalance < $refundAmount) {
            throw new PaymentException('موجودی کاربر برای بازگشت وجه کافی نیست');
        }

        // Deduct from user wallet
        $this->deductFromUserWallet($payment['user_id'], $refundAmount);

        // Create refund record
        $refundData = [
            'payment_id' => $paymentId,
            'user_id' => $payment['user_id'],
            'amount' => $refundAmount,
            'reason' => $reason,
            'status' => 'completed',
            'created_at' => date('Y-m-d H:i:s')
        ];

        $this->database->insert('refunds', $refundData);

        $this->logger->info('Refund processed', [
            'payment_id' => $paymentId,
            'user_id' => $payment['user_id'],
            'amount' => $refundAmount,
            'reason' => $reason
        ]);

        return ['success' => true, 'message' => 'Refund processed successfully'];
    }

    /**
     * Initiate payment (for testing)
     */
    public function initiatePayment(array $paymentData): array
    {
        try {
            $userId = $paymentData['user_id'];
            $amount = $paymentData['amount'];
            $gateway = $paymentData['gateway'] ?? 'stripe';

            $paymentResult = $this->createPayment($userId, $amount, $gateway, $paymentData);

            return [
                'success' => true,
                'payment_id' => $paymentResult['payment_id'] ?? 0,
                'redirect_url' => 'https://payment.gateway.com/pay/' . ($paymentResult['payment_id'] ?? 0),
                'message' => 'Payment initiated successfully'
            ];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Process Stripe webhook
     */
    public function processStripeWebhook(array $webhookData): array
    {
        try {
            // Mock Stripe webhook processing
            $paymentId = $webhookData['payment_id'] ?? 0;

            if ($paymentId) {
                $this->verifyPayment($paymentId, $webhookData);
            }

            return ['success' => true, 'message' => 'Webhook processed'];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Approve PayPal payment
     */
    public function approvePayPalPayment(int $paymentId): array
    {
        try {
            // Mock PayPal approval
            return ['success' => true, 'approval_url' => 'https://paypal.com/approve/' . $paymentId];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Execute PayPal payment
     */
    public function executePayPalPayment(int $paymentId, string $payerId): array
    {
        try {
            // Mock PayPal execution
            $this->verifyPayment($paymentId, ['payer_id' => $payerId]);
            return ['success' => true, 'message' => 'Payment executed'];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Confirm crypto payment
     */
    public function confirmCryptoPayment(int $paymentId, string $transactionHash): array
    {
        try {
            // Mock crypto confirmation
            $this->verifyPayment($paymentId, ['tx_hash' => $transactionHash]);
            return ['success' => true, 'message' => 'Crypto payment confirmed'];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Retry payment
     */
    public function retryPayment(int $paymentId): array
    {
        try {
            $payment = $this->getPaymentById($paymentId);
            if (!$payment) {
                return ['success' => false, 'message' => 'Payment not found'];
            }

            // Mock retry logic
            return ['success' => true, 'message' => 'Payment retry initiated'];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Create subscription
     */
    public function createSubscription(array $subscriptionData): array
    {
        try {
            // Mock subscription creation
            return [
                'success' => true,
                'subscription_id' => 'sub_' . time(),
                'message' => 'Subscription created'
            ];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Process subscription payment
     */
    public function processSubscriptionPayment(string $subscriptionId): array
    {
        try {
            // Mock subscription payment
            return ['success' => true, 'message' => 'Subscription payment processed'];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Process subscription renewal
     */
    public function processSubscriptionRenewal(string $subscriptionId): array
    {
        try {
            // Mock subscription renewal
            return ['success' => true, 'message' => 'Subscription renewed'];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Cancel subscription
     */
    public function cancelSubscription(string $subscriptionId): array
    {
        try {
            // Mock subscription cancellation
            return ['success' => true, 'message' => 'Subscription cancelled'];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Generate webhook signature
     */
    public function generateWebhookSignature(array $data): string
    {
        $secret = $this->config->get('payment.webhook_secret', 'default_secret');
        return hash_hmac('sha256', json_encode($data), $secret);
    }

    /**
     * Verify webhook signature
     */
    public function verifyWebhookSignature(array $data, string $signature): bool
    {
        $expectedSignature = $this->generateWebhookSignature($data);
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Process webhook
     */
    public function processWebhook(string $provider, array $data, string $signature): array
    {
        try {
            if (!$this->verifyWebhookSignature($data, $signature)) {
                return ['success' => false, 'message' => 'Invalid signature'];
            }

            // Mock webhook processing based on provider
            return match ($provider) {
                'stripe' => $this->processStripeWebhook($data),
                'paypal' => ['success' => true, 'message' => 'PayPal webhook processed'],
                'crypto' => ['success' => true, 'message' => 'Crypto webhook processed'],
                default => ['success' => false, 'message' => 'Unknown provider']
            };
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
}
