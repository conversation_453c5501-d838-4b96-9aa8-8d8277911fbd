<?php

declare(strict_types=1);

namespace WeBot\Tests\Integration;

use WeBot\Tests\Unit\BaseTestCase;

/**
 * Real Database Integration Tests
 * 
 * Tests actual database operations with real data flow
 * and transaction integrity across multiple services.
 * 
 * @package WeBot\Tests\Integration
 * @version 2.0
 */
class RealDatabaseIntegrationTest extends BaseTestCase
{
    /**
     * Test complete user registration flow
     */
    public function testCompleteUserRegistrationFlow(): void
    {
        // Create new user
        $telegramId = 111222333;
        $userData = [
            'telegram_id' => $telegramId,
            'username' => 'integration_test_user',
            'first_name' => 'Integration Test',
            'phone' => '+1234567890',
            'status' => 'active'
        ];
        
        $this->database->execute(
            "INSERT INTO users (telegram_id, username, first_name, phone, status) VALUES (?, ?, ?, ?, ?)",
            [$userData['telegram_id'], $userData['username'], $userData['first_name'], $userData['phone'], $userData['status']]
        );
        
        // Verify user was created
        $users = $this->database->query(
            "SELECT * FROM users WHERE telegram_id = ?",
            [$telegramId]
        );
        
        $this->assertEquals(1, count($users));
        $this->assertEquals($userData['username'], $users[0]['username']);
        $this->assertEquals($userData['phone'], $users[0]['phone']);
        
        $userId = $users[0]['id'];
        
        // Test user update
        $this->database->execute(
            "UPDATE users SET first_name = ? WHERE id = ?",
            ['Updated Name', $userId]
        );
        
        $updatedUsers = $this->database->query(
            "SELECT * FROM users WHERE id = ?",
            [$userId]
        );
        
        $this->assertEquals('Updated Name', $updatedUsers[0]['first_name']);
    }
    
    /**
     * Test payment creation and verification flow
     */
    public function testPaymentFlow(): void
    {
        // Get test user
        $users = $this->database->query(
            "SELECT * FROM users WHERE telegram_id = ?",
            [123456789]
        );
        
        $this->assertNotNull($users);
        $this->assertTrue(count($users) > 0);
        
        $userId = $users[0]['id'];
        
        // Create payment
        $paymentData = [
            'user_id' => $userId,
            'amount' => 25.50,
            'currency' => 'USD',
            'status' => 'pending',
            'gateway' => 'test_gateway',
            'transaction_id' => 'test_txn_' . time()
        ];
        
        $this->database->execute(
            "INSERT INTO payments (user_id, amount, currency, status, gateway, transaction_id) VALUES (?, ?, ?, ?, ?, ?)",
            [$paymentData['user_id'], $paymentData['amount'], $paymentData['currency'], $paymentData['status'], $paymentData['gateway'], $paymentData['transaction_id']]
        );
        
        // Verify payment was created
        $payments = $this->database->query(
            "SELECT * FROM payments WHERE transaction_id = ?",
            [$paymentData['transaction_id']]
        );
        
        $this->assertEquals(1, count($payments));
        $this->assertEquals($paymentData['amount'], (float)$payments[0]['amount']);
        $this->assertEquals($paymentData['status'], $payments[0]['status']);
        
        $paymentId = $payments[0]['id'];
        
        // Update payment status to completed
        $this->database->execute(
            "UPDATE payments SET status = ? WHERE id = ?",
            ['completed', $paymentId]
        );
        
        // Verify payment status update
        $updatedPayments = $this->database->query(
            "SELECT * FROM payments WHERE id = ?",
            [$paymentId]
        );
        
        $this->assertEquals('completed', $updatedPayments[0]['status']);
    }
    
    /**
     * Test service creation and management
     */
    public function testServiceManagement(): void
    {
        // Get test user
        $users = $this->database->query(
            "SELECT * FROM users WHERE telegram_id = ?",
            [123456789]
        );
        
        $userId = $users[0]['id'];
        
        // Create service
        $serviceData = [
            'user_id' => $userId,
            'name' => 'Test VPN Service',
            'type' => 'vpn',
            'status' => 'active',
            'expires_at' => date('Y-m-d H:i:s', strtotime('+30 days'))
        ];
        
        $this->database->execute(
            "INSERT INTO services (user_id, name, type, status, expires_at) VALUES (?, ?, ?, ?, ?)",
            [$serviceData['user_id'], $serviceData['name'], $serviceData['type'], $serviceData['status'], $serviceData['expires_at']]
        );
        
        // Verify service was created
        $services = $this->database->query(
            "SELECT * FROM services WHERE user_id = ? AND name = ?",
            [$userId, $serviceData['name']]
        );
        
        $this->assertEquals(1, count($services));
        $this->assertEquals($serviceData['type'], $services[0]['type']);
        $this->assertEquals($serviceData['status'], $services[0]['status']);
        
        $serviceId = $services[0]['id'];
        
        // Test service expiration
        $this->database->execute(
            "UPDATE services SET status = ?, expires_at = ? WHERE id = ?",
            ['expired', date('Y-m-d H:i:s', strtotime('-1 day')), $serviceId]
        );
        
        // Verify service expiration
        $expiredServices = $this->database->query(
            "SELECT * FROM services WHERE id = ?",
            [$serviceId]
        );
        
        $this->assertEquals('expired', $expiredServices[0]['status']);
    }
    
    /**
     * Test database transaction integrity
     */
    public function testTransactionIntegrity(): void
    {
        // Start transaction
        $this->database->beginTransaction();
        
        try {
            // Create user
            $this->database->execute(
                "INSERT INTO users (telegram_id, username, first_name, status) VALUES (?, ?, ?, ?)",
                [444555666, 'transaction_test', 'Transaction Test', 'active']
            );
            
            // Get user ID
            $users = $this->database->query(
                "SELECT * FROM users WHERE telegram_id = ?",
                [444555666]
            );
            
            $userId = $users[0]['id'];
            
            // Create payment
            $this->database->execute(
                "INSERT INTO payments (user_id, amount, currency, status) VALUES (?, ?, ?, ?)",
                [$userId, 50.00, 'USD', 'completed']
            );
            
            // Create service
            $this->database->execute(
                "INSERT INTO services (user_id, name, type, status) VALUES (?, ?, ?, ?)",
                [$userId, 'Transaction Test Service', 'vpn', 'active']
            );
            
            // Commit transaction
            $this->database->commit();
            
            // Verify all data was committed
            $users = $this->database->query(
                "SELECT * FROM users WHERE telegram_id = ?",
                [444555666]
            );
            $this->assertEquals(1, count($users));
            
            $payments = $this->database->query(
                "SELECT * FROM payments WHERE user_id = ?",
                [$userId]
            );
            $this->assertEquals(1, count($payments));
            
            $services = $this->database->query(
                "SELECT * FROM services WHERE user_id = ?",
                [$userId]
            );
            $this->assertEquals(1, count($services));
            
        } catch (\Exception $e) {
            $this->database->rollback();
            $this->assertTrue(false, "Transaction failed: " . $e->getMessage());
        }
    }
    
    /**
     * Test database rollback functionality
     */
    public function testTransactionRollback(): void
    {
        // Count existing users
        $initialUsers = $this->database->query("SELECT COUNT(*) as count FROM users");
        $initialCount = $initialUsers[0]['count'];
        
        // Start transaction
        $this->database->beginTransaction();
        
        try {
            // Create user
            $this->database->execute(
                "INSERT INTO users (telegram_id, username, first_name, status) VALUES (?, ?, ?, ?)",
                [777888999, 'rollback_test', 'Rollback Test', 'active']
            );
            
            // Intentionally cause an error (duplicate telegram_id)
            $this->database->execute(
                "INSERT INTO users (telegram_id, username, first_name, status) VALUES (?, ?, ?, ?)",
                [777888999, 'rollback_test2', 'Rollback Test 2', 'active']
            );
            
            $this->database->commit();
            
        } catch (\Exception $e) {
            $this->database->rollback();
            
            // Verify rollback worked
            $finalUsers = $this->database->query("SELECT COUNT(*) as count FROM users");
            $finalCount = $finalUsers[0]['count'];
            
            $this->assertEquals($initialCount, $finalCount);
        }
    }
    
    /**
     * Test complex queries with joins
     */
    public function testComplexQueries(): void
    {
        // Test join query: users with their payments
        $results = $this->database->query("
            SELECT u.telegram_id, u.username, p.amount, p.status 
            FROM users u 
            LEFT JOIN payments p ON u.id = p.user_id 
            WHERE u.telegram_id = ?
        ", [123456789]);
        
        $this->assertNotNull($results);
        
        // Test aggregation query: total payments per user
        $aggregateResults = $this->database->query("
            SELECT u.telegram_id, u.username, 
                   COUNT(p.id) as payment_count,
                   COALESCE(SUM(p.amount), 0) as total_amount
            FROM users u 
            LEFT JOIN payments p ON u.id = p.user_id 
            GROUP BY u.id, u.telegram_id, u.username
            HAVING u.telegram_id = ?
        ", [123456789]);
        
        $this->assertNotNull($aggregateResults);
        
        if (count($aggregateResults) > 0) {
            $this->assertArrayHasKey('payment_count', $aggregateResults[0]);
            $this->assertArrayHasKey('total_amount', $aggregateResults[0]);
        }
    }
    
    /**
     * Test database performance with bulk operations
     */
    public function testBulkOperations(): void
    {
        $startTime = microtime(true);
        
        // Bulk insert test
        $this->database->beginTransaction();
        
        for ($i = 1; $i <= 100; $i++) {
            $this->database->execute(
                "INSERT INTO setting (type, value) VALUES (?, ?)",
                ["bulk_test_{$i}", "value_{$i}"]
            );
        }
        
        $this->database->commit();
        
        $endTime = microtime(true);
        $duration = $endTime - $startTime;
        
        // Verify bulk insert worked
        $results = $this->database->query(
            "SELECT COUNT(*) as count FROM setting WHERE type LIKE 'bulk_test_%'"
        );
        
        $this->assertEquals(100, $results[0]['count']);
        
        // Performance should be reasonable (less than 1 second for 100 inserts)
        $this->assertTrue($duration < 1.0, "Bulk operations took too long: {$duration} seconds");
        
        // Cleanup
        $this->database->execute("DELETE FROM setting WHERE type LIKE 'bulk_test_%'");
    }
}
