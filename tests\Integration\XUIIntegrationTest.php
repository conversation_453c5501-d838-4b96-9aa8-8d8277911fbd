<?php

declare(strict_types=1);

namespace WeBot\Tests\Integration;

use WeBot\Services\PanelService;
use WeBot\Core\Config;

/**
 * X-UI Integration Test
 * 
 * Comprehensive tests for X-UI panel integration
 * including implementation verification and functionality testing.
 * 
 * @package WeBot\Tests\Integration
 * @version 2.0
 */
class XUIIntegrationTest
{
    private Config $config;
    private PanelService $panelService;
    private object $mockDatabase;
    private array $testResults = [];
    private int $totalTests = 0;
    private int $passedTests = 0;
    private int $failedTests = 0;

    public function __construct()
    {
        $this->setupTestEnvironment();
        $this->initializeServices();
    }

    /**
     * Setup test environment
     */
    private function setupTestEnvironment(): void
    {
        putenv('WEBOT_TEST_MODE=true');
        putenv('DISABLE_EXTERNAL_APIS=true');
        
        // Setup mock X-UI responses
        $GLOBALS['mock_xui_responses'] = [
            'createUser' => [
                'success' => true,
                'msg' => 'Client added successfully',
                'obj' => [
                    'id' => 'test-uuid-123',
                    'email' => 'xui_user_123'
                ]
            ],
            'getUser' => [
                'success' => true,
                'obj' => [
                    [
                        'id' => 1,
                        'settings' => json_encode([
                            'clients' => [
                                [
                                    'id' => 'test-uuid-123',
                                    'email' => 'xui_user_123',
                                    'enable' => true,
                                    'totalGB' => 10737418240,
                                    'expiryTime' => (time() + 2592000) * 1000,
                                    'limitIp' => 0
                                ]
                            ]
                        ]),
                        'up' => 1073741824,
                        'down' => 2147483648
                    ]
                ]
            ],
            'updateUser' => [
                'success' => true,
                'msg' => 'Client updated successfully'
            ],
            'deleteUser' => [
                'success' => true,
                'msg' => 'Client deleted successfully'
            ],
            'login' => [
                'success' => true,
                'session' => 'test_session_123'
            ]
        ];
        
        $this->config = new Config();
    }

    /**
     * Initialize services
     */
    private function initializeServices(): void
    {
        $this->mockDatabase = $this->createMockDatabase();
        $this->panelService = new PanelService($this->config, $this->mockDatabase);
    }

    /**
     * Create mock database
     */
    private function createMockDatabase(): object
    {
        return new class {
            public function fetchRow(string $sql, array $params = [], string $types = ''): ?array {
                // Mock X-UI server info
                return [
                    'id' => 3,
                    'name' => 'Test X-UI Server',
                    'url' => 'https://test-xui.com',
                    'username' => 'admin',
                    'password' => 'admin123',
                    'panel_type' => 'x-ui',
                    'status' => 'active',
                    'port' => 443,
                    'inbound_id' => 1
                ];
            }
            
            public function fetchAll(string $sql, array $params = [], string $types = ''): array {
                return [];
            }
        };
    }

    /**
     * Run all X-UI integration tests
     */
    public function runAllTests(): array
    {
        echo "🧪 X-UI Integration Tests\n";
        echo "========================\n\n";

        $this->testXUIImplementationExists();
        $this->testXUIAuthentication();
        $this->testXUIUserCreation();
        $this->testXUIUserRetrieval();
        $this->testXUIUserUpdate();
        $this->testXUIUserDeletion();
        $this->testXUIConfigGeneration();
        $this->testXUISessionManagement();
        $this->testXUIUUIDGeneration();
        $this->testXUIAPICompatibility();

        return $this->getTestResults();
    }

    /**
     * Test that X-UI implementation exists and is not placeholder
     */
    private function testXUIImplementationExists(): void
    {
        $this->runTest('X-UI Implementation Exists', function() {
            // Check that X-UI methods are implemented
            $reflection = new \ReflectionClass($this->panelService);
            
            $requiredMethods = [
                'createXUIUser',
                'getXUIUser', 
                'updateXUIUser',
                'deleteXUIUser',
                'getXUIConfig',
                'getXUISession'
            ];

            foreach ($requiredMethods as $method) {
                if (!$reflection->hasMethod($method)) {
                    throw new \Exception("Missing X-UI method: {$method}");
                }
            }

            // Check that methods are not just throwing "not implemented" exceptions
            $panelServiceCode = file_get_contents('src/Services/PanelService.php');
            
            if (str_contains($panelServiceCode, 'X-UI integration not implemented yet')) {
                throw new \Exception('X-UI integration still has placeholder implementations');
            }

            return true;
        });
    }

    /**
     * Test X-UI authentication
     */
    private function testXUIAuthentication(): void
    {
        $this->runTest('X-UI Authentication', function() {
            $server = $this->mockDatabase->fetchRow("SELECT * FROM server_info WHERE id = ?", [3]);
            
            // Test authentication method
            $reflection = new \ReflectionClass($this->panelService);
            $method = $reflection->getMethod('getXUISession');
            $method->setAccessible(true);
            
            try {
                $sessionId = $method->invoke($this->panelService, $server);
                if (empty($sessionId)) {
                    throw new \Exception('X-UI session ID is empty');
                }
            } catch (\Exception $e) {
                // In test mode, this might throw an exception, which is acceptable
                if (!str_contains($e->getMessage(), 'test') && !str_contains($e->getMessage(), 'mock')) {
                    throw $e;
                }
            }

            return true;
        });
    }

    /**
     * Test X-UI user creation
     */
    private function testXUIUserCreation(): void
    {
        $this->runTest('X-UI User Creation', function() {
            $userData = [
                'username' => 'xui_test_' . time(),
                'data_limit' => 10737418240, // 10GB
                'expire' => time() + 2592000 // 30 days
            ];

            $result = $this->panelService->createUser(3, $userData);

            if (!is_array($result)) {
                throw new \Exception('X-UI createUser should return array');
            }

            // Verify X-UI-specific fields
            $xuiFields = ['uuid', 'email', 'status'];
            foreach ($xuiFields as $field) {
                if (!isset($result[$field])) {
                    throw new \Exception("Missing X-UI field in response: {$field}");
                }
            }

            return true;
        });
    }

    /**
     * Test X-UI user retrieval
     */
    private function testXUIUserRetrieval(): void
    {
        $this->runTest('X-UI User Retrieval', function() {
            $email = 'xui_user_123';
            
            $result = $this->panelService->getUser(3, $email);

            if (!is_array($result)) {
                throw new \Exception('X-UI getUser should return array');
            }

            // Verify X-UI-specific structure
            $expectedFields = ['uuid', 'email', 'status', 'data_limit'];
            foreach ($expectedFields as $field) {
                if (!isset($result[$field])) {
                    throw new \Exception("Missing field in X-UI user data: {$field}");
                }
            }

            return true;
        });
    }

    /**
     * Test X-UI user update
     */
    private function testXUIUserUpdate(): void
    {
        $this->runTest('X-UI User Update', function() {
            $email = 'xui_user_123';
            $updateData = [
                'data_limit' => 21474836480, // 20GB
                'expire' => time() + 5184000 // 60 days
            ];

            $result = $this->panelService->updateUser(3, $email, $updateData);

            if (!$result) {
                throw new \Exception('X-UI updateUser should return true on success');
            }

            return true;
        });
    }

    /**
     * Test X-UI user deletion
     */
    private function testXUIUserDeletion(): void
    {
        $this->runTest('X-UI User Deletion', function() {
            $email = 'xui_user_123';

            $result = $this->panelService->deleteUser(3, $email);

            if (!$result) {
                throw new \Exception('X-UI deleteUser should return true on success');
            }

            return true;
        });
    }

    /**
     * Test X-UI config generation
     */
    private function testXUIConfigGeneration(): void
    {
        $this->runTest('X-UI Config Generation', function() {
            $email = 'xui_user_123';

            $config = $this->panelService->getUserConfig(3, $email);

            if (empty($config)) {
                throw new \Exception('X-UI getUserConfig should return non-empty config');
            }

            // Verify config format (should be valid VLESS URL)
            if (!str_starts_with($config, 'vless://')) {
                throw new \Exception('X-UI config should be valid VLESS URL');
            }

            // Verify config contains UUID
            if (!preg_match('/vless:\/\/([a-f0-9-]+)@/', $config, $matches)) {
                throw new \Exception('X-UI config should contain valid UUID');
            }

            return true;
        });
    }

    /**
     * Test X-UI session management
     */
    private function testXUISessionManagement(): void
    {
        $this->runTest('X-UI Session Management', function() {
            $server = $this->mockDatabase->fetchRow("SELECT * FROM server_info WHERE id = ?", [3]);
            
            // Test session extraction
            $reflection = new \ReflectionClass($this->panelService);
            $method = $reflection->getMethod('extractSessionFromResponse');
            $method->setAccessible(true);
            
            $mockResponse = ['headers' => ['Set-Cookie: session=test123']];
            $sessionId = $method->invoke($this->panelService, $mockResponse);
            
            if (empty($sessionId)) {
                throw new \Exception('Session extraction should return non-empty session ID');
            }

            return true;
        });
    }

    /**
     * Test X-UI UUID generation
     */
    private function testXUIUUIDGeneration(): void
    {
        $this->runTest('X-UI UUID Generation', function() {
            $reflection = new \ReflectionClass($this->panelService);
            $method = $reflection->getMethod('generateUUID');
            $method->setAccessible(true);
            
            $uuid = $method->invoke($this->panelService);
            
            if (empty($uuid)) {
                throw new \Exception('UUID generation should return non-empty UUID');
            }

            // Verify UUID format
            if (!preg_match('/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/', $uuid)) {
                throw new \Exception('Generated UUID should be in valid format');
            }

            // Test uniqueness
            $uuid2 = $method->invoke($this->panelService);
            if ($uuid === $uuid2) {
                throw new \Exception('Generated UUIDs should be unique');
            }

            return true;
        });
    }

    /**
     * Test X-UI API compatibility
     */
    private function testXUIAPICompatibility(): void
    {
        $this->runTest('X-UI API Compatibility', function() {
            // Test that our implementation follows X-UI API structure
            $userData = [
                'username' => 'api_compatibility_test',
                'data_limit' => 10737418240,
                'expire' => time() + 2592000
            ];

            $result = $this->panelService->createUser(3, $userData);

            // Verify API response structure matches X-UI
            $expectedStructure = [
                'uuid' => 'string',
                'email' => 'string',
                'status' => 'string',
                'data_limit' => 'integer'
            ];

            foreach ($expectedStructure as $field => $type) {
                if (!isset($result[$field])) {
                    continue; // Some fields might not be in mock response
                }

                $actualType = gettype($result[$field]);
                if ($actualType !== $type && !($type === 'integer' && $actualType === 'double')) {
                    throw new \Exception("X-UI field {$field} should be {$type}, got {$actualType}");
                }
            }

            return true;
        });
    }

    /**
     * Run individual test
     */
    private function runTest(string $testName, callable $test): void
    {
        $this->totalTests++;
        
        try {
            $result = $test();
            
            if ($result === true) {
                $this->passedTests++;
                $this->testResults[] = ['name' => $testName, 'status' => 'PASS', 'error' => null];
                echo "✅ {$testName}\n";
            } else {
                $this->failedTests++;
                $this->testResults[] = ['name' => $testName, 'status' => 'FAIL', 'error' => 'Test returned false'];
                echo "❌ {$testName}: Test returned false\n";
            }
        } catch (\Throwable $e) {
            $this->failedTests++;
            $this->testResults[] = ['name' => $testName, 'status' => 'FAIL', 'error' => $e->getMessage()];
            echo "❌ {$testName}: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Get test results
     */
    private function getTestResults(): array
    {
        $successRate = $this->totalTests > 0 ? round(($this->passedTests / $this->totalTests) * 100, 2) : 0;
        
        return [
            'total_tests' => $this->totalTests,
            'passed_tests' => $this->passedTests,
            'failed_tests' => $this->failedTests,
            'success_rate' => $successRate,
            'results' => $this->testResults
        ];
    }

    /**
     * Print test summary
     */
    public function printSummary(): void
    {
        $results = $this->getTestResults();
        
        echo "\n📊 X-UI Integration Test Summary:\n";
        echo "================================\n";
        echo "Total Tests: {$results['total_tests']}\n";
        echo "Passed: {$results['passed_tests']}\n";
        echo "Failed: {$results['failed_tests']}\n";
        echo "Success Rate: {$results['success_rate']}%\n";
        
        if ($results['failed_tests'] > 0) {
            echo "\n❌ Failed Tests:\n";
            foreach ($results['results'] as $result) {
                if ($result['status'] === 'FAIL') {
                    echo "  - {$result['name']}: {$result['error']}\n";
                }
            }
            echo "\n🔴 X-UI Integration Test: FAILED\n";
        } else {
            echo "\n🟢 X-UI Integration Test: PASSED\n";
            echo "\n🎉 X-UI integration implemented and verified successfully!\n";
        }
    }
}

// Run tests if executed directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'] ?? '')) {
    $tester = new XUIIntegrationTest();
    $tester->runAllTests();
    $tester->printSummary();
}
