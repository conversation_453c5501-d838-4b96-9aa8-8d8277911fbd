# WeBot Tests
## تست‌های WeBot

این پوشه شامل تمام تست‌های خودکار WeBot است که برای اطمینان از کیفیت و عملکرد صحیح کد طراحی شده‌اند.

---

## 📁 ساختار تست‌ها

### 🔬 Unit/
**تست‌های واحد - تست کلاس‌ها و متدهای مجزا**

#### Controllers/
- **AuthControllerTest.php** - تست احراز هویت
- **UserControllerTest.php** - تست مدیریت کاربران
- **PaymentControllerTest.php** - تست پردازش پرداخت
- **ServiceControllerTest.php** - تست مدیریت سرویس‌ها

#### Services/
- **TelegramServiceTest.php** - تست API تلگرام
- **DatabaseServiceTest.php** - تست عملیات دیتابیس
- **PaymentServiceTest.php** - تست منطق پرداخت
- **PanelServiceTest.php** - تست اتصال به پنل‌ها

#### Models/
- **UserTest.php** - تست مدل کاربر
- **PaymentTest.php** - تست مدل پرداخت
- **ServiceTest.php** - تست مدل سرویس

### 🔗 Integration/
**تست‌های یکپارچگی - تست تعامل بین اجزاء**

#### Database/
- **UserRepositoryTest.php** - تست CRUD کاربران
- **PaymentRepositoryTest.php** - تست CRUD پرداخت‌ها
- **MigrationTest.php** - تست مایگریشن‌ها

#### API/
- **TelegramAPITest.php** - تست API تلگرام
- **PanelAPITest.php** - تست API پنل‌ها
- **PaymentAPITest.php** - تست API درگاه‌ها

#### Payments/
- **ZarinpalIntegrationTest.php** - تست درگاه زرین‌پال
- **NowpaymentsIntegrationTest.php** - تست درگاه NOWPayments

### 🎭 Feature/
**تست‌های عملکردی - تست سناریوهای کامل کاربر**

#### UserFlow/
- **UserRegistrationTest.php** - تست ثبت‌نام کاربر
- **ServicePurchaseTest.php** - تست خرید سرویس
- **ServiceRenewalTest.php** - تست تمدید سرویس

#### AdminFlow/
- **AdminPanelTest.php** - تست پنل ادمین
- **UserManagementTest.php** - تست مدیریت کاربران
- **ReportGenerationTest.php** - تست تولید گزارش

#### PaymentFlow/
- **PaymentProcessTest.php** - تست فرآیند پرداخت
- **RefundProcessTest.php** - تست فرآیند بازگشت وجه

---

## 🛠 ابزارهای تست

### PHPUnit
```bash
# اجرای تمام تست‌ها
./vendor/bin/phpunit

# اجرای تست‌های واحد
./vendor/bin/phpunit tests/Unit

# اجرای تست‌های یکپارچگی
./vendor/bin/phpunit tests/Integration

# اجرای تست‌های عملکردی
./vendor/bin/phpunit tests/Feature
```

### Coverage Report
```bash
# تولید گزارش پوشش کد
./vendor/bin/phpunit --coverage-html coverage/
```

---

## 📝 نحوه نوشتن تست

### تست واحد نمونه:
```php
<?php
namespace WeBot\Tests\Unit\Services;

use PHPUnit\Framework\TestCase;
use WeBot\Services\PaymentService;
use WeBot\Repositories\PaymentRepository;

class PaymentServiceTest extends TestCase
{
    private $paymentService;
    private $mockRepository;

    protected function setUp(): void
    {
        $this->mockRepository = $this->createMock(PaymentRepository::class);
        $this->paymentService = new PaymentService($this->mockRepository);
    }

    public function testCreatePayment()
    {
        // Arrange
        $paymentData = [
            'user_id' => 123,
            'amount' => 50000,
            'gateway' => 'zarinpal'
        ];

        $this->mockRepository
            ->expects($this->once())
            ->method('create')
            ->with($paymentData)
            ->willReturn(['id' => 1, ...$paymentData]);

        // Act
        $result = $this->paymentService->createPayment($paymentData);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals(1, $result['id']);
        $this->assertEquals(50000, $result['amount']);
    }
}
```

### تست یکپارچگی نمونه:
```php
<?php
namespace WeBot\Tests\Integration\Database;

use PHPUnit\Framework\TestCase;
use WeBot\Repositories\UserRepository;

class UserRepositoryTest extends TestCase
{
    private $userRepository;

    protected function setUp(): void
    {
        // Setup test database
        $this->userRepository = new UserRepository();
    }

    public function testCreateAndRetrieveUser()
    {
        // Arrange
        $userData = [
            'telegram_id' => 123456789,
            'username' => 'testuser',
            'first_name' => 'Test'
        ];

        // Act
        $userId = $this->userRepository->create($userData);
        $user = $this->userRepository->findById($userId);

        // Assert
        $this->assertNotNull($user);
        $this->assertEquals(123456789, $user['telegram_id']);
        $this->assertEquals('testuser', $user['username']);
    }
}
```

### تست عملکردی نمونه:
```php
<?php
namespace WeBot\Tests\Feature\UserFlow;

use PHPUnit\Framework\TestCase;
use WeBot\Application;

class UserRegistrationTest extends TestCase
{
    private $app;

    protected function setUp(): void
    {
        $this->app = new Application();
    }

    public function testCompleteUserRegistration()
    {
        // Simulate /start command
        $response = $this->app->handleMessage([
            'message' => [
                'from' => ['id' => 123456789, 'first_name' => 'Test'],
                'text' => '/start'
            ]
        ]);

        // Assert welcome message sent
        $this->assertStringContains('به WeBot خوش آمدید', $response);

        // Simulate accepting rules
        $response = $this->app->handleCallback([
            'callback_query' => [
                'from' => ['id' => 123456789],
                'data' => 'accept_rules'
            ]
        ]);

        // Assert user registered successfully
        $this->assertStringContains('قوانین تایید شد', $response);
    }
}
```

---

## 🎯 استراتژی تست

### Test Pyramid
```
    🔺 Feature Tests (کم)
   🔺🔺 Integration Tests (متوسط)  
  🔺🔺🔺 Unit Tests (زیاد)
```

### Coverage Goals
- **Unit Tests**: >90%
- **Integration Tests**: >80%
- **Feature Tests**: >70%
- **Overall**: >85%

---

## 🔧 تنظیمات PHPUnit

### phpunit.xml
```xml
<?xml version="1.0" encoding="UTF-8"?>
<phpunit bootstrap="tests/bootstrap.php"
         colors="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         stopOnFailure="false">
    
    <testsuites>
        <testsuite name="Unit">
            <directory>tests/Unit</directory>
        </testsuite>
        <testsuite name="Integration">
            <directory>tests/Integration</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory>tests/Feature</directory>
        </testsuite>
    </testsuites>
    
    <filter>
        <whitelist>
            <directory>src/</directory>
        </whitelist>
    </filter>
</phpunit>
```

### tests/bootstrap.php
```php
<?php
require_once __DIR__ . '/../vendor/autoload.php';

// Load test environment
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../', '.env.testing');
$dotenv->load();

// Setup test database
// Initialize test fixtures
```

---

## 📊 CI/CD Integration

### GitHub Actions
```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.1
      - name: Install dependencies
        run: composer install
      - name: Run tests
        run: ./vendor/bin/phpunit --coverage-clover coverage.xml
```

---

## 📋 بهترین روش‌ها

### 1. **Test Naming**:
- نام‌های توصیفی و واضح
- الگوی `testMethodName_Scenario_ExpectedResult`

### 2. **Test Structure**:
- Arrange, Act, Assert pattern
- یک assertion در هر تست

### 3. **Mocking**:
- Mock کردن dependencies خارجی
- استفاده از test doubles

### 4. **Data Providers**:
- استفاده از data providers برای تست‌های متعدد
- تست edge cases

---

*این ساختار برای اطمینان از کیفیت و پایداری WeBot طراحی شده است.*
