<?php

declare(strict_types=1);

namespace WeBot\Exceptions;

use Exception;
use Throwable;

/**
 * WeBot Base Exception
 *
 * Base exception class for all WeBot specific exceptions.
 * Provides additional context and logging capabilities.
 *
 * @package WeBot\Exceptions
 * @version 2.0
 */
class WeBotException extends Exception
{
    protected array $context = [];
    protected string $userMessage = '';

    public function __construct(
        string $message = '',
        int $code = 0,
        ?Throwable $previous = null,
        array $context = [],
        string $userMessage = ''
    ) {
        parent::__construct($message, $code, $previous);

        $this->context = $context;
        $this->userMessage = $userMessage ?: $message;
    }

    /**
     * Get exception context
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Set exception context
     */
    public function setContext(array $context): self
    {
        $this->context = $context;
        return $this;
    }

    /**
     * Add context data
     */
    public function addContext(string $key, $value): self
    {
        $this->context[$key] = $value;
        return $this;
    }

    /**
     * Get user-friendly message
     */
    public function getUserMessage(): string
    {
        return $this->userMessage ?: $this->getMessage();
    }

    /**
     * Set user-friendly message
     */
    public function setUserMessage(string $message): self
    {
        $this->userMessage = $message;
        return $this;
    }

    /**
     * Convert exception to array for logging
     */
    public function toArray(): array
    {
        return [
            'message' => $this->getMessage(),
            'user_message' => $this->getUserMessage(),
            'code' => $this->getCode(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'context' => $this->getContext(),
            'trace' => $this->getTraceAsString(),
        ];
    }

    /**
     * Convert exception to JSON
     */
    public function toJson(): string
    {
        return json_encode($this->toArray(), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }

    /**
     * Check if exception is critical
     */
    public function isCritical(): bool
    {
        return $this->getCode() >= 500;
    }

    /**
     * Check if exception should be reported
     */
    public function shouldReport(): bool
    {
        return true;
    }

    /**
     * Get HTTP status code for this exception
     */
    public function getHttpStatusCode(): int
    {
        return 500;
    }
}
