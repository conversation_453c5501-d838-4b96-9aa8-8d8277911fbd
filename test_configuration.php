<?php
/**
 * Configuration Loading Test for WeBot
 * 
 * This script tests the configuration system and verifies
 * that all config files can be loaded properly.
 */

declare(strict_types=1);

echo "=== WeBot Configuration Loading Test ===\n\n";

// Load the application
try {
    require_once __DIR__ . '/autoload.php';
    echo "✅ Application loaded successfully\n\n";
} catch (Exception $e) {
    echo "❌ Failed to load application: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 1: Config service availability
echo "1. Config Service Availability Test:\n";
try {
    $config = service('config');
    echo "   ✅ Config service is available\n";
    
    if (method_exists($config, 'get')) {
        echo "   ✅ Config service has get() method\n";
    } else {
        echo "   ❌ Config service missing get() method\n";
        exit(1);
    }
} catch (Exception $e) {
    echo "   ❌ Config service not available: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 2: Environment variables loading
echo "\n2. Environment Variables Loading Test:\n";
$envVars = [
    'APP_ENV' => 'Application environment',
    'APP_DEBUG' => 'Debug mode',
    'APP_TIMEZONE' => 'Application timezone',
    'DB_HOST' => 'Database host',
    'DB_DATABASE' => 'Database name'
];

$envOk = true;
foreach ($envVars as $var => $description) {
    $value = env($var);
    if ($value !== null) {
        echo "   ✅ {$var}: {$value} - {$description}\n";
    } else {
        echo "   ❌ {$var}: Not set - {$description}\n";
        $envOk = false;
    }
}

// Test 3: Config files existence
echo "\n3. Config Files Existence Test:\n";
$configFiles = [
    'config/app.php' => 'Application configuration',
    'config/database.php' => 'Database configuration',
    'config/logging.php' => 'Logging configuration',
    'config/cache.php' => 'Cache configuration',
    'config/validation.php' => 'Validation configuration'
];

$filesOk = true;
foreach ($configFiles as $file => $description) {
    if (file_exists($file)) {
        echo "   ✅ {$file}: Exists - {$description}\n";
    } else {
        echo "   ❌ {$file}: Missing - {$description}\n";
        $filesOk = false;
    }
}

// Test 4: Config loading functionality
echo "\n4. Config Loading Functionality Test:\n";
$configLoadingOk = true;

try {
    // Test loading app config
    $appConfig = config('app');
    if (is_array($appConfig)) {
        echo "   ✅ App config loaded as array\n";
    } else {
        echo "   ❌ App config not loaded properly\n";
        $configLoadingOk = false;
    }
} catch (Exception $e) {
    echo "   ❌ Failed to load app config: " . $e->getMessage() . "\n";
    $configLoadingOk = false;
}

try {
    // Test specific config values
    $appName = config('app.name', 'WeBot');
    echo "   ✅ App name: {$appName}\n";
} catch (Exception $e) {
    echo "   ❌ Failed to get app name: " . $e->getMessage() . "\n";
    $configLoadingOk = false;
}

try {
    // Test database config
    $dbConfig = config('database');
    if (is_array($dbConfig)) {
        echo "   ✅ Database config loaded as array\n";
    } else {
        echo "   ❌ Database config not loaded properly\n";
        $configLoadingOk = false;
    }
} catch (Exception $e) {
    echo "   ❌ Failed to load database config: " . $e->getMessage() . "\n";
    $configLoadingOk = false;
}

// Test 5: Config helper function
echo "\n5. Config Helper Function Test:\n";
$helperOk = true;

try {
    $testValue = config('app.name', 'default');
    echo "   ✅ config() helper function works\n";
} catch (Exception $e) {
    echo "   ❌ config() helper function failed: " . $e->getMessage() . "\n";
    $helperOk = false;
}

try {
    $defaultValue = config('non.existent.key', 'default_value');
    if ($defaultValue === 'default_value') {
        echo "   ✅ Default value handling works\n";
    } else {
        echo "   ❌ Default value handling failed\n";
        $helperOk = false;
    }
} catch (Exception $e) {
    echo "   ❌ Default value test failed: " . $e->getMessage() . "\n";
    $helperOk = false;
}

// Test 6: Panel configurations
echo "\n6. Panel Configurations Test:\n";
$panelConfigFiles = [
    'config/panels/marzban.php' => 'Marzban panel config',
    'config/panels/marzneshin.php' => 'Marzneshin panel config',
    'config/panels/x-ui.php' => 'X-UI panel config'
];

$panelConfigsOk = true;
foreach ($panelConfigFiles as $file => $description) {
    if (file_exists($file)) {
        echo "   ✅ {$file}: Exists - {$description}\n";
        
        // Try to load the config
        try {
            $panelConfig = include $file;
            if (is_array($panelConfig)) {
                echo "   ✅ {$file}: Loads properly\n";
            } else {
                echo "   ❌ {$file}: Invalid format\n";
                $panelConfigsOk = false;
            }
        } catch (Exception $e) {
            echo "   ❌ {$file}: Load error - " . $e->getMessage() . "\n";
            $panelConfigsOk = false;
        }
    } else {
        echo "   ❌ {$file}: Missing - {$description}\n";
        $panelConfigsOk = false;
    }
}

echo "\n=== Overall Status ===\n";
if ($envOk && $filesOk && $configLoadingOk && $helperOk && $panelConfigsOk) {
    echo "✅ Configuration system is working perfectly!\n";
    echo "ℹ️  All config files are loaded and accessible\n";
    exit(0);
} else {
    echo "❌ Configuration system has some issues.\n";
    echo "\n🔧 To fix configuration issues:\n";
    echo "   1. Ensure all config files exist in config/ directory\n";
    echo "   2. Check config file syntax (valid PHP arrays)\n";
    echo "   3. Verify environment variables are set\n";
    echo "   4. Check Config class implementation\n";
    exit(1);
}
