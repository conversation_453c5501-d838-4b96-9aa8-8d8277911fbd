# WeBot Installation Guide
## راهنمای نصب WeBot

⚠️ **مشکلات Dependencies حل شده!** ⚠️

اگر خطاهای زیر را می‌بینید:
- `Call to unknown method: Dotenv\Dotenv::createImmutable()`
- `Use of unknown class: 'League\Container\Container'`

**راه‌حل:** نصب dependencies با دستور زیر:
```bash
php scripts/install-dependencies.php
```

---

# WeBot Installation Guide
## راهنمای نصب WeBot

**نسخه**: 2.0  
**تاریخ**: 2025-01-07

---

## 📋 پیش‌نیازها

### 1. سرور Requirements
- **PHP**: 8.1 یا بالاتر
- **MySQL**: 5.7 یا بالاتر (یا MariaDB 10.3+)
- **Web Server**: Apache یا Nginx
- **Composer**: برای مدیریت dependencies
- **Git**: برای دریافت کد

### 2. PHP Extensions
```bash
# Extensions مورد نیاز
php -m | grep -E "(curl|json|pdo|mbstring|gd|mysqli|zip|xml)"
```

مطمئن شوید این extension ها نصب هستند:
- `curl` - برای API calls
- `json` - برای پردازش JSON
- `pdo` - برای دیتابیس
- `pdo_mysql` - برای MySQL
- `mbstring` - برای رشته‌های UTF-8
- `gd` - برای پردازش تصاویر
- `zip` - برای فایل‌های فشرده
- `xml` - برای پردازش XML

---

## 🚀 نصب سریع

### 1. دانلود کد
```bash
# Clone repository
git clone https://github.com/webotdev/webot.git
cd webot

# یا دانلود ZIP
wget https://github.com/webotdev/webot/archive/main.zip
unzip main.zip
cd webot-main
```

### 2. نصب Dependencies
```bash
# نصب Composer (اگر نصب نیست)
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# نصب dependencies
composer install --no-dev --optimize-autoloader

# برای development
composer install
```

### 3. تنظیم Environment
```bash
# کپی فایل environment
cp .env.example .env

# ویرایش تنظیمات
nano .env
```

### 4. تنظیم دیتابیس
```bash
# ایجاد دیتابیس
mysql -u root -p
CREATE DATABASE webot_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'webot_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON webot_db.* TO 'webot_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# اجرای migrations
php createDB.php
```

### 5. تنظیم وب سرور
```bash
# تنظیم مجوزها
chmod -R 755 .
chmod -R 777 public/uploads
chmod -R 777 public/temp
chmod -R 777 storage/logs

# تنظیم ownership
chown -R www-data:www-data .
```

---

## ⚙️ تنظیمات تفصیلی

### 1. فایل .env
```env
# Application
APP_ENV=production
APP_DEBUG=false
APP_TIMEZONE=Asia/Tehran

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_bot_token_here
WEBHOOK_URL=https://yourdomain.com/webhook
ADMIN_ID=123456789

# Database
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=webot_db
DB_USERNAME=webot_user
DB_PASSWORD=secure_password

# Panels
MARZBAN_URL=https://panel.example.com
MARZBAN_USERNAME=admin
MARZBAN_PASSWORD=admin_password

# Payment Gateways
ZARINPAL_MERCHANT_ID=your_merchant_id
NOWPAYMENTS_API_KEY=your_api_key
```

### 2. Apache Configuration
```apache
<VirtualHost *:80>
    ServerName yourdomain.com
    DocumentRoot /path/to/webot/public
    
    <Directory /path/to/webot/public>
        AllowOverride All
        Require all granted
    </Directory>
    
    # Security headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    
    # Error and access logs
    ErrorLog ${APACHE_LOG_DIR}/webot_error.log
    CustomLog ${APACHE_LOG_DIR}/webot_access.log combined
</VirtualHost>
```

### 3. Nginx Configuration
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    root /path/to/webot/public;
    index index.php;

    # Security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";

    # PHP handling
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Static files
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Deny access to sensitive files
    location ~ /\. {
        deny all;
    }

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
}
```

---

## 🔧 تست نصب

### 1. تست Autoloader
```bash
php test_autoload.php
```

### 2. تست دیتابیس
```bash
php -r "
require 'autoload.php';
try {
    \$db = service('database');
    echo 'Database connection: OK\n';
} catch (Exception \$e) {
    echo 'Database error: ' . \$e->getMessage() . '\n';
}
"
```

### 3. تست Telegram Bot
```bash
# تست webhook
curl -X POST https://yourdomain.com/webhook \
  -H "Content-Type: application/json" \
  -d '{"message":{"text":"/start","from":{"id":123456789}}}'
```

### 4. اجرای تست‌ها
```bash
# Unit tests
./vendor/bin/phpunit tests/Unit

# Integration tests  
./vendor/bin/phpunit tests/Integration

# تمام تست‌ها
./vendor/bin/phpunit
```

---

## 🔐 امنیت

### 1. تنظیمات امنیتی
```bash
# حذف فایل‌های غیرضروری
rm test_autoload.php
rm INSTALLATION.md

# تنظیم مجوزهای امن
find . -type f -exec chmod 644 {} \;
find . -type d -exec chmod 755 {} \;
chmod 600 .env
```

### 2. Firewall Rules
```bash
# فقط پورت‌های ضروری
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw enable
```

### 3. SSL Certificate
```bash
# با Let's Encrypt
certbot --apache -d yourdomain.com

# یا با Nginx
certbot --nginx -d yourdomain.com
```

---

## 📊 مانیتورینگ

### 1. Log Files
```bash
# Application logs
tail -f storage/logs/webot.log

# Web server logs
tail -f /var/log/apache2/webot_error.log
tail -f /var/log/nginx/error.log
```

### 2. Performance Monitoring
```bash
# PHP-FPM status
curl http://localhost/status

# Database performance
mysql -e "SHOW PROCESSLIST;"
```

---

## 🔄 به‌روزرسانی

### 1. Backup
```bash
# Backup database
mysqldump -u webot_user -p webot_db > backup_$(date +%Y%m%d).sql

# Backup files
tar -czf webot_backup_$(date +%Y%m%d).tar.gz .
```

### 2. Update
```bash
# Pull latest changes
git pull origin main

# Update dependencies
composer install --no-dev --optimize-autoloader

# Run migrations
php install/update.php

# Clear cache
rm -rf public/temp/cache/*
```

---

## 🆘 عیب‌یابی

### مشکلات رایج:

#### 1. Composer not found
```bash
# نصب Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

#### 2. Permission denied
```bash
# تنظیم مجوزها
sudo chown -R www-data:www-data .
sudo chmod -R 755 .
sudo chmod -R 777 public/uploads public/temp
```

#### 3. Database connection failed
```bash
# بررسی تنظیمات .env
# بررسی MySQL service
sudo systemctl status mysql
```

#### 4. Webhook not working
```bash
# بررسی SSL certificate
curl -I https://yourdomain.com

# بررسی logs
tail -f storage/logs/webot.log
```

---

## 📞 پشتیبانی

- **مستندات**: [docs/](docs/)
- **Issues**: GitHub Issues
- **تلگرام**: @webotch

---

*این راهنما برای نصب حرفه‌ای WeBot طراحی شده است.*
