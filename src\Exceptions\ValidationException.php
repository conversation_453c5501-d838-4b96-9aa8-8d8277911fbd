<?php

declare(strict_types=1);

namespace WeBot\Exceptions;

/**
 * Validation Exception
 *
 * Thrown when input validation fails.
 *
 * @package WeBot\Exceptions
 * @version 2.0
 */
class ValidationException extends WeBotException
{
    protected array $errors = [];

    public function __construct(
        string $message = 'Validation failed',
        array $errors = [],
        int $code = 422
    ) {
        parent::__construct($message, $code);

        $this->errors = $errors;
        $this->setUserMessage('اطلاعات وارد شده صحیح نیست.');
    }

    /**
     * Get validation errors
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Set validation errors
     */
    public function setErrors(array $errors): self
    {
        $this->errors = $errors;
        return $this;
    }

    /**
     * Add validation error
     */
    public function addError(string $field, string $message): self
    {
        $this->errors[$field][] = $message;
        return $this;
    }

    /**
     * Check if field has errors
     */
    public function hasError(string $field): bool
    {
        return isset($this->errors[$field]);
    }

    /**
     * Get errors for specific field
     */
    public function getFieldErrors(string $field): array
    {
        return $this->errors[$field] ?? [];
    }

    /**
     * Get first error message
     */
    public function getFirstError(): string
    {
        foreach ($this->errors as $fieldErrors) {
            if (!empty($fieldErrors)) {
                return is_array($fieldErrors) ? $fieldErrors[0] : $fieldErrors;
            }
        }

        return $this->getMessage();
    }

    /**
     * Get HTTP status code
     */
    public function getHttpStatusCode(): int
    {
        return 422;
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return array_merge(parent::toArray(), [
            'errors' => $this->getErrors(),
            'first_error' => $this->getFirstError(),
        ]);
    }
}
