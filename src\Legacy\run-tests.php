<?php

declare(strict_types=1);

/**
 * WeBot Test Runner Script
 * 
 * Command-line script to run WeBot test suite with various options.
 * 
 * Usage:
 *   php run-tests.php                    # Run all tests
 *   php run-tests.php --unit             # Run only unit tests
 *   php run-tests.php --integration      # Run only integration tests
 *   php run-tests.php --feature          # Run only feature tests
 *   php run-tests.php --file tests/Unit/Controllers/UserControllerTest.php
 *   php run-tests.php --verbose          # Verbose output
 *   php run-tests.php --help             # Show help
 * 
 * @package WeBot
 * @version 2.0
 */

// Load autoloader
require_once __DIR__ . '/autoload.php';

use WeBot\Tests\Framework\TestRunner;

/**
 * Display help information
 */
function showHelp(): void
{
    echo "WeBot Test Runner\n";
    echo "================\n\n";
    echo "Usage: php run-tests.php [options]\n\n";
    echo "Options:\n";
    echo "  --unit              Run only unit tests\n";
    echo "  --integration       Run only integration tests\n";
    echo "  --feature           Run only feature tests\n";
    echo "  --e2e               Run only E2E tests\n";
    echo "  --file <path>       Run specific test file\n";
    echo "  --verbose, -v       Verbose output\n";
    echo "  --help, -h          Show this help message\n\n";
    echo "Examples:\n";
    echo "  php run-tests.php\n";
    echo "  php run-tests.php --unit --verbose\n";
    echo "  php run-tests.php --file tests/Unit/Controllers/UserControllerTest.php\n";
    echo "  php run-tests.php --integration\n\n";
}

/**
 * Parse command line arguments
 */
function parseArguments(array $argv): array
{
    $options = [
        'type' => 'all',
        'file' => null,
        'verbose' => false,
        'help' => false
    ];
    
    for ($i = 1; $i < count($argv); $i++) {
        switch ($argv[$i]) {
            case '--unit':
                $options['type'] = 'Unit';
                break;
            case '--integration':
                $options['type'] = 'Integration';
                break;
            case '--feature':
                $options['type'] = 'Feature';
                break;
            case '--e2e':
                $options['type'] = 'E2E';
                break;
            case '--file':
                if (isset($argv[$i + 1])) {
                    $options['file'] = $argv[$i + 1];
                    $i++; // Skip next argument
                } else {
                    echo "Error: --file option requires a file path\n";
                    exit(1);
                }
                break;
            case '--verbose':
            case '-v':
                $options['verbose'] = true;
                break;
            case '--help':
            case '-h':
                $options['help'] = true;
                break;
            default:
                echo "Error: Unknown option {$argv[$i]}\n";
                echo "Use --help for usage information\n";
                exit(1);
        }
    }
    
    return $options;
}

/**
 * Validate test environment
 */
function validateTestEnvironment(): void
{
    // Check if we're in test mode
    if (getenv('WEBOT_ENV') !== 'testing') {
        putenv('WEBOT_ENV=testing');
    }
    
    // Check required directories
    $requiredDirs = [
        __DIR__ . '/tests',
        __DIR__ . '/tests/Unit',
        __DIR__ . '/tests/Integration',
        __DIR__ . '/tests/Feature',
        __DIR__ . '/tests/Framework'
    ];
    
    foreach ($requiredDirs as $dir) {
        if (!is_dir($dir)) {
            echo "Error: Required test directory not found: {$dir}\n";
            exit(1);
        }
    }
    
    // Check if test framework files exist
    $requiredFiles = [
        __DIR__ . '/tests/Framework/TestCase.php',
        __DIR__ . '/tests/Framework/TestRunner.php'
    ];
    
    foreach ($requiredFiles as $file) {
        if (!file_exists($file)) {
            echo "Error: Required test framework file not found: {$file}\n";
            exit(1);
        }
    }
}

/**
 * Setup test environment
 */
function setupTestEnvironment(): void
{
    // Set test mode environment variables
    putenv('WEBOT_TEST_MODE=true');
    putenv('WEBOT_ENV=testing');
    putenv('DISABLE_EXTERNAL_APIS=true');
    
    // Disable error reporting for cleaner test output
    error_reporting(E_ERROR | E_PARSE);
    
    // Set memory limit for tests
    ini_set('memory_limit', '256M');
    
    // Set time limit for tests
    set_time_limit(300); // 5 minutes
}

/**
 * Main execution
 */
function main(array $argv): void
{
    echo "🧪 WeBot Test Suite\n";
    echo "==================\n\n";
    
    // Parse arguments
    $options = parseArguments($argv);
    
    // Show help if requested
    if ($options['help']) {
        showHelp();
        return;
    }
    
    // Validate environment
    try {
        validateTestEnvironment();
        setupTestEnvironment();
    } catch (Exception $e) {
        echo "❌ Environment validation failed: " . $e->getMessage() . "\n";
        exit(1);
    }
    
    // Create test runner
    $runner = new TestRunner($options['verbose']);
    
    try {
        // Run tests based on options
        if ($options['file']) {
            // Run specific file
            if (!file_exists($options['file'])) {
                echo "❌ Test file not found: {$options['file']}\n";
                exit(1);
            }
            
            echo "🎯 Running specific test file: {$options['file']}\n\n";
            $runner->runTestFile($options['file']);
            
        } elseif ($options['type'] !== 'all') {
            // Run specific type
            echo "🎯 Running {$options['type']} tests only\n\n";
            $runner->discoverTests();
            $runner->runTestsByType($options['type']);
            
        } else {
            // Run all tests
            echo "🚀 Running all tests\n\n";
            $runner->discoverTests();
            $runner->runAllTests();
        }
        
    } catch (Exception $e) {
        echo "❌ Test execution failed: " . $e->getMessage() . "\n";
        echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
        exit(1);
    }
}

// Check if script is run directly
if (basename($_SERVER['SCRIPT_NAME']) === 'run-tests.php') {
    main($argv);
}
