<?php
/**
 * WeBot v2.0 - Main Entry Point
 * نقطه ورود اصلی پروژه WeBot
 */

declare(strict_types=1);

// تنظیم timezone
date_default_timezone_set('Asia/Tehran');

// تنظیم error reporting
error_reporting(E_ALL);
ini_set('display_errors', '1');

// تنظیم memory limit
ini_set('memory_limit', '512M');

// تنظیم execution time
set_time_limit(300);

// بارگذاری autoloader
require_once __DIR__ . '/../vendor/autoload.php';

use WeBot\Core\Config;
use WeBot\Core\Database;
use WeBot\Core\TelegramBot;
use WeBot\Utils\Logger; // Correct namespace

try {
    // بارگذاری تنظیمات
    $config = new Config();
    
    // ErrorHandler has been removed or replaced, commenting out for now.
    // $errorHandler = new ErrorHandler($config);
    // $errorHandler->register();
    
    // راه‌اندازی logger
    Logger::configure($config->get('logging', []));
    $logger = Logger::getInstance();
    
    // بررسی محیط
    if ($config->get('app_env') === 'production') {
        ini_set('display_errors', '0');
        error_reporting(0);
    }
    
    // اتصال به دیتابیس
    $database = new Database($config->get('database'));
    
    // راه‌اندازی Telegram Bot
    $bot = new TelegramBot($config, $database, $logger);
    
    // نمایش صفحه اصلی
    ?>
    <!DOCTYPE html>
    <html lang="fa" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>WeBot v2.0 - Telegram VPN Bot</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            
            body {
                font-family: 'Vazirmatn', 'Tahoma', sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
            }
            
            .container {
                text-align: center;
                background: rgba(255, 255, 255, 0.1);
                padding: 3rem;
                border-radius: 20px;
                backdrop-filter: blur(10px);
                box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
                border: 1px solid rgba(255, 255, 255, 0.18);
                max-width: 600px;
                margin: 2rem;
            }
            
            .logo {
                font-size: 4rem;
                margin-bottom: 1rem;
            }
            
            h1 {
                font-size: 2.5rem;
                margin-bottom: 1rem;
                font-weight: 700;
            }
            
            .version {
                font-size: 1.2rem;
                opacity: 0.8;
                margin-bottom: 2rem;
            }
            
            .status {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1rem;
                margin: 2rem 0;
            }
            
            .status-item {
                background: rgba(255, 255, 255, 0.1);
                padding: 1rem;
                border-radius: 10px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
            
            .status-item h3 {
                margin-bottom: 0.5rem;
                font-size: 1.1rem;
            }
            
            .status-ok {
                color: #4ade80;
            }
            
            .status-error {
                color: #f87171;
            }
            
            .status-warning {
                color: #fbbf24;
            }
            
            .links {
                margin-top: 2rem;
                display: flex;
                gap: 1rem;
                justify-content: center;
                flex-wrap: wrap;
            }
            
            .link {
                background: rgba(255, 255, 255, 0.2);
                color: white;
                text-decoration: none;
                padding: 0.8rem 1.5rem;
                border-radius: 25px;
                transition: all 0.3s ease;
                border: 1px solid rgba(255, 255, 255, 0.3);
            }
            
            .link:hover {
                background: rgba(255, 255, 255, 0.3);
                transform: translateY(-2px);
            }
            
            .footer {
                margin-top: 2rem;
                opacity: 0.7;
                font-size: 0.9rem;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="logo">🤖</div>
            <h1>WeBot v2.0</h1>
            <div class="version">Telegram VPN Bot Management System</div>
            
            <div class="status">
                <div class="status-item">
                    <h3>🔧 System Status</h3>
                    <div class="status-ok">✅ Online</div>
                </div>
                
                <div class="status-item">
                    <h3>🗄️ Database</h3>
                    <div class="<?php echo $database->isConnected() ? 'status-ok' : 'status-error'; ?>">
                        <?php echo $database->isConnected() ? '✅ Connected' : '❌ Disconnected'; ?>
                    </div>
                </div>
                
                <div class="status-item">
                    <h3>🤖 Bot Status</h3>
                    <div class="status-ok">✅ Ready</div>
                </div>
                
                <div class="status-item">
                    <h3>📊 Environment</h3>
                    <div class="status-warning"><?php echo ucfirst($config->get('APP_ENV', 'unknown')); ?></div>
                </div>
            </div>
            
            <div class="links">
                <a href="/webhook" class="link">🔗 Webhook</a>
                <a href="/admin" class="link">⚙️ Admin Panel</a>
                <a href="/docs" class="link">📚 Documentation</a>
                <a href="/api/status" class="link">📊 API Status</a>
            </div>
            
            <div class="footer">
                <p>Powered by WeBot Framework</p>
                <p>PHP <?php echo PHP_VERSION; ?> | <?php echo date('Y-m-d H:i:s'); ?></p>
            </div>
        </div>
    </body>
    </html>
    <?php
    
} catch (Exception $e) {
    // خطای کلی
    http_response_code(500);
    
    if (isset($config) && $config->get('APP_DEBUG', false)) {
        echo "<h1>Application Error</h1>";
        echo "<p><strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
        echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    } else {
        echo "<h1>Service Temporarily Unavailable</h1>";
        echo "<p>Please try again later.</p>";
    }
    
    // لاگ خطا
    if (isset($logger)) {
        $logger->error('Application Error: ' . $e->getMessage(), [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]);
    } else {
        error_log('WeBot Error: ' . $e->getMessage());
    }
}
?>
