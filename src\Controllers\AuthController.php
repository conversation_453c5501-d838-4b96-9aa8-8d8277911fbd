<?php

declare(strict_types=1);

namespace WeBot\Controllers;

use WeBot\Services\AuthService;

/**
 * Authentication Controller
 *
 * Handles user authentication, registration, login,
 * and session management for the bot.
 *
 * @package WeBot\Controllers
 * @version 2.0
 */
class AuthController extends BaseController
{
    private AuthService $authService;

    public function __construct($container)
    {
        parent::__construct($container);
        $this->authService = $container->get('authService');
    }

    /**
     * Handle user registration
     */
    public function register(array $message): array
    {
        try {
            $this->initialize(['message' => $message]);
            $this->logAction('registration_attempt');

            // Check if user already exists
            $existingUser = $this->getUserInfo();
            if ($existingUser) {
                return $this->sendMessage('شما قبلاً ثبت نام کرده‌اید.');
            }

            // Create new user
            $userData = [
                'telegram_id' => $this->fromId,
                'username' => $this->username,
                'first_name' => $this->firstName,
                'last_name' => $this->lastName ?? '',
                'language_code' => $this->languageCode ?? 'fa',
                'status' => 'active',
                'role' => 'user',
                'registered_at' => date('Y-m-d H:i:s')
            ];

            $result = $this->authService->registerUser($userData);

            if ($result['success']) {
                $this->logAction('user_registered', ['user_id' => $result['user_id']]);

                $text = "🎉 **خوش آمدید به WeBot!**\n\n";
                $text .= "✅ ثبت نام شما با موفقیت انجام شد.\n";
                $text .= "🆔 شناسه کاربری: {$result['user_id']}\n\n";
                $text .= "حالا می‌توانید از تمام امکانات ربات استفاده کنید.";

                $keyboard = $this->createInlineKeyboard([
                    [
                        ['text' => '🏠 منو اصلی', 'callback_data' => 'main_menu']
                    ],
                    [
                        ['text' => '👤 پروفایل من', 'callback_data' => 'profile'],
                        ['text' => '🛒 خرید سرویس', 'callback_data' => 'buy_service']
                    ]
                ]);

                return $this->sendMessage($text, $keyboard);
            } else {
                return $this->sendMessage('❌ خطا در ثبت نام: ' . $result['error']);
            }
        } catch (\Throwable $e) {
            return $this->handleError($e, 'خطا در فرآیند ثبت نام.');
        }
    }

    /**
     * Handle user login
     */
    public function login(array $message): array
    {
        try {
            $this->initialize(['message' => $message]);
            $this->logAction('login_attempt');

            $userInfo = $this->getUserInfo();
            if (!$userInfo) {
                return $this->register($message);
            }

            // Update last login
            $this->authService->updateLastLogin($this->fromId);

            // Check user status
            if ($userInfo['status'] !== 'active') {
                $statusMessages = [
                    'suspended' => 'حساب کاربری شما مسدود شده است. لطفاً با پشتیبانی تماس بگیرید.',
                    'banned' => 'حساب کاربری شما مسدود شده است.',
                    'pending' => 'حساب کاربری شما در انتظار تأیید است.'
                ];

                return $this->sendMessage($statusMessages[$userInfo['status']] ?? 'وضعیت حساب کاربری نامشخص است.');
            }

            $this->logAction('user_logged_in');

            $text = "👋 **سلام {$userInfo['first_name']}!**\n\n";
            $text .= "خوش برگشتید به WeBot.\n";
            $text .= "📅 آخرین ورود: " . ($userInfo['last_login_at'] ?? 'اولین بار');

            $keyboard = $this->createInlineKeyboard([
                [
                    ['text' => '🏠 منو اصلی', 'callback_data' => 'main_menu']
                ],
                [
                    ['text' => '📊 سرویس‌های من', 'callback_data' => 'my_services'],
                    ['text' => '💰 موجودی', 'callback_data' => 'balance']
                ]
            ]);

            return $this->sendMessage($text, $keyboard);
        } catch (\Throwable $e) {
            return $this->handleError($e, 'خطا در فرآیند ورود.');
        }
    }

    /**
     * Handle logout
     */
    public function logout(): array
    {
        try {
            $this->logAction('logout_attempt');

            $userInfo = $this->getUserInfo();
            if (!$userInfo) {
                return $this->sendMessage('شما وارد نشده‌اید.');
            }

            // Clear user session
            $this->authService->clearUserSession($this->fromId);

            $this->logAction('user_logged_out');

            $text = "👋 **خداحافظ!**\n\n";
            $text .= "شما با موفقیت از حساب کاربری خود خارج شدید.\n";
            $text .= "برای ورود مجدد از دستور /start استفاده کنید.";

            return $this->sendMessage($text);
        } catch (\Throwable $e) {
            return $this->handleError($e, 'خطا در فرآیند خروج.');
        }
    }

    /**
     * Handle profile view
     */
    public function viewProfile(): array
    {
        try {
            $this->logAction('profile_view');

            $userInfo = $this->getUserInfo();
            if (!$userInfo) {
                return $this->sendMessage('ابتدا باید ثبت نام کنید.');
            }

            $text = "👤 **پروفایل کاربری**\n\n";
            $text .= "🆔 شناسه: {$userInfo['id']}\n";
            $text .= "👤 نام: {$userInfo['first_name']} {$userInfo['last_name']}\n";
            $text .= "📱 نام کاربری: @{$userInfo['username']}\n";
            $text .= "🌐 زبان: {$userInfo['language_code']}\n";
            $text .= "📊 وضعیت: {$this->getStatusText($userInfo['status'])}\n";
            $text .= "🎭 نقش: {$this->getRoleText($userInfo['role'])}\n";
            $text .= "📅 تاریخ عضویت: {$userInfo['created_at']}\n";
            $text .= "🕐 آخرین ورود: " . ($userInfo['last_login_at'] ?? 'نامشخص');

            $keyboard = $this->createInlineKeyboard([
                [
                    ['text' => '✏️ ویرایش پروفایل', 'callback_data' => 'edit_profile']
                ],
                [
                    ['text' => '🔐 تغییر رمز عبور', 'callback_data' => 'change_password'],
                    ['text' => '🌐 تغییر زبان', 'callback_data' => 'change_language']
                ],
                [
                    ['text' => '🏠 منو اصلی', 'callback_data' => 'main_menu']
                ]
            ]);

            return $this->sendMessage($text, $keyboard);
        } catch (\Throwable $e) {
            return $this->handleError($e, 'خطا در نمایش پروفایل.');
        }
    }

    /**
     * Handle profile editing
     */
    public function editProfile(array $callbackQuery): array
    {
        try {
            $this->initialize(['callback_query' => $callbackQuery]);

            $userInfo = $this->getUserInfo();
            if (!$userInfo) {
                return $this->answerCallback('ابتدا باید ثبت نام کنید.');
            }

            $text = "✏️ **ویرایش پروفایل**\n\n";
            $text .= "کدام بخش از پروفایل خود را می‌خواهید ویرایش کنید؟";

            $keyboard = $this->createInlineKeyboard([
                [
                    ['text' => '👤 نام', 'callback_data' => 'edit_name'],
                    ['text' => '📱 نام کاربری', 'callback_data' => 'edit_username']
                ],
                [
                    ['text' => '🌐 زبان', 'callback_data' => 'edit_language']
                ],
                [
                    ['text' => '🔙 بازگشت', 'callback_data' => 'profile']
                ]
            ]);

            return $this->editMessage($text, $keyboard);
        } catch (\Throwable $e) {
            return $this->handleError($e, 'خطا در ویرایش پروفایل.');
        }
    }

    /**
     * Handle language change
     */
    public function changeLanguage(array $callbackQuery): array
    {
        try {
            $this->initialize(['callback_query' => $callbackQuery]);

            $userInfo = $this->getUserInfo();
            if (!$userInfo) {
                return $this->answerCallback('ابتدا باید ثبت نام کنید.');
            }

            $text = "🌐 **انتخاب زبان**\n\n";
            $text .= "لطفاً زبان مورد نظر خود را انتخاب کنید:";

            $keyboard = $this->createInlineKeyboard([
                [
                    ['text' => '🇮🇷 فارسی', 'callback_data' => 'lang_fa'],
                    ['text' => '🇺🇸 English', 'callback_data' => 'lang_en']
                ],
                [
                    ['text' => '🇦🇪 العربية', 'callback_data' => 'lang_ar']
                ],
                [
                    ['text' => '🔙 بازگشت', 'callback_data' => 'profile']
                ]
            ]);

            return $this->editMessage($text, $keyboard);
        } catch (\Throwable $e) {
            return $this->handleError($e, 'خطا در تغییر زبان.');
        }
    }

    /**
     * Handle language selection
     */
    public function setLanguage(array $callbackQuery): array
    {
        try {
            $this->initialize(['callback_query' => $callbackQuery]);

            $userInfo = $this->getUserInfo();
            if (!$userInfo) {
                return $this->answerCallback('ابتدا باید ثبت نام کنید.');
            }

            $language = str_replace('lang_', '', $this->data);

            $result = $this->authService->updateUserLanguage($this->fromId, $language);

            if ($result['success']) {
                $languageNames = [
                    'fa' => 'فارسی',
                    'en' => 'English',
                    'ar' => 'العربية'
                ];

                $languageName = $languageNames[$language] ?? $language;

                return $this->answerCallback("✅ زبان به {$languageName} تغییر یافت.");
            } else {
                return $this->answerCallback('❌ خطا در تغییر زبان.');
            }
        } catch (\Throwable $e) {
            return $this->handleError($e, 'خطا در تنظیم زبان.');
        }
    }

    /**
     * Get status text
     */
    private function getStatusText(string $status): string
    {
        return match ($status) {
            'active' => '✅ فعال',
            'suspended' => '⏸️ معلق',
            'banned' => '🚫 مسدود',
            'pending' => '⏳ در انتظار تأیید',
            default => '❓ نامشخص'
        };
    }

    /**
     * Get role text
     */
    private function getRoleText(string $role): string
    {
        return match ($role) {
            'user' => '👤 کاربر عادی',
            'vip' => '⭐ کاربر ویژه',
            'moderator' => '🛡️ مدیر',
            'admin' => '👑 ادمین',
            default => '❓ نامشخص'
        };
    }
}
