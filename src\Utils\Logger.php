<?php

declare(strict_types=1);

namespace WeBot\Utils;

use Monolog\Handler\StreamHandler;
use Monolog\Logger as MonologLogger;
use Monolog\Level;
use Monolog\Processor\UidProcessor;
use Psr\Log\LoggerInterface;

class Logger implements LoggerInterface
{
    private static ?MonologLogger $instance = null;

    private function __construct()
    {
        // private constructor to prevent direct instantiation
    }

    public static function getInstance(): MonologLogger
    {
        if (self::$instance === null) {
            self::$instance = new MonologLogger('webot');
            self::$instance->pushProcessor(new UidProcessor());
            // Default handler in case configure is not called
            self::$instance->pushHandler(new StreamHandler('php://stderr', MonologLogger::DEBUG));
        }

        return self::$instance;
    }

    public static function configure(array $config): void
    {
        $logger = self::getInstance();

        // Clear existing handlers
        $logger->setHandlers([]);

        $defaultChannel = $config['default'] ?? 'stack';
        $channels = $config['channels'] ?? [];

        if (isset($channels[$defaultChannel])) {
            $channelConfig = $channels[$defaultChannel];
            if ($channelConfig['driver'] === 'stack') {
                foreach ($channelConfig['channels'] as $stackedChannelName) {
                    if (isset($channels[$stackedChannelName])) {
                        self::addChannelHandler($logger, $channels[$stackedChannelName]);
                    }
                }
            } else {
                self::addChannelHandler($logger, $channelConfig);
            }
        } else {
            // Fallback to a single file logger if config is weird
            $logger->pushHandler(new StreamHandler(storage_path('logs/webot.log'), MonologLogger::DEBUG));
        }
    }

    private static function addChannelHandler(MonologLogger $logger, array $channelConfig): void
    {
        $driver = $channelConfig['driver'] ?? 'single';
        $path = $channelConfig['path'] ?? storage_path('logs/webot.log');
        $levelName = $channelConfig['level'] ?? 'debug';
        $level = Level::fromName(strtoupper($levelName)) ?? Level::Debug;

        switch ($driver) {
            case 'single':
                $logger->pushHandler(new StreamHandler($path, $level));
                break;
            case 'daily':
                // Monolog does not have a daily handler out of the box like this.
                // Using StreamHandler with a date in the path.
                $path = str_replace('.log', '-' . date('Y-m-d') . '.log', $path);
                $logger->pushHandler(new StreamHandler($path, $level));
                break;
            // Other drivers like slack, syslog etc. can be added here.
        }
    }

    public function emergency($message, array $context = []): void
    {
        self::getInstance()->emergency($message, $context);
    }

    public function alert($message, array $context = []): void
    {
        self::getInstance()->alert($message, $context);
    }

    public function critical($message, array $context = []): void
    {
        self::getInstance()->critical($message, $context);
    }

    public function error($message, array $context = []): void
    {
        self::getInstance()->error($message, $context);
    }

    public function warning($message, array $context = []): void
    {
        self::getInstance()->warning($message, $context);
    }

    public function notice($message, array $context = []): void
    {
        self::getInstance()->notice($message, $context);
    }

    public function info($message, array $context = []): void
    {
        self::getInstance()->info($message, $context);
    }

    public function debug($message, array $context = []): void
    {
        self::getInstance()->debug($message, $context);
    }

    public function log($level, $message, array $context = []): void
    {
        self::getInstance()->log($level, $message, $context);
    }

    private function __clone()
    {
    }

    public function __wakeup()
    {
    }
}
