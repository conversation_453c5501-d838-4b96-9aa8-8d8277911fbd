<?php

declare(strict_types=1);

namespace WeBot\Core\Cache\Managers;

use WeBot\Core\Cache\Contracts\CacheInterface;

/**
 * Cache Stats Manager
 *
 * Manages cache statistics and performance metrics.
 *
 * @package WeBot\Core\Cache\Managers
 * @version 2.0
 */
class CacheStatsManager
{
    private CacheInterface $cache;
    private string $statsPrefix = 'stats:';

    public function __construct(CacheInterface $cache)
    {
        $this->cache = $cache;
    }

    /**
     * Record cache hit
     */
    public function recordHit(string $key): void
    {
        $this->incrementCounter('hits');
        $this->incrementCounter('hits:' . $this->getKeyCategory($key));
    }

    /**
     * Record cache miss
     */
    public function recordMiss(string $key): void
    {
        $this->incrementCounter('misses');
        $this->incrementCounter('misses:' . $this->getKeyCategory($key));
    }

    /**
     * Record cache set operation
     */
    public function recordSet(string $key): void
    {
        $this->incrementCounter('sets');
        $this->incrementCounter('sets:' . $this->getKeyCategory($key));
    }

    /**
     * Record cache delete operation
     */
    public function recordDelete(string $key): void
    {
        $this->incrementCounter('deletes');
        $this->incrementCounter('deletes:' . $this->getKeyCategory($key));
    }

    /**
     * Get cache statistics
     */
    public function getStats(): array
    {
        $hits = $this->getCounter('hits');
        $misses = $this->getCounter('misses');
        $total = $hits + $misses;

        return [
            'hits' => $hits,
            'misses' => $misses,
            'total_requests' => $total,
            'hit_ratio' => $total > 0 ? round(($hits / $total) * 100, 2) : 0,
            'sets' => $this->getCounter('sets'),
            'deletes' => $this->getCounter('deletes'),
            'uptime' => $this->getUptime(),
            'memory_usage' => $this->getMemoryUsage()
        ];
    }

    /**
     * Get detailed statistics by category
     */
    public function getDetailedStats(): array
    {
        $categories = ['user', 'session', 'api', 'panel', 'system'];
        $stats = [];

        foreach ($categories as $category) {
            $hits = $this->getCounter('hits:' . $category);
            $misses = $this->getCounter('misses:' . $category);
            $total = $hits + $misses;

            $stats[$category] = [
                'hits' => $hits,
                'misses' => $misses,
                'total_requests' => $total,
                'hit_ratio' => $total > 0 ? round(($hits / $total) * 100, 2) : 0,
                'sets' => $this->getCounter('sets:' . $category),
                'deletes' => $this->getCounter('deletes:' . $category)
            ];
        }

        return $stats;
    }

    /**
     * Reset statistics
     */
    public function resetStats(): bool
    {
        $keys = [
            'hits', 'misses', 'sets', 'deletes',
            'hits:user', 'misses:user', 'sets:user', 'deletes:user',
            'hits:session', 'misses:session', 'sets:session', 'deletes:session',
            'hits:api', 'misses:api', 'sets:api', 'deletes:api',
            'hits:panel', 'misses:panel', 'sets:panel', 'deletes:panel',
            'hits:system', 'misses:system', 'sets:system', 'deletes:system'
        ];

        $success = true;
        foreach ($keys as $key) {
            if (!$this->cache->delete($this->statsPrefix . $key)) {
                $success = false;
            }
        }

        // Reset start time
        $this->cache->set($this->statsPrefix . 'start_time', time());

        return $success;
    }

    /**
     * Get cache performance metrics
     */
    public function getPerformanceMetrics(): array
    {
        $stats = $this->getStats();

        return [
            'efficiency_score' => $this->calculateEfficiencyScore($stats),
            'performance_grade' => $this->getPerformanceGrade($stats['hit_ratio']),
            'recommendations' => $this->getRecommendations($stats)
        ];
    }

    /**
     * Increment counter
     */
    private function incrementCounter(string $counter): void
    {
        $key = $this->statsPrefix . $counter;
        $this->cache->increment($key, 1);
    }

    /**
     * Get counter value
     */
    private function getCounter(string $counter): int
    {
        $key = $this->statsPrefix . $counter;
        return (int) $this->cache->get($key, 0);
    }

    /**
     * Get key category based on key pattern
     */
    private function getKeyCategory(string $key): string
    {
        if (strpos($key, 'user:') === 0) {
            return 'user';
        } elseif (strpos($key, 'session:') === 0) {
            return 'session';
        } elseif (strpos($key, 'api:') === 0) {
            return 'api';
        } elseif (strpos($key, 'panel:') === 0) {
            return 'panel';
        } else {
            return 'system';
        }
    }

    /**
     * Get cache uptime in seconds
     */
    private function getUptime(): int
    {
        $startTime = $this->cache->get($this->statsPrefix . 'start_time');
        if ($startTime === null) {
            $startTime = time();
            $this->cache->set($this->statsPrefix . 'start_time', $startTime);
        }

        return time() - (int) $startTime;
    }

    /**
     * Get memory usage (placeholder - implementation depends on cache driver)
     */
    private function getMemoryUsage(): array
    {
        return [
            'used_memory' => 0,
            'max_memory' => 0,
            'memory_usage_percent' => 0
        ];
    }

    /**
     * Calculate efficiency score
     */
    private function calculateEfficiencyScore(array $stats): int
    {
        $hitRatio = $stats['hit_ratio'];
        $totalRequests = $stats['total_requests'];

        // Base score from hit ratio
        $score = (int) $hitRatio;

        // Bonus for high volume
        if ($totalRequests > 1000) {
            $score += 5;
        } elseif ($totalRequests > 100) {
            $score += 2;
        }

        return min(100, $score);
    }

    /**
     * Get performance grade
     */
    private function getPerformanceGrade(float $hitRatio): string
    {
        if ($hitRatio >= 90) {
            return 'A+';
        } elseif ($hitRatio >= 80) {
            return 'A';
        } elseif ($hitRatio >= 70) {
            return 'B';
        } elseif ($hitRatio >= 60) {
            return 'C';
        } else {
            return 'D';
        }
    }

    /**
     * Get performance recommendations
     */
    private function getRecommendations(array $stats): array
    {
        $recommendations = [];

        if ($stats['hit_ratio'] < 70) {
            $recommendations[] = 'Consider increasing cache TTL for frequently accessed data';
            $recommendations[] = 'Review caching strategy for better hit ratio';
        }

        if ($stats['total_requests'] < 100) {
            $recommendations[] = 'Cache usage is low - consider caching more data';
        }

        if ($stats['deletes'] > $stats['sets'] * 0.5) {
            $recommendations[] = 'High delete ratio detected - review cache invalidation strategy';
        }

        return $recommendations;
    }
}
