<?php

declare(strict_types=1);

namespace WeBot\Events;

/**
 * User Events
 *
 * Domain events related to user operations.
 *
 * @package WeBot\Events
 * @version 2.0
 */

/**
 * User Registered Event
 */
class UserRegisteredEvent extends BaseEvent
{
    public function __construct(int $userId, array $userData)
    {
        parent::__construct([
            'user_id' => $userId,
            'user_data' => $userData,
            'telegram_id' => $userData['telegram_id'] ?? null,
            'first_name' => $userData['first_name'] ?? null,
            'username' => $userData['username'] ?? null
        ]);
    }

    public function getUserId(): int
    {
        return $this->get('user_id');
    }

    public function getUserData(): array
    {
        return $this->get('user_data', []);
    }

    public function getTelegramId(): ?int
    {
        return $this->get('telegram_id');
    }

    public function getFirstName(): ?string
    {
        return $this->get('first_name');
    }

    public function getUsername(): ?string
    {
        return $this->get('username');
    }
}

/**
 * User Updated Event
 */
class UserUpdatedEvent extends BaseEvent
{
    public function __construct(int $userId, array $oldData, array $newData)
    {
        parent::__construct([
            'user_id' => $userId,
            'old_data' => $oldData,
            'new_data' => $newData,
            'changes' => $this->calculateChanges($oldData, $newData)
        ]);
    }

    public function getUserId(): int
    {
        return $this->get('user_id');
    }

    public function getOldData(): array
    {
        return $this->get('old_data', []);
    }

    public function getNewData(): array
    {
        return $this->get('new_data', []);
    }

    public function getChanges(): array
    {
        return $this->get('changes', []);
    }

    private function calculateChanges(array $oldData, array $newData): array
    {
        $changes = [];

        foreach ($newData as $key => $value) {
            if (!isset($oldData[$key]) || $oldData[$key] !== $value) {
                $changes[$key] = [
                    'old' => $oldData[$key] ?? null,
                    'new' => $value
                ];
            }
        }

        return $changes;
    }
}

/**
 * User Deleted Event
 */
class UserDeletedEvent extends BaseEvent
{
    public function __construct(int $userId, array $userData)
    {
        parent::__construct([
            'user_id' => $userId,
            'user_data' => $userData,
            'deleted_at' => date('Y-m-d H:i:s')
        ]);
    }

    public function getUserId(): int
    {
        return $this->get('user_id');
    }

    public function getUserData(): array
    {
        return $this->get('user_data', []);
    }

    public function getDeletedAt(): string
    {
        return $this->get('deleted_at');
    }
}

/**
 * User Login Event
 */
class UserLoginEvent extends BaseEvent
{
    public function __construct(int $userId, string $ipAddress, string $userAgent = '')
    {
        parent::__construct([
            'user_id' => $userId,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'login_at' => date('Y-m-d H:i:s')
        ]);
    }

    public function getUserId(): int
    {
        return $this->get('user_id');
    }

    public function getIpAddress(): string
    {
        return $this->get('ip_address');
    }

    public function getUserAgent(): string
    {
        return $this->get('user_agent', '');
    }

    public function getLoginAt(): string
    {
        return $this->get('login_at');
    }
}

/**
 * User Blocked Event
 */
class UserBlockedEvent extends BaseEvent
{
    public function __construct(int $userId, string $reason, int $blockedBy = null)
    {
        parent::__construct([
            'user_id' => $userId,
            'reason' => $reason,
            'blocked_by' => $blockedBy,
            'blocked_at' => date('Y-m-d H:i:s')
        ]);
    }

    public function getUserId(): int
    {
        return $this->get('user_id');
    }

    public function getReason(): string
    {
        return $this->get('reason');
    }

    public function getBlockedBy(): ?int
    {
        return $this->get('blocked_by');
    }

    public function getBlockedAt(): string
    {
        return $this->get('blocked_at');
    }
}

/**
 * User Unblocked Event
 */
class UserUnblockedEvent extends BaseEvent
{
    public function __construct(int $userId, int $unblockedBy = null)
    {
        parent::__construct([
            'user_id' => $userId,
            'unblocked_by' => $unblockedBy,
            'unblocked_at' => date('Y-m-d H:i:s')
        ]);
    }

    public function getUserId(): int
    {
        return $this->get('user_id');
    }

    public function getUnblockedBy(): ?int
    {
        return $this->get('unblocked_by');
    }

    public function getUnblockedAt(): string
    {
        return $this->get('unblocked_at');
    }
}
