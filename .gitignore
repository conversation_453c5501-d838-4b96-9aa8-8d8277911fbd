# WeBot .gitignore
# =================

# Environment files
.env
.env.local
.env.production
.env.staging

# Logs
storage/logs/*.log
storage/logs/*.txt
*.log

# Cache
storage/cache/*
!storage/cache/.gitkeep
storage/sessions/*
!storage/sessions/.gitkeep

# Uploads
public/uploads/*
!public/uploads/.gitkeep
storage/uploads/*
!storage/uploads/.gitkeep

# Temporary files
storage/temp/*
!storage/temp/.gitkeep
public/temp/*
!public/temp/.gitkeep

# Backups
storage/backups/*
!storage/backups/.gitkeep

# Vendor
vendor/
node_modules/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db
desktop.ini

# PHP
*.cache
.phpunit.result.cache

# Composer
composer.lock

# NPM
package-lock.json
yarn.lock

# Build files
public/build/
public/mix-manifest.json

# QR Code cache
phpqrcode/cache/*
phpqrcode/temp/*

# Legacy files (if any remain)
bot_old.php
config_old.php
functions_old.php

# Test files
tests/coverage/
tests/reports/

# Development
.env.development
.env.dev

# Production sensitive files
.env.production.local
ssl/
certificates/

# Database
*.sqlite
*.db

# Monitoring
storage/monitoring/*
!storage/monitoring/.gitkeep
