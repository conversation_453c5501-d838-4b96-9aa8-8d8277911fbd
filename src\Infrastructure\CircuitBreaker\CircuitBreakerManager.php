<?php

declare(strict_types=1);

namespace WeBot\Infrastructure\CircuitBreaker;

use WeBot\Core\CacheManager;
use WeBot\Utils\Logger;
use WeBot\Exceptions\WeBotException;

/**
 * Circuit Breaker Manager
 *
 * Implements circuit breaker pattern for fault tolerance,
 * preventing cascading failures and providing graceful degradation.
 *
 * @package WeBot\Infrastructure\CircuitBreaker
 * @version 2.0
 */
class CircuitBreakerManager
{
    private CacheManager $cache;
    private Logger $logger;
    private array $config;
    private array $circuitBreakers = [];

    // Circuit breaker states
    private const STATE_CLOSED = 'closed';
    private const STATE_OPEN = 'open';
    private const STATE_HALF_OPEN = 'half_open';

    public function __construct(
        CacheManager $cache,
        array $config = []
    ) {
        $this->cache = $cache;
        $this->logger = Logger::getInstance();
        $this->config = array_merge($this->getDefaultConfig(), $config);

        $this->loadCircuitBreakers();
    }

    /**
     * Execute operation with circuit breaker protection
     */
    public function execute(string $serviceName, callable $operation, ?callable $fallback = null)
    {
        $circuitBreaker = $this->getCircuitBreaker($serviceName);

        try {
            // Check if circuit breaker allows execution
            if (!$this->canExecute($circuitBreaker)) {
                $this->logger->warning("Circuit breaker is open", [
                    'service' => $serviceName,
                    'state' => $circuitBreaker['state']
                ]);

                if ($fallback) {
                    return $fallback();
                }

                throw new WeBotException("Service {$serviceName} is currently unavailable (circuit breaker open)");
            }

            $startTime = microtime(true);

            // Execute the operation
            $result = $operation();

            $executionTime = (microtime(true) - $startTime) * 1000; // ms

            // Record successful execution
            $this->recordSuccess($serviceName, $executionTime);

            return $result;
        } catch (\Exception $e) {
            // Record failure
            $this->recordFailure($serviceName, $e);

            // Try fallback if available
            if ($fallback) {
                try {
                    return $fallback();
                } catch (\Exception $fallbackException) {
                    $this->logger->error("Fallback also failed", [
                        'service' => $serviceName,
                        'original_error' => $e->getMessage(),
                        'fallback_error' => $fallbackException->getMessage()
                    ]);
                }
            }

            throw $e;
        }
    }

    /**
     * Get circuit breaker status
     */
    public function getStatus(string $serviceName): array
    {
        $circuitBreaker = $this->getCircuitBreaker($serviceName);

        return [
            'service' => $serviceName,
            'state' => $circuitBreaker['state'],
            'failure_count' => $circuitBreaker['failure_count'],
            'success_count' => $circuitBreaker['success_count'],
            'last_failure_time' => $circuitBreaker['last_failure_time'],
            'last_success_time' => $circuitBreaker['last_success_time'],
            'failure_rate' => $this->calculateFailureRate($circuitBreaker),
            'avg_response_time' => $this->calculateAverageResponseTime($circuitBreaker),
            'next_attempt_time' => $this->getNextAttemptTime($circuitBreaker),
            'is_available' => $this->canExecute($circuitBreaker),
            'config' => $circuitBreaker['config']
        ];
    }

    /**
     * Get all circuit breakers status
     */
    public function getAllStatus(): array
    {
        $status = [];

        foreach ($this->circuitBreakers as $serviceName => $circuitBreaker) {
            $status[$serviceName] = $this->getStatus($serviceName);
        }

        return [
            'circuit_breakers' => $status,
            'total_services' => count($this->circuitBreakers),
            'healthy_services' => count(array_filter($status, fn($s) => $s['state'] === self::STATE_CLOSED)),
            'degraded_services' => count(array_filter($status, fn($s) => $s['state'] === self::STATE_HALF_OPEN)),
            'failed_services' => count(array_filter($status, fn($s) => $s['state'] === self::STATE_OPEN)),
            'last_updated' => time()
        ];
    }

    /**
     * Reset circuit breaker
     */
    public function reset(string $serviceName): bool
    {
        try {
            $circuitBreaker = $this->getCircuitBreaker($serviceName);

            $circuitBreaker['state'] = self::STATE_CLOSED;
            $circuitBreaker['failure_count'] = 0;
            $circuitBreaker['success_count'] = 0;
            $circuitBreaker['last_failure_time'] = null;
            $circuitBreaker['response_times'] = [];
            $circuitBreaker['reset_time'] = time();

            $this->circuitBreakers[$serviceName] = $circuitBreaker;
            $this->saveCircuitBreakers();

            $this->logger->info("Circuit breaker reset", [
                'service' => $serviceName
            ]);

            return true;
        } catch (\Exception $e) {
            $this->logger->error("Failed to reset circuit breaker", [
                'service' => $serviceName,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Configure circuit breaker for service
     */
    public function configure(string $serviceName, array $config): bool
    {
        try {
            $this->validateConfig($config);

            if (!isset($this->circuitBreakers[$serviceName])) {
                $this->circuitBreakers[$serviceName] = $this->createCircuitBreaker($serviceName, $config);
            } else {
                $this->circuitBreakers[$serviceName]['config'] = array_merge(
                    $this->circuitBreakers[$serviceName]['config'],
                    $config
                );
            }

            $this->saveCircuitBreakers();

            $this->logger->info("Circuit breaker configured", [
                'service' => $serviceName,
                'config' => $config
            ]);

            return true;
        } catch (\Exception $e) {
            $this->logger->error("Failed to configure circuit breaker", [
                'service' => $serviceName,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Check if operation can be executed
     */
    private function canExecute(array $circuitBreaker): bool
    {
        switch ($circuitBreaker['state']) {
            case self::STATE_CLOSED:
                return true;

            case self::STATE_OPEN:
                // Check if timeout period has passed
                $timeoutPeriod = $circuitBreaker['config']['timeout'] ?? $this->config['default_timeout'];
                $timeSinceLastFailure = time() - ($circuitBreaker['last_failure_time'] ?? 0);

                if ($timeSinceLastFailure >= $timeoutPeriod) {
                    // Transition to half-open state
                    $this->transitionToHalfOpen($circuitBreaker['service']);
                    return true;
                }

                return false;

            case self::STATE_HALF_OPEN:
                return true;

            default:
                return false;
        }
    }

    /**
     * Record successful execution
     */
    private function recordSuccess(string $serviceName, float $responseTime): void
    {
        $circuitBreaker = &$this->circuitBreakers[$serviceName];

        $circuitBreaker['success_count']++;
        $circuitBreaker['last_success_time'] = time();
        $circuitBreaker['response_times'][] = $responseTime;

        // Keep only recent response times
        $maxResponseTimes = $circuitBreaker['config']['max_response_times'] ?? 100;
        if (count($circuitBreaker['response_times']) > $maxResponseTimes) {
            $circuitBreaker['response_times'] = array_slice($circuitBreaker['response_times'], -$maxResponseTimes);
        }

        // Check if we should transition from half-open to closed
        if ($circuitBreaker['state'] === self::STATE_HALF_OPEN) {
            $successThreshold = $circuitBreaker['config']['success_threshold'] ?? $this->config['default_success_threshold'];

            if ($circuitBreaker['success_count'] >= $successThreshold) {
                $this->transitionToClosed($serviceName);
            }
        }

        $this->saveCircuitBreakers();
    }

    /**
     * Record failed execution
     */
    private function recordFailure(string $serviceName, \Exception $exception): void
    {
        $circuitBreaker = &$this->circuitBreakers[$serviceName];

        $circuitBreaker['failure_count']++;
        $circuitBreaker['last_failure_time'] = time();
        $circuitBreaker['last_error'] = $exception->getMessage();

        // Check if we should open the circuit breaker
        $failureThreshold = $circuitBreaker['config']['failure_threshold'] ?? $this->config['default_failure_threshold'];

        if ($circuitBreaker['failure_count'] >= $failureThreshold) {
            $this->transitionToOpen($serviceName);
        }

        $this->saveCircuitBreakers();

        $this->logger->warning("Circuit breaker recorded failure", [
            'service' => $serviceName,
            'failure_count' => $circuitBreaker['failure_count'],
            'state' => $circuitBreaker['state'],
            'error' => $exception->getMessage()
        ]);
    }

    /**
     * Transition circuit breaker to open state
     */
    private function transitionToOpen(string $serviceName): void
    {
        $this->circuitBreakers[$serviceName]['state'] = self::STATE_OPEN;
        $this->circuitBreakers[$serviceName]['opened_time'] = time();

        $this->logger->warning("Circuit breaker opened", [
            'service' => $serviceName,
            'failure_count' => $this->circuitBreakers[$serviceName]['failure_count']
        ]);

        // Trigger alert
        $this->triggerCircuitBreakerAlert($serviceName, self::STATE_OPEN);
    }

    /**
     * Transition circuit breaker to half-open state
     */
    private function transitionToHalfOpen(string $serviceName): void
    {
        $this->circuitBreakers[$serviceName]['state'] = self::STATE_HALF_OPEN;
        $this->circuitBreakers[$serviceName]['success_count'] = 0; // Reset success count

        $this->logger->info("Circuit breaker transitioned to half-open", [
            'service' => $serviceName
        ]);
    }

    /**
     * Transition circuit breaker to closed state
     */
    private function transitionToClosed(string $serviceName): void
    {
        $this->circuitBreakers[$serviceName]['state'] = self::STATE_CLOSED;
        $this->circuitBreakers[$serviceName]['failure_count'] = 0; // Reset failure count
        $this->circuitBreakers[$serviceName]['closed_time'] = time();

        $this->logger->info("Circuit breaker closed", [
            'service' => $serviceName
        ]);

        // Trigger recovery alert
        $this->triggerCircuitBreakerAlert($serviceName, self::STATE_CLOSED);
    }

    /**
     * Get or create circuit breaker for service
     */
    private function getCircuitBreaker(string $serviceName): array
    {
        if (!isset($this->circuitBreakers[$serviceName])) {
            $this->circuitBreakers[$serviceName] = $this->createCircuitBreaker($serviceName);
            $this->saveCircuitBreakers();
        }

        return $this->circuitBreakers[$serviceName];
    }

    /**
     * Create new circuit breaker
     */
    private function createCircuitBreaker(string $serviceName, array $config = []): array
    {
        return [
            'service' => $serviceName,
            'state' => self::STATE_CLOSED,
            'failure_count' => 0,
            'success_count' => 0,
            'last_failure_time' => null,
            'last_success_time' => null,
            'last_error' => null,
            'response_times' => [],
            'created_time' => time(),
            'config' => array_merge($this->getDefaultServiceConfig(), $config)
        ];
    }

    /**
     * Calculate failure rate
     */
    private function calculateFailureRate(array $circuitBreaker): float
    {
        $totalRequests = $circuitBreaker['failure_count'] + $circuitBreaker['success_count'];

        if ($totalRequests === 0) {
            return 0.0;
        }

        return ($circuitBreaker['failure_count'] / $totalRequests) * 100;
    }

    /**
     * Calculate average response time
     */
    private function calculateAverageResponseTime(array $circuitBreaker): float
    {
        $responseTimes = $circuitBreaker['response_times'] ?? [];

        if (empty($responseTimes)) {
            return 0.0;
        }

        return array_sum($responseTimes) / count($responseTimes);
    }

    /**
     * Get next attempt time for open circuit breaker
     */
    private function getNextAttemptTime(array $circuitBreaker): ?int
    {
        if ($circuitBreaker['state'] !== self::STATE_OPEN) {
            return null;
        }

        $timeout = $circuitBreaker['config']['timeout'] ?? $this->config['default_timeout'];
        $lastFailureTime = $circuitBreaker['last_failure_time'] ?? 0;

        return $lastFailureTime + $timeout;
    }

    /**
     * Trigger circuit breaker alert
     */
    private function triggerCircuitBreakerAlert(string $serviceName, string $newState): void
    {
        $alertLevel = $newState === self::STATE_OPEN ? 'critical' : 'info';

        $this->logger->log($alertLevel, "Circuit breaker state changed", [
            'service' => $serviceName,
            'new_state' => $newState,
            'timestamp' => time()
        ]);

        // In production, implement actual alerting (email, SMS, Slack, etc.)
    }

    /**
     * Validate circuit breaker configuration
     */
    private function validateConfig(array $config): void
    {
        $requiredFields = [];
        $validFields = ['failure_threshold', 'timeout', 'success_threshold', 'max_response_times'];

        foreach ($requiredFields as $field) {
            if (!isset($config[$field])) {
                throw new WeBotException("Required configuration field missing: " . $field);
            }
        }

        foreach ($config as $field => $value) {
            if (!in_array($field, $validFields)) {
                throw new WeBotException("Invalid configuration field: {$field}");
            }
        }

        if (isset($config['failure_threshold']) && $config['failure_threshold'] < 1) {
            throw new WeBotException("Failure threshold must be at least 1");
        }

        if (isset($config['timeout']) && $config['timeout'] < 1) {
            throw new WeBotException("Timeout must be at least 1 second");
        }
    }

    /**
     * Load circuit breakers from cache
     */
    private function loadCircuitBreakers(): void
    {
        $cached = $this->cache->get('circuit_breakers', []);
        $this->circuitBreakers = $cached;
    }

    /**
     * Save circuit breakers to cache
     */
    private function saveCircuitBreakers(): void
    {
        $this->cache->set('circuit_breakers', $this->circuitBreakers, 86400); // 24 hours
    }

    /**
     * Get default service configuration
     */
    private function getDefaultServiceConfig(): array
    {
        return [
            'failure_threshold' => $this->config['default_failure_threshold'],
            'timeout' => $this->config['default_timeout'],
            'success_threshold' => $this->config['default_success_threshold'],
            'max_response_times' => 100
        ];
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'default_failure_threshold' => 5,      // Number of failures before opening
            'default_timeout' => 60,               // Seconds to wait before half-open
            'default_success_threshold' => 3,      // Successes needed to close from half-open
            'enable_alerts' => true,               // Enable alerting
            'cache_ttl' => 86400                   // Cache TTL in seconds
        ];
    }
}
