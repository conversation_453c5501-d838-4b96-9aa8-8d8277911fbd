<?php

declare(strict_types=1);

namespace Tests\Integration;

// WeBot Test Framework
abstract class WeBotTestCase
{
    protected function assertTrue($condition, $message = '') {
        if (!$condition) {
            throw new \Exception($message ?: '<PERSON>ser<PERSON> failed');
        }
    }

    protected function assertFalse($condition, $message = '') {
        if ($condition) {
            throw new \Exception($message ?: 'Assertion failed');
        }
    }

    protected function assertEquals($expected, $actual, $message = '') {
        if ($expected !== $actual) {
            throw new \Exception($message ?: "Expected $expected, got $actual");
        }
    }

    protected function assertNotNull($value, $message = '') {
        if ($value === null) {
            throw new \Exception($message ?: 'Value should not be null');
        }
    }

    protected function assertArrayHasKey($key, $array, $message = '') {
        if (!array_key_exists($key, $array)) {
            throw new \Exception($message ?: "Array should have key $key");
        }
    }

    protected function assertContains($needle, $haystack, $message = '') {
        if (!in_array($needle, $haystack)) {
            throw new \Exception($message ?: "Array should contain $needle");
        }
    }

    protected function assertIsArray($value, $message = '') {
        if (!is_array($value)) {
            throw new \Exception($message ?: 'Value should be an array');
        }
    }

    protected function assertStringContainsString($needle, $haystack, $message = '') {
        if (strpos($haystack, $needle) === false) {
            throw new \Exception($message ?: "String should contain '$needle'");
        }
    }

    protected function assertNotEmpty($value, $message = '') {
        if (empty($value)) {
            throw new \Exception($message ?: 'Value should not be empty');
        }
    }

    protected function assertGreaterThan($expected, $actual, $message = '') {
        if ($actual <= $expected) {
            throw new \Exception($message ?: "Expected $actual to be greater than $expected");
        }
    }

    protected function assertCount($expectedCount, $array, $message = '') {
        $actualCount = count($array);
        if ($actualCount !== $expectedCount) {
            throw new \Exception($message ?: "Expected count $expectedCount, got $actualCount");
        }
    }

    protected function setUp(): void
    {
        // Override in child classes
    }

    protected function tearDown(): void
    {
        // Override in child classes
    }
}
use WeBot\Controllers\TicketController;
use WeBot\Services\MessageService;
use WeBot\Models\Ticket;
use WeBot\Repositories\TicketRepository;
use WeBot\Core\Database;
use WeBot\Core\CacheManager;

/**
 * Ticket System Integration Test
 * 
 * Integration tests for the complete ticket system including
 * controller, service, model, and repository interactions.
 * 
 * @package Tests\Integration
 * @version 2.0
 */
class TicketSystemIntegrationTest extends WeBotTestCase
{
    private Database $database;
    private TicketRepository $ticketRepository;
    private TicketController $ticketController;
    private MessageService $messageService;
    private CacheManager $cache;
    private array $testUsers = [];
    private array $testTickets = [];

    protected function setUp(): void
    {
        // Initialize database connection
        $this->database = new Database([
            'host' => $_ENV['TEST_DB_HOST'] ?? 'localhost',
            'username' => $_ENV['TEST_DB_USER'] ?? 'test',
            'password' => $_ENV['TEST_DB_PASS'] ?? 'test',
            'database' => $_ENV['TEST_DB_NAME'] ?? 'webot_test',
            'charset' => 'utf8mb4'
        ]);

        // Initialize cache
        $this->cache = new CacheManager([
            'driver' => 'array' // Use array driver for testing
        ]);

        // Initialize services
        $this->messageService = new MessageService($this->database, $this->cache);
        $this->ticketRepository = new TicketRepository($this->database);

        // Initialize controller
        $container = $this->createMockContainer();
        $this->ticketController = new TicketController($container);

        // Setup test data
        $this->setupTestData();
    }

    protected function tearDown(): void
    {
        // Clean up test data
        $this->cleanupTestData();
    }

    public function testCompleteTicketCreationFlow(): void
    {
        $userId = $this->testUsers[0]['userid'];
        
        // Step 1: User starts support
        $supportMessage = [
            'message_id' => 123,
            'from' => [
                'id' => $userId,
                'first_name' => 'Test',
                'username' => 'test_user'
            ],
            'text' => '/support',
            'date' => time()
        ];

        $result = $this->ticketController->handleSupport($supportMessage);
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('text', $result);
        $this->assertStringContainsString('سیستم پشتیبانی', $result['text']);

        // Step 2: User selects create ticket
        $createTicketCallback = [
            'id' => 'callback_123',
            'from' => ['id' => $userId, 'first_name' => 'Test'],
            'data' => 'create_ticket',
            'message' => ['message_id' => 456, 'chat' => ['id' => $userId]]
        ];

        $result = $this->ticketController->handleCallback($createTicketCallback);
        
        $this->assertArrayHasKey('text', $result);
        $this->assertStringContainsString('دسته‌بندی', $result['text']);

        // Step 3: User selects category
        $categoryCallback = [
            'id' => 'callback_124',
            'from' => ['id' => $userId, 'first_name' => 'Test'],
            'data' => 'category_technical',
            'message' => ['message_id' => 457, 'chat' => ['id' => $userId]]
        ];

        $result = $this->ticketController->handleCallback($categoryCallback);
        
        $this->assertStringContainsString('اولویت', $result['text']);

        // Step 4: User selects priority
        $priorityCallback = [
            'id' => 'callback_125',
            'from' => ['id' => $userId, 'first_name' => 'Test'],
            'data' => 'priority_high',
            'message' => ['message_id' => 458, 'chat' => ['id' => $userId]]
        ];

        $result = $this->ticketController->handleCallback($priorityCallback);
        
        $this->assertStringContainsString('موضوع', $result['text']);

        // Step 5: User enters subject
        $subjectMessage = [
            'message_id' => 126,
            'from' => ['id' => $userId, 'first_name' => 'Test'],
            'text' => 'VPN Connection Issue',
            'date' => time()
        ];

        // Set user step to ticket_subject
        $this->setUserStep($userId, 'ticket_subject');
        
        $result = $this->ticketController->handleMessage($subjectMessage);
        
        $this->assertStringContainsString('توضیحات', $result['text']);

        // Step 6: User enters description
        $descriptionMessage = [
            'message_id' => 127,
            'from' => ['id' => $userId, 'first_name' => 'Test'],
            'text' => 'I am experiencing frequent disconnections with my VPN service.',
            'date' => time()
        ];

        // Set user step to ticket_description and add session data
        $this->setUserStep($userId, 'ticket_description');
        $this->setUserData($userId, 'ticket_category', 'technical');
        $this->setUserData($userId, 'ticket_priority', 'high');
        $this->setUserData($userId, 'ticket_subject', 'VPN Connection Issue');
        
        $result = $this->ticketController->handleMessage($descriptionMessage);
        
        $this->assertStringContainsString('موفقیت ایجاد شد', $result['text']);
        $this->assertStringContainsString('#', $result['text']); // Ticket ID

        // Verify ticket was created in database
        $tickets = $this->ticketRepository->getUserTickets($userId);
        $this->assertNotEmpty($tickets);
        
        $createdTicket = $tickets[0];
        $this->assertEquals('VPN Connection Issue', $createdTicket->getAttribute('subject'));
        $this->assertEquals('technical', $createdTicket->getAttribute('category'));
        $this->assertEquals('high', $createdTicket->getAttribute('priority'));
        $this->assertEquals('open', $createdTicket->getAttribute('status'));
    }

    public function testTicketViewAndReplyFlow(): void
    {
        // Create a test ticket
        $ticketData = [
            'user_id' => $this->testUsers[0]['userid'],
            'subject' => 'Test Ticket',
            'description' => 'Test description',
            'category' => 'technical',
            'priority' => 'medium',
            'status' => 'open'
        ];

        $ticket = $this->ticketRepository->create($ticketData);
        $ticketId = $ticket->getKey();

        // Test viewing ticket
        $viewCallback = [
            'id' => 'callback_view',
            'from' => ['id' => $this->testUsers[0]['userid'], 'first_name' => 'Test'],
            'data' => "ticket_view_{$ticketId}",
            'message' => ['message_id' => 500, 'chat' => ['id' => $this->testUsers[0]['userid']]]
        ];

        $result = $this->ticketController->handleCallback($viewCallback);
        
        $this->assertStringContainsString("جزئیات تیکت #{$ticketId}", $result['text']);
        $this->assertStringContainsString('Test Ticket', $result['text']);
        $this->assertStringContainsString('technical', $result['text']);

        // Test adding reply
        $replyResult = $ticket->addReply(
            $this->testUsers[1]['userid'], // Admin user
            'Thank you for your report. We are investigating this issue.',
            true // is_staff
        );

        $this->assertTrue($replyResult);

        // Verify reply was added
        $replies = $ticket->getReplies();
        $this->assertCount(1, $replies);
        $this->assertEquals('Thank you for your report. We are investigating this issue.', $replies[0]['message']);
        $this->assertTrue((bool) $replies[0]['is_staff']);

        // Verify ticket status was updated
        $updatedTicket = $this->ticketRepository->findById($ticketId);
        $this->assertEquals('waiting', $updatedTicket->getAttribute('status'));
    }

    public function testTicketSearchAndFiltering(): void
    {
        // Create multiple test tickets
        $ticketsData = [
            [
                'user_id' => $this->testUsers[0]['userid'],
                'subject' => 'Payment Issue',
                'description' => 'Payment not processed',
                'category' => 'payment',
                'priority' => 'urgent',
                'status' => 'open'
            ],
            [
                'user_id' => $this->testUsers[0]['userid'],
                'subject' => 'Connection Problem',
                'description' => 'Cannot connect to VPN',
                'category' => 'connection',
                'priority' => 'high',
                'status' => 'in_progress'
            ],
            [
                'user_id' => $this->testUsers[1]['userid'],
                'subject' => 'Technical Support',
                'description' => 'Need help with configuration',
                'category' => 'technical',
                'priority' => 'medium',
                'status' => 'resolved'
            ]
        ];

        foreach ($ticketsData as $data) {
            $this->ticketRepository->create($data);
        }

        // Test search by subject
        $searchResults = $this->ticketRepository->search('Payment');
        $this->assertCount(1, $searchResults);
        $this->assertEquals('Payment Issue', $searchResults[0]->getAttribute('subject'));

        // Test filter by category
        $categoryResults = $this->ticketRepository->search('', ['category' => 'technical']);
        $this->assertCount(1, $categoryResults);
        $this->assertEquals('technical', $categoryResults[0]->getAttribute('category'));

        // Test filter by priority
        $priorityResults = $this->ticketRepository->search('', ['priority' => 'urgent']);
        $this->assertCount(1, $priorityResults);
        $this->assertEquals('urgent', $priorityResults[0]->getAttribute('priority'));

        // Test filter by status
        $statusResults = $this->ticketRepository->search('', ['status' => 'resolved']);
        $this->assertCount(1, $statusResults);
        $this->assertEquals('resolved', $statusResults[0]->getAttribute('status'));
    }

    public function testTicketStatistics(): void
    {
        // Create tickets with different statuses
        $ticketsData = [
            ['user_id' => $this->testUsers[0]['userid'], 'subject' => 'Open Ticket 1', 'description' => 'Test', 'category' => 'technical', 'priority' => 'low', 'status' => 'open'],
            ['user_id' => $this->testUsers[0]['userid'], 'subject' => 'Open Ticket 2', 'description' => 'Test', 'category' => 'payment', 'priority' => 'urgent', 'status' => 'open'],
            ['user_id' => $this->testUsers[1]['userid'], 'subject' => 'In Progress Ticket', 'description' => 'Test', 'category' => 'connection', 'priority' => 'high', 'status' => 'in_progress'],
            ['user_id' => $this->testUsers[1]['userid'], 'subject' => 'Resolved Ticket', 'description' => 'Test', 'category' => 'technical', 'priority' => 'medium', 'status' => 'resolved']
        ];

        foreach ($ticketsData as $data) {
            $this->ticketRepository->create($data);
        }

        // Test general statistics
        $stats = $this->ticketRepository->getStatistics();
        
        $this->assertEquals(4, $stats['total_tickets']);
        $this->assertEquals(3, $stats['open_tickets']); // open + in_progress
        $this->assertEquals(1, $stats['closed_tickets']); // resolved
        $this->assertEquals(1, $stats['urgent_tickets']);

        // Test category statistics
        $categoryStats = $this->ticketRepository->getCategoryStatistics();
        
        $this->assertCount(3, $categoryStats); // technical, payment, connection
        
        $technicalStats = array_filter($categoryStats, fn($s) => $s['category'] === 'technical')[0];
        $this->assertEquals(2, $technicalStats['total']);

        // Test priority statistics
        $priorityStats = $this->ticketRepository->getPriorityStatistics();
        
        $this->assertCount(4, $priorityStats); // low, medium, high, urgent
        
        $urgentStats = array_filter($priorityStats, fn($s) => $s['priority'] === 'urgent')[0];
        $this->assertEquals(1, $urgentStats['total']);
    }

    public function testTicketBulkOperations(): void
    {
        // Create multiple tickets
        $ticketsData = [
            ['user_id' => $this->testUsers[0]['userid'], 'subject' => 'Ticket 1', 'description' => 'Test', 'category' => 'technical', 'priority' => 'low', 'status' => 'open'],
            ['user_id' => $this->testUsers[0]['userid'], 'subject' => 'Ticket 2', 'description' => 'Test', 'category' => 'payment', 'priority' => 'medium', 'status' => 'open'],
            ['user_id' => $this->testUsers[1]['userid'], 'subject' => 'Ticket 3', 'description' => 'Test', 'category' => 'connection', 'priority' => 'high', 'status' => 'open']
        ];

        $ticketIds = [];
        foreach ($ticketsData as $data) {
            $ticket = $this->ticketRepository->create($data);
            $ticketIds[] = $ticket->getKey();
        }

        // Test bulk assignment
        $adminId = $this->testUsers[1]['userid']; // Admin user
        $assignedCount = $this->ticketRepository->bulkAssign($ticketIds, $adminId);
        
        $this->assertEquals(3, $assignedCount);

        // Verify assignments
        foreach ($ticketIds as $ticketId) {
            $ticket = $this->ticketRepository->findById($ticketId);
            $this->assertEquals($adminId, $ticket->getAttribute('assigned_to'));
        }

        // Test bulk status update
        $updatedCount = $this->ticketRepository->bulkUpdateStatus($ticketIds, 'resolved');
        
        $this->assertEquals(3, $updatedCount);

        // Verify status updates
        foreach ($ticketIds as $ticketId) {
            $ticket = $this->ticketRepository->findById($ticketId);
            $this->assertEquals('resolved', $ticket->getAttribute('status'));
            $this->assertNotNull($ticket->getAttribute('resolved_at'));
        }
    }

    /**
     * Setup test data
     */
    private function setupTestData(): void
    {
        // Create test users
        $this->testUsers = [
            [
                'userid' => 999888777,
                'first_name' => 'Test',
                'last_name' => 'User',
                'username' => 'test_user',
                'isAdmin' => 0
            ],
            [
                'userid' => 999888778,
                'first_name' => 'Admin',
                'last_name' => 'User',
                'username' => 'admin_user',
                'isAdmin' => 1
            ]
        ];

        foreach ($this->testUsers as $user) {
            $this->database->insert('users', $user);
        }
    }

    /**
     * Clean up test data
     */
    private function cleanupTestData(): void
    {
        // Delete test tickets and replies
        foreach ($this->testUsers as $user) {
            $this->database->delete('ticket_replies', ['user_id' => $user['userid']]);
            $this->database->delete('tickets', ['user_id' => $user['userid']]);
            $this->database->delete('user_sessions', ['user_id' => $user['userid']]);
            $this->database->delete('users', ['userid' => $user['userid']]);
        }
    }

    /**
     * Create mock container
     */
    private function createMockContainer()
    {
        $container = new \stdClass();
        $container->database = $this->database;
        $container->messageService = $this->messageService;
        $container->cache = $this->cache;
        
        return $container;
    }

    /**
     * Set user step for testing
     */
    private function setUserStep(int $userId, string $step): void
    {
        $this->database->insert('user_sessions', [
            'user_id' => $userId,
            'session_key' => 'step',
            'session_value' => $step,
            'expires_at' => date('Y-m-d H:i:s', time() + 3600)
        ]);
    }

    /**
     * Set user data for testing
     */
    private function setUserData(int $userId, string $key, string $value): void
    {
        $this->database->insert('user_sessions', [
            'user_id' => $userId,
            'session_key' => $key,
            'session_value' => $value,
            'expires_at' => date('Y-m-d H:i:s', time() + 3600)
        ]);
    }
}
