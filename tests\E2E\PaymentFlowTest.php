<?php

declare(strict_types=1);

namespace WeBot\Tests\E2E;

use WeBot\Core\Application;
use WeBot\Services\PaymentService;
use WeBot\Services\TelegramService;
use WeBot\Services\DatabaseService;
use WeBot\Models\Payment;
use WeBot\Models\User;

/**
 * Payment Flow End-to-End Test
 * 
 * Complete testing of payment processing flows
 * including multiple gateways and error scenarios.
 * 
 * @package WeBot\Tests\E2E
 * @version 2.0
 */
class PaymentFlowTest
{
    private Application $app;
    private DatabaseService $database;
    private PaymentService $paymentService;
    private TelegramService $telegramService;
    private array $testResults = [];
    private int $totalTests = 0;
    private int $passedTests = 0;
    private int $failedTests = 0;

    public function __construct()
    {
        $this->setupTestEnvironment();
        $this->initializeServices();
    }

    /**
     * Run all payment flow tests
     */
    public function runAllTests(): array
    {
        echo "💳 Starting Payment Flow E2E Tests...\n\n";

        $this->testStripePaymentFlow();
        $this->testPayPalPaymentFlow();
        $this->testCryptoPaymentFlow();
        $this->testPaymentFailureHandling();
        $this->testRefundProcessing();
        $this->testRecurringPayments();
        $this->testPaymentWebhooks();
        $this->testPaymentSecurity();

        return $this->generateTestReport();
    }

    /**
     * Test Stripe payment flow
     */
    private function testStripePaymentFlow(): void
    {
        echo "🔵 Testing Stripe Payment Flow...\n";

        try {
            $userId = 111222333;
            $this->createTestUser($userId);

            // Step 1: Initiate payment
            $paymentData = [
                'user_id' => $userId,
                'amount' => 29.99,
                'currency' => 'USD',
                'service_type' => 'vpn',
                'plan' => 'premium_monthly',
                'gateway' => 'stripe'
            ];

            $paymentResult = $this->paymentService->initiatePayment($paymentData);
            $this->assertTrue($paymentResult['success'], 'Payment initiation should succeed');
            $this->assertNotEmpty($paymentResult['payment_url'], 'Payment URL should be provided');

            $paymentId = $paymentResult['payment_id'];

            // Step 2: Simulate successful Stripe webhook
            $webhookData = [
                'id' => 'evt_test_webhook',
                'object' => 'event',
                'type' => 'payment_intent.succeeded',
                'data' => [
                    'object' => [
                        'id' => 'pi_test_payment',
                        'amount' => 2999, // Stripe uses cents
                        'currency' => 'usd',
                        'status' => 'succeeded',
                        'metadata' => [
                            'payment_id' => $paymentId,
                            'user_id' => $userId
                        ]
                    ]
                ]
            ];

            $webhookResult = $this->paymentService->processStripeWebhook($webhookData);
            $this->assertTrue($webhookResult['success'], 'Stripe webhook should be processed');

            // Step 3: Verify payment status
            $payment = Payment::find($this->database, $paymentId);
            $this->assertEquals('completed', $payment->getStatus(), 'Payment status should be completed');
            $this->assertEquals('pi_test_payment', $payment->getTransactionId(), 'Transaction ID should be saved');

            // Step 4: Verify user notification
            $this->verifyPaymentNotification($userId, 'success');

            $this->recordTestResult('Stripe Payment Flow', true, 'Complete Stripe payment flow successful');

        } catch (\Exception $e) {
            $this->recordTestResult('Stripe Payment Flow', false, $e->getMessage());
        }
    }

    /**
     * Test PayPal payment flow
     */
    private function testPayPalPaymentFlow(): void
    {
        echo "🟡 Testing PayPal Payment Flow...\n";

        try {
            $userId = 444555666;
            $this->createTestUser($userId);

            // Step 1: Initiate PayPal payment
            $paymentData = [
                'user_id' => $userId,
                'amount' => 19.99,
                'currency' => 'USD',
                'service_type' => 'proxy',
                'plan' => 'basic_monthly',
                'gateway' => 'paypal'
            ];

            $paymentResult = $this->paymentService->initiatePayment($paymentData);
            $this->assertTrue($paymentResult['success'], 'PayPal payment initiation should succeed');
            $this->assertNotEmpty($paymentResult['approval_url'], 'PayPal approval URL should be provided');

            $paymentId = $paymentResult['payment_id'];

            // Step 2: Simulate PayPal approval
            $approvalResult = $this->paymentService->approvePayPalPayment($paymentId);
            $this->assertTrue($approvalResult['success'], 'PayPal approval should succeed');

            // Step 3: Execute PayPal payment
            $executeResult = $this->paymentService->executePayPalPayment($paymentId, 'PAYERID123');
            $this->assertTrue($executeResult['success'], 'PayPal execution should succeed');

            // Step 4: Verify payment completion
            $payment = Payment::find($this->database, $paymentId);
            $this->assertEquals('completed', $payment->getStatus(), 'PayPal payment should be completed');

            $this->recordTestResult('PayPal Payment Flow', true, 'Complete PayPal payment flow successful');

        } catch (\Exception $e) {
            $this->recordTestResult('PayPal Payment Flow', false, $e->getMessage());
        }
    }

    /**
     * Test cryptocurrency payment flow
     */
    private function testCryptoPaymentFlow(): void
    {
        echo "₿ Testing Cryptocurrency Payment Flow...\n";

        try {
            $userId = 777888999;
            $this->createTestUser($userId);

            // Step 1: Initiate crypto payment
            $paymentData = [
                'user_id' => $userId,
                'amount' => 0.001,
                'currency' => 'BTC',
                'service_type' => 'vpn',
                'plan' => 'annual',
                'gateway' => 'crypto'
            ];

            $paymentResult = $this->paymentService->initiatePayment($paymentData);
            $this->assertTrue($paymentResult['success'], 'Crypto payment initiation should succeed');
            $this->assertNotEmpty($paymentResult['wallet_address'], 'Wallet address should be provided');
            $this->assertNotEmpty($paymentResult['qr_code'], 'QR code should be generated');

            $paymentId = $paymentResult['payment_id'];

            // Step 2: Simulate blockchain confirmation
            $confirmationData = [
                'payment_id' => $paymentId,
                'transaction_hash' => '0x1234567890abcdef',
                'confirmations' => 6,
                'amount_received' => 0.001
            ];

            $confirmResult = $this->paymentService->confirmCryptoPayment($paymentId, 'tx_hash_123');
            $this->assertTrue($confirmResult['success'], 'Crypto confirmation should succeed');

            // Step 3: Verify payment status
            $payment = Payment::find($this->database, $paymentId);
            $this->assertEquals('completed', $payment->getStatus(), 'Crypto payment should be completed');

            $this->recordTestResult('Crypto Payment Flow', true, 'Complete crypto payment flow successful');

        } catch (\Exception $e) {
            $this->recordTestResult('Crypto Payment Flow', false, $e->getMessage());
        }
    }

    /**
     * Test payment failure handling
     */
    private function testPaymentFailureHandling(): void
    {
        echo "❌ Testing Payment Failure Handling...\n";

        try {
            $userId = 123123123;
            $this->createTestUser($userId);

            // Test insufficient funds
            $paymentData = [
                'user_id' => $userId,
                'amount' => 99.99,
                'currency' => 'USD',
                'service_type' => 'vpn',
                'gateway' => 'stripe'
            ];

            // Simulate card declined
            $GLOBALS['mock_responses']['stripe']['create_payment_intent'] = [
                'error' => [
                    'type' => 'card_error',
                    'code' => 'card_declined',
                    'message' => 'Your card was declined.'
                ]
            ];

            $paymentResult = $this->paymentService->initiatePayment($paymentData);
            $this->assertFalse($paymentResult['success'], 'Payment should fail with declined card');
            $this->assertEquals('card_declined', $paymentResult['error_code'], 'Error code should be correct');

            // Verify user notification
            $this->verifyPaymentNotification($userId, 'failed');

            // Test retry mechanism
            $retryResult = $this->paymentService->retryPayment($paymentResult['payment_id']);
            $this->assertTrue(isset($retryResult['retry_url']), 'Retry URL should be provided');

            $this->recordTestResult('Payment Failure Handling', true, 'Payment failures handled correctly');

        } catch (\Exception $e) {
            $this->recordTestResult('Payment Failure Handling', false, $e->getMessage());
        }
    }

    /**
     * Test refund processing
     */
    private function testRefundProcessing(): void
    {
        echo "↩️ Testing Refund Processing...\n";

        try {
            $userId = 456456456;
            $this->createTestUser($userId);

            // Create a completed payment first
            $payment = $this->createCompletedPayment($userId, 39.99, 'stripe');

            // Test full refund
            $refundResult = $this->paymentService->processRefund($payment->getId(), (int) 39.99, 'Customer request');
            $this->assertTrue($refundResult['success'], 'Full refund should succeed');

            // Verify payment status
            $updatedPayment = Payment::find($this->database, $payment->getId());
            $this->assertEquals('refunded', $updatedPayment->getStatus(), 'Payment should be marked as refunded');

            // Test partial refund
            $payment2 = $this->createCompletedPayment($userId, 59.99, 'stripe');
            $partialRefundResult = $this->paymentService->processRefund($payment2->getId(), 20, 'Partial refund');
            $this->assertTrue($partialRefundResult['success'], 'Partial refund should succeed');

            // Verify user notification
            $this->verifyPaymentNotification($userId, 'refunded');

            $this->recordTestResult('Refund Processing', true, 'Refund processing working correctly');

        } catch (\Exception $e) {
            $this->recordTestResult('Refund Processing', false, $e->getMessage());
        }
    }

    /**
     * Test recurring payments
     */
    private function testRecurringPayments(): void
    {
        echo "🔄 Testing Recurring Payments...\n";

        try {
            $userId = 789789789;
            $this->createTestUser($userId);

            // Setup recurring payment
            $subscriptionData = [
                'user_id' => $userId,
                'amount' => 9.99,
                'currency' => 'USD',
                'interval' => 'monthly',
                'service_type' => 'vpn',
                'gateway' => 'stripe'
            ];

            $subscriptionResult = $this->paymentService->createSubscription($subscriptionData);
            $this->assertTrue($subscriptionResult['success'], 'Subscription creation should succeed');

            $subscriptionId = $subscriptionResult['subscription_id'];

            // Simulate first payment
            $firstPaymentResult = $this->paymentService->processSubscriptionPayment($subscriptionId);
            $this->assertTrue($firstPaymentResult['success'], 'First subscription payment should succeed');

            // Simulate renewal
            $renewalResult = $this->paymentService->processSubscriptionRenewal($subscriptionId);
            $this->assertTrue($renewalResult['success'], 'Subscription renewal should succeed');

            // Test subscription cancellation
            $cancelResult = $this->paymentService->cancelSubscription($subscriptionId);
            $this->assertTrue($cancelResult['success'], 'Subscription cancellation should succeed');

            $this->recordTestResult('Recurring Payments', true, 'Recurring payments working correctly');

        } catch (\Exception $e) {
            $this->recordTestResult('Recurring Payments', false, $e->getMessage());
        }
    }

    /**
     * Test payment webhooks
     */
    private function testPaymentWebhooks(): void
    {
        echo "🔗 Testing Payment Webhooks...\n";

        try {
            // Test webhook signature verification
            $webhookData = ['test' => 'data'];
            $signature = $this->paymentService->generateWebhookSignature($webhookData);
            
            $verificationResult = $this->paymentService->verifyWebhookSignature($webhookData, $signature);
            $this->assertTrue($verificationResult, 'Webhook signature should be verified');

            // Test invalid signature
            $invalidVerification = $this->paymentService->verifyWebhookSignature($webhookData, 'invalid_signature');
            $this->assertFalse($invalidVerification, 'Invalid signature should be rejected');

            // Test webhook processing
            $webhookResult = $this->paymentService->processWebhook('stripe', $webhookData, $signature);
            $this->assertTrue($webhookResult['success'], 'Webhook should be processed successfully');

            $this->recordTestResult('Payment Webhooks', true, 'Webhook processing working correctly');

        } catch (\Exception $e) {
            $this->recordTestResult('Payment Webhooks', false, $e->getMessage());
        }
    }

    /**
     * Test payment security
     */
    private function testPaymentSecurity(): void
    {
        echo "🔒 Testing Payment Security...\n";

        try {
            $userId = 999888777;
            $this->createTestUser($userId);

            // Test rate limiting
            for ($i = 0; $i < 10; $i++) {
                $paymentData = [
                    'user_id' => $userId,
                    'amount' => 1.00,
                    'currency' => 'USD',
                    'gateway' => 'stripe'
                ];
                
                $result = $this->paymentService->initiatePayment($paymentData);
                
                if ($i >= 5) {
                    $this->assertFalse($result['success'], 'Rate limiting should kick in');
                    $this->assertEquals('rate_limited', $result['error_code'], 'Rate limit error should be returned');
                    break;
                }
            }

            // Test amount validation
            $invalidAmountData = [
                'user_id' => $userId,
                'amount' => -10.00, // Negative amount
                'currency' => 'USD',
                'gateway' => 'stripe'
            ];

            $invalidResult = $this->paymentService->initiatePayment($invalidAmountData);
            $this->assertFalse($invalidResult['success'], 'Negative amount should be rejected');

            // Test currency validation
            $invalidCurrencyData = [
                'user_id' => $userId,
                'amount' => 10.00,
                'currency' => 'INVALID',
                'gateway' => 'stripe'
            ];

            $currencyResult = $this->paymentService->initiatePayment($invalidCurrencyData);
            $this->assertFalse($currencyResult['success'], 'Invalid currency should be rejected');

            $this->recordTestResult('Payment Security', true, 'Payment security measures working');

        } catch (\Exception $e) {
            $this->recordTestResult('Payment Security', false, $e->getMessage());
        }
    }

    /**
     * Helper methods
     */
    private function setupTestEnvironment(): void
    {
        putenv('WEBOT_TEST_MODE=true');
        putenv('DISABLE_EXTERNAL_APIS=true');
        
        $GLOBALS['mock_responses'] = [
            'stripe' => [
                'create_payment_intent' => ['success' => true, 'client_secret' => 'pi_test_secret'],
                'confirm_payment' => ['success' => true, 'status' => 'succeeded']
            ],
            'paypal' => [
                'create_payment' => ['success' => true, 'approval_url' => 'https://paypal.com/approve'],
                'execute_payment' => ['success' => true, 'status' => 'approved']
            ],
            'crypto' => [
                'generate_address' => ['success' => true, 'address' => '**********************************'],
                'check_payment' => ['success' => true, 'confirmed' => true]
            ]
        ];
    }

    private function initializeServices(): void
    {
        $this->app = new Application();
        $this->database = $this->app->getContainer()->get(DatabaseService::class);
        $this->paymentService = $this->app->getContainer()->get(PaymentService::class);
        $this->telegramService = $this->app->getContainer()->get(TelegramService::class);
    }

    private function createTestUser(int $telegramId): User
    {
        return User::create($this->database, [
            'telegram_id' => $telegramId,
            'username' => "testuser{$telegramId}",
            'first_name' => 'Test',
            'last_name' => 'User'
        ]);
    }

    private function createCompletedPayment(int $userId, float $amount, string $gateway): Payment
    {
        return Payment::create($this->database, [
            'user_id' => $userId,
            'amount' => $amount,
            'currency' => 'USD',
            'gateway' => $gateway,
            'status' => 'completed',
            'transaction_id' => 'test_txn_' . uniqid()
        ]);
    }

    private function verifyPaymentNotification(int $userId, string $type): void
    {
        // Verify that appropriate notification was sent to user
        $notifications = $this->telegramService->getRecentNotifications($userId);
        $this->assertNotEmpty($notifications, 'Payment notification should be sent');
        
        $lastNotification = end($notifications);
        $this->assertContains($type, strtolower($lastNotification['message']), "Notification should mention {$type}");
    }

    // Assert helper methods (same as UserJourneyTest)
    private function assertTrue(bool $condition, string $message): void
    {
        if (!$condition) {
            throw new \Exception("Assertion failed: {$message}");
        }
    }

    private function assertFalse(bool $condition, string $message): void
    {
        if ($condition) {
            throw new \Exception("Assertion failed: {$message}");
        }
    }

    private function assertEquals($expected, $actual, string $message): void
    {
        if ($expected !== $actual) {
            throw new \Exception("Assertion failed: {$message}. Expected: {$expected}, Actual: {$actual}");
        }
    }

    private function assertNotEmpty($value, string $message): void
    {
        if (empty($value)) {
            throw new \Exception("Assertion failed: {$message}");
        }
    }

    private function assertContains(string $needle, string $haystack, string $message): void
    {
        if (strpos($haystack, $needle) === false) {
            throw new \Exception("Assertion failed: {$message}");
        }
    }

    private function recordTestResult(string $testName, bool $passed, string $message): void
    {
        $this->totalTests++;
        
        if ($passed) {
            $this->passedTests++;
            echo "  ✅ {$testName}: PASSED\n";
        } else {
            $this->failedTests++;
            echo "  ❌ {$testName}: FAILED - {$message}\n";
        }

        $this->testResults[] = [
            'test' => $testName,
            'status' => $passed ? 'PASSED' : 'FAILED',
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }

    private function generateTestReport(): array
    {
        $successRate = $this->totalTests > 0 ? ($this->passedTests / $this->totalTests) * 100 : 0;

        echo "\n📊 Payment Flow Test Results:\n";
        echo "Total Tests: {$this->totalTests}\n";
        echo "Passed: {$this->passedTests}\n";
        echo "Failed: {$this->failedTests}\n";
        echo "Success Rate: " . round($successRate, 2) . "%\n\n";

        return [
            'total_tests' => $this->totalTests,
            'passed_tests' => $this->passedTests,
            'failed_tests' => $this->failedTests,
            'success_rate' => round($successRate, 2),
            'results' => $this->testResults,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
}
