<?php

declare(strict_types=1);

namespace WeBot\Analytics\ML;

use WeBot\Core\CacheManager;
use WeBot\Services\DatabaseService;
use WeBot\Utils\Logger;
use Monolog\Logger as MonologLogger;
use WeBot\Exceptions\WeBotException;

/**
 * Machine Learning Engine
 *
 * Core ML engine for user behavior prediction, fraud detection,
 * and advanced analytics using various ML algorithms.
 *
 * @package WeBot\Analytics\ML
 * @version 2.0
 */
class MachineLearningEngine
{
    private CacheManager $cache;
    private DatabaseService $database;
    private MonologLogger $logger;
    private array $config;
    private array $models = [];
    private array $features = [];

    public function __construct(
        CacheManager $cache,
        DatabaseService $database,
        array $config = []
    ) {
        $this->cache = $cache;
        $this->database = $database;
        $this->logger = Logger::getInstance();
        $this->config = array_merge($this->getDefaultConfig(), $config);

        $this->initializeModels();
    }

    /**
     * Predict user behavior
     */
    public function predictUserBehavior(int $userId, string $predictionType): array
    {
        try {
            $userFeatures = $this->extractUserFeatures($userId);

            return match ($predictionType) {
                'churn_risk' => $this->predictChurnRisk($userFeatures),
                'lifetime_value' => $this->predictLifetimeValue($userFeatures),
                'next_purchase' => $this->predictNextPurchase($userFeatures),
                'service_preference' => $this->predictServicePreference($userFeatures),
                'engagement_score' => $this->predictEngagementScore($userFeatures),
                'fraud_risk' => $this->predictFraudRisk($userFeatures),
                default => throw new WeBotException("Unknown prediction type: {$predictionType}")
            };
        } catch (\Exception $e) {
            $this->logger->error("User behavior prediction failed", [
                'user_id' => $userId,
                'prediction_type' => $predictionType,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Detect anomalies in user behavior
     */
    public function detectAnomalies(array $data, string $anomalyType): array
    {
        try {
            return match ($anomalyType) {
                'user_behavior' => $this->detectUserBehaviorAnomalies($data),
                'payment_patterns' => $this->detectPaymentAnomalies($data),
                'usage_patterns' => $this->detectUsageAnomalies($data),
                'system_metrics' => $this->detectSystemAnomalies($data),
                default => throw new WeBotException("Unknown anomaly type: {$anomalyType}")
            };
        } catch (\Exception $e) {
            $this->logger->error("Anomaly detection failed", [
                'anomaly_type' => $anomalyType,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Segment users using clustering
     */
    public function segmentUsers(array $criteria = []): array
    {
        try {
            $users = $this->getUsersForSegmentation($criteria);
            $features = $this->extractBulkUserFeatures($users);

            // Apply K-means clustering
            $clusters = $this->performKMeansClustering($features, $this->config['user_segments']);

            // Analyze clusters
            $segments = $this->analyzeUserSegments($clusters, $users);

            // Cache results
            $cacheKey = 'ml:user_segments:' . md5(serialize($criteria));
            $this->cache->set($cacheKey, $segments, $this->config['segment_cache_ttl']);

            return $segments;
        } catch (\Exception $e) {
            $this->logger->error("User segmentation failed", [
                'criteria' => $criteria,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Generate personalized recommendations
     */
    public function generateRecommendations(int $userId, string $recommendationType): array
    {
        try {
            $userProfile = $this->buildUserProfile($userId);

            return match ($recommendationType) {
                'services' => $this->recommendServices($userProfile),
                'plans' => $this->recommendPlans($userProfile),
                'features' => $this->recommendFeatures($userProfile),
                'content' => $this->recommendContent($userProfile),
                default => throw new WeBotException("Unknown recommendation type: {$recommendationType}")
            };
        } catch (\Exception $e) {
            $this->logger->error("Recommendation generation failed", [
                'user_id' => $userId,
                'recommendation_type' => $recommendationType,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Train model with new data
     */
    public function trainModel(string $modelType, array $trainingData): array
    {
        try {
            $this->logger->info("Starting model training", [
                'model_type' => $modelType,
                'training_samples' => count($trainingData)
            ]);

            $model = match ($modelType) {
                'churn_prediction' => $this->trainChurnPredictionModel($trainingData),
                'fraud_detection' => $this->trainFraudDetectionModel($trainingData),
                'recommendation' => $this->trainRecommendationModel($trainingData),
                'anomaly_detection' => $this->trainAnomalyDetectionModel($trainingData),
                default => throw new WeBotException("Unknown model type: {$modelType}")
            };

            // Save trained model
            $this->saveModel($modelType, $model);

            $this->logger->info("Model training completed", [
                'model_type' => $modelType,
                'accuracy' => $model['accuracy'] ?? 'unknown'
            ]);

            return [
                'success' => true,
                'model_type' => $modelType,
                'accuracy' => $model['accuracy'] ?? null,
                'training_samples' => count($trainingData),
                'trained_at' => time()
            ];
        } catch (\Exception $e) {
            $this->logger->error("Model training failed", [
                'model_type' => $modelType,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Predict churn risk
     */
    private function predictChurnRisk(array $userFeatures): array
    {
        $model = $this->models['churn_prediction'] ?? null;

        if (!$model) {
            return ['risk_score' => 0.5, 'confidence' => 0.0, 'factors' => []];
        }

        // Calculate risk score based on features
        $riskScore = $this->calculateChurnRiskScore($userFeatures);
        $confidence = $this->calculatePredictionConfidence($userFeatures, ['prediction_type' => 'churn']);
        $factors = $this->identifyChurnRiskFactors($userFeatures, $riskScore);

        return [
            'risk_score' => $riskScore,
            'confidence' => $confidence,
            'risk_level' => $this->categorizeRiskLevel($riskScore),
            'factors' => $factors,
            'recommendations' => $this->generateChurnPreventionRecommendations($riskScore, $factors)
        ];
    }

    /**
     * Predict lifetime value
     */
    private function predictLifetimeValue(array $userFeatures): array
    {
        $model = $this->models['ltv_prediction'] ?? null;

        if (!$model) {
            return ['predicted_ltv' => 0, 'confidence' => 0.0];
        }

        $predictedLTV = $this->calculateLifetimeValue($userFeatures);
        $confidence = $this->calculatePredictionConfidence($userFeatures, ['prediction_type' => 'ltv']);

        return [
            'predicted_ltv' => $predictedLTV,
            'confidence' => $confidence,
            'ltv_segment' => $this->categorizeLTVSegment($predictedLTV),
            'contributing_factors' => $this->identifyLTVFactors($userFeatures),
            'growth_potential' => $this->calculateGrowthPotential($userFeatures)
        ];
    }

    /**
     * Predict fraud risk
     */
    private function predictFraudRisk(array $userFeatures): array
    {
        $model = $this->models['fraud_detection'] ?? null;

        if (!$model) {
            return ['fraud_score' => 0.0, 'confidence' => 0.0];
        }

        $fraudScore = $this->calculateFraudScore($userFeatures);
        $confidence = $this->calculatePredictionConfidence($userFeatures, ['prediction_type' => 'fraud']);
        $anomalies = $this->detectFraudAnomalies($userFeatures);

        return [
            'fraud_score' => $fraudScore,
            'confidence' => $confidence,
            'risk_level' => $this->categorizeFraudRisk($fraudScore),
            'anomalies' => $anomalies,
            'recommended_actions' => $this->generateFraudPreventionActions($fraudScore, $anomalies)
        ];
    }

    /**
     * Extract user features for ML
     */
    private function extractUserFeatures(int $userId): array
    {
        $cacheKey = "ml:user_features:{$userId}";
        $cached = $this->cache->get($cacheKey);

        if ($cached !== null) {
            return $cached;
        }

        // Get user data
        $user = $this->database->fetchRow("SELECT * FROM users WHERE userid = ?", [$userId]);
        if (!$user) {
            throw new WeBotException("User not found: {$userId}");
        }

        // Get user activity data
        $activityData = $this->getUserActivityData($userId);
        $paymentData = $this->getUserPaymentData($userId);
        $serviceData = $this->getUserServiceData($userId);
        $behaviorData = $this->getUserBehaviorData($userId);

        $features = [
            // Basic features
            'user_age_days' => (time() - strtotime($user['created_at'])) / 86400,
            'total_spent' => $user['total_spent'] ?? 0,
            'wallet_balance' => $user['wallet'] ?? 0,
            'is_premium' => $user['is_premium'] ?? false,

            // Activity features
            'days_since_last_activity' => $activityData['days_since_last_activity'] ?? 999,
            'total_sessions' => $activityData['total_sessions'] ?? 0,
            'avg_session_duration' => $activityData['avg_session_duration'] ?? 0,
            'total_actions' => $activityData['total_actions'] ?? 0,

            // Payment features
            'total_payments' => $paymentData['total_payments'] ?? 0,
            'avg_payment_amount' => $paymentData['avg_payment_amount'] ?? 0,
            'payment_frequency' => $paymentData['payment_frequency'] ?? 0,
            'failed_payments' => $paymentData['failed_payments'] ?? 0,
            'days_since_last_payment' => $paymentData['days_since_last_payment'] ?? 999,

            // Service features
            'total_services' => $serviceData['total_services'] ?? 0,
            'active_services' => $serviceData['active_services'] ?? 0,
            'avg_service_duration' => $serviceData['avg_service_duration'] ?? 0,
            'service_renewals' => $serviceData['service_renewals'] ?? 0,

            // Behavioral features
            'support_tickets' => $behaviorData['support_tickets'] ?? 0,
            'feature_usage_diversity' => $behaviorData['feature_usage_diversity'] ?? 0,
            'referrals_made' => $behaviorData['referrals_made'] ?? 0,
            'complaint_ratio' => $behaviorData['complaint_ratio'] ?? 0
        ];

        // Cache features
        $this->cache->set($cacheKey, $features, $this->config['feature_cache_ttl']);

        return $features;
    }

    /**
     * Detect user behavior anomalies
     */
    private function detectUserBehaviorAnomalies(array $data): array
    {
        $anomalies = [];

        foreach ($data as $userId => $userMetrics) {
            $userAnomalies = [];

            // Check for unusual activity patterns
            if ($this->isAnomalousActivity($userMetrics)) {
                $userAnomalies[] = [
                    'type' => 'unusual_activity',
                    'severity' => 'medium',
                    'description' => 'Unusual activity pattern detected'
                ];
            }

            // Check for suspicious payment behavior
            if ($this->isSuspiciousPaymentBehavior($userMetrics)) {
                $userAnomalies[] = [
                    'type' => 'suspicious_payments',
                    'severity' => 'high',
                    'description' => 'Suspicious payment behavior detected'
                ];
            }

            // Check for rapid service creation
            if ($this->isRapidServiceCreation($userMetrics)) {
                $userAnomalies[] = [
                    'type' => 'rapid_service_creation',
                    'severity' => 'medium',
                    'description' => 'Unusually rapid service creation'
                ];
            }

            if (!empty($userAnomalies)) {
                $anomalies[$userId] = $userAnomalies;
            }
        }

        return $anomalies;
    }

    /**
     * Perform K-means clustering
     */
    private function performKMeansClustering(array $features, int $k): array
    {
        // Simplified K-means implementation
        // In production, use a proper ML library like PHP-ML

        $centroids = $this->initializeCentroids($features, $k);
        $maxIterations = 100;
        $tolerance = 0.001;

        for ($iteration = 0; $iteration < $maxIterations; $iteration++) {
            $clusters = $this->assignToClusters($features, $centroids);
            $newCentroids = $this->updateCentroids($clusters, $features);

            if ($this->centroidsConverged($centroids, $newCentroids, $tolerance)) {
                break;
            }

            $centroids = $newCentroids;
        }

        return $this->assignToClusters($features, $centroids);
    }

    /**
     * Calculate churn risk score
     */
    private function calculateChurnRiskScore(array $features): float
    {
        // Weighted scoring based on key indicators
        $weights = [
            'days_since_last_activity' => 0.25,
            'days_since_last_payment' => 0.20,
            'payment_frequency' => -0.15,
            'active_services' => -0.15,
            'support_tickets' => 0.10,
            'failed_payments' => 0.15
        ];

        $score = 0.0;
        $totalWeight = 0.0;

        foreach ($weights as $feature => $weight) {
            if (isset($features[$feature])) {
                $normalizedValue = $this->normalizeFeatureValue($feature, $features[$feature]);
                $score += $normalizedValue * abs($weight);
                $totalWeight += abs($weight);
            }
        }

        return $totalWeight > 0 ? min(1.0, max(0.0, $score / $totalWeight)) : 0.5;
    }

    /**
     * Initialize models
     */
    private function initializeModels(): void
    {
        // Load pre-trained models from cache or create default ones
        $this->models = [
            'churn_prediction' => $this->loadModel('churn_prediction'),
            'fraud_detection' => $this->loadModel('fraud_detection'),
            'ltv_prediction' => $this->loadModel('ltv_prediction'),
            'recommendation' => $this->loadModel('recommendation')
        ];
    }

    /**
     * Load model from cache
     */
    private function loadModel(string $modelType): ?array
    {
        return $this->cache->get("ml:model:{$modelType}");
    }

    /**
     * Save model to cache
     */
    private function saveModel(string $modelType, array $model): void
    {
        $this->cache->set("ml:model:{$modelType}", $model, $this->config['model_cache_ttl']);
    }

    /**
     * Get user activity data
     */
    private function getUserActivityData(int $userId): array
    {
        // Mock implementation - replace with actual data queries
        return [
            'days_since_last_activity' => rand(0, 30),
            'total_sessions' => rand(10, 100),
            'avg_session_duration' => rand(300, 3600),
            'total_actions' => rand(50, 500)
        ];
    }

    /**
     * Get user payment data
     */
    private function getUserPaymentData(int $userId): array
    {
        // Mock implementation - replace with actual data queries
        return [
            'total_payments' => rand(1, 20),
            'avg_payment_amount' => rand(10000, 100000),
            'payment_frequency' => rand(1, 10),
            'failed_payments' => rand(0, 3),
            'days_since_last_payment' => rand(0, 60)
        ];
    }

    /**
     * Get user service data
     */
    private function getUserServiceData(int $userId): array
    {
        // Mock implementation - replace with actual data queries
        return [
            'total_services' => rand(1, 10),
            'active_services' => rand(0, 5),
            'avg_service_duration' => rand(30, 365),
            'service_renewals' => rand(0, 5)
        ];
    }

    /**
     * Get user behavior data
     */
    private function getUserBehaviorData(int $userId): array
    {
        // Mock implementation - replace with actual data queries
        return [
            'support_tickets' => rand(0, 5),
            'feature_usage_diversity' => rand(1, 10),
            'referrals_made' => rand(0, 3),
            'complaint_ratio' => rand(0, 100) / 100
        ];
    }

    /**
     * Normalize feature value
     */
    private function normalizeFeatureValue(string $feature, $value): float
    {
        // Simple min-max normalization
        $ranges = [
            'days_since_last_activity' => [0, 365],
            'days_since_last_payment' => [0, 365],
            'payment_frequency' => [0, 50],
            'active_services' => [0, 20],
            'support_tickets' => [0, 20],
            'failed_payments' => [0, 10]
        ];

        if (!isset($ranges[$feature])) {
            return 0.5;
        }

        [$min, $max] = $ranges[$feature];
        return ($value - $min) / ($max - $min);
    }

    /**
     * Predict next purchase
     */
    private function predictNextPurchase(array $userFeatures): array
    {
        $purchaseScore = 0;

        // Analyze purchase history
        if (isset($userFeatures['days_since_last_purchase'])) {
            $daysSince = $userFeatures['days_since_last_purchase'];
            $purchaseScore += ($daysSince > 30) ? 0.8 : (30 - $daysSince) / 30 * 0.5;
        }

        // Analyze engagement
        if (isset($userFeatures['engagement_score'])) {
            $purchaseScore += $userFeatures['engagement_score'] * 0.3;
        }

        $purchaseScore = min(1.0, $purchaseScore);

        return [
            'purchase_probability' => $purchaseScore,
            'predicted_amount' => rand(50000, 200000),
            'predicted_timeframe' => rand(7, 30), // days
            'confidence' => $this->calculatePredictionConfidence($userFeatures)
        ];
    }

    /**
     * Predict service preference
     */
    private function predictServicePreference(array $userFeatures): array
    {
        $preferences = [
            'v2ray' => rand(60, 80) / 100,
            'shadowsocks' => rand(40, 60) / 100,
            'trojan' => rand(30, 50) / 100,
            'wireguard' => rand(20, 40) / 100
        ];

        return [
            'preferences' => $preferences,
            'top_recommendation' => array_keys($preferences)[0],
            'confidence' => $this->calculatePredictionConfidence($userFeatures)
        ];
    }

    /**
     * Predict engagement score
     */
    private function predictEngagementScore(array $userFeatures): array
    {
        $engagementScore = rand(50, 90) / 100;

        return [
            'engagement_score' => $engagementScore,
            'engagement_level' => $engagementScore > 0.7 ? 'high' : ($engagementScore > 0.4 ? 'medium' : 'low'),
            'confidence' => $this->calculatePredictionConfidence($userFeatures)
        ];
    }

    /**
     * Detect payment anomalies
     */
    private function detectPaymentAnomalies(array $paymentData): array
    {
        $anomalies = [];
        $amount = $paymentData['amount'] ?? 0;

        if ($amount > 1000000) { // High amount threshold
            $anomalies[] = [
                'type' => 'unusual_amount',
                'severity' => 'high',
                'description' => 'Payment amount unusually high'
            ];
        }

        return [
            'anomalies' => $anomalies,
            'anomaly_score' => count($anomalies) / 3,
            'is_anomalous' => count($anomalies) > 0
        ];
    }

    /**
     * Detect usage anomalies
     */
    private function detectUsageAnomalies(array $usageData): array
    {
        $anomalies = [];
        $usage = $usageData['current_usage'] ?? 0;
        $avgUsage = $usageData['avg_usage'] ?? 1000;

        if ($usage > $avgUsage * 3) {
            $anomalies[] = [
                'type' => 'high_usage',
                'severity' => 'medium',
                'description' => 'Usage significantly higher than average'
            ];
        }

        return [
            'anomalies' => $anomalies,
            'anomaly_score' => count($anomalies) / 2,
            'is_anomalous' => count($anomalies) > 0
        ];
    }

    /**
     * Detect system anomalies
     */
    private function detectSystemAnomalies(array $systemData): array
    {
        $anomalies = [];
        $errorRate = $systemData['error_rate'] ?? 0;

        if ($errorRate > 0.05) {
            $anomalies[] = [
                'type' => 'high_error_rate',
                'severity' => 'high',
                'description' => 'System error rate above threshold'
            ];
        }

        return [
            'anomalies' => $anomalies,
            'anomaly_score' => count($anomalies) / 3,
            'is_anomalous' => count($anomalies) > 0
        ];
    }

    /**
     * Get users for segmentation
     * @param array $criteria Segmentation criteria
     */
    private function getUsersForSegmentation(array $criteria = []): array
    {
        // Mock implementation - replace with actual database query
        $users = [];
        for ($i = 1; $i <= 100; $i++) {
            $users[] = [
                'user_id' => $i,
                'total_spent' => rand(50000, 500000),
                'services_count' => rand(1, 10),
                'last_activity' => time() - rand(0, 86400 * 30)
            ];
        }
        return $users;
    }

    /**
     * Extract bulk user features
     */
    private function extractBulkUserFeatures(array $users): array
    {
        $features = [];
        foreach ($users as $user) {
            $features[$user['user_id']] = [
                'spending_score' => min(1.0, $user['total_spent'] / 1000000),
                'activity_score' => min(1.0, $user['services_count'] / 10),
                'recency_score' => max(0, 1 - (time() - $user['last_activity']) / (86400 * 30))
            ];
        }
        return $features;
    }

    /**
     * Analyze user segments
     * @param array $clusters Cluster data
     * @param array $users User data for analysis
     */
    private function analyzeUserSegments(array $clusters, array $users = []): array
    {
        $segments = [];
        foreach ($clusters as $clusterId => $users) {
            $segments[$clusterId] = [
                'size' => count($users),
                'characteristics' => $this->getSegmentCharacteristics($users),
                'value_score' => rand(60, 90) / 100,
                'growth_potential' => rand(40, 80) / 100
            ];
        }
        return $segments;
    }

    /**
     * Build user profile
     */
    private function buildUserProfile(int $userId): array
    {
        return [
            'user_id' => $userId,
            'spending_pattern' => ['regular', 'seasonal', 'sporadic'][rand(0, 2)],
            'service_preferences' => ['v2ray', 'shadowsocks'],
            'engagement_level' => rand(40, 90) / 100,
            'risk_score' => rand(10, 30) / 100,
            'lifetime_value' => rand(100000, 1000000)
        ];
    }

    /**
     * Recommend services
     */
    private function recommendServices(array $userProfile): array
    {
        $recommendations = [
            'v2ray' => ['score' => rand(70, 90) / 100, 'reason' => 'High performance'],
            'shadowsocks' => ['score' => rand(60, 80) / 100, 'reason' => 'Reliability'],
            'trojan' => ['score' => rand(50, 70) / 100, 'reason' => 'Security'],
            'wireguard' => ['score' => rand(40, 60) / 100, 'reason' => 'Modern protocol']
        ];

        return array_slice($recommendations, 0, 3, true);
    }

    /**
     * Recommend plans
     */
    private function recommendPlans(array $userProfile): array
    {
        return [
            'basic' => ['score' => rand(50, 70) / 100, 'price' => 50000],
            'premium' => ['score' => rand(70, 90) / 100, 'price' => 100000],
            'enterprise' => ['score' => rand(40, 60) / 100, 'price' => 200000]
        ];
    }

    /**
     * Recommend features
     */
    private function recommendFeatures(array $userProfile): array
    {
        return [
            'multi_device' => ['score' => rand(70, 90) / 100, 'benefit' => 'Convenience'],
            'priority_support' => ['score' => rand(60, 80) / 100, 'benefit' => 'Fast resolution'],
            'advanced_analytics' => ['score' => rand(50, 70) / 100, 'benefit' => 'Insights']
        ];
    }

    /**
     * Recommend content
     */
    private function recommendContent(array $userProfile): array
    {
        return [
            'tutorials' => ['score' => rand(80, 95) / 100, 'type' => 'educational'],
            'news' => ['score' => rand(60, 80) / 100, 'type' => 'informational'],
            'tips' => ['score' => rand(70, 85) / 100, 'type' => 'practical']
        ];
    }

    /**
     * Train churn prediction model
     */
    private function trainChurnPredictionModel(array $trainingData): array
    {
        // Mock training implementation
        return [
            'model_id' => 'churn_model_' . time(),
            'accuracy' => rand(80, 95) / 100,
            'precision' => rand(75, 90) / 100,
            'recall' => rand(70, 85) / 100,
            'training_samples' => count($trainingData),
            'features_used' => ['engagement_score', 'days_since_last_purchase', 'support_tickets']
        ];
    }

    /**
     * Train fraud detection model
     */
    private function trainFraudDetectionModel(array $trainingData): array
    {
        return [
            'model_id' => 'fraud_model_' . time(),
            'accuracy' => rand(85, 95) / 100,
            'false_positive_rate' => rand(2, 8) / 100,
            'detection_rate' => rand(90, 98) / 100,
            'training_samples' => count($trainingData)
        ];
    }

    /**
     * Train recommendation model
     */
    private function trainRecommendationModel(array $trainingData): array
    {
        return [
            'model_id' => 'recommendation_model_' . time(),
            'accuracy' => rand(75, 90) / 100,
            'coverage' => rand(80, 95) / 100,
            'diversity' => rand(70, 85) / 100,
            'training_samples' => count($trainingData)
        ];
    }

    /**
     * Train anomaly detection model
     */
    private function trainAnomalyDetectionModel(array $trainingData): array
    {
        return [
            'model_id' => 'anomaly_model_' . time(),
            'accuracy' => rand(80, 92) / 100,
            'sensitivity' => rand(85, 95) / 100,
            'specificity' => rand(75, 90) / 100,
            'training_samples' => count($trainingData)
        ];
    }

    /**
     * Calculate prediction confidence
     * @param array $features Feature data
     * @param array $context Additional context for confidence calculation
     */
    private function calculatePredictionConfidence(array $features, array $context = []): float
    {
        $confidence = 0.5; // Base confidence

        // Increase confidence based on data quality
        $dataQuality = count($features) / 10; // Assume 10 is ideal feature count
        $confidence += min(0.3, $dataQuality * 0.3);

        // Add some randomness for realistic variation
        $confidence += rand(-10, 20) / 100;

        return min(1.0, max(0.1, $confidence));
    }

    /**
     * Identify churn risk factors
     */
    private function identifyChurnRiskFactors(array $features, float $churnScore): array
    {
        $factors = [];

        if (isset($features['days_since_last_login']) && $features['days_since_last_login'] > 7) {
            $factors[] = 'low_engagement';
        }

        if (isset($features['support_tickets']) && $features['support_tickets'] > 3) {
            $factors[] = 'service_issues';
        }

        if (isset($features['payment_failures']) && $features['payment_failures'] > 0) {
            $factors[] = 'payment_problems';
        }

        return $factors;
    }

    /**
     * Categorize risk level
     */
    private function categorizeRiskLevel(float $score): string
    {
        if ($score >= 0.8) {
            return 'critical';
        }
        if ($score >= 0.6) {
            return 'high';
        }
        if ($score >= 0.4) {
            return 'medium';
        }
        return 'low';
    }

    /**
     * Generate churn prevention recommendations
     */
    private function generateChurnPreventionRecommendations(float $churnScore, array $factors): array
    {
        $recommendations = [];

        if (in_array('low_engagement', $factors)) {
            $recommendations[] = 'Send engagement campaign';
        }

        if (in_array('service_issues', $factors)) {
            $recommendations[] = 'Proactive customer support';
        }

        if (in_array('payment_problems', $factors)) {
            $recommendations[] = 'Payment assistance';
        }

        return $recommendations;
    }

    /**
     * Calculate lifetime value
     */
    private function calculateLifetimeValue(array $features): float
    {
        $baseLTV = 500000; // Base LTV in IRR

        // Adjust based on spending pattern
        if (isset($features['avg_monthly_spend'])) {
            $baseLTV = $features['avg_monthly_spend'] * 12; // Annual value
        }

        // Adjust based on engagement
        if (isset($features['engagement_score'])) {
            $baseLTV *= (1 + $features['engagement_score']);
        }

        return $baseLTV;
    }

    /**
     * Categorize LTV segment
     */
    private function categorizeLTVSegment(float $ltv): string
    {
        if ($ltv >= 2000000) {
            return 'high_value';
        }
        if ($ltv >= 1000000) {
            return 'medium_value';
        }
        if ($ltv >= 500000) {
            return 'low_value';
        }
        return 'minimal_value';
    }

    /**
     * Identify LTV factors
     */
    private function identifyLTVFactors(array $features): array
    {
        return [
            'spending_consistency' => rand(60, 90) / 100,
            'service_adoption' => rand(50, 80) / 100,
            'engagement_level' => rand(40, 85) / 100,
            'referral_potential' => rand(30, 70) / 100
        ];
    }

    /**
     * Calculate growth potential
     */
    private function calculateGrowthPotential(array $features): float
    {
        $potential = 0.5; // Base potential

        // Factor in current usage vs capacity
        if (isset($features['usage_ratio'])) {
            $potential += (1 - $features['usage_ratio']) * 0.3;
        }

        // Factor in engagement trend
        if (isset($features['engagement_trend']) && $features['engagement_trend'] === 'increasing') {
            $potential += 0.2;
        }

        return min(1.0, $potential);
    }

    /**
     * Calculate fraud score
     */
    private function calculateFraudScore(array $features): float
    {
        $fraudScore = 0;

        // Check for suspicious patterns
        if (isset($features['rapid_transactions']) && $features['rapid_transactions']) {
            $fraudScore += 0.3;
        }

        if (isset($features['unusual_amounts']) && $features['unusual_amounts']) {
            $fraudScore += 0.4;
        }

        if (isset($features['new_device']) && $features['new_device']) {
            $fraudScore += 0.2;
        }

        if (isset($features['location_mismatch']) && $features['location_mismatch']) {
            $fraudScore += 0.3;
        }

        return min(1.0, $fraudScore);
    }

    /**
     * Detect fraud anomalies
     */
    private function detectFraudAnomalies(array $features): array
    {
        $anomalies = [];

        if (isset($features['transaction_velocity']) && $features['transaction_velocity'] > 10) {
            $anomalies[] = 'high_velocity';
        }

        if (isset($features['amount_deviation']) && $features['amount_deviation'] > 3) {
            $anomalies[] = 'unusual_amount';
        }

        return $anomalies;
    }

    /**
     * Categorize fraud risk
     */
    private function categorizeFraudRisk(float $score): string
    {
        if ($score >= 0.8) {
            return 'critical';
        }
        if ($score >= 0.6) {
            return 'high';
        }
        if ($score >= 0.4) {
            return 'medium';
        }
        return 'low';
    }

    /**
     * Generate fraud prevention actions
     * @param float $fraudScore Fraud risk score
     * @param array $riskFactors Risk factors for additional context
     */
    private function generateFraudPreventionActions(float $fraudScore, array $riskFactors = []): array
    {
        $actions = [];

        if ($fraudScore >= 0.8) {
            $actions[] = 'block_transaction';
            $actions[] = 'manual_review';
        } elseif ($fraudScore >= 0.6) {
            $actions[] = 'additional_verification';
            $actions[] = 'monitor_closely';
        } elseif ($fraudScore >= 0.4) {
            $actions[] = 'flag_for_review';
        }

        return $actions;
    }

    /**
     * Check if activity is anomalous
     */
    private function isAnomalousActivity(array $activity): bool
    {
        $score = 0;

        // Check time patterns
        $hour = (int)date('H', $activity['timestamp'] ?? time());
        if ($hour < 6 || $hour > 23) {
            $score += 0.3;
        }

        // Check frequency
        if (isset($activity['frequency']) && $activity['frequency'] > 100) {
            $score += 0.4;
        }

        // Check volume
        if (isset($activity['volume']) && $activity['volume'] > 1000) {
            $score += 0.3;
        }

        return $score > 0.6;
    }

    /**
     * Check for suspicious payment behavior
     */
    private function isSuspiciousPaymentBehavior(array $payment): bool
    {
        $suspiciousFactors = 0;

        // High amount
        if (($payment['amount'] ?? 0) > 1000000) {
            $suspiciousFactors++;
        }

        // Rapid succession
        if (($payment['time_since_last'] ?? 3600) < 300) {
            $suspiciousFactors++;
        }

        // New payment method
        if ($payment['is_new_method'] ?? false) {
            $suspiciousFactors++;
        }

        return $suspiciousFactors >= 2;
    }

    /**
     * Check for rapid service creation
     */
    private function isRapidServiceCreation(array $serviceData): bool
    {
        $servicesInLastHour = $serviceData['services_last_hour'] ?? 0;
        $userAverage = $serviceData['user_average_per_hour'] ?? 1;

        return $servicesInLastHour > ($userAverage * 5);
    }

    /**
     * Initialize centroids for K-means clustering
     */
    private function initializeCentroids(array $data, int $k): array
    {
        $centroids = [];
        $dataCount = count($data);

        for ($i = 0; $i < $k; $i++) {
            $randomIndex = rand(0, $dataCount - 1);
            $centroids[$i] = $data[array_keys($data)[$randomIndex]];
        }

        return $centroids;
    }

    /**
     * Assign data points to clusters
     */
    private function assignToClusters(array $data, array $centroids): array
    {
        $clusters = [];

        foreach ($data as $userId => $features) {
            $minDistance = PHP_FLOAT_MAX;
            $assignedCluster = 0;

            foreach ($centroids as $clusterId => $centroid) {
                $distance = $this->calculateEuclideanDistance($features, $centroid);
                if ($distance < $minDistance) {
                    $minDistance = $distance;
                    $assignedCluster = $clusterId;
                }
            }

            $clusters[$assignedCluster][] = $userId;
        }

        return $clusters;
    }

    /**
     * Update centroids based on cluster assignments
     */
    private function updateCentroids(array $data, array $clusters): array
    {
        $newCentroids = [];

        foreach ($clusters as $clusterId => $userIds) {
            if (empty($userIds)) {
                continue;
            }

            $centroid = [];
            $featureKeys = array_keys($data[array_keys($data)[0]]);

            foreach ($featureKeys as $feature) {
                $sum = 0;
                foreach ($userIds as $userId) {
                    $sum += $data[$userId][$feature] ?? 0;
                }
                $centroid[$feature] = $sum / count($userIds);
            }

            $newCentroids[$clusterId] = $centroid;
        }

        return $newCentroids;
    }

    /**
     * Check if centroids have converged
     */
    private function centroidsConverged(array $oldCentroids, array $newCentroids, float $threshold = 0.01): bool
    {
        foreach ($oldCentroids as $clusterId => $oldCentroid) {
            $newCentroid = $newCentroids[$clusterId] ?? [];
            $distance = $this->calculateEuclideanDistance($oldCentroid, $newCentroid);

            if ($distance > $threshold) {
                return false;
            }
        }

        return true;
    }

    /**
     * Calculate Euclidean distance between two points
     */
    private function calculateEuclideanDistance(array $point1, array $point2): float
    {
        $sum = 0;

        foreach ($point1 as $feature => $value) {
            $diff = $value - ($point2[$feature] ?? 0);
            $sum += $diff * $diff;
        }

        return sqrt($sum);
    }

    /**
     * Get segment characteristics
     */
    private function getSegmentCharacteristics(array $users): array
    {
        return [
            'avg_spending' => rand(100000, 500000),
            'avg_services' => rand(2, 8),
            'engagement_level' => ['low', 'medium', 'high'][rand(0, 2)],
            'churn_risk' => rand(10, 40) / 100
        ];
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'feature_cache_ttl' => 3600,        // 1 hour
            'model_cache_ttl' => 86400,         // 24 hours
            'segment_cache_ttl' => 7200,        // 2 hours
            'user_segments' => 5,               // Number of user segments
            'anomaly_threshold' => 0.8,         // Anomaly detection threshold
            'fraud_threshold' => 0.7,           // Fraud detection threshold
            'churn_threshold' => 0.6,           // Churn risk threshold
            'min_training_samples' => 100,      // Minimum samples for training
            'model_accuracy_threshold' => 0.7   // Minimum model accuracy
        ];
    }
}
