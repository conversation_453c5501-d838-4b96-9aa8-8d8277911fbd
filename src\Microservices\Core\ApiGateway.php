<?php

declare(strict_types=1);

namespace WeBot\Microservices\Core;

use WeBot\Core\CacheManager;
use WeBot\Utils\Logger;
use WeBot\Exceptions\WeBotException;
use WeBot\Microservices\Core\ServiceRegistry;
use WeBot\Microservices\Core\CircuitBreaker;
use WeBot\Microservices\Core\RateLimiter;

/**
 * API Gateway
 *
 * Central entry point for all microservice requests with routing,
 * authentication, rate limiting, and circuit breaker patterns.
 *
 * @package WeBot\Microservices\Core
 * @version 2.0
 */
class ApiGateway
{
    private ServiceRegistry $serviceRegistry;
    private CircuitBreaker $circuitBreaker;
    private RateLimiter $rateLimiter;
    private CacheManager $cache;
    private Logger $logger;
    private array $config;
    private array $routes = [];

    public function __construct(
        ServiceRegistry $serviceRegistry,
        CircuitBreaker $circuitBreaker,
        RateLimiter $rateLimiter,
        CacheManager $cache,
        array $config = []
    ) {
        $this->serviceRegistry = $serviceRegistry;
        $this->circuitBreaker = $circuitBreaker;
        $this->rateLimiter = $rateLimiter;
        $this->cache = $cache;
        $this->logger = Logger::getInstance();
        $this->config = array_merge($this->getDefaultConfig(), $config);

        $this->loadRoutes();
    }

    /**
     * Handle incoming request
     */
    public function handleRequest(array $request): array
    {
        $startTime = microtime(true);
        $requestId = $this->generateRequestId();

        try {
            // Log incoming request
            $this->logger->info("Gateway request received", [
                'request_id' => $requestId,
                'path' => $request['path'] ?? '',
                'method' => $request['method'] ?? 'POST',
                'user_id' => $request['user_id'] ?? null
            ]);

            // Rate limiting
            if (!$this->checkRateLimit($request)) {
                throw new WeBotException("Rate limit exceeded", 429);
            }

            // Authentication
            if (!$this->authenticate($request)) {
                throw new WeBotException("Authentication failed", 401);
            }

            // Route resolution
            $route = $this->resolveRoute($request);
            if (!$route) {
                throw new WeBotException("Route not found", 404);
            }

            // Service discovery
            $serviceInstance = $this->serviceRegistry->getServiceInstance(
                $route['service'],
                $route['load_balancing'] ?? 'round_robin'
            );

            // Circuit breaker check
            $circuitKey = "circuit:{$route['service']}:{$serviceInstance['id']}";
            if (!$this->circuitBreaker->canExecute($circuitKey)) {
                throw new WeBotException("Service temporarily unavailable", 503);
            }

            // Forward request to service
            $response = $this->forwardRequest($serviceInstance, $route, $request);

            // Record success
            $this->circuitBreaker->recordSuccess($circuitKey);

            // Log successful response
            $responseTime = (microtime(true) - $startTime) * 1000;
            $this->logger->info("Gateway request completed", [
                'request_id' => $requestId,
                'service' => $route['service'],
                'response_time' => $responseTime,
                'status' => 'success'
            ]);

            return [
                'success' => true,
                'data' => $response,
                'metadata' => [
                    'request_id' => $requestId,
                    'service' => $route['service'],
                    'response_time' => $responseTime
                ]
            ];
        } catch (\Exception $e) {
            // Record failure for circuit breaker
            if (isset($circuitKey)) {
                $this->circuitBreaker->recordFailure($circuitKey);
            }

            // Log error
            $responseTime = (microtime(true) - $startTime) * 1000;
            $this->logger->error("Gateway request failed", [
                'request_id' => $requestId,
                'error' => $e->getMessage(),
                'response_time' => $responseTime
            ]);

            return [
                'success' => false,
                'error' => [
                    'code' => $e->getCode() ?: 500,
                    'message' => $e->getMessage()
                ],
                'metadata' => [
                    'request_id' => $requestId,
                    'response_time' => $responseTime
                ]
            ];
        }
    }

    /**
     * Register a route
     */
    public function registerRoute(string $path, string $service, array $options = []): void
    {
        $this->routes[$path] = [
            'service' => $service,
            'method' => $options['method'] ?? 'POST',
            'auth_required' => $options['auth_required'] ?? true,
            'rate_limit' => $options['rate_limit'] ?? $this->config['default_rate_limit'],
            'timeout' => $options['timeout'] ?? $this->config['default_timeout'],
            'load_balancing' => $options['load_balancing'] ?? 'round_robin',
            'cache_ttl' => $options['cache_ttl'] ?? 0,
            'transform_request' => $options['transform_request'] ?? null,
            'transform_response' => $options['transform_response'] ?? null
        ];

        $this->persistRoutes();
    }

    /**
     * Check rate limit
     */
    private function checkRateLimit(array $request): bool
    {
        $userId = $request['user_id'] ?? 'anonymous';
        $path = $request['path'] ?? '';

        $route = $this->resolveRoute($request);
        $limit = $route['rate_limit'] ?? $this->config['default_rate_limit'];

        return $this->rateLimiter->isAllowed($userId, $path, $limit);
    }

    /**
     * Authenticate request
     */
    private function authenticate(array $request): bool
    {
        $route = $this->resolveRoute($request);

        if (!($route['auth_required'] ?? true)) {
            return true;
        }

        // Check for authentication token
        $token = $request['headers']['Authorization'] ?? $request['token'] ?? null;

        if (!$token) {
            return false;
        }

        // Validate token (implement your token validation logic)
        return $this->validateToken($token);
    }

    /**
     * Resolve route from request
     */
    private function resolveRoute(array $request): ?array
    {
        $path = $request['path'] ?? '';
        $method = $request['method'] ?? 'POST';

        // Exact match
        if (isset($this->routes[$path])) {
            $route = $this->routes[$path];
            if ($route['method'] === $method || $route['method'] === 'ANY') {
                return $route;
            }
        }

        // Pattern matching
        foreach ($this->routes as $routePath => $route) {
            if (
                $this->matchRoute($routePath, $path) &&
                ($route['method'] === $method || $route['method'] === 'ANY')
            ) {
                return $route;
            }
        }

        return null;
    }

    /**
     * Forward request to service
     */
    private function forwardRequest(array $serviceInstance, array $route, array $request): array
    {
        $url = $this->buildServiceUrl($serviceInstance, $request['path'] ?? '');

        // Transform request if needed
        if ($route['transform_request']) {
            $request = call_user_func($route['transform_request'], $request);
        }

        // Check cache first
        if ($route['cache_ttl'] > 0) {
            $cacheKey = $this->buildCacheKey($url, $request);
            $cached = $this->cache->get($cacheKey);
            if ($cached !== null) {
                return $cached;
            }
        }

        // Prepare request
        $postData = json_encode($request['data'] ?? []);
        $headers = array_merge(
            $request['headers'] ?? [],
            [
                'Content-Type: application/json',
                'Content-Length: ' . strlen($postData),
                'X-Request-ID: ' . ($request['request_id'] ?? $this->generateRequestId()),
                'X-Gateway-Version: 1.0'
            ]
        );

        $context = stream_context_create([
            'http' => [
                'method' => $request['method'] ?? 'POST',
                'header' => implode("\r\n", $headers),
                'content' => $postData,
                'timeout' => $route['timeout']
            ]
        ]);

        // Make request
        $response = file_get_contents($url, false, $context);

        if ($response === false) {
            throw new WeBotException("Service request failed");
        }

        $responseData = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new WeBotException("Invalid JSON response from service");
        }

        // Transform response if needed
        if ($route['transform_response']) {
            $responseData = call_user_func($route['transform_response'], $responseData);
        }

        // Cache response if configured
        if ($route['cache_ttl'] > 0) {
            $this->cache->set($cacheKey, $responseData, $route['cache_ttl']);
        }

        return $responseData;
    }

    /**
     * Validate authentication token
     */
    private function validateToken(string $token): bool
    {
        // Remove 'Bearer ' prefix if present
        $token = str_replace('Bearer ', '', $token);

        // Check token in cache first
        $cacheKey = "auth_token:{$token}";
        $cached = $this->cache->get($cacheKey);

        if ($cached !== null) {
            return $cached['valid'] ?? false;
        }

        // Validate token (implement your validation logic)
        // For now, we'll use a simple check
        $isValid = strlen($token) >= 32; // Basic validation

        // Cache result
        $this->cache->set($cacheKey, ['valid' => $isValid], 300);

        return $isValid;
    }

    /**
     * Match route pattern
     */
    private function matchRoute(string $pattern, string $path): bool
    {
        // Convert route pattern to regex
        $regex = preg_replace('/\{[^}]+\}/', '([^/]+)', $pattern);
        $regex = '#^' . $regex . '$#';

        return preg_match($regex, $path) === 1;
    }

    /**
     * Build service URL
     */
    private function buildServiceUrl(array $serviceInstance, string $path): string
    {
        $baseUrl = "{$serviceInstance['protocol']}://{$serviceInstance['host']}:{$serviceInstance['port']}";
        return rtrim($baseUrl, '/') . '/' . ltrim($path, '/');
    }

    /**
     * Build cache key
     */
    private function buildCacheKey(string $url, array $request): string
    {
        $data = $request['data'] ?? [];
        return 'gateway_cache:' . md5($url . serialize($data));
    }

    /**
     * Generate unique request ID
     */
    private function generateRequestId(): string
    {
        return uniqid('req_', true);
    }

    /**
     * Load routes from cache
     */
    private function loadRoutes(): void
    {
        $this->routes = $this->cache->get('api_gateway:routes', []);

        // Load default routes if empty
        if (empty($this->routes)) {
            $this->loadDefaultRoutes();
        }
    }

    /**
     * Persist routes to cache
     */
    private function persistRoutes(): void
    {
        $this->cache->set('api_gateway:routes', $this->routes, 0);
    }

    /**
     * Load default routes
     */
    private function loadDefaultRoutes(): void
    {
        // User service routes
        $this->registerRoute('/api/users/{id}', 'user-service');
        $this->registerRoute('/api/users', 'user-service');
        $this->registerRoute('/api/auth/login', 'user-service', ['auth_required' => false]);

        // Payment service routes
        $this->registerRoute('/api/payments', 'payment-service');
        $this->registerRoute('/api/payments/{id}', 'payment-service');
        $this->registerRoute('/api/wallet', 'payment-service');

        // Panel service routes
        $this->registerRoute('/api/panels', 'panel-service');
        $this->registerRoute('/api/services', 'panel-service');
        $this->registerRoute('/api/services/{id}', 'panel-service');
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'default_timeout' => 30,
            'default_rate_limit' => ['requests' => 100, 'window' => 3600],
            'max_request_size' => 1024 * 1024, // 1MB
            'enable_cors' => true,
            'cors_origins' => ['*'],
            'enable_compression' => true
        ];
    }
}
