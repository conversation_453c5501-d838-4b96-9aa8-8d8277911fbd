-- WeBot Database Migration: Create Payments Table
-- Version: 1.0
-- Date: 2025-01-07

-- Create payments table
CREATE TABLE IF NOT EXISTS payments (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    
    -- Payment details
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    description TEXT NULL,
    
    -- Payment method
    payment_method ENUM('stripe', 'paypal', 'crypto', 'bank_transfer', 'wallet') NOT NULL,
    payment_gateway VARCHAR(50) NULL,
    
    -- Transaction info
    transaction_id VARCHAR(255) UNIQUE NULL,
    gateway_transaction_id VARCHAR(255) NULL,
    gateway_payment_id VARCHAR(255) NULL,
    
    -- Status tracking
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded') DEFAULT 'pending',
    failure_reason TEXT NULL,
    
    -- Timestamps
    initiated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL,
    
    -- Gateway response
    gateway_response JSON NULL,
    gateway_webhook_data JSON NULL,
    
    -- Refund info
    refund_amount DECIMAL(10,2) DEFAULT 0.00,
    refund_reason TEXT NULL,
    refunded_at TIMESTAMP NULL,
    
    -- Metadata
    metadata JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_payment_method (payment_method),
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_gateway_transaction_id (gateway_transaction_id),
    INDEX idx_created_at (created_at),
    INDEX idx_completed_at (completed_at),
    
    -- Foreign key constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create payment_items table for detailed breakdown
CREATE TABLE IF NOT EXISTS payment_items (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    payment_id BIGINT UNSIGNED NOT NULL,
    
    -- Item details
    item_type ENUM('subscription', 'service', 'addon', 'credit') NOT NULL,
    item_name VARCHAR(255) NOT NULL,
    item_description TEXT NULL,
    
    -- Pricing
    quantity INT UNSIGNED DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    
    -- Service specific
    service_duration_days INT UNSIGNED NULL,
    service_data_limit BIGINT UNSIGNED NULL,
    
    -- Metadata
    metadata JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_payment_id (payment_id),
    INDEX idx_item_type (item_type),
    
    -- Foreign key constraints
    FOREIGN KEY (payment_id) REFERENCES payments(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create payment_logs table for audit trail
CREATE TABLE IF NOT EXISTS payment_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    payment_id BIGINT UNSIGNED NOT NULL,
    
    -- Log details
    event_type VARCHAR(50) NOT NULL,
    event_description TEXT NULL,
    
    -- Status change
    old_status VARCHAR(20) NULL,
    new_status VARCHAR(20) NULL,
    
    -- Additional data
    event_data JSON NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    
    -- Timestamp
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_payment_id (payment_id),
    INDEX idx_event_type (event_type),
    INDEX idx_created_at (created_at),
    
    -- Foreign key constraints
    FOREIGN KEY (payment_id) REFERENCES payments(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create triggers for payment status changes
DELIMITER $$
CREATE TRIGGER payments_status_log 
    AFTER UPDATE ON payments 
    FOR EACH ROW 
BEGIN
    IF OLD.status != NEW.status THEN
        INSERT INTO payment_logs (
            payment_id, 
            event_type, 
            event_description,
            old_status,
            new_status
        ) VALUES (
            NEW.id,
            'status_change',
            CONCAT('Payment status changed from ', OLD.status, ' to ', NEW.status),
            OLD.status,
            NEW.status
        );
    END IF;
END$$
DELIMITER ;

-- Create trigger for updating user balance on completed payments
DELIMITER $$
CREATE TRIGGER payments_update_balance 
    AFTER UPDATE ON payments 
    FOR EACH ROW 
BEGIN
    IF OLD.status != 'completed' AND NEW.status = 'completed' THEN
        UPDATE users 
        SET balance = balance + NEW.amount 
        WHERE id = NEW.user_id;
    END IF;
    
    IF OLD.status = 'completed' AND NEW.status = 'refunded' THEN
        UPDATE users 
        SET balance = balance - NEW.amount 
        WHERE id = NEW.user_id;
    END IF;
END$$
DELIMITER ;
