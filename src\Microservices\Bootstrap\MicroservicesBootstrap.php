<?php

declare(strict_types=1);

namespace WeBot\Microservices\Bootstrap;

use WeBot\Core\CacheManager;
use WeBot\Services\DatabaseService;
use WeBot\Utils\Logger;
use WeBot\Exceptions\WeBotException;
use WeBot\Microservices\Core\ServiceRegistry;
use WeBot\Microservices\Core\ApiGateway;
use WeBot\Microservices\Core\CircuitBreaker;
use WeBot\Microservices\Core\RateLimiter;
use WeBot\Microservices\Core\ServiceOrchestrator;
use WeBot\Microservices\Services\UserService;
use WeBot\Microservices\Services\PaymentService;
use WeBot\Microservices\Services\PanelService;
use WeBot\Repositories\UserRepository;
use WeBot\Repositories\PaymentRepository;
use WeBot\Repositories\PanelRepository;
use WeBot\Repositories\ServiceRepository;

/**
 * Microservices Bootstrap
 *
 * Initializes and configures the microservices architecture,
 * registers services, and sets up the API Gateway.
 *
 * @package WeBot\Microservices\Bootstrap
 * @version 2.0
 */
class MicroservicesBootstrap
{
    private CacheManager $cache;
    private DatabaseService $database;
    private Logger $logger;
    private array $config;
    private array $services = [];
    private ?ServiceRegistry $serviceRegistry = null;
    private ?ApiGateway $apiGateway = null;
    private ?ServiceOrchestrator $orchestrator = null;

    public function __construct(
        CacheManager $cache,
        DatabaseService $database,
        array $config = []
    ) {
        $this->cache = $cache;
        $this->database = $database;
        $this->logger = Logger::getInstance();
        $this->config = array_merge($this->getDefaultConfig(), $config);
    }

    /**
     * Initialize microservices architecture
     */
    public function initialize(): array
    {
        try {
            $this->logger->info("Initializing microservices architecture");

            // Step 1: Initialize core components
            $this->initializeCoreComponents();

            // Step 2: Initialize services
            $this->initializeServices();

            // Step 3: Register services
            $this->registerServices();

            // Step 4: Configure API Gateway routes
            $this->configureApiGateway();

            // Step 5: Start health monitoring
            $this->startHealthMonitoring();

            $this->logger->info("Microservices architecture initialized successfully");

            return [
                'success' => true,
                'message' => 'Microservices architecture initialized successfully',
                'services' => array_keys($this->services),
                'timestamp' => time()
            ];
        } catch (\Exception $e) {
            $this->logger->error("Failed to initialize microservices", [
                'error' => $e->getMessage()
            ]);

            throw new WeBotException("Microservices initialization failed: " . $e->getMessage());
        }
    }

    /**
     * Get service registry
     */
    public function getServiceRegistry(): ServiceRegistry
    {
        if (!$this->serviceRegistry) {
            throw new WeBotException("Service registry not initialized");
        }

        return $this->serviceRegistry;
    }

    /**
     * Get API Gateway
     */
    public function getApiGateway(): ApiGateway
    {
        if (!$this->apiGateway) {
            throw new WeBotException("API Gateway not initialized");
        }

        return $this->apiGateway;
    }

    /**
     * Get service orchestrator
     */
    public function getOrchestrator(): ServiceOrchestrator
    {
        if (!$this->orchestrator) {
            throw new WeBotException("Service orchestrator not initialized");
        }

        return $this->orchestrator;
    }

    /**
     * Get service instance
     */
    public function getService(string $serviceName)
    {
        if (!isset($this->services[$serviceName])) {
            throw new WeBotException("Service not found: {$serviceName}");
        }

        return $this->services[$serviceName];
    }

    /**
     * Handle microservice request
     */
    public function handleRequest(array $request): array
    {
        if (!$this->apiGateway) {
            throw new WeBotException("API Gateway not initialized");
        }

        return $this->apiGateway->handleRequest($request);
    }

    /**
     * Handle orchestrated request
     */
    public function handleOrchestratedRequest(array $request): array
    {
        if (!$this->orchestrator) {
            throw new WeBotException("Service orchestrator not initialized");
        }

        return $this->orchestrator->handleRequest($request);
    }

    /**
     * Get system status
     */
    public function getSystemStatus(): array
    {
        $status = [
            'overall_status' => 'healthy',
            'services' => [],
            'components' => [
                'service_registry' => $this->serviceRegistry ? 'healthy' : 'unhealthy',
                'api_gateway' => $this->apiGateway ? 'healthy' : 'unhealthy',
                'orchestrator' => $this->orchestrator ? 'healthy' : 'unhealthy'
            ],
            'timestamp' => time()
        ];

        // Check service health
        foreach ($this->services as $name => $service) {
            try {
                $healthResult = $service->handleRequest(['action' => 'health']);
                $status['services'][$name] = $healthResult['status'] ?? 'unknown';
            } catch (\Exception $e) {
                $status['services'][$name] = 'unhealthy';
                $status['overall_status'] = 'degraded';
            }
        }

        // Check if any component is unhealthy
        if (in_array('unhealthy', $status['components'])) {
            $status['overall_status'] = 'unhealthy';
        }

        return $status;
    }

    /**
     * Shutdown microservices
     */
    public function shutdown(): void
    {
        $this->logger->info("Shutting down microservices");

        // Deregister services
        foreach (array_keys($this->services) as $serviceName) {
            try {
                $this->serviceRegistry->deregisterService($serviceName, $serviceName . '_instance');
            } catch (\Exception $e) {
                $this->logger->warning("Failed to deregister service", [
                    'service' => $serviceName,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $this->logger->info("Microservices shutdown completed");
    }

    /**
     * Initialize core components
     */
    private function initializeCoreComponents(): void
    {
        // Initialize Service Registry
        $this->serviceRegistry = new ServiceRegistry(
            $this->cache,
            $this->config['service_registry']
        );

        // Initialize Circuit Breaker
        $circuitBreaker = new CircuitBreaker(
            $this->cache,
            $this->config['circuit_breaker']
        );

        // Initialize Rate Limiter
        $rateLimiter = new RateLimiter(
            $this->cache,
            $this->config['rate_limiter']
        );

        // Initialize API Gateway
        $this->apiGateway = new ApiGateway(
            $this->serviceRegistry,
            $circuitBreaker,
            $rateLimiter,
            $this->cache,
            $this->config['api_gateway']
        );

        // Initialize Service Orchestrator
        $this->orchestrator = new ServiceOrchestrator(
            $this->serviceRegistry,
            $this->apiGateway,
            $this->cache,
            $this->config['orchestrator']
        );

        $this->logger->info("Core components initialized");
    }

    /**
     * Initialize services
     */
    private function initializeServices(): void
    {
        // Create Database adapter from DatabaseService
        $databaseAdapter = $this->createDatabaseAdapter();

        // Initialize repositories
        $userRepository = new UserRepository($databaseAdapter);
        $paymentRepository = new PaymentRepository($databaseAdapter);
        $panelRepository = new PanelRepository($databaseAdapter);
        $serviceRepository = new ServiceRepository($databaseAdapter);

        // Initialize User Service
        $this->services['user-service'] = new UserService(
            $this->database,
            $this->cache,
            $userRepository,
            $this->config['services']['user']
        );

        // Initialize Payment Service
        $this->services['payment-service'] = new PaymentService(
            $this->database,
            $this->cache,
            $paymentRepository,
            $this->config['services']['payment']
        );

        // Initialize Panel Service
        $this->services['panel-service'] = new PanelService(
            $this->database,
            $this->cache,
            $panelRepository,
            $serviceRepository,
            $this->config['services']['panel']
        );

        $this->logger->info("Services initialized", [
            'services' => array_keys($this->services)
        ]);
    }

    /**
     * Register services with service registry
     */
    private function registerServices(): void
    {
        $basePort = $this->config['base_port'];

        foreach ($this->services as $serviceName => $service) {
            $serviceConfig = [
                'id' => $serviceName . '_instance',
                'host' => $this->config['host'],
                'port' => $basePort++,
                'protocol' => 'http',
                'version' => '1.0.0',
                'health_check_url' => '/health',
                'metadata' => [
                    'service_type' => $serviceName,
                    'started_at' => time()
                ],
                'tags' => ['microservice', 'webot'],
                'weight' => 100
            ];

            $this->serviceRegistry->registerService($serviceName, $serviceConfig);

            $this->logger->info("Service registered", [
                'service' => $serviceName,
                'config' => $serviceConfig
            ]);
        }
    }

    /**
     * Configure API Gateway routes
     */
    private function configureApiGateway(): void
    {
        // User service routes
        $this->apiGateway->registerRoute('/api/users/register', 'user-service', [
            'auth_required' => false,
            'rate_limit' => ['requests' => 10, 'window' => 3600]
        ]);

        $this->apiGateway->registerRoute('/api/users/authenticate', 'user-service', [
            'auth_required' => false,
            'rate_limit' => ['requests' => 50, 'window' => 3600]
        ]);

        $this->apiGateway->registerRoute('/api/users/{id}', 'user-service');
        $this->apiGateway->registerRoute('/api/users/{id}/stats', 'user-service');

        // Payment service routes
        $this->apiGateway->registerRoute('/api/payments/create', 'payment-service');
        $this->apiGateway->registerRoute('/api/payments/{id}', 'payment-service');
        $this->apiGateway->registerRoute('/api/payments/{id}/verify', 'payment-service');
        $this->apiGateway->registerRoute('/api/wallet/balance', 'payment-service');
        $this->apiGateway->registerRoute('/api/wallet/transactions', 'payment-service');

        // Panel service routes
        $this->apiGateway->registerRoute('/api/panels', 'panel-service');
        $this->apiGateway->registerRoute('/api/panels/{id}', 'panel-service');
        $this->apiGateway->registerRoute('/api/panels/{id}/test', 'panel-service');
        $this->apiGateway->registerRoute('/api/services/create', 'panel-service');
        $this->apiGateway->registerRoute('/api/services/{id}', 'panel-service');
        $this->apiGateway->registerRoute('/api/services/{id}/config', 'panel-service');
        $this->apiGateway->registerRoute('/api/services/{id}/usage', 'panel-service');

        // Orchestrated operations
        $this->apiGateway->registerRoute('/api/orchestrator/user-with-service', 'orchestrator');
        $this->apiGateway->registerRoute('/api/orchestrator/payment-and-service', 'orchestrator');
        $this->apiGateway->registerRoute('/api/orchestrator/dashboard/user', 'orchestrator');
        $this->apiGateway->registerRoute('/api/orchestrator/dashboard/admin', 'orchestrator');

        $this->logger->info("API Gateway routes configured");
    }

    /**
     * Start health monitoring
     */
    private function startHealthMonitoring(): void
    {
        // In a real implementation, this would start background health checks
        // For now, we'll just log that monitoring is started
        $this->logger->info("Health monitoring started");
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'host' => 'localhost',
            'base_port' => 8080,
            'service_registry' => [
                'health_check_timeout' => 5,
                'health_check_interval' => 30,
                'max_failures' => 3
            ],
            'circuit_breaker' => [
                'failure_threshold' => 5,
                'timeout' => 60,
                'half_open_max_requests' => 3
            ],
            'rate_limiter' => [
                'default_limits' => [
                    60 => 100,      // 100 requests per minute
                    3600 => 1000    // 1000 requests per hour
                ]
            ],
            'api_gateway' => [
                'default_timeout' => 30,
                'enable_cors' => true,
                'enable_compression' => true
            ],
            'orchestrator' => [
                'dashboard_cache_ttl' => 300,
                'transaction_timeout' => 300
            ],
            'services' => [
                'user' => [
                    'user_cache_ttl' => 3600,
                    'stats_cache_ttl' => 1800
                ],
                'payment' => [
                    'stats_cache_ttl' => 1800,
                    'balance_cache_ttl' => 300
                ],
                'panel' => [
                    'panel_cache_ttl' => 3600,
                    'stats_cache_ttl' => 1800
                ]
            ]
        ];
    }

    /**
     * Create Database adapter from DatabaseService
     */
    private function createDatabaseAdapter(): \WeBot\Core\Database
    {
        // Get database configuration from DatabaseService
        $config = [
            'host' => 'localhost',
            'database' => 'webot',
            'username' => 'root',
            'password' => '',
            'charset' => 'utf8mb4'
        ];

        return new \WeBot\Core\Database($config);
    }
}
