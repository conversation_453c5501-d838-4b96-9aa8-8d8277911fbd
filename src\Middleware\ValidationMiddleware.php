<?php

declare(strict_types=1);

namespace WeBot\Middleware;

/**
 * Validation Middleware
 *
 * Handles request validation and sanitization
 *
 * @package WeBot\Middleware
 * @version 2.0
 */
class ValidationMiddleware
{
    public function handle(array $request, callable $next): array
    {
        // Validate and sanitize request
        $validatedRequest = $this->validateRequest($request);

        if (!$validatedRequest) {
            return [
                'method' => 'sendMessage',
                'chat_id' => $request['chat']['id'] ?? $request['from']['id'] ?? 0,
                'text' => '❌ درخواست نامعتبر است.'
            ];
        }

        return $next($request);
    }

    private function validateRequest(array $request): bool
    {
        // Basic validation
        if (empty($request)) {
            return false;
        }

        // Check required fields
        if (!isset($request['from']['id'])) {
            return false;
        }

        // Validate message content if present
        if (isset($request['text'])) {
            $text = $request['text'];

            // Check for malicious content
            if ($this->containsMaliciousContent($text)) {
                return false;
            }

            // Check length limits
            if (strlen($text) > 4096) {
                return false;
            }
        }

        return true;
    }

    private function containsMaliciousContent(string $text): bool
    {
        $maliciousPatterns = [
            '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi',
            '/javascript:/i',
            '/on\w+\s*=/i'
        ];

        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $text)) {
                return true;
            }
        }

        return false;
    }
}
