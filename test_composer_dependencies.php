<?php
/**
 * Composer Dependencies Checker for WeBot
 * 
 * This script verifies that all required Composer dependencies
 * are properly installed and can be loaded.
 */

declare(strict_types=1);

echo "=== WeBot Composer Dependencies Check ===\n\n";

// Check if vendor/autoload.php exists
echo "1. Autoloader Check:\n";
$autoloaderPath = __DIR__ . '/vendor/autoload.php';
if (file_exists($autoloaderPath)) {
    echo "   ✅ vendor/autoload.php exists\n";
    require_once $autoloaderPath;
    echo "   ✅ Autoloader loaded successfully\n";
} else {
    echo "   ❌ vendor/autoload.php not found\n";
    echo "   🔧 Run: php composer.phar install\n";
    exit(1);
}

echo "\n2. Core Dependencies Check:\n";

// Check critical dependencies
$dependencies = [
    'Dotenv\Dotenv' => 'Environment configuration',
    'Monolog\Logger' => 'Logging system',
    'GuzzleHttp\Client' => 'HTTP client',
    'Symfony\Component\Validator\Validator' => 'Input validation',
    'League\Container\Container' => 'Dependency injection',
    'Ramsey\Uuid\Uuid' => 'UUID generation',
    'Firebase\JWT\JWT' => 'JWT token handling',
    'Endroid\QrCode\QrCode' => 'QR code generation'
];

$allDependenciesOk = true;
foreach ($dependencies as $class => $description) {
    if (class_exists($class)) {
        echo "   ✅ {$class} - {$description}\n";
    } else {
        echo "   ❌ {$class} - {$description}\n";
        $allDependenciesOk = false;
    }
}

echo "\n3. Development Dependencies Check:\n";

$devDependencies = [
    'PHPUnit\Framework\TestCase' => 'Unit testing framework',
    'PHPStan\Analyser\Analyser' => 'Static analysis tool',
    'PHP_CodeSniffer\Config' => 'Code style checker'
];

foreach ($devDependencies as $class => $description) {
    if (class_exists($class)) {
        echo "   ✅ {$class} - {$description}\n";
    } else {
        echo "   ⚠️  {$class} - {$description} (dev dependency)\n";
    }
}

echo "\n4. Composer Configuration Check:\n";

// Check composer.json
if (file_exists('composer.json')) {
    echo "   ✅ composer.json exists\n";
    $composerData = json_decode(file_get_contents('composer.json'), true);
    
    if (isset($composerData['autoload']['psr-4']['WeBot\\'])) {
        echo "   ✅ PSR-4 autoloading configured for WeBot namespace\n";
    } else {
        echo "   ❌ PSR-4 autoloading not configured properly\n";
        $allDependenciesOk = false;
    }
} else {
    echo "   ❌ composer.json not found\n";
    $allDependenciesOk = false;
}

// Check composer.lock
if (file_exists('composer.lock')) {
    echo "   ✅ composer.lock exists (dependencies locked)\n";
} else {
    echo "   ⚠️  composer.lock not found (dependencies not locked)\n";
}

echo "\n=== Overall Status ===\n";
if ($allDependenciesOk) {
    echo "✅ All dependencies are properly installed and configured!\n";
    exit(0);
} else {
    echo "❌ Some dependencies are missing or misconfigured.\n";
    echo "\n🔧 To fix dependency issues:\n";
    echo "   1. Run: php composer.phar install\n";
    echo "   2. If Redis extension is required, install it\n";
    echo "   3. Check composer.json for correct configuration\n";
    exit(1);
}
