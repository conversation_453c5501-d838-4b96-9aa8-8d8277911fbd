<?php

declare(strict_types=1);

namespace WeBot\Controllers;

use WeBot\Core\DIContainer;
use WeBot\Core\Config;
use WeBot\Core\Database;
use WeBot\Utils\Logger;
use Monolog\Logger as MonologLogger;
use WeBot\Utils\Validator;

/**
 * Base Controller
 *
 * Base class for all WeBot controllers providing common functionality
 * and dependency injection.
 *
 * @package WeBot\Controllers
 * @version 2.0
 */
abstract class BaseController
{
    protected DIContainer $container;
    protected Config $config;
    protected Database $db;
    protected MonologLogger $logger;

    protected array $update = [];
    protected array $message = [];
    protected array $callbackQuery = [];
    protected int $fromId = 0;
    protected int $chatId = 0;
    protected int $messageId = 0;
    protected string $text = '';
    protected string $data = '';
    protected ?string $username = null;
    protected ?string $firstName = null;
    protected ?string $lastName = null;
    protected ?string $languageCode = null;

    public function __construct(DIContainer $container)
    {
        $this->container = $container;
        $this->config = $container->make(Config::class);
        $this->db = $container->make(Database::class);
        $this->logger = $container->make(Logger::class);
    }

    /**
     * Initialize controller with Telegram update data
     */
    protected function initialize(array $update): void
    {
        $this->update = $update;

        // Extract message data
        if (isset($update['message'])) {
            $this->message = $update['message'];
            $this->fromId = $this->message['from']['id'] ?? 0;
            $this->chatId = $this->message['chat']['id'] ?? 0;
            $this->messageId = $this->message['message_id'] ?? 0;
            $this->text = $this->message['text'] ?? '';
            $this->username = $this->message['from']['username'] ?? null;
            $this->firstName = $this->message['from']['first_name'] ?? null;
            $this->lastName = $this->message['from']['last_name'] ?? null;
            $this->languageCode = $this->message['from']['language_code'] ?? null;
        }

        // Extract callback query data
        if (isset($update['callback_query'])) {
            $this->callbackQuery = $update['callback_query'];
            $this->fromId = $this->callbackQuery['from']['id'] ?? 0;
            $this->chatId = $this->callbackQuery['message']['chat']['id'] ?? 0;
            $this->messageId = $this->callbackQuery['message']['message_id'] ?? 0;
            $this->data = $this->callbackQuery['data'] ?? '';
            $this->username = $this->callbackQuery['from']['username'] ?? null;
            $this->firstName = $this->callbackQuery['from']['first_name'] ?? null;
            $this->lastName = $this->callbackQuery['from']['last_name'] ?? null;
            $this->languageCode = $this->callbackQuery['from']['language_code'] ?? null;
        }

        $this->logger->info('Controller initialized', [
            'controller' => static::class,
            'from_id' => $this->fromId,
            'chat_id' => $this->chatId,
            'text' => $this->text,
            'data' => $this->data
        ]);
    }

    /**
     * Send message to user
     */
    protected function sendMessage(
        string $text,
        ?array $keyboard = null,
        string $parseMode = 'HTML',
        ?int $chatId = null
    ): array {
        $chatId ??= $this->chatId;

        return $this->container->get('telegram')->sendMessage([
            'chat_id' => $chatId,
            'text' => $text,
            'parse_mode' => $parseMode,
            'reply_markup' => $keyboard ? json_encode($keyboard) : null
        ]);
    }

    /**
     * Edit message text
     */
    protected function editMessage(
        string $text,
        ?array $keyboard = null,
        string $parseMode = 'HTML',
        ?int $messageId = null,
        ?int $chatId = null
    ): array {
        $messageId ??= $this->messageId;
        $chatId ??= $this->chatId;

        return $this->container->get('telegram')->editMessageText([
            'chat_id' => $chatId,
            'message_id' => $messageId,
            'text' => $text,
            'parse_mode' => $parseMode,
            'reply_markup' => $keyboard ? json_encode($keyboard) : null
        ]);
    }

    /**
     * Delete message
     */
    protected function deleteMessage(?int $messageId = null, ?int $chatId = null): array
    {
        $messageId ??= $this->messageId;
        $chatId ??= $this->chatId;

        return $this->container->get('telegram')->deleteMessage([
            'chat_id' => $chatId,
            'message_id' => $messageId
        ]);
    }

    /**
     * Send photo
     */
    protected function sendPhoto(
        string $photo,
        string $caption = '',
        ?array $keyboard = null,
        string $parseMode = 'HTML',
        ?int $chatId = null
    ): array {
        $chatId ??= $this->chatId;

        return $this->container->get('telegram')->sendPhoto([
            'chat_id' => $chatId,
            'photo' => $photo,
            'caption' => $caption,
            'parse_mode' => $parseMode,
            'reply_markup' => $keyboard ? json_encode($keyboard) : null
        ]);
    }

    /**
     * Answer callback query
     */
    protected function answerCallback(
        string $text = '',
        bool $showAlert = false,
        ?string $callbackQueryId = null
    ): array {
        $callbackQueryId ??= $this->callbackQuery['id'] ?? '';

        return $this->container->get('telegram')->answerCallbackQuery([
            'callback_query_id' => $callbackQueryId,
            'text' => $text,
            'show_alert' => $showAlert
        ]);
    }

    /**
     * Get user information
     */
    protected function getUserInfo(?int $userId = null): ?array
    {
        $userId ??= $this->fromId;

        $stmt = $this->db->prepare("SELECT * FROM `users` WHERE `userid` = ?");
        $stmt->bind_param("i", $userId);
        $stmt->execute();
        $result = $stmt->get_result()->fetch_assoc();
        $stmt->close();

        return $result ?: null;
    }

    /**
     * Update user step
     */
    protected function setUserStep(string $step, ?int $userId = null): bool
    {
        $userId ??= $this->fromId;

        $stmt = $this->db->prepare("UPDATE `users` SET `step` = ? WHERE `userid` = ?");
        $stmt->bind_param("si", $step, $userId);
        $success = $stmt->execute();
        $stmt->close();

        return $success;
    }

    /**
     * Update user temp data
     */
    protected function setUserTemp(string $temp, ?int $userId = null): bool
    {
        $userId ??= $this->fromId;

        $stmt = $this->db->prepare("UPDATE `users` SET `temp` = ? WHERE `userid` = ?");
        $stmt->bind_param("si", $temp, $userId);
        $success = $stmt->execute();
        $stmt->close();

        return $success;
    }

    /**
     * Check if user is admin
     */
    protected function isAdmin(?int $userId = null): bool
    {
        $userId ??= $this->fromId;
        $adminId = $this->config->get('telegram.admin_id');

        if ($userId == $adminId) {
            return true;
        }

        $userInfo = $this->getUserInfo($userId);
        return $userInfo && ($userInfo['isAdmin'] ?? false);
    }

    /**
     * Validate input data
     */
    protected function validate(array $data, array $rules, array $messages = []): Validator
    {
        return Validator::make($data, $rules, $messages);
    }

    /**
     * Get bot settings
     */
    protected function getBotSettings(): array
    {
        $stmt = $this->db->prepare("SELECT * FROM `setting`");
        $stmt->execute();
        $results = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
        $stmt->close();

        $settings = [];
        foreach ($results as $setting) {
            $settings[$setting['type']] = $setting['value'];
        }

        return $settings;
    }

    /**
     * Get text values
     */
    protected function getTextValues(): array
    {
        // This would load from text.php or database
        // For now, return basic values
        return [
            'welcome_message' => 'به WeBot خوش آمدید',
            'main_menu' => 'منوی اصلی',
            'back_button' => '🔙 بازگشت',
            'cancel' => '❌ لغو',
            'saved_successfully' => '✅ با موفقیت ذخیره شد'
        ];
    }

    /**
     * Create inline keyboard
     */
    protected function createInlineKeyboard(array $buttons): array
    {
        return ['inline_keyboard' => $buttons];
    }

    /**
     * Create reply keyboard
     */
    protected function createReplyKeyboard(array $buttons, bool $resize = true, bool $oneTime = false): array
    {
        return [
            'keyboard' => $buttons,
            'resize_keyboard' => $resize,
            'one_time_keyboard' => $oneTime
        ];
    }

    /**
     * Remove keyboard
     */
    protected function removeKeyboard(): array
    {
        return ['remove_keyboard' => true];
    }

    /**
     * Log controller action
     */
    protected function logAction(string $action, array $context = []): void
    {
        $this->logger->info("Controller action: {$action}", array_merge([
            'controller' => static::class,
            'user_id' => $this->fromId,
            'chat_id' => $this->chatId
        ], $context));
    }

    /**
     * Handle errors gracefully
     */
    protected function handleError(\Throwable $e, string $userMessage = 'خطایی رخ داده است. لطفاً مجدداً تلاش کنید.'): array
    {
        $this->logger->error('Controller error', [
            'controller' => static::class,
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'user_id' => $this->fromId
        ]);

        return $this->sendMessage($userMessage);
    }
}
