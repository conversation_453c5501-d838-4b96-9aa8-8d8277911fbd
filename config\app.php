<?php
/**
 * WeBot Application Configuration
 * 
 * This file contains the core application configuration settings.
 * All values are loaded from environment variables for security.
 * 
 * @package WeBot
 * @version 2.0
 */

declare(strict_types=1);

return [
    
    /*
    |--------------------------------------------------------------------------
    | Application Name
    |--------------------------------------------------------------------------
    |
    | This value is the name of your application. This value is used when the
    | framework needs to place the application's name in a notification or
    | any other location as required by the application or its packages.
    |
    */
    
    'name' => env('APP_NAME', 'WeBot'),
    
    /*
    |--------------------------------------------------------------------------
    | Application Environment
    |--------------------------------------------------------------------------
    |
    | This value determines the "environment" your application is currently
    | running in. This may determine how you prefer to configure various
    | services the application utilizes.
    |
    */
    
    'env' => env('APP_ENV', 'production'),
    
    /*
    |--------------------------------------------------------------------------
    | Application Debug Mode
    |--------------------------------------------------------------------------
    |
    | When your application is in debug mode, detailed error messages with
    | stack traces will be shown on every error that occurs within your
    | application. If disabled, a simple generic error page is shown.
    |
    */
    
    'debug' => env('APP_DEBUG', false),
    
    /*
    |--------------------------------------------------------------------------
    | Application URL
    |--------------------------------------------------------------------------
    |
    | This URL is used by the console to properly generate URLs when using
    | the Artisan command line tool. You should set this to the root of
    | your application so that it is used when running Artisan tasks.
    |
    */
    
    'url' => env('APP_URL', 'https://localhost'),
    
    /*
    |--------------------------------------------------------------------------
    | Application Timezone
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default timezone for your application, which
    | will be used by the PHP date and date-time functions. We have gone
    | ahead and set this to a sensible default for you out of the box.
    |
    */
    
    'timezone' => env('APP_TIMEZONE', 'Asia/Tehran'),
    
    /*
    |--------------------------------------------------------------------------
    | Application Locale Configuration
    |--------------------------------------------------------------------------
    |
    | The application locale determines the default locale that will be used
    | by the translation service provider. You are free to set this value
    | to any of the locales which will be supported by the application.
    |
    */
    
    'locale' => env('APP_LOCALE', 'fa'),
    
    /*
    |--------------------------------------------------------------------------
    | Application Fallback Locale
    |--------------------------------------------------------------------------
    |
    | The fallback locale determines the locale to use when the current one
    | is not available. You may change the value to correspond to any of
    | the language folders that are provided through your application.
    |
    */
    
    'fallback_locale' => env('APP_FALLBACK_LOCALE', 'en'),
    
    /*
    |--------------------------------------------------------------------------
    | Application Version
    |--------------------------------------------------------------------------
    |
    | This value represents the version of your application. This value is
    | used when the framework needs to place the application's version in
    | a notification or any other location as required by the application.
    |
    */
    
    'version' => env('APP_VERSION', '2.0.0'),
    
    /*
    |--------------------------------------------------------------------------
    | Encryption Key
    |--------------------------------------------------------------------------
    |
    | This key is used by the encryption service and should be set
    | to a random, 32 character string, otherwise these encrypted strings
    | will not be safe. Please do this before deploying an application!
    |
    */
    
    'key' => env('APP_KEY', ''),
    
    /*
    |--------------------------------------------------------------------------
    | Autoloaded Service Providers
    |--------------------------------------------------------------------------
    |
    | The service providers listed here will be automatically loaded on the
    | request to your application. Feel free to add your own services to
    | this array to grant expanded functionality to your applications.
    |
    */
    
    'providers' => [
        // Core Service Providers (will be implemented in future tasks)
        // WeBot\Core\Providers\AppServiceProvider::class,
        // WeBot\Core\Providers\DatabaseServiceProvider::class,
        // WeBot\Core\Providers\CacheServiceProvider::class,
        // WeBot\Core\Providers\LogServiceProvider::class,

        // Application Service Providers (will be implemented in future tasks)
        // WeBot\Services\TelegramServiceProvider::class,
        // WeBot\Services\PaymentServiceProvider::class,
        // WeBot\Services\PanelServiceProvider::class,
        // WeBot\Services\MessageServiceProvider::class,

        // Third Party Service Providers
        // Add third party providers here
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Class Aliases
    |--------------------------------------------------------------------------
    |
    | This array of class aliases will be registered when this application
    | is started. However, feel free to register as many as you wish as
    | the aliases are "lazy" loaded so they don't hinder performance.
    |
    */
    
    'aliases' => [
        'App' => WeBot\Core\Application::class,
        'Config' => WeBot\Core\Config::class,
        'DB' => WeBot\Services\DatabaseService::class,
        // 'Cache' => WeBot\Core\Cache::class, // Will be implemented in future tasks
        // 'Log' => WeBot\Core\Logger::class, // Will be implemented in future tasks
        'Telegram' => WeBot\Services\TelegramService::class,
        'Payment' => WeBot\Services\PaymentService::class,
        'Panel' => WeBot\Services\PanelService::class,
        'Message' => WeBot\Services\MessageService::class,
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Application Features
    |--------------------------------------------------------------------------
    |
    | Here you can enable or disable various features of the application.
    | These settings can be overridden by environment variables.
    |
    */
    
    'features' => [
        'web_panel' => env('FEATURE_WEB_PANEL', true),
        'advanced_tickets' => env('FEATURE_ADVANCED_TICKETS', true),
        'multi_admin' => env('FEATURE_MULTI_ADMIN', true),
        'subscription_link' => env('FEATURE_SUBSCRIPTION_LINK', true),
        'qr_code' => env('FEATURE_QR_CODE', true),
        'referral_system' => env('FEATURE_REFERRAL_SYSTEM', true),
        'payment_enabled' => env('FEATURE_PAYMENT_ENABLED', true),
        'analytics_enabled' => env('FEATURE_ANALYTICS_ENABLED', true),
        'maintenance_mode' => env('FEATURE_MAINTENANCE_MODE', false),
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    |
    | Security related configuration options for the application.
    |
    */
    
    'security' => [
        'rate_limit_enabled' => env('RATE_LIMIT_ENABLED', true),
        'rate_limit_max_attempts' => env('RATE_LIMIT_MAX_ATTEMPTS', 10),
        'rate_limit_time_window' => env('RATE_LIMIT_TIME_WINDOW', 60),
        'spam_protection' => env('SPAM_PROTECTION', true),
        'spam_max_messages' => env('SPAM_MAX_MESSAGES', 5),
        'spam_time_window' => env('SPAM_TIME_WINDOW', 30),
        'csrf_protection' => env('CSRF_PROTECTION', true),
        'ssl_enabled' => env('SSL_ENABLED', true),
        'ssl_redirect' => env('SSL_REDIRECT', true),
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Performance Configuration
    |--------------------------------------------------------------------------
    |
    | Performance related settings for optimization.
    |
    */
    
    'performance' => [
        'cache_ttl' => env('CACHE_TTL', 3600),
        'session_timeout' => env('SESSION_TIMEOUT', 1800),
        'max_upload_size' => env('MAX_UPLOAD_SIZE', '10M'),
        'memory_limit' => env('MEMORY_LIMIT', '256M'),
        'opcache_enabled' => env('OPCACHE_ENABLED', true),
        'gzip_enabled' => env('GZIP_ENABLED', true),
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Monitoring Configuration
    |--------------------------------------------------------------------------
    |
    | Monitoring and analytics configuration.
    |
    */
    
    'monitoring' => [
        'enabled' => env('MONITORING_ENABLED', true),
        'health_check_token' => env('HEALTH_CHECK_TOKEN', ''),
        'metrics_enabled' => env('METRICS_ENABLED', true),
        'prometheus_enabled' => env('PROMETHEUS_ENABLED', false),
        'sentry_dsn' => env('SENTRY_DSN', ''),
        'google_analytics_id' => env('GOOGLE_ANALYTICS_ID', ''),
    ],
    
];
