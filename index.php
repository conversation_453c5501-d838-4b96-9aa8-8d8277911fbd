<?php
/**
 * WeBot Entry Point
 * 
 * This is the main entry point for the WeBot application.
 * It handles Telegram webhook requests and routes them to
 * the appropriate controllers.
 * 
 * @package WeBot
 * @version 2.0
 * <AUTHOR> Team
 */

declare(strict_types=1);

// Load the application
require_once __DIR__ . '/autoload.php';

try {
    // Initialize the application
    $app = new WeBot\Core\Application();
    
    // Handle the incoming request
    $app->run();
    
} catch (WeBot\Exceptions\WeBotException $e) {
    // Handle WeBot specific exceptions
    logger()->error('WeBot Exception: ' . $e->getMessage(), [
        'code' => $e->getCode(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);
    
    if (env('APP_DEBUG', false)) {
        echo json_encode([
            'error' => 'WeBot Exception',
            'message' => $e->getMessage(),
            'code' => $e->getCode()
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Internal server error']);
    }
    
} catch (Exception $e) {
    // Handle general exceptions
    logger()->critical('Unhandled Exception: ' . $e->getMessage(), [
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);
    
    if (env('APP_DEBUG', false)) {
        echo json_encode([
            'error' => 'Unhandled Exception',
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Internal server error']);
    }
    
} catch (Throwable $e) {
    // Handle fatal errors
    logger()->emergency('Fatal Error: ' . $e->getMessage(), [
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);
    
    http_response_code(500);
    echo json_encode(['error' => 'Fatal error occurred']);
}
