<?php

declare(strict_types=1);

/**
 * Logging Configuration
 * 
 * @package WeBot\Config
 * @version 2.0
 */

return [
    /*
    |--------------------------------------------------------------------------
    | Default Log Channel
    |--------------------------------------------------------------------------
    */
    'default' => $_ENV['LOG_CHANNEL'] ?? 'stack',

    /*
    |--------------------------------------------------------------------------
    | Deprecations Log Channel
    |--------------------------------------------------------------------------
    */
    'deprecations' => $_ENV['LOG_DEPRECATIONS_CHANNEL'] ?? 'null',

    /*
    |--------------------------------------------------------------------------
    | Log Channels
    |--------------------------------------------------------------------------
    */
    'channels' => [
        'stack' => [
            'driver' => 'stack',
            'channels' => ['single'],
            'ignore_exceptions' => false,
        ],

        'single' => [
            'driver' => 'single',
            'path' => storage_path('logs/webot.log'),
            'level' => $_ENV['LOG_LEVEL'] ?? 'debug',
        ],

        'daily' => [
            'driver' => 'daily',
            'path' => storage_path('logs/webot.log'),
            'level' => $_ENV['LOG_LEVEL'] ?? 'debug',
            'days' => 14,
        ],

        'slack' => [
            'driver' => 'slack',
            'url' => $_ENV['LOG_SLACK_WEBHOOK_URL'] ?? null,
            'username' => 'WeBot',
            'emoji' => ':boom:',
            'level' => $_ENV['LOG_LEVEL'] ?? 'critical',
        ],

        'papertrail' => [
            'driver' => 'monolog',
            'level' => $_ENV['LOG_LEVEL'] ?? 'debug',
            'handler' => 'Monolog\Handler\SyslogUdpHandler',
            'handler_with' => [
                'host' => $_ENV['PAPERTRAIL_URL'] ?? null,
                'port' => $_ENV['PAPERTRAIL_PORT'] ?? null,
                'connectionString' => 'tls://' . $_ENV['PAPERTRAIL_URL'] . ':' . $_ENV['PAPERTRAIL_PORT'],
            ],
        ],

        'stderr' => [
            'driver' => 'monolog',
            'level' => $_ENV['LOG_LEVEL'] ?? 'debug',
            'handler' => 'Monolog\Handler\StreamHandler',
            'formatter' => $_ENV['LOG_STDERR_FORMATTER'] ?? null,
            'with' => [
                'stream' => 'php://stderr',
            ],
        ],

        'syslog' => [
            'driver' => 'syslog',
            'level' => $_ENV['LOG_LEVEL'] ?? 'debug',
        ],

        'errorlog' => [
            'driver' => 'errorlog',
            'level' => $_ENV['LOG_LEVEL'] ?? 'debug',
        ],

        'null' => [
            'driver' => 'monolog',
            'handler' => 'Monolog\Handler\NullHandler',
        ],

        'emergency' => [
            'path' => storage_path('logs/webot.log'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Log Levels
    |--------------------------------------------------------------------------
    */
    'levels' => [
        'emergency' => 0,
        'alert' => 1,
        'critical' => 2,
        'error' => 3,
        'warning' => 4,
        'notice' => 5,
        'info' => 6,
        'debug' => 7,
    ],
];
