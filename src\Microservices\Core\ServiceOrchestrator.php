<?php

declare(strict_types=1);

namespace WeBot\Microservices\Core;

use WeBot\Core\CacheManager;
use WeBot\Utils\Logger;
use WeBot\Exceptions\WeBotException;
use WeBot\Microservices\Core\ServiceRegistry;
use WeBot\Microservices\Core\ApiGateway;
use WeBot\Microservices\Services\UserService;
use WeBot\Microservices\Services\PaymentService;
use WeBot\Microservices\Services\PanelService;

/**
 * Service Orchestrator
 *
 * Orchestrates complex operations across multiple microservices,
 * handles distributed transactions, and manages service dependencies.
 *
 * @package WeBot\Microservices\Core
 * @version 2.0
 */
class ServiceOrchestrator
{
    private ServiceRegistry $serviceRegistry;
    private ApiGateway $apiGateway;
    private CacheManager $cache;
    private Logger $logger;
    private array $config;
    private array $activeTransactions = [];

    public function __construct(
        ServiceRegistry $serviceRegistry,
        ApiGateway $apiGateway,
        CacheManager $cache,
        array $config = []
    ) {
        $this->serviceRegistry = $serviceRegistry;
        $this->apiGateway = $apiGateway;
        $this->cache = $cache;
        $this->logger = Logger::getInstance();
        $this->config = array_merge($this->getDefaultConfig(), $config);
    }

    /**
     * Handle orchestrated request
     */
    public function handleRequest(array $request): array
    {
        $operation = $request['operation'] ?? '';
        $data = $request['data'] ?? [];

        try {
            return match ($operation) {
                'create_user_with_service' => $this->createUserWithService($data),
                'process_payment_and_create_service' => $this->processPaymentAndCreateService($data),
                'renew_service_with_payment' => $this->renewServiceWithPayment($data),
                'refund_and_suspend_service' => $this->refundAndSuspendService($data),
                'migrate_user_services' => $this->migrateUserServices($data),
                'bulk_service_operations' => $this->bulkServiceOperations($data),
                'get_user_dashboard' => $this->getUserDashboard($data),
                'get_admin_dashboard' => $this->getAdminDashboard($data),
                'health_check_all' => $this->healthCheckAll(),
                default => throw new WeBotException("Unknown operation: {$operation}")
            };
        } catch (\Exception $e) {
            $this->logger->error("Orchestrator error", [
                'operation' => $operation,
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            throw $e;
        }
    }

    /**
     * Create user with initial service
     */
    public function createUserWithService(array $data): array
    {
        $transactionId = $this->startTransaction('create_user_with_service');

        try {
            // Step 1: Create user
            $userResult = $this->callService('user-service', 'register', $data['user_data']);

            if (!$userResult['success']) {
                throw new WeBotException("Failed to create user: " . ($userResult['error']['message'] ?? 'Unknown error'));
            }

            $userId = $userResult['user']['userid'];

            // Step 2: Create service if requested
            if (isset($data['service_data'])) {
                $serviceData = array_merge($data['service_data'], ['user_id' => $userId]);
                $serviceResult = $this->callService('panel-service', 'create_service', $serviceData);

                if (!$serviceResult['success']) {
                    // Rollback user creation
                    $this->callService('user-service', 'delete_user', ['user_id' => $userId]);
                    throw new WeBotException("Failed to create service: " . ($serviceResult['error']['message'] ?? 'Unknown error'));
                }

                $this->commitTransaction($transactionId);

                return [
                    'success' => true,
                    'user' => $userResult['user'],
                    'service' => $serviceResult['service'],
                    'transaction_id' => $transactionId,
                    'message' => 'User and service created successfully'
                ];
            }

            $this->commitTransaction($transactionId);

            return [
                'success' => true,
                'user' => $userResult['user'],
                'transaction_id' => $transactionId,
                'message' => 'User created successfully'
            ];
        } catch (\Exception $e) {
            $this->rollbackTransaction($transactionId);
            throw $e;
        }
    }

    /**
     * Process payment and create service
     */
    public function processPaymentAndCreateService(array $data): array
    {
        $transactionId = $this->startTransaction('process_payment_and_create_service');

        try {
            // Step 1: Create payment
            $paymentResult = $this->callService('payment-service', 'create_payment', $data['payment_data']);

            if (!$paymentResult['success']) {
                throw new WeBotException("Failed to create payment: " . ($paymentResult['error']['message'] ?? 'Unknown error'));
            }

            $paymentId = $paymentResult['payment']['id'];

            // Step 2: Process payment
            $processResult = $this->callService('payment-service', 'process_payment', ['payment_id' => $paymentId]);

            if (!$processResult['success'] || $processResult['status'] !== 'completed') {
                throw new WeBotException("Payment processing failed");
            }

            // Step 3: Create service
            $serviceResult = $this->callService('panel-service', 'create_service', $data['service_data']);

            if (!$serviceResult['success']) {
                // Rollback payment
                $this->callService('payment-service', 'refund_payment', [
                    'payment_id' => $paymentId,
                    'reason' => 'Service creation failed'
                ]);
                throw new WeBotException("Failed to create service: " . ($serviceResult['error']['message'] ?? 'Unknown error'));
            }

            $this->commitTransaction($transactionId);

            return [
                'success' => true,
                'payment' => $paymentResult['payment'],
                'service' => $serviceResult['service'],
                'transaction_id' => $transactionId,
                'message' => 'Payment processed and service created successfully'
            ];
        } catch (\Exception $e) {
            $this->rollbackTransaction($transactionId);
            throw $e;
        }
    }

    /**
     * Renew service with payment
     */
    public function renewServiceWithPayment(array $data): array
    {
        $transactionId = $this->startTransaction('renew_service_with_payment');

        try {
            // Step 1: Verify service exists
            $serviceResult = $this->callService('panel-service', 'get_service', ['service_id' => $data['service_id']]);

            if (!$serviceResult['success']) {
                throw new WeBotException("Service not found");
            }

            $service = $serviceResult['service'];

            // Step 2: Create renewal payment
            $paymentData = array_merge($data['payment_data'], [
                'user_id' => $service['user_id'],
                'description' => "Service renewal for {$service['remark']}"
            ]);

            $paymentResult = $this->callService('payment-service', 'create_payment', $paymentData);

            if (!$paymentResult['success']) {
                throw new WeBotException("Failed to create payment");
            }

            // Step 3: Process payment
            $processResult = $this->callService('payment-service', 'process_payment', [
                'payment_id' => $paymentResult['payment']['id']
            ]);

            if (!$processResult['success'] || $processResult['status'] !== 'completed') {
                throw new WeBotException("Payment processing failed");
            }

            // Step 4: Renew service
            $renewResult = $this->callService('panel-service', 'renew_service', [
                'service_id' => $data['service_id'],
                'days' => $data['renewal_days']
            ]);

            if (!$renewResult['success']) {
                // Rollback payment
                $this->callService('payment-service', 'refund_payment', [
                    'payment_id' => $paymentResult['payment']['id'],
                    'reason' => 'Service renewal failed'
                ]);
                throw new WeBotException("Failed to renew service");
            }

            $this->commitTransaction($transactionId);

            return [
                'success' => true,
                'payment' => $paymentResult['payment'],
                'service' => $renewResult['service'],
                'transaction_id' => $transactionId,
                'message' => 'Service renewed successfully'
            ];
        } catch (\Exception $e) {
            $this->rollbackTransaction($transactionId);
            throw $e;
        }
    }

    /**
     * Refund payment and suspend service
     */
    public function refundAndSuspendService(array $data): array
    {
        $transactionId = $this->startTransaction('refund_and_suspend_service');

        try {
            // Step 1: Suspend service
            $suspendResult = $this->callService('panel-service', 'suspend_service', ['service_id' => $data['service_id']]);

            if (!$suspendResult['success']) {
                throw new WeBotException("Failed to suspend service");
            }

            // Step 2: Process refund
            $refundResult = $this->callService('payment-service', 'refund_payment', [
                'payment_id' => $data['payment_id'],
                'reason' => $data['reason'] ?? 'Service suspended'
            ]);

            if (!$refundResult['success']) {
                // Rollback service suspension
                $this->callService('panel-service', 'activate_service', ['service_id' => $data['service_id']]);
                throw new WeBotException("Failed to process refund");
            }

            $this->commitTransaction($transactionId);

            return [
                'success' => true,
                'refund' => $refundResult,
                'service' => $suspendResult['service'],
                'transaction_id' => $transactionId,
                'message' => 'Service suspended and payment refunded successfully'
            ];
        } catch (\Exception $e) {
            $this->rollbackTransaction($transactionId);
            throw $e;
        }
    }

    /**
     * Get user dashboard data
     */
    public function getUserDashboard(array $data): array
    {
        $userId = $data['user_id'] ?? null;

        if (!$userId) {
            throw new WeBotException("User ID is required");
        }

        $cacheKey = "user_dashboard:{$userId}";
        $cached = $this->cache->get($cacheKey);

        if ($cached !== null) {
            return [
                'success' => true,
                'dashboard' => $cached,
                'from_cache' => true
            ];
        }

        // Parallel service calls
        $promises = [
            'user' => $this->callServiceAsync('user-service', 'get_user', ['user_id' => $userId]),
            'services' => $this->callServiceAsync('panel-service', 'get_user_services', ['user_id' => $userId]),
            'payments' => $this->callServiceAsync('payment-service', 'get_user_payments', ['user_id' => $userId, 'limit' => 10]),
            'wallet' => $this->callServiceAsync('payment-service', 'get_wallet_balance', ['user_id' => $userId]),
            'stats' => $this->callServiceAsync('user-service', 'get_user_stats', ['user_id' => $userId])
        ];

        $results = $this->resolvePromises($promises);

        $dashboard = [
            'user' => $results['user']['user'] ?? null,
            'services' => $results['services']['services'] ?? [],
            'recent_payments' => $results['payments']['payments'] ?? [],
            'wallet_balance' => $results['wallet']['balance'] ?? 0,
            'statistics' => $results['stats']['stats'] ?? [],
            'generated_at' => time()
        ];

        // Cache dashboard
        $this->cache->set($cacheKey, $dashboard, $this->config['dashboard_cache_ttl']);

        return [
            'success' => true,
            'dashboard' => $dashboard,
            'from_cache' => false
        ];
    }

    /**
     * Get admin dashboard data
     */
    public function getAdminDashboard(array $data): array
    {
        $cacheKey = "admin_dashboard";
        $cached = $this->cache->get($cacheKey);

        if ($cached !== null) {
            return [
                'success' => true,
                'dashboard' => $cached,
                'from_cache' => true
            ];
        }

        // Parallel service calls
        $promises = [
            'user_stats' => $this->callServiceAsync('user-service', 'search_users', ['query' => '', 'limit' => 1]),
            'payment_stats' => $this->callServiceAsync('payment-service', 'get_payment_stats', []),
            'panel_stats' => $this->callServiceAsync('panel-service', 'get_panels', ['limit' => 100]),
            'service_registry' => $this->getServiceRegistryStats()
        ];

        $results = $this->resolvePromises($promises);

        $dashboard = [
            'overview' => [
                'total_users' => count($results['user_stats']['users'] ?? []),
                'total_payments' => $results['payment_stats']['stats']['total_payments'] ?? 0,
                'total_panels' => count($results['panel_stats']['panels'] ?? []),
                'active_services' => count($this->serviceRegistry->getAllServices())
            ],
            'service_health' => $results['service_registry'],
            'recent_activity' => $this->getRecentActivity(),
            'generated_at' => time()
        ];

        // Cache dashboard
        $this->cache->set($cacheKey, $dashboard, $this->config['admin_dashboard_cache_ttl']);

        return [
            'success' => true,
            'dashboard' => $dashboard,
            'from_cache' => false
        ];
    }

    /**
     * Health check all services
     */
    public function healthCheckAll(): array
    {
        $services = $this->serviceRegistry->getAllServices();
        $healthResults = [];

        foreach ($services as $serviceName => $instances) {
            $healthResults[$serviceName] = [];

            foreach ($instances as $instance) {
                try {
                    $result = $this->callService($serviceName, 'health', []);
                    $healthResults[$serviceName][$instance['id']] = [
                        'status' => $result['status'] ?? 'unknown',
                        'response_time' => $result['response_time'] ?? null,
                        'timestamp' => time()
                    ];
                } catch (\Exception $e) {
                    $healthResults[$serviceName][$instance['id']] = [
                        'status' => 'unhealthy',
                        'error' => $e->getMessage(),
                        'timestamp' => time()
                    ];
                }
            }
        }

        return [
            'success' => true,
            'health_results' => $healthResults,
            'timestamp' => time()
        ];
    }

    /**
     * Call service through API Gateway
     */
    private function callService(string $serviceName, string $action, array $data): array
    {
        $request = [
            'path' => "/api/{$serviceName}",
            'method' => 'POST',
            'data' => [
                'action' => $action,
                'data' => $data
            ],
            'headers' => [
                'Content-Type' => 'application/json',
                'X-Service-Orchestrator' => '1.0'
            ]
        ];

        return $this->apiGateway->handleRequest($request);
    }

    /**
     * Call service asynchronously (mock implementation)
     */
    private function callServiceAsync(string $serviceName, string $action, array $data): array
    {
        // In a real implementation, this would use async HTTP clients
        // For now, we'll just call synchronously
        return $this->callService($serviceName, $action, $data);
    }

    /**
     * Resolve async promises (mock implementation)
     */
    private function resolvePromises(array $promises): array
    {
        // In a real implementation, this would resolve async promises
        // For now, we'll just return the results
        return $promises;
    }

    /**
     * Start distributed transaction
     */
    private function startTransaction(string $operation): string
    {
        $transactionId = uniqid('txn_', true);

        $this->activeTransactions[$transactionId] = [
            'operation' => $operation,
            'started_at' => time(),
            'steps' => [],
            'status' => 'active'
        ];

        $this->logger->info("Transaction started", [
            'transaction_id' => $transactionId,
            'operation' => $operation
        ]);

        return $transactionId;
    }

    /**
     * Commit transaction
     */
    private function commitTransaction(string $transactionId): void
    {
        if (isset($this->activeTransactions[$transactionId])) {
            $this->activeTransactions[$transactionId]['status'] = 'committed';
            $this->activeTransactions[$transactionId]['completed_at'] = time();

            $this->logger->info("Transaction committed", [
                'transaction_id' => $transactionId
            ]);
        }
    }

    /**
     * Rollback transaction
     */
    private function rollbackTransaction(string $transactionId): void
    {
        if (isset($this->activeTransactions[$transactionId])) {
            $this->activeTransactions[$transactionId]['status'] = 'rolled_back';
            $this->activeTransactions[$transactionId]['completed_at'] = time();

            $this->logger->warning("Transaction rolled back", [
                'transaction_id' => $transactionId
            ]);
        }
    }

    /**
     * Get service registry statistics
     */
    private function getServiceRegistryStats(): array
    {
        return $this->serviceRegistry->getServiceStatistics();
    }

    /**
     * Get recent activity
     */
    private function getRecentActivity(): array
    {
        // Mock implementation - replace with actual activity tracking
        return [
            ['type' => 'user_registered', 'timestamp' => time() - 300],
            ['type' => 'payment_processed', 'timestamp' => time() - 600],
            ['type' => 'service_created', 'timestamp' => time() - 900]
        ];
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'dashboard_cache_ttl' => 300,       // 5 minutes
            'admin_dashboard_cache_ttl' => 600, // 10 minutes
            'transaction_timeout' => 300,       // 5 minutes
            'max_concurrent_operations' => 10
        ];
    }

    /**
     * Migrate user services
     */
    private function migrateUserServices(array $data): array
    {
        try {
            $userId = $data['user_id'];
            $fromPlan = $data['from_plan'];
            $toPlan = $data['to_plan'];

            // Mock implementation - in real scenario, get from service registry
            $currentServices = [
                ['id' => 'service_1', 'plan_id' => $fromPlan, 'user_id' => $userId],
                ['id' => 'service_2', 'plan_id' => $fromPlan, 'user_id' => $userId]
            ];

            // Migrate each service
            $migrationResults = [];
            foreach ($currentServices as $service) {
                if ($service['plan_id'] === $fromPlan) {
                    $migrationResult = $this->migrateService($service, $toPlan);
                    $migrationResults[] = $migrationResult;
                }
            }

            return [
                'success' => true,
                'migrated_services' => count($migrationResults),
                'results' => $migrationResults
            ];
        } catch (\Exception $e) {
            $this->logger->error("Service migration failed", ['error' => $e->getMessage()]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Bulk service operations
     */
    private function bulkServiceOperations(array $data): array
    {
        try {
            $operations = $data['operations'];
            $results = [];

            foreach ($operations as $operation) {
                $operationType = $operation['type'];
                $operationData = $operation['data'];

                switch ($operationType) {
                    case 'create':
                        $results[] = $this->createService($operationData);
                        break;
                    case 'suspend':
                        $results[] = $this->suspendService($operationData);
                        break;
                    case 'resume':
                        $results[] = $this->resumeService($operationData);
                        break;
                    case 'delete':
                        $results[] = $this->deleteService($operationData);
                        break;
                    default:
                        $results[] = ['success' => false, 'error' => 'Unknown operation type'];
                }
            }

            return [
                'success' => true,
                'total_operations' => count($operations),
                'results' => $results
            ];
        } catch (\Exception $e) {
            $this->logger->error("Bulk operations failed", ['error' => $e->getMessage()]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Migrate a single service
     */
    private function migrateService(array $service, string $newPlan): array
    {
        try {
            // Mock migration process
            return [
                'success' => true,
                'service_id' => $service['id'],
                'old_plan' => $service['plan_id'],
                'new_plan' => $newPlan,
                'migrated_at' => time()
            ];
        } catch (\Exception $e) {
            return ['success' => false, 'service_id' => $service['id'], 'error' => $e->getMessage()];
        }
    }

    /**
     * Create service
     */
    private function createService(array $data): array
    {
        try {
            // Mock service creation
            return [
                'success' => true,
                'service_id' => 'service_' . uniqid(),
                'user_id' => $data['user_id'] ?? null,
                'plan_id' => $data['plan_id'] ?? null,
                'created_at' => time()
            ];
        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Suspend service
     */
    private function suspendService(array $data): array
    {
        try {
            // Mock service suspension
            return [
                'success' => true,
                'service_id' => $data['service_id'],
                'status' => 'suspended',
                'suspended_at' => time()
            ];
        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Resume service
     */
    private function resumeService(array $data): array
    {
        try {
            // Mock service resumption
            return [
                'success' => true,
                'service_id' => $data['service_id'],
                'status' => 'active',
                'resumed_at' => time()
            ];
        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Delete service
     */
    private function deleteService(array $data): array
    {
        try {
            // Mock service deletion
            return [
                'success' => true,
                'service_id' => $data['service_id'],
                'deleted_at' => time()
            ];
        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
}
