<?php

declare(strict_types=1);

namespace WeBot\Core\Cache\Managers;

use WeBot\Core\Cache\Contracts\CacheInterface;

/**
 * Cache Specialized Manager
 *
 * Manages specialized cache operations for different data types.
 *
 * @package WeBot\Core\Cache\Managers
 * @version 2.0
 */
class CacheSpecializedManager
{
    private CacheInterface $cache;
    private array $prefixes = [
        'user' => 'user:',
        'api' => 'api:',
        'panel' => 'panel:',
        'config' => 'config:',
        'temp' => 'temp:'
    ];

    public function __construct(CacheInterface $cache)
    {
        $this->cache = $cache;
    }

    /**
     * User-specific cache operations
     */
    public function user(): UserCacheOperations
    {
        return new UserCacheOperations($this->cache, $this->prefixes['user']);
    }

    /**
     * API-specific cache operations
     */
    public function api(): ApiCacheOperations
    {
        return new ApiCacheOperations($this->cache, $this->prefixes['api']);
    }

    /**
     * Panel-specific cache operations
     */
    public function panel(): PanelCacheOperations
    {
        return new PanelCacheOperations($this->cache, $this->prefixes['panel']);
    }

    /**
     * Configuration cache operations
     */
    public function config(): ConfigCacheOperations
    {
        return new ConfigCacheOperations($this->cache, $this->prefixes['config']);
    }

    /**
     * Temporary cache operations
     */
    public function temp(): TempCacheOperations
    {
        return new TempCacheOperations($this->cache, $this->prefixes['temp']);
    }
}

/**
 * User Cache Operations
 */
class UserCacheOperations
{
    private CacheInterface $cache;
    private string $prefix;

    public function __construct(CacheInterface $cache, string $prefix)
    {
        $this->cache = $cache;
        $this->prefix = $prefix;
    }

    public function getUserData(int $userId): ?array
    {
        return $this->cache->get($this->prefix . $userId);
    }

    public function setUserData(int $userId, array $data, int $ttl = 3600): bool
    {
        return $this->cache->set($this->prefix . $userId, $data, $ttl);
    }

    public function deleteUserData(int $userId): bool
    {
        return $this->cache->delete($this->prefix . $userId);
    }

    public function getUserSessions(int $userId): array
    {
        return $this->cache->get($this->prefix . $userId . ':sessions', []);
    }

    public function setUserSessions(int $userId, array $sessions, int $ttl = 7200): bool
    {
        return $this->cache->set($this->prefix . $userId . ':sessions', $sessions, $ttl);
    }
}

/**
 * API Cache Operations
 */
class ApiCacheOperations
{
    private CacheInterface $cache;
    private string $prefix;

    public function __construct(CacheInterface $cache, string $prefix)
    {
        $this->cache = $cache;
        $this->prefix = $prefix;
    }

    public function getResponse(string $endpoint, array $params = []): ?array
    {
        $key = $this->makeApiKey($endpoint, $params);
        return $this->cache->get($key);
    }

    public function setResponse(string $endpoint, array $params, array $response, int $ttl = 300): bool
    {
        $key = $this->makeApiKey($endpoint, $params);
        return $this->cache->set($key, $response, $ttl);
    }

    public function invalidateEndpoint(string $endpoint): bool
    {
        // This would require pattern-based deletion
        // Implementation depends on cache driver capabilities
        return true;
    }

    private function makeApiKey(string $endpoint, array $params): string
    {
        $paramString = empty($params) ? '' : ':' . md5(serialize($params));
        return $this->prefix . $endpoint . $paramString;
    }
}

/**
 * Panel Cache Operations
 */
class PanelCacheOperations
{
    private CacheInterface $cache;
    private string $prefix;

    public function __construct(CacheInterface $cache, string $prefix)
    {
        $this->cache = $cache;
        $this->prefix = $prefix;
    }

    public function getPanelData(string $panelId): ?array
    {
        return $this->cache->get($this->prefix . $panelId);
    }

    public function setPanelData(string $panelId, array $data, int $ttl = 1800): bool
    {
        return $this->cache->set($this->prefix . $panelId, $data, $ttl);
    }

    public function getPanelUsers(string $panelId): ?array
    {
        return $this->cache->get($this->prefix . $panelId . ':users');
    }

    public function setPanelUsers(string $panelId, array $users, int $ttl = 600): bool
    {
        return $this->cache->set($this->prefix . $panelId . ':users', $users, $ttl);
    }

    public function invalidatePanel(string $panelId): bool
    {
        $keys = [
            $this->prefix . $panelId,
            $this->prefix . $panelId . ':users',
            $this->prefix . $panelId . ':stats'
        ];

        $success = true;
        foreach ($keys as $key) {
            if (!$this->cache->delete($key)) {
                $success = false;
            }
        }

        return $success;
    }
}

/**
 * Configuration Cache Operations
 */
class ConfigCacheOperations
{
    private CacheInterface $cache;
    private string $prefix;

    public function __construct(CacheInterface $cache, string $prefix)
    {
        $this->cache = $cache;
        $this->prefix = $prefix;
    }

    public function getConfig(string $key): mixed
    {
        return $this->cache->get($this->prefix . $key);
    }

    public function setConfig(string $key, mixed $value, int $ttl = 86400): bool
    {
        return $this->cache->set($this->prefix . $key, $value, $ttl);
    }

    public function deleteConfig(string $key): bool
    {
        return $this->cache->delete($this->prefix . $key);
    }

    public function refreshAllConfigs(): bool
    {
        // This would require pattern-based deletion
        // Implementation depends on cache driver capabilities
        return true;
    }
}

/**
 * Temporary Cache Operations
 */
class TempCacheOperations
{
    private CacheInterface $cache;
    private string $prefix;

    public function __construct(CacheInterface $cache, string $prefix)
    {
        $this->cache = $cache;
        $this->prefix = $prefix;
    }

    public function store(string $key, mixed $value, int $ttl = 300): bool
    {
        return $this->cache->set($this->prefix . $key, $value, $ttl);
    }

    public function retrieve(string $key): mixed
    {
        return $this->cache->get($this->prefix . $key);
    }

    public function delete(string $key): bool
    {
        return $this->cache->delete($this->prefix . $key);
    }

    public function storeWithCallback(string $key, callable $callback, int $ttl = 300): mixed
    {
        $value = $this->retrieve($key);

        if ($value === null) {
            $value = $callback();
            $this->store($key, $value, $ttl);
        }

        return $value;
    }
}
