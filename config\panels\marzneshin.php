<?php

declare(strict_types=1);

/**
 * Marzneshin Panel Configuration
 * 
 * Configuration settings for Marzneshin panel integration
 * with enhanced features and OAuth2 authentication.
 * 
 * @package WeBot\Config\Panels
 * @version 2.0
 */

return [
    /*
    |--------------------------------------------------------------------------
    | Marzneshin Panel Settings
    |--------------------------------------------------------------------------
    */
    'enabled' => (bool)($_ENV['MARZNESHIN_ENABLED'] ?? false),
    'name' => 'Marzneshin',
    'description' => 'Marzneshin Enhanced VPN Panel Integration',
    'version' => '0.5.0',

    /*
    |--------------------------------------------------------------------------
    | Connection Settings
    |--------------------------------------------------------------------------
    */
    'connection' => [
        'url' => $_ENV['MARZNESHIN_URL'] ?? 'https://panel.example.com',
        'api_version' => 'v1',
        'timeout' => (int)($_ENV['MARZNESHIN_TIMEOUT'] ?? 30),
        'verify_ssl' => (bool)($_ENV['MARZNESHIN_VERIFY_SSL'] ?? true),
        'retry_attempts' => (int)($_ENV['MARZNESHIN_RETRY_ATTEMPTS'] ?? 3),
        'retry_delay' => (int)($_ENV['MARZNESHIN_RETRY_DELAY'] ?? 1000),
    ],

    /*
    |--------------------------------------------------------------------------
    | OAuth2 Authentication (Enhanced based on botmirzapanel)
    |--------------------------------------------------------------------------
    */
    'auth' => [
        'type' => $_ENV['MARZNESHIN_AUTH_TYPE'] ?? 'oauth2', // oauth2 or basic
        'username' => $_ENV['MARZNESHIN_USERNAME'] ?? '',
        'password' => $_ENV['MARZNESHIN_PASSWORD'] ?? '',
        'client_id' => $_ENV['MARZNESHIN_CLIENT_ID'] ?? '',
        'client_secret' => $_ENV['MARZNESHIN_CLIENT_SECRET'] ?? '',
        'scope' => $_ENV['MARZNESHIN_SCOPE'] ?? 'admin',
        'grant_type' => $_ENV['MARZNESHIN_GRANT_TYPE'] ?? 'password',
        'token_lifetime' => (int)($_ENV['MARZNESHIN_TOKEN_LIFETIME'] ?? 3600),
        'refresh_token_lifetime' => (int)($_ENV['MARZNESHIN_REFRESH_TOKEN_LIFETIME'] ?? 86400),
        'auto_refresh' => (bool)($_ENV['MARZNESHIN_AUTO_REFRESH'] ?? true),
        'refresh_threshold' => (int)($_ENV['MARZNESHIN_REFRESH_THRESHOLD'] ?? 300),
        'token_storage' => $_ENV['MARZNESHIN_TOKEN_STORAGE'] ?? 'cache', // 'cache', 'database', 'file'
        'token_encryption' => (bool)($_ENV['MARZNESHIN_TOKEN_ENCRYPTION'] ?? true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Enhanced Default Settings
    |--------------------------------------------------------------------------
    */
    'defaults' => [
        'data_limit' => (int)($_ENV['MARZNESHIN_DEFAULT_DATA_LIMIT'] ?? 100 * 1024 * 1024 * 1024), // 100GB
        'expire_days' => (int)($_ENV['MARZNESHIN_DEFAULT_EXPIRE_DAYS'] ?? 30),
        'data_limit_reset_strategy' => $_ENV['MARZNESHIN_DEFAULT_RESET_STRATEGY'] ?? 'monthly',
        'status' => $_ENV['MARZNESHIN_DEFAULT_STATUS'] ?? 'active',
        'on_hold_expire_duration' => (int)($_ENV['MARZNESHIN_DEFAULT_ON_HOLD_DURATION'] ?? 0),
        'auto_delete_in_days' => (int)($_ENV['MARZNESHIN_DEFAULT_AUTO_DELETE_DAYS'] ?? 0),
        'proxies' => [
            'vmess' => (bool)($_ENV['MARZNESHIN_DEFAULT_VMESS'] ?? true),
            'vless' => (bool)($_ENV['MARZNESHIN_DEFAULT_VLESS'] ?? true),
            'trojan' => (bool)($_ENV['MARZNESHIN_DEFAULT_TROJAN'] ?? true),
            'shadowsocks' => (bool)($_ENV['MARZNESHIN_DEFAULT_SS'] ?? true),
            'hysteria2' => (bool)($_ENV['MARZNESHIN_DEFAULT_HYSTERIA2'] ?? false),
            'tuic' => (bool)($_ENV['MARZNESHIN_DEFAULT_TUIC'] ?? false),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Enhanced Protocol Support
    |--------------------------------------------------------------------------
    */
    'protocols' => [
        'vmess' => [
            'enabled' => true,
            'security' => 'auto',
            'alter_id' => 0,
        ],
        'vless' => [
            'enabled' => true,
            'flow' => 'xtls-rprx-vision',
            'encryption' => 'none',
        ],
        'trojan' => [
            'enabled' => true,
            'password_length' => 32,
            'flow' => '',
        ],
        'shadowsocks' => [
            'enabled' => true,
            'method' => 'chacha20-ietf-poly1305',
            'password_length' => 32,
        ],
        'hysteria2' => [
            'enabled' => false,
            'password_length' => 32,
            'obfs' => 'salamander',
        ],
        'tuic' => [
            'enabled' => false,
            'uuid_length' => 36,
            'password_length' => 32,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Enhanced Client Configuration
    |--------------------------------------------------------------------------
    */
    'clients' => [
        'supported_types' => [
            'v2ray' => 'V2Ray/V2RayN',
            'clash' => 'Clash/ClashX',
            'clash-meta' => 'Clash Meta',
            'sing-box' => 'Sing-box',
            'json' => 'JSON Config',
            'uri' => 'URI Format',
            'outline' => 'Outline',
            'nekobox' => 'NekoBox',
        ],
        'default_type' => 'v2ray',
        'subscription_format' => 'base64',
        'include_stats' => true,
        'user_agent_tracking' => (bool)($_ENV['MARZNESHIN_USER_AGENT_TRACKING'] ?? true),
        'subscription_update_interval' => (int)($_ENV['MARZNESHIN_SUB_UPDATE_INTERVAL'] ?? 3600),
    ],

    /*
    |--------------------------------------------------------------------------
    | Enhanced Features
    |--------------------------------------------------------------------------
    */
    'features' => [
        'user_management' => true,
        'traffic_monitoring' => true,
        'subscription_urls' => true,
        'config_generation' => true,
        'bulk_operations' => true,
        'user_statistics' => true,
        'traffic_reset' => true,
        'user_suspension' => true,
        'expiry_management' => true,
        'user_groups' => (bool)($_ENV['MARZNESHIN_USER_GROUPS'] ?? true),
        'templates' => (bool)($_ENV['MARZNESHIN_TEMPLATES'] ?? true),
        'notes_support' => true,
        'on_hold_management' => true,
        'auto_delete' => true,
        'advanced_routing' => true,
        'multi_inbound' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | User Groups Configuration
    |--------------------------------------------------------------------------
    */
    'user_groups' => [
        'enabled' => (bool)($_ENV['MARZNESHIN_USER_GROUPS_ENABLED'] ?? true),
        'default_group' => $_ENV['MARZNESHIN_DEFAULT_GROUP'] ?? 'default',
        'auto_assign' => (bool)($_ENV['MARZNESHIN_AUTO_ASSIGN_GROUP'] ?? true),
        'group_based_limits' => (bool)($_ENV['MARZNESHIN_GROUP_BASED_LIMITS'] ?? true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Templates Configuration
    |--------------------------------------------------------------------------
    */
    'templates' => [
        'enabled' => (bool)($_ENV['MARZNESHIN_TEMPLATES_ENABLED'] ?? true),
        'default_template' => $_ENV['MARZNESHIN_DEFAULT_TEMPLATE'] ?? 'basic',
        'auto_apply' => (bool)($_ENV['MARZNESHIN_AUTO_APPLY_TEMPLATE'] ?? true),
        'custom_templates' => [
            'basic' => [
                'data_limit' => 50 * 1024 * 1024 * 1024, // 50GB
                'expire_days' => 30,
                'protocols' => ['vmess', 'vless'],
            ],
            'premium' => [
                'data_limit' => 200 * 1024 * 1024 * 1024, // 200GB
                'expire_days' => 30,
                'protocols' => ['vmess', 'vless', 'trojan'],
            ],
            'unlimited' => [
                'data_limit' => null, // Unlimited
                'expire_days' => 30,
                'protocols' => ['vmess', 'vless', 'trojan', 'shadowsocks'],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Enhanced Rate Limiting
    |--------------------------------------------------------------------------
    */
    'rate_limiting' => [
        'enabled' => (bool)($_ENV['MARZNESHIN_RATE_LIMITING'] ?? true),
        'requests_per_minute' => (int)($_ENV['MARZNESHIN_REQUESTS_PER_MINUTE'] ?? 120),
        'burst_limit' => (int)($_ENV['MARZNESHIN_BURST_LIMIT'] ?? 20),
        'cooldown_period' => (int)($_ENV['MARZNESHIN_COOLDOWN_PERIOD'] ?? 60),
        'per_user_limits' => (bool)($_ENV['MARZNESHIN_PER_USER_LIMITS'] ?? true),
        'admin_bypass' => (bool)($_ENV['MARZNESHIN_ADMIN_BYPASS'] ?? true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Enhanced Caching
    |--------------------------------------------------------------------------
    */
    'caching' => [
        'enabled' => (bool)($_ENV['MARZNESHIN_CACHING'] ?? true),
        'user_info_ttl' => (int)($_ENV['MARZNESHIN_USER_CACHE_TTL'] ?? 300),
        'system_info_ttl' => (int)($_ENV['MARZNESHIN_SYSTEM_CACHE_TTL'] ?? 60),
        'config_cache_ttl' => (int)($_ENV['MARZNESHIN_CONFIG_CACHE_TTL'] ?? 3600),
        'subscription_cache_ttl' => (int)($_ENV['MARZNESHIN_SUB_CACHE_TTL'] ?? 1800),
        'template_cache_ttl' => (int)($_ENV['MARZNESHIN_TEMPLATE_CACHE_TTL'] ?? 7200),
        'cache_prefix' => 'marzneshin:',
    ],

    /*
    |--------------------------------------------------------------------------
    | Enhanced Monitoring
    |--------------------------------------------------------------------------
    */
    'monitoring' => [
        'health_check_interval' => (int)($_ENV['MARZNESHIN_HEALTH_CHECK_INTERVAL'] ?? 300),
        'health_check_timeout' => (int)($_ENV['MARZNESHIN_HEALTH_CHECK_TIMEOUT'] ?? 10),
        'alert_on_failure' => (bool)($_ENV['MARZNESHIN_ALERT_ON_FAILURE'] ?? true),
        'max_consecutive_failures' => (int)($_ENV['MARZNESHIN_MAX_FAILURES'] ?? 3),
        'metrics_collection' => (bool)($_ENV['MARZNESHIN_METRICS'] ?? true),
        'performance_monitoring' => (bool)($_ENV['MARZNESHIN_PERFORMANCE_MONITORING'] ?? true),
        'user_activity_tracking' => (bool)($_ENV['MARZNESHIN_USER_ACTIVITY_TRACKING'] ?? true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Enhanced Error Handling
    |--------------------------------------------------------------------------
    */
    'error_handling' => [
        'log_errors' => true,
        'retry_on_failure' => true,
        'fallback_enabled' => (bool)($_ENV['MARZNESHIN_FALLBACK'] ?? false),
        'graceful_degradation' => (bool)($_ENV['MARZNESHIN_GRACEFUL_DEGRADATION'] ?? true),
        'circuit_breaker' => [
            'enabled' => (bool)($_ENV['MARZNESHIN_CIRCUIT_BREAKER'] ?? true),
            'failure_threshold' => 5,
            'recovery_timeout' => 60,
            'half_open_max_calls' => 3,
        ],
        'error_notifications' => (bool)($_ENV['MARZNESHIN_ERROR_NOTIFICATIONS'] ?? true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Enhanced Security
    |--------------------------------------------------------------------------
    */
    'security' => [
        'encrypt_credentials' => (bool)($_ENV['MARZNESHIN_ENCRYPT_CREDENTIALS'] ?? true),
        'validate_ssl_cert' => (bool)($_ENV['MARZNESHIN_VALIDATE_SSL'] ?? true),
        'allowed_ips' => explode(',', $_ENV['MARZNESHIN_ALLOWED_IPS'] ?? ''),
        'api_key_rotation' => (bool)($_ENV['MARZNESHIN_API_KEY_ROTATION'] ?? true),
        'audit_logging' => (bool)($_ENV['MARZNESHIN_AUDIT_LOGGING'] ?? true),
        'token_encryption' => (bool)($_ENV['MARZNESHIN_TOKEN_ENCRYPTION'] ?? true),
        'request_signing' => (bool)($_ENV['MARZNESHIN_REQUEST_SIGNING'] ?? false),
        'ip_whitelist_enforcement' => (bool)($_ENV['MARZNESHIN_IP_WHITELIST'] ?? false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Enhanced Backup & Sync
    |--------------------------------------------------------------------------
    */
    'backup' => [
        'enabled' => (bool)($_ENV['MARZNESHIN_BACKUP_ENABLED'] ?? true),
        'sync_interval' => (int)($_ENV['MARZNESHIN_SYNC_INTERVAL'] ?? 1800), // 30 minutes
        'backup_user_data' => true,
        'backup_configurations' => true,
        'backup_templates' => true,
        'backup_groups' => true,
        'retention_days' => (int)($_ENV['MARZNESHIN_BACKUP_RETENTION'] ?? 14),
        'incremental_backup' => (bool)($_ENV['MARZNESHIN_INCREMENTAL_BACKUP'] ?? true),
        'compression' => (bool)($_ENV['MARZNESHIN_BACKUP_COMPRESSION'] ?? true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Advanced Features (Enhanced based on botmirzapanel)
    |--------------------------------------------------------------------------
    */
    'advanced' => [
        'custom_headers' => [
            'User-Agent' => 'WeBot/2.0 Marzneshin-Adapter',
            'X-Client-Version' => '2.0.0',
            'X-API-Version' => 'v1',
            'X-Bot-Name' => 'WeBot',
        ],
        'webhook_support' => (bool)($_ENV['MARZNESHIN_WEBHOOK_SUPPORT'] ?? true),
        'webhook_url' => $_ENV['MARZNESHIN_WEBHOOK_URL'] ?? '',
        'webhook_secret' => $_ENV['MARZNESHIN_WEBHOOK_SECRET'] ?? '',
        'webhook_events' => [
            'user.created',
            'user.updated',
            'user.deleted',
            'user.suspended',
            'user.expired',
            'user.activated',
            'quota.exceeded',
            'quota.reset',
            'subscription.updated',
            'node.online',
            'node.offline',
        ],
        'real_time_updates' => (bool)($_ENV['MARZNESHIN_REAL_TIME_UPDATES'] ?? true),
        'batch_processing' => (bool)($_ENV['MARZNESHIN_BATCH_PROCESSING'] ?? true),
        'async_operations' => (bool)($_ENV['MARZNESHIN_ASYNC_OPERATIONS'] ?? true),
        'subscription_tracking' => (bool)($_ENV['MARZNESHIN_SUBSCRIPTION_TRACKING'] ?? true),
        'user_agent_logging' => (bool)($_ENV['MARZNESHIN_USER_AGENT_LOGGING'] ?? true),
        'multi_node_support' => (bool)($_ENV['MARZNESHIN_MULTI_NODE_SUPPORT'] ?? true),
        'auto_node_selection' => (bool)($_ENV['MARZNESHIN_AUTO_NODE_SELECTION'] ?? false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Node Management (Marzneshin Multi-Node)
    |--------------------------------------------------------------------------
    */
    'nodes' => [
        'enabled' => (bool)($_ENV['MARZNESHIN_NODES_ENABLED'] ?? true),
        'auto_discovery' => (bool)($_ENV['MARZNESHIN_AUTO_DISCOVERY'] ?? false),
        'health_check_interval' => (int)($_ENV['MARZNESHIN_NODE_HEALTH_CHECK'] ?? 300), // 5 minutes
        'load_balancing' => (bool)($_ENV['MARZNESHIN_LOAD_BALANCING'] ?? false),
        'failover' => (bool)($_ENV['MARZNESHIN_NODE_FAILOVER'] ?? true),
        'preferred_nodes' => explode(',', $_ENV['MARZNESHIN_PREFERRED_NODES'] ?? ''),
        'node_selection_strategy' => $_ENV['MARZNESHIN_NODE_SELECTION'] ?? 'round_robin', // 'round_robin', 'least_load', 'random'
    ],

    /*
    |--------------------------------------------------------------------------
    | Subscription Management (Enhanced)
    |--------------------------------------------------------------------------
    */
    'subscription' => [
        'enabled' => (bool)($_ENV['MARZNESHIN_SUBSCRIPTION_ENABLED'] ?? true),
        'format' => $_ENV['MARZNESHIN_SUBSCRIPTION_FORMAT'] ?? 'base64',
        'update_interval' => (int)($_ENV['MARZNESHIN_SUBSCRIPTION_UPDATE_INTERVAL'] ?? 3600), // 1 hour
        'track_user_agents' => (bool)($_ENV['MARZNESHIN_TRACK_USER_AGENTS'] ?? true),
        'track_ips' => (bool)($_ENV['MARZNESHIN_TRACK_IPS'] ?? true),
        'max_update_frequency' => (int)($_ENV['MARZNESHIN_MAX_UPDATE_FREQUENCY'] ?? 300), // 5 minutes
        'custom_headers' => [
            'profile-update-interval' => '24',
            'subscription-userinfo' => 'upload=0; download=0; total=0; expire=0',
        ],
        'url_shortening' => (bool)($_ENV['MARZNESHIN_URL_SHORTENING'] ?? false),
        'qr_code_generation' => (bool)($_ENV['MARZNESHIN_QR_CODE_GENERATION'] ?? true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Compatibility Mode
    |--------------------------------------------------------------------------
    */
    'compatibility' => [
        'marzban_mode' => (bool)($_ENV['MARZNESHIN_MARZBAN_MODE'] ?? false),
        'legacy_api_support' => (bool)($_ENV['MARZNESHIN_LEGACY_API'] ?? true),
        'auto_migration' => (bool)($_ENV['MARZNESHIN_AUTO_MIGRATION'] ?? false),
        'feature_detection' => (bool)($_ENV['MARZNESHIN_FEATURE_DETECTION'] ?? true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Optimization
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'connection_pooling' => (bool)($_ENV['MARZNESHIN_CONNECTION_POOLING'] ?? true),
        'max_connections' => (int)($_ENV['MARZNESHIN_MAX_CONNECTIONS'] ?? 15),
        'keep_alive' => (bool)($_ENV['MARZNESHIN_KEEP_ALIVE'] ?? true),
        'compression' => (bool)($_ENV['MARZNESHIN_COMPRESSION'] ?? true),
        'batch_operations' => (bool)($_ENV['MARZNESHIN_BATCH_OPERATIONS'] ?? true),
        'async_operations' => (bool)($_ENV['MARZNESHIN_ASYNC_OPERATIONS'] ?? true),
        'lazy_loading' => (bool)($_ENV['MARZNESHIN_LAZY_LOADING'] ?? true),
        'prefetch_data' => (bool)($_ENV['MARZNESHIN_PREFETCH_DATA'] ?? false),
    ],
];
