<?php
/**
 * WeBot v2.0 - Telegram Webhook Handler
 * مدیریت webhook های تلگرام
 */

declare(strict_types=1);

// تنظیم timezone
date_default_timezone_set('Asia/Tehran');

// تنظیم error reporting برای production
if (getenv('APP_ENV') === 'production') {
    error_reporting(0);
    ini_set('display_errors', '0');
} else {
    error_reporting(E_ALL);
    ini_set('display_errors', '1');
}

// تنظیم memory و time limit
ini_set('memory_limit', '256M');
set_time_limit(30);

// بارگذاری autoloader
require_once __DIR__ . '/../vendor/autoload.php';

use WeBot\Core\Config;
use WeBot\Core\Database;
use WeBot\Core\TelegramBot;
use WeBot\Utils\Logger;
use WeBot\Core\RateLimiter;
use WeBot\Core\SecurityManager as Security; // Renamed to SecurityManager
use WeBot\Core\Cache\CacheManager;

// تنظیم headers
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

try {
    // بررسی method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'Method not allowed'
        ]);
        exit;
    }
    
    // بارگذاری تنظیمات
    $config = new Config();
    
    // راه‌اندازی error handler (Removed)
    // $errorHandler = new ErrorHandler($config);
    // $errorHandler->register();
    
    // راه‌اندازی logger
    Logger::configure($config->get('logging', []));
    $logger = Logger::getInstance();
    $logger->info('Webhook request received', [
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'content_length' => $_SERVER['CONTENT_LENGTH'] ?? 0
    ]);
    
    // راه‌اندازی Cache و Rate Limiter
    $cacheManager = new CacheManager($config->get('cache', []));
    $rateLimiter = new RateLimiter($cacheManager, $config->get('ratelimit', [])); // Use CacheManager directly
    $clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    
    if (!$rateLimiter->isAllowed($clientIp, $config->get('ratelimit.rules.webhook', []))) {
        http_response_code(429);
        $logger->warning('Rate limit exceeded', ['ip' => $clientIp]);
        echo json_encode([
            'success' => false,
            'error' => 'Rate limit exceeded'
        ]);
        exit;
    }
    
    // خواندن input
    $input = file_get_contents('php://input');
    if (empty($input)) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Empty request body'
        ]);
        exit;
    }
    
    // بررسی امنیتی
    $security = new Security($config->get('security', []));
    
    // بررسی Telegram IP (اختیاری)
    // TODO: The isTelegramIP method does not exist in SecurityManager. It needs to be implemented or the logic re-evaluated.
    // if ($config->get('telegram.ip_check', false)) {
    //     if (!$security->isTelegramIP($clientIp)) { 
    //         http_response_code(403);
    //         $logger->warning('Invalid IP for webhook', ['ip' => $clientIp]);
    //         echo json_encode([
    //             'success' => false,
    //             'error' => 'Forbidden'
    //         ]);
    //         exit;
    //     }
    // }
    
    // بررسی secret token (اگر تنظیم شده باشد)
    $secretToken = $config->get('telegram.secret_token');
    if ($secretToken) {
        $receivedToken = $_SERVER['HTTP_X_TELEGRAM_BOT_API_SECRET_TOKEN'] ?? '';
        if (!hash_equals($secretToken, $receivedToken)) {
            http_response_code(403);
            $logger->warning('Invalid secret token', ['ip' => $clientIp]);
            echo json_encode([
                'success' => false,
                'error' => 'Forbidden'
            ]);
            exit;
        }
    }
    
    // پارس کردن JSON
    $update = json_decode($input, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        http_response_code(400);
        $logger->error('Invalid JSON in webhook', [
            'error' => json_last_error_msg(),
            'input_length' => strlen($input)
        ]);
        echo json_encode([
            'success' => false,
            'error' => 'Invalid JSON'
        ]);
        exit;
    }
    
    // بررسی ساختار update
    if (!isset($update['update_id'])) {
        http_response_code(400);
        $logger->error('Invalid update structure', ['update' => $update]);
        echo json_encode([
            'success' => false,
            'error' => 'Invalid update structure'
        ]);
        exit;
    }
    
    // اتصال به دیتابیس
    $database = new Database($config->get('database', []));
    if (!$database->isConnected()) {
        http_response_code(503);
        $logger->error('Database connection failed');
        echo json_encode([
            'success' => false,
            'error' => 'Service unavailable'
        ]);
        exit;
    }
    
    // راه‌اندازی bot
    $bot = new TelegramBot($config, $database, $logger);
    
    // پردازش update
    $startTime = microtime(true);
    $result = $bot->processUpdate($update);
    $processingTime = round((microtime(true) - $startTime) * 1000, 2);
    
    // لاگ نتیجه
    $logger->info('Update processed', [
        'update_id' => $update['update_id'],
        'processing_time_ms' => $processingTime,
        'result' => $result,
        'memory_usage' => memory_get_peak_usage(true)
    ]);
    
    // پاسخ موفق
    http_response_code(200);
    echo json_encode([
        'success' => true,
        'update_id' => $update['update_id'],
        'processing_time_ms' => $processingTime
    ]);
    
} catch (Exception $e) {
    // مدیریت خطا
    http_response_code(500);
    
    $errorData = [
        'success' => false,
        'error' => 'Internal server error',
        'timestamp' => date('c')
    ];
    
    // اگر debug mode فعال باشد، جزئیات خطا را نمایش بده
    if (isset($config) && $config->get('APP_DEBUG', false)) {
        $errorData['debug'] = [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ];
    }
    
    echo json_encode($errorData);
    
    // لاگ خطا
    if (isset($logger)) {
        $logger->error('Webhook error: ' . $e->getMessage(), [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString(),
            'input' => $input ?? 'not available',
            'update_id' => $update['update_id'] ?? 'unknown'
        ]);
    } else {
        error_log('WeBot Webhook Error: ' . $e->getMessage());
    }
    
} finally {
    // پاکسازی
    if (isset($database)) {
        // $database->close(); // This method does not exist
    }
    
    // آمار memory
    if (isset($logger)) {
        $logger->debug('Webhook request completed', [
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
            'execution_time' => microtime(true) - ($_SERVER['REQUEST_TIME_FLOAT'] ?? microtime(true))
        ]);
    }
}
?>
