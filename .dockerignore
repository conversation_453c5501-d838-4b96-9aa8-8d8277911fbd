# WeBot Docker Ignore File

# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
CHANGELOG.md
docs/
*.md

# Development files
.env.example
.env.local
.env.development
docker-compose.override.yml
docker-compose.dev.yml

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db
*.tmp

# Logs
*.log
logs/
storage/logs/*
!storage/logs/.gitkeep

# Cache
storage/cache/*
!storage/cache/.gitkeep
storage/sessions/*
!storage/sessions/.gitkeep

# Uploads (exclude from image)
storage/uploads/*
!storage/uploads/.gitkeep
public/uploads/*
!public/uploads/.gitkeep

# Testing
phpunit.xml
.phpunit.result.cache
coverage/

# Node.js (if used for frontend)
node_modules/
npm-debug.log
yarn-error.log
package-lock.json
yarn.lock

# Composer development
vendor/bin/

# Backup files
*.bak
*.backup
*.sql

# Temporary files
tmp/
temp/
*.tmp

# Build artifacts
build/
dist/

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
Jenkinsfile

# Docker
Dockerfile.dev
docker-compose.dev.yml
docker-compose.override.yml

# Environment specific
.env.staging
.env.testing

# IDE specific
.phpstorm.meta.php
_ide_helper.php
_ide_helper_models.php

# Security
*.pem
*.key
*.crt
ssl/

# Monitoring
prometheus/
grafana/
