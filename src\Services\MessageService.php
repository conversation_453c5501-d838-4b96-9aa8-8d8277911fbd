<?php

declare(strict_types=1);

namespace WeBot\Services;

use WeBot\Core\CacheManager;
use WeBot\Core\Database;
use WeBot\Exceptions\WeBotException;
use WeBot\Services\MessageTemplateEngine;
use WeBot\Utils\Logger;
use Monolog\Logger as MonologLogger;

/**
 * Message Service
 *
 * Handles message templates, localization, formatting,
 * and message queue management for the bot.
 *
 * @package WeBot\Services
 * @version 2.0
 */
class MessageService
{
    private Database $database;
    private CacheManager $cache;
    private MessageTemplateEngine $templateEngine;
    private MonologLogger $logger;
    private array $config;
    private array $messages = [];
    private string $defaultLanguage = 'fa';

    public function __construct(Database $database, CacheManager $cache, array $config = [])
    {
        $this->database = $database;
        $this->cache = $cache;
        $this->logger = Logger::getInstance();
        $this->config = array_merge([
            'default_language' => 'fa',
            'supported_languages' => ['fa', 'en', 'ar'],
            'cache_ttl' => 3600,
            'message_queue_enabled' => true,
            'max_message_length' => 4096,
            'templates_path' => 'resources/templates',
        ], $config);

        $this->defaultLanguage = $this->config['default_language'];

        // Initialize template engine
        $this->templateEngine = new MessageTemplateEngine([
            'templates_path' => $this->config['templates_path'],
            'default_language' => $this->defaultLanguage
        ]);

        $this->loadMessages();
    }

    /**
     * Get message by key with parameters
     */
    public function get(string $key, array $params = [], string $language = null): string
    {
        $language = $language ?? $this->defaultLanguage;

        // Get message from cache or database
        $message = $this->getMessage($key, $language);

        if (!$message) {
            // Fallback to default language
            if ($language !== $this->defaultLanguage) {
                $message = $this->getMessage($key, $this->defaultLanguage);
            }

            // If still not found, return key
            if (!$message) {
                return $key;
            }
        }

        // Replace parameters
        return $this->replaceParameters($message, $params);
    }

    /**
     * Render template with variables
     */
    public function renderTemplate(string $template, array $variables = [], string $language = null): string
    {
        $language = $language ?? $this->defaultLanguage;

        try {
            return $this->templateEngine->render($template, $variables, $language);
        } catch (\Exception $e) {
            $this->logger->error('Template rendering failed', [
                'template' => $template,
                'language' => $language,
                'error' => $e->getMessage()
            ]);

            // Fallback to simple message
            return $this->get("template.{$template}.fallback", $variables, $language);
        }
    }

    /**
     * Render inline template
     */
    public function renderInline(string $content, array $variables = []): string
    {
        return $this->templateEngine->renderInline($content, $variables);
    }

    /**
     * Check if template exists
     */
    public function templateExists(string $template, string $language = null): bool
    {
        $language = $language ?? $this->defaultLanguage;
        return $this->templateEngine->templateExists($template, $language);
    }

    /**
     * Get welcome message using template
     */
    public function getWelcomeMessage(array $user, string $language = null): string
    {
        $language = $language ?? $this->defaultLanguage;

        $variables = [
            'first_name' => $user['first_name'] ?? 'کاربر',
            'last_name' => $user['last_name'] ?? '',
            'username' => $user['username'] ?? '',
            'user_id' => $user['id'] ?? 0,
            'is_premium' => $user['is_premium'] ?? false,
            'language_code' => $user['language_code'] ?? $language
        ];

        // Try template first, fallback to simple message
        if ($this->templateExists('welcome', $language)) {
            return $this->renderTemplate('welcome', $variables, $language);
        }

        return $this->get('welcome', $variables, $language);
    }

    /**
     * Get help message
     */
    public function getHelpMessage(string $language = null): string
    {
        return $this->get('help', [], $language);
    }

    /**
     * Get error message
     */
    public function getErrorMessage(string $errorType, array $params = [], string $language = null): string
    {
        return $this->get("error.{$errorType}", $params, $language);
    }

    /**
     * Get success message
     */
    public function getSuccessMessage(string $successType, array $params = [], string $language = null): string
    {
        return $this->get("success.{$successType}", $params, $language);
    }

    /**
     * Format service information
     */
    public function formatServiceInfo(array $service, string $language = null): string
    {
        $params = [
            'service_name' => $service['name'] ?? 'نامشخص',
            'service_type' => $service['type'] ?? 'نامشخص',
            'data_limit' => $this->formatBytes($service['data_limit'] ?? 0),
            'used_traffic' => $this->formatBytes($service['used_traffic'] ?? 0),
            'remaining_traffic' => $this->formatBytes(($service['data_limit'] ?? 0) - ($service['used_traffic'] ?? 0)),
            'expire_date' => $this->formatDate($service['expire_date'] ?? null),
            'status' => $this->getStatusText($service['status'] ?? 'unknown', $language),
            'days_remaining' => $this->getDaysRemaining($service['expire_date'] ?? null)
        ];

        return $this->get('service_info', $params, $language);
    }

    /**
     * Format payment information
     */
    public function formatPaymentInfo(array $payment, string $language = null): string
    {
        $params = [
            'payment_id' => $payment['id'] ?? 'نامشخص',
            'amount' => number_format($payment['amount'] ?? 0, 2),
            'currency' => $payment['currency'] ?? 'USD',
            'status' => $this->getPaymentStatusText($payment['status'] ?? 'unknown', $language),
            'gateway' => $payment['gateway'] ?? 'نامشخص',
            'created_at' => $this->formatDate($payment['created_at'] ?? null),
            'description' => $payment['description'] ?? ''
        ];

        return $this->get('payment_info', $params, $language);
    }

    /**
     * Format user statistics
     */
    public function formatUserStats(array $stats, string $language = null): string
    {
        $params = [
            'total_services' => $stats['total_services'] ?? 0,
            'active_services' => $stats['active_services'] ?? 0,
            'expired_services' => $stats['expired_services'] ?? 0,
            'total_traffic' => $this->formatBytes($stats['total_traffic'] ?? 0),
            'total_payments' => $stats['total_payments'] ?? 0,
            'total_spent' => number_format($stats['total_spent'] ?? 0, 2),
            'member_since' => $this->formatDate($stats['member_since'] ?? null)
        ];

        return $this->get('user_stats', $params, $language);
    }

    /**
     * Create notification message
     */
    public function createNotification(string $type, array $data, string $language = null): array
    {
        $templates = [
            'service_expiring' => [
                'title' => 'service_expiring_title',
                'message' => 'service_expiring_message',
                'priority' => 'high'
            ],
            'service_expired' => [
                'title' => 'service_expired_title',
                'message' => 'service_expired_message',
                'priority' => 'high'
            ],
            'payment_successful' => [
                'title' => 'payment_successful_title',
                'message' => 'payment_successful_message',
                'priority' => 'medium'
            ],
            'payment_failed' => [
                'title' => 'payment_failed_title',
                'message' => 'payment_failed_message',
                'priority' => 'high'
            ],
            'service_created' => [
                'title' => 'service_created_title',
                'message' => 'service_created_message',
                'priority' => 'medium'
            ]
        ];

        $template = $templates[$type] ?? null;
        if (!$template) {
            throw new WeBotException("Unknown notification type: {$type}");
        }

        return [
            'type' => $type,
            'title' => $this->get($template['title'], $data, $language),
            'message' => $this->get($template['message'], $data, $language),
            'priority' => $template['priority'],
            'created_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Queue message for sending
     */
    public function queueMessage(int $userId, string $message, array $options = []): bool
    {
        if (!$this->config['message_queue_enabled']) {
            return false;
        }

        try {
            $optionsJson = json_encode($options);
            $scheduledAt = $options['scheduled_at'] ?? date('Y-m-d H:i:s');

            $messageData = [
                'user_id' => $userId,
                'message' => $message,
                'options' => $optionsJson,
                'scheduled_at' => $scheduledAt,
                'created_at' => date('Y-m-d H:i:s')
            ];
            $success = $this->database->insert('scheduled_messages', $messageData) > 0;

            return $success;
        } catch (\Exception $e) {
            error_log("Failed to queue message: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get pending messages from queue
     */
    public function getPendingMessages(int $limit = 100): array
    {
        try {
            $sql = "SELECT * FROM message_queue WHERE status = 'pending' AND scheduled_at <= NOW() ORDER BY created_at ASC LIMIT ?";
            $messages = $this->database->query($sql, [$limit]);

            return $messages;
        } catch (\Exception $e) {
            error_log("Failed to get pending messages: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Mark message as sent
     */
    public function markMessageSent(int $messageId): bool
    {
        try {
            $success = $this->database->update(
                'message_queue',
                ['status' => 'sent', 'sent_at' => date('Y-m-d H:i:s')],
                ['id' => $messageId]
            ) > 0;

            return $success;
        } catch (\Exception $e) {
            error_log("Failed to mark message as sent: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Mark message as failed
     */
    public function markMessageFailed(int $messageId, string $error): bool
    {
        try {
            $success = $this->database->update(
                'message_queue',
                ['status' => 'failed', 'error_message' => $error],
                ['id' => $messageId]
            ) > 0;

            return $success;
        } catch (\Exception $e) {
            error_log("Failed to mark message as failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Load messages from cache or database
     */
    private function loadMessages(): void
    {
        $cacheKey = 'messages_all_languages';
        $cached = $this->cache->get($cacheKey);

        if ($cached) {
            $this->messages = $cached;
            return;
        }

        // Load from database
        try {
            $results = $this->database->query("SELECT language, message_key, message_value FROM message_templates WHERE is_active = 1");

            foreach ($results as $row) {
                $this->messages[$row['language']][$row['message_key']] = $row['message_value'];
            }

            // Cache the messages
            $this->cache->set($cacheKey, $this->messages, $this->config['cache_ttl']);
        } catch (\Exception $e) {
            error_log("Failed to load messages: " . $e->getMessage());
            $this->loadDefaultMessages();
        }
    }

    /**
     * Load default messages if database fails
     */
    private function loadDefaultMessages(): void
    {
        $this->messages = [
            'fa' => [
                'welcome' => "🎉 سلام {first_name}!\nبه WeBot خوش آمدید.",
                'help' => "📚 راهنمای استفاده از ربات:\n\n/start - شروع\n/help - راهنما\n/profile - پروفایل",
                'error.general' => "❌ خطایی رخ داده است. لطفاً مجدداً تلاش کنید.",
                'error.not_found' => "❌ موردی یافت نشد.",
                'success.general' => "✅ عملیات با موفقیت انجام شد.",
                'service_info' => "📊 **اطلاعات سرویس**\n\n📝 نام: {service_name}\n📊 وضعیت: {status}\n📈 ترافیک: {used_traffic} / {data_limit}\n📅 انقضا: {expire_date}",
                'payment_info' => "💳 **اطلاعات پرداخت**\n\n🆔 شناسه: {payment_id}\n💰 مبلغ: {amount} {currency}\n📊 وضعیت: {status}\n📅 تاریخ: {created_at}",
                'user_stats' => "📊 **آمار کاربری**\n\n📱 سرویس‌ها: {total_services}\n✅ فعال: {active_services}\n📈 ترافیک کل: {total_traffic}\n💰 کل پرداخت‌ها: {total_spent}\n📅 عضویت از: {member_since}"
            ],
            'en' => [
                'welcome' => "🎉 Hello {first_name}!\nWelcome to WeBot.",
                'help' => "📚 Bot Usage Guide:\n\n/start - Start\n/help - Help\n/profile - Profile",
                'error.general' => "❌ An error occurred. Please try again.",
                'error.not_found' => "❌ Not found.",
                'success.general' => "✅ Operation completed successfully."
            ]
        ];
    }

    /**
     * Get message from loaded messages
     */
    private function getMessage(string $key, string $language): ?string
    {
        return $this->messages[$language][$key] ?? null;
    }

    /**
     * Replace parameters in message
     */
    private function replaceParameters(string $message, array $params): string
    {
        foreach ($params as $key => $value) {
            $message = str_replace("{{$key}}", (string) $value, $message);
        }

        return $message;
    }

    /**
     * Format bytes to human readable
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= (1 << (10 * $pow));

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Format date
     */
    private function formatDate(?string $date): string
    {
        if (!$date) {
            return 'نامشخص';
        }

        try {
            $dateTime = new \DateTime($date);
            return $dateTime->format('Y-m-d H:i');
        } catch (\Exception $e) {
            return 'نامشخص';
        }
    }

    /**
     * Get days remaining
     */
    private function getDaysRemaining(?string $expireDate): int
    {
        if (!$expireDate) {
            return 0;
        }

        try {
            $expire = new \DateTime($expireDate);
            $now = new \DateTime();
            $diff = $now->diff($expire);

            return $diff->invert ? 0 : $diff->days;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get status text
     */
    private function getStatusText(string $status, ?string $language): string
    {
        $statusTexts = [
            'fa' => [
                'active' => '✅ فعال',
                'expired' => '❌ منقضی',
                'suspended' => '⏸️ معلق',
                'unknown' => '❓ نامشخص'
            ],
            'en' => [
                'active' => '✅ Active',
                'expired' => '❌ Expired',
                'suspended' => '⏸️ Suspended',
                'unknown' => '❓ Unknown'
            ]
        ];

        $language = $language ?? $this->defaultLanguage;
        return $statusTexts[$language][$status] ?? $statusTexts[$language]['unknown'];
    }

    /**
     * Get payment status text
     */
    private function getPaymentStatusText(string $status, ?string $language): string
    {
        $statusTexts = [
            'fa' => [
                'pending' => '⏳ در انتظار',
                'completed' => '✅ تکمیل شده',
                'failed' => '❌ ناموفق',
                'cancelled' => '🚫 لغو شده',
                'unknown' => '❓ نامشخص'
            ],
            'en' => [
                'pending' => '⏳ Pending',
                'completed' => '✅ Completed',
                'failed' => '❌ Failed',
                'cancelled' => '🚫 Cancelled',
                'unknown' => '❓ Unknown'
            ]
        ];

        $language = $language ?? $this->defaultLanguage;
        return $statusTexts[$language][$status] ?? $statusTexts[$language]['unknown'];
    }

    /**
     * Clear message cache
     */
    public function clearCache(): bool
    {
        return $this->cache->delete('messages_all_languages');
    }

    /**
     * Calculate days remaining until expiration
     */
    private function calculateDaysRemaining(?string $expiresAt): int
    {
        if (!$expiresAt) {
            return 0;
        }

        $expirationTime = strtotime($expiresAt);
        $currentTime = time();

        if ($expirationTime <= $currentTime) {
            return 0;
        }

        return (int)ceil(($expirationTime - $currentTime) / 86400);
    }

    /**
     * Get gateway title by language
     */
    private function getGatewayTitle(string $gateway, string $language): string
    {
        $gateways = [
            'fa' => [
                'zarinpal' => 'زرین‌پال',
                'idpay' => 'آیدی‌پی',
                'nextpay' => 'نکست‌پی',
                'zibal' => 'زیبال',
                'wallet' => 'کیف پول'
            ],
            'en' => [
                'zarinpal' => 'ZarinPal',
                'idpay' => 'IDPay',
                'nextpay' => 'NextPay',
                'zibal' => 'Zibal',
                'wallet' => 'Wallet'
            ]
        ];

        return $gateways[$language][$gateway] ?? $gateway;
    }

    /**
     * Get status icon
     */
    private function getStatusIcon(string $status): string
    {
        return match ($status) {
            'pending' => '🟡',
            'paid' => '🟢',
            'failed' => '🔴',
            'cancelled' => '⚫',
            'refunded' => '🔵',
            'active' => '✅',
            'expired' => '❌',
            'suspended' => '⚠️',
            default => '⚪'
        };
    }

    /**
     * Get status title by language
     */
    private function getStatusTitle(string $status, string $language): string
    {
        $statuses = [
            'fa' => [
                'pending' => 'در انتظار پرداخت',
                'paid' => 'پرداخت شده',
                'failed' => 'ناموفق',
                'cancelled' => 'لغو شده',
                'refunded' => 'بازگشت داده شده',
                'active' => 'فعال',
                'expired' => 'منقضی',
                'suspended' => 'معلق'
            ],
            'en' => [
                'pending' => 'Pending Payment',
                'paid' => 'Paid',
                'failed' => 'Failed',
                'cancelled' => 'Cancelled',
                'refunded' => 'Refunded',
                'active' => 'Active',
                'expired' => 'Expired',
                'suspended' => 'Suspended'
            ]
        ];

        return $statuses[$language][$status] ?? $status;
    }

    /**
     * Reload messages
     */
    public function reload(): void
    {
        $this->clearCache();
        $this->loadMessages();
    }
}
