-- WeBot Advanced Database Optimization Migration
-- Version: 2.0
-- Date: 2025-01-27
-- Task: TASK-003 Database Schema Optimization

-- ==============================================
-- COMPOSITE INDEXES FOR BETTER PERFORMANCE
-- ==============================================

-- Users table composite indexes
ALTER TABLE users 
ADD INDEX IF NOT EXISTS idx_phone (phone),
ADD INDEX IF NOT EXISTS idx_status_role (status, role),
ADD INDEX IF NOT EXISTS idx_telegram_status (telegram_id, status),
ADD INDEX IF NOT EXISTS idx_subscription_expires (subscription_status, subscription_expires_at),
ADD INDEX IF NOT EXISTS idx_balance_status (balance, status),
ADD INDEX IF NOT EXISTS idx_referral_active (referred_by, status),
ADD INDEX IF NOT EXISTS idx_verification_status (is_verified, status),
ADD INDEX IF NOT EXISTS idx_last_login (last_login_at),
ADD INDEX IF NOT EXISTS idx_created_status (created_at, status);

-- Services table composite indexes  
ALTER TABLE services 
ADD INDEX IF NOT EXISTS idx_user_status (user_id, status),
ADD INDEX IF NOT EXISTS idx_panel_created (panel_id, created_at),
ADD INDEX IF NOT EXISTS idx_server_status (server_id, status),
ADD INDEX IF NOT EXISTS idx_expires_status (expires_at, status),
ADD INDEX IF NOT EXISTS idx_volume_usage (volume, used_volume),
ADD INDEX IF NOT EXISTS idx_user_expires (user_id, expires_at),
ADD INDEX IF NOT EXISTS idx_active_services (status, expires_at, user_id);

-- Payments table composite indexes
ALTER TABLE payments 
ADD INDEX IF NOT EXISTS idx_user_created (user_id, created_at),
ADD INDEX IF NOT EXISTS idx_gateway_status (payment_gateway, status),
ADD INDEX IF NOT EXISTS idx_method_status (payment_method, status),
ADD INDEX IF NOT EXISTS idx_amount_status (amount, status),
ADD INDEX IF NOT EXISTS idx_user_status (user_id, status),
ADD INDEX IF NOT EXISTS idx_created_status (created_at, status),
ADD INDEX IF NOT EXISTS idx_completed_range (completed_at, status);

-- Tickets table composite indexes (if exists)
ALTER TABLE tickets 
ADD INDEX IF NOT EXISTS idx_user_status (user_id, status),
ADD INDEX IF NOT EXISTS idx_priority_status (priority, status),
ADD INDEX IF NOT EXISTS idx_created_status (created_at, status),
ADD INDEX IF NOT EXISTS idx_assigned_status (assigned_to, status);

-- Sessions table optimization
ALTER TABLE sessions 
ADD INDEX IF NOT EXISTS idx_expires (expires_at),
ADD INDEX IF NOT EXISTS idx_user_expires (user_id, expires_at),
ADD INDEX IF NOT EXISTS idx_ip_created (ip_address, created_at);

-- ==============================================
-- FOREIGN KEY CONSTRAINTS
-- ==============================================

-- Add proper foreign key constraints with CASCADE options
ALTER TABLE services 
ADD CONSTRAINT IF NOT EXISTS fk_services_user 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT IF NOT EXISTS fk_services_panel 
    FOREIGN KEY (panel_id) REFERENCES panels(id) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE payments 
ADD CONSTRAINT IF NOT EXISTS fk_payments_user 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE tickets 
ADD CONSTRAINT IF NOT EXISTS fk_tickets_user 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE,
ADD CONSTRAINT IF NOT EXISTS fk_tickets_assigned 
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE;

-- ==============================================
-- DATA VALIDATION CONSTRAINTS
-- ==============================================

-- Users table constraints
ALTER TABLE users 
ADD CONSTRAINT chk_users_balance CHECK (balance >= 0),
ADD CONSTRAINT chk_users_referral_count CHECK (referral_count >= 0),
ADD CONSTRAINT chk_users_login_attempts CHECK (login_attempts >= 0),
ADD CONSTRAINT chk_users_phone_format CHECK (phone IS NULL OR phone REGEXP '^[+]?[0-9]{10,15}$'),
ADD CONSTRAINT chk_users_email_format CHECK (email IS NULL OR email REGEXP '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');

-- Payments table constraints
ALTER TABLE payments 
ADD CONSTRAINT chk_payments_amount CHECK (amount > 0),
ADD CONSTRAINT chk_payments_refund_amount CHECK (refund_amount >= 0 AND refund_amount <= amount),
ADD CONSTRAINT chk_payments_currency_length CHECK (CHAR_LENGTH(currency) = 3);

-- Services table constraints
ALTER TABLE services 
ADD CONSTRAINT chk_services_volume CHECK (volume > 0),
ADD CONSTRAINT chk_services_used_volume CHECK (used_volume >= 0),
ADD CONSTRAINT chk_services_expires_future CHECK (expires_at > created_at);

-- ==============================================
-- PERFORMANCE INDEXES FOR SPECIFIC QUERIES
-- ==============================================

-- Indexes for dashboard queries
ALTER TABLE users 
ADD INDEX IF NOT EXISTS idx_dashboard_user (id, status, balance, subscription_status);

-- Indexes for admin panel queries
ALTER TABLE users 
ADD INDEX IF NOT EXISTS idx_admin_users (role, status, created_at);

ALTER TABLE payments 
ADD INDEX IF NOT EXISTS idx_admin_payments (status, payment_method, created_at, amount);

ALTER TABLE services 
ADD INDEX IF NOT EXISTS idx_admin_services (status, server_id, created_at, expires_at);

-- Indexes for reporting queries
ALTER TABLE payments 
ADD INDEX IF NOT EXISTS idx_revenue_report (completed_at, amount, payment_method),
ADD INDEX IF NOT EXISTS idx_daily_revenue (DATE(completed_at), status, amount);

ALTER TABLE services 
ADD INDEX IF NOT EXISTS idx_usage_report (created_at, volume, used_volume, status);

-- ==============================================
-- FULL-TEXT SEARCH INDEXES
-- ==============================================

-- Full-text search for users
ALTER TABLE users 
ADD FULLTEXT INDEX ft_users_search (first_name, last_name, username);

-- Full-text search for tickets (if exists)
ALTER TABLE tickets 
ADD FULLTEXT INDEX ft_tickets_search (subject, message);

-- ==============================================
-- PARTITIONING FOR LARGE TABLES
-- ==============================================

-- Partition payments table by month (for better performance on large datasets)
-- Note: This requires the table to be empty or use pt-online-schema-change
/*
ALTER TABLE payments 
PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    PARTITION p202503 VALUES LESS THAN (202504),
    PARTITION p202504 VALUES LESS THAN (202505),
    PARTITION p202505 VALUES LESS THAN (202506),
    PARTITION p202506 VALUES LESS THAN (202507),
    PARTITION p202507 VALUES LESS THAN (202508),
    PARTITION p202508 VALUES LESS THAN (202509),
    PARTITION p202509 VALUES LESS THAN (202510),
    PARTITION p202510 VALUES LESS THAN (202511),
    PARTITION p202511 VALUES LESS THAN (202512),
    PARTITION p202512 VALUES LESS THAN (202513),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
*/

-- ==============================================
-- MATERIALIZED VIEWS FOR COMPLEX QUERIES
-- ==============================================

-- Create summary tables for better performance
CREATE TABLE IF NOT EXISTS user_statistics_cache (
    user_id BIGINT UNSIGNED PRIMARY KEY,
    total_payments DECIMAL(10,2) DEFAULT 0,
    successful_payments INT DEFAULT 0,
    failed_payments INT DEFAULT 0,
    active_services INT DEFAULT 0,
    total_volume BIGINT DEFAULT 0,
    used_volume BIGINT DEFAULT 0,
    last_payment_at TIMESTAMP NULL,
    last_service_at TIMESTAMP NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_total_payments (total_payments),
    INDEX idx_active_services (active_services),
    INDEX idx_updated (updated_at)
);

-- Create daily statistics table
CREATE TABLE IF NOT EXISTS daily_statistics (
    date DATE PRIMARY KEY,
    new_users INT DEFAULT 0,
    total_payments DECIMAL(10,2) DEFAULT 0,
    successful_payments INT DEFAULT 0,
    failed_payments INT DEFAULT 0,
    new_services INT DEFAULT 0,
    active_services INT DEFAULT 0,
    total_volume BIGINT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_date_payments (date, total_payments),
    INDEX idx_date_users (date, new_users)
);

-- ==============================================
-- STORED PROCEDURES FOR MAINTENANCE
-- ==============================================

DELIMITER $$

-- Procedure to update user statistics cache
CREATE PROCEDURE UpdateUserStatistics(IN target_user_id BIGINT)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    INSERT INTO user_statistics_cache (
        user_id, 
        total_payments, 
        successful_payments, 
        failed_payments,
        active_services,
        total_volume,
        used_volume,
        last_payment_at,
        last_service_at
    )
    SELECT 
        u.id,
        COALESCE(SUM(CASE WHEN p.status = 'completed' THEN p.amount ELSE 0 END), 0),
        COUNT(CASE WHEN p.status = 'completed' THEN 1 END),
        COUNT(CASE WHEN p.status = 'failed' THEN 1 END),
        COUNT(CASE WHEN s.status = 'active' AND s.expires_at > NOW() THEN 1 END),
        COALESCE(SUM(s.volume), 0),
        COALESCE(SUM(s.used_volume), 0),
        MAX(p.completed_at),
        MAX(s.created_at)
    FROM users u
    LEFT JOIN payments p ON u.id = p.user_id
    LEFT JOIN services s ON u.id = s.user_id
    WHERE u.id = target_user_id
    GROUP BY u.id
    ON DUPLICATE KEY UPDATE
        total_payments = VALUES(total_payments),
        successful_payments = VALUES(successful_payments),
        failed_payments = VALUES(failed_payments),
        active_services = VALUES(active_services),
        total_volume = VALUES(total_volume),
        used_volume = VALUES(used_volume),
        last_payment_at = VALUES(last_payment_at),
        last_service_at = VALUES(last_service_at),
        updated_at = CURRENT_TIMESTAMP;
    
    COMMIT;
END$$

-- Procedure to cleanup old data
CREATE PROCEDURE CleanupOldData()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Delete old sessions (older than 30 days)
    DELETE FROM sessions WHERE expires_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- Delete old verification codes (older than 24 hours)
    UPDATE users SET 
        verification_code = NULL, 
        verification_expires_at = NULL 
    WHERE verification_expires_at < NOW();
    
    -- Delete old failed payments (older than 90 days)
    DELETE FROM payments 
    WHERE status = 'failed' 
    AND created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
    
    COMMIT;
END$$

DELIMITER ;

-- ==============================================
-- TRIGGERS FOR AUTOMATIC STATISTICS
-- ==============================================

-- Trigger to update user statistics when payment is completed
DELIMITER $$
CREATE TRIGGER payment_completed_stats
    AFTER UPDATE ON payments
    FOR EACH ROW
BEGIN
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        CALL UpdateUserStatistics(NEW.user_id);
    END IF;
END$$
DELIMITER ;

-- ==============================================
-- SCHEDULED EVENTS FOR MAINTENANCE
-- ==============================================

-- Event to cleanup old data daily
CREATE EVENT IF NOT EXISTS daily_cleanup
ON SCHEDULE EVERY 1 DAY
STARTS TIMESTAMP(CURDATE() + INTERVAL 1 DAY, '03:00:00')
DO CALL CleanupOldData();

-- Event to update all user statistics weekly
CREATE EVENT IF NOT EXISTS weekly_stats_update
ON SCHEDULE EVERY 1 WEEK
STARTS TIMESTAMP(CURDATE() + INTERVAL 1 DAY, '04:00:00')
DO BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE user_id BIGINT;
    DECLARE user_cursor CURSOR FOR SELECT id FROM users WHERE status = 'active';
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN user_cursor;
    read_loop: LOOP
        FETCH user_cursor INTO user_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        CALL UpdateUserStatistics(user_id);
    END LOOP;
    CLOSE user_cursor;
END;

-- Enable event scheduler
SET GLOBAL event_scheduler = ON;
