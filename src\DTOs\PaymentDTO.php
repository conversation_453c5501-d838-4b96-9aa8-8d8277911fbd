<?php

declare(strict_types=1);

namespace WeBot\DTOs;

/**
 * Payment DTO
 *
 * Data transfer object for payment processing.
 * Handles validation and data transformation for payments.
 *
 * @package WeBot\DTOs
 * @version 2.0
 */
class PaymentDTO extends BaseDTO
{
    /**
     * Validate payment data
     */
    protected function validate(): void
    {
        $this->validateRequired(['user_id', 'amount', 'gateway']);
        $this->validateNumeric('user_id', 1);
        $this->validateNumeric('amount', 1000); // Minimum 1000 Toman
        $this->validateIn('gateway', ['zarinpal', 'idpay', 'nextpay', 'zibal', 'wallet']);
        $this->validateLength('description', 0, 255);

        if ($this->has('status')) {
            $this->validateIn('status', ['pending', 'paid', 'failed', 'cancelled', 'refunded']);
        }

        if ($this->has('type')) {
            $this->validateIn('type', ['service_purchase', 'service_renewal', 'wallet_charge', 'plan_upgrade']);
        }
    }

    /**
     * Get validation rules
     */
    protected function rules(): array
    {
        return [
            'user_id' => 'required|integer|min:1',
            'amount' => 'required|numeric|min:1000',
            'gateway' => 'required|in:zarinpal,idpay,nextpay,zibal,wallet',
            'description' => 'string|max:255',
            'status' => 'in:pending,paid,failed,cancelled,refunded',
            'type' => 'in:service_purchase,service_renewal,wallet_charge,plan_upgrade',
            'service_id' => 'integer|min:1',
            'plan_id' => 'integer|min:1'
        ];
    }

    /**
     * Get field labels
     */
    protected function labels(): array
    {
        return [
            'user_id' => 'شناسه کاربر',
            'amount' => 'مبلغ',
            'gateway' => 'درگاه پرداخت',
            'description' => 'توضیحات',
            'status' => 'وضعیت',
            'type' => 'نوع پرداخت',
            'service_id' => 'شناسه سرویس',
            'plan_id' => 'شناسه پلن'
        ];
    }

    /**
     * Get user ID
     */
    public function getUserId(): int
    {
        return (int)$this->get('user_id');
    }

    /**
     * Get amount in Toman
     */
    public function getAmount(): int
    {
        return (int)$this->get('amount');
    }

    /**
     * Get amount in Rial
     */
    public function getAmountRial(): int
    {
        return $this->getAmount() * 10;
    }

    /**
     * Get formatted amount
     */
    public function getFormattedAmount(): string
    {
        return number_format($this->getAmount()) . ' تومان';
    }

    /**
     * Get gateway
     */
    public function getGateway(): string
    {
        return $this->get('gateway');
    }

    /**
     * Get gateway title
     */
    public function getGatewayTitle(): string
    {
        return match ($this->getGateway()) {
            'zarinpal' => 'زرین‌پال',
            'idpay' => 'آیدی‌پی',
            'nextpay' => 'نکست‌پی',
            'zibal' => 'زیبال',
            'wallet' => 'کیف پول',
            default => 'نامشخص'
        };
    }

    /**
     * Get description
     */
    public function getDescription(): string
    {
        return $this->get('description', '');
    }

    /**
     * Get status
     */
    public function getStatus(): string
    {
        return $this->get('status', 'pending');
    }

    /**
     * Get status title
     */
    public function getStatusTitle(): string
    {
        return match ($this->getStatus()) {
            'pending' => 'در انتظار پرداخت',
            'paid' => 'پرداخت شده',
            'failed' => 'ناموفق',
            'cancelled' => 'لغو شده',
            'refunded' => 'بازگشت داده شده',
            default => 'نامشخص'
        };
    }

    /**
     * Get status icon
     */
    public function getStatusIcon(): string
    {
        return match ($this->getStatus()) {
            'pending' => '🟡',
            'paid' => '🟢',
            'failed' => '🔴',
            'cancelled' => '⚫',
            'refunded' => '🔵',
            default => '⚪'
        };
    }

    /**
     * Get payment type
     */
    public function getType(): string
    {
        return $this->get('type', 'service_purchase');
    }

    /**
     * Get type title
     */
    public function getTypeTitle(): string
    {
        return match ($this->getType()) {
            'service_purchase' => 'خرید سرویس',
            'service_renewal' => 'تمدید سرویس',
            'wallet_charge' => 'شارژ کیف پول',
            'plan_upgrade' => 'ارتقاء پلن',
            default => 'نامشخص'
        };
    }

    /**
     * Get service ID
     */
    public function getServiceId(): ?int
    {
        $serviceId = $this->get('service_id');
        return $serviceId ? (int)$serviceId : null;
    }

    /**
     * Get plan ID
     */
    public function getPlanId(): ?int
    {
        $planId = $this->get('plan_id');
        return $planId ? (int)$planId : null;
    }

    /**
     * Check if payment is wallet payment
     */
    public function isWalletPayment(): bool
    {
        return $this->getGateway() === 'wallet';
    }

    /**
     * Check if payment is successful
     */
    public function isSuccessful(): bool
    {
        return $this->getStatus() === 'paid';
    }

    /**
     * Check if payment is pending
     */
    public function isPending(): bool
    {
        return $this->getStatus() === 'pending';
    }

    /**
     * Generate payment reference
     */
    public function generateReference(): string
    {
        $userId = $this->getUserId();
        $timestamp = time();
        return "WB_{$userId}_{$timestamp}";
    }

    /**
     * Convert to database array
     */
    public function toDatabaseArray(): array
    {
        return [
            'user_id' => $this->getUserId(),
            'amount' => $this->getAmount(),
            'gateway' => $this->getGateway(),
            'description' => $this->getDescription(),
            'status' => $this->getStatus(),
            'type' => $this->getType(),
            'service_id' => $this->getServiceId(),
            'plan_id' => $this->getPlanId(),
            'reference' => $this->generateReference(),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Create from service purchase
     */
    public static function fromServicePurchase(int $userId, int $amount, int $planId, string $gateway = 'zarinpal'): self
    {
        return new self([
            'user_id' => $userId,
            'amount' => $amount,
            'gateway' => $gateway,
            'type' => 'service_purchase',
            'plan_id' => $planId,
            'description' => 'خرید سرویس VPN',
            'status' => 'pending'
        ]);
    }

    /**
     * Create from wallet charge
     */
    public static function fromWalletCharge(int $userId, int $amount, string $gateway = 'zarinpal'): self
    {
        return new self([
            'user_id' => $userId,
            'amount' => $amount,
            'gateway' => $gateway,
            'type' => 'wallet_charge',
            'description' => 'شارژ کیف پول',
            'status' => 'pending'
        ]);
    }
}
