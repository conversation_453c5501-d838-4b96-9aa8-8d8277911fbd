<?php

declare(strict_types=1);

namespace WeBot\Core;

use WeBot\Models\BaseModel;
use WeBot\Models\User;
use WeBot\Models\Payment;
use WeBot\Models\Service;
use WeBot\Models\Panel;

/**
 * Model Factory
 *
 * Factory for creating model instances with proper dependencies.
 *
 * @package WeBot\Core
 * @version 2.0
 */
class ModelFactory
{
    private DIContainer $container;

    public function __construct(DIContainer $container)
    {
        $this->container = $container;
    }

    /**
     * Create User model
     */
    public function createUser(array $attributes = []): User
    {
        $database = $this->container->make(Database::class);
        $user = new User($database);

        if (!empty($attributes)) {
            $user->fill($attributes);
        }

        return $user;
    }

    /**
     * Create Payment model
     */
    public function createPayment(array $attributes = []): Payment
    {
        $database = $this->container->make(Database::class);
        $payment = new Payment($database);

        if (!empty($attributes)) {
            $payment->fill($attributes);
        }

        return $payment;
    }

    /**
     * Create Service model
     */
    public function createService(array $attributes = []): Service
    {
        $database = $this->container->make(Database::class);
        $service = new Service($database);

        if (!empty($attributes)) {
            $service->fill($attributes);
        }

        return $service;
    }

    /**
     * Create Panel model
     */
    public function createPanel(array $attributes = []): Panel
    {
        $database = $this->container->make(Database::class);
        $panel = new Panel($database);

        if (!empty($attributes)) {
            $panel->fill($attributes);
        }

        return $panel;
    }

    /**
     * Create any model by class name
     */
    public function create(string $modelClass, array $attributes = []): BaseModel
    {
        $database = $this->container->make(Database::class);

        if (!class_exists($modelClass)) {
            throw new \InvalidArgumentException("Model class {$modelClass} not found");
        }

        if (!is_subclass_of($modelClass, BaseModel::class)) {
            throw new \InvalidArgumentException("Class {$modelClass} must extend BaseModel");
        }

        $model = new $modelClass($database);

        if (!empty($attributes)) {
            $model->fill($attributes);
        }

        return $model;
    }

    /**
     * Find model by ID
     */
    public function find(string $modelClass, int $id): ?BaseModel
    {
        $model = $this->create($modelClass);
        return $model->find($id);
    }

    /**
     * Create model and save to database
     */
    public function createAndSave(string $modelClass, array $attributes): BaseModel
    {
        $model = $this->create($modelClass, $attributes);
        $model->save();
        return $model;
    }
}
