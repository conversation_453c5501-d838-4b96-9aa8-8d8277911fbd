# phpstan.neon
parameters:
    level: 5
    paths:
        - src
    bootstrapFiles:
        - tests/phpstan-bootstrap.php
    excludePaths:
        - src/Legacy/*
        - vendor/*
    ignoreErrors:
        # This error is frequent due to dynamic properties in some models/DTOs.
        # Can be fixed later by using __get and __set or explicit properties.
        - '#Access to an undefined property WeBot\\.*::\$w+#'

        # This indicates a potential issue where a method might return a more specific type
        # than its interface definition. We can ignore it for now.
        - '#Return type of method .* should be compatible with#'
        
        # Caused by older style code not using modern PHP features.
        - '#PHPDoc tag @package has invalid value#'
