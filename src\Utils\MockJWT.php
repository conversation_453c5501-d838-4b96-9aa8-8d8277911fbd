<?php

declare(strict_types=1);

namespace WeBot\Utils;

/**
 * Mock JWT for Firebase\JWT\JWT
 * Used when Firebase JWT is not available
 */
class MockJWT
{
    /**
     * Encode a payload into a JWT token
     */
    public static function encode(array $payload, string $key, string $alg = 'HS256'): string
    {
        unset($alg); // Suppress unused parameter warning

        // Simple mock implementation
        $header = base64_encode(json_encode(['typ' => 'JWT', 'alg' => 'HS256']));
        $payload = base64_encode(json_encode($payload));
        $signature = base64_encode(hash_hmac('sha256', $header . '.' . $payload, $key, true));

        return $header . '.' . $payload . '.' . $signature;
    }

    /**
     * Decode a JWT token
     */
    public static function decode(string $jwt, $key, array $allowed_algs = ['HS256']): object
    {
        unset($allowed_algs); // Suppress unused parameter warning

        $parts = explode('.', $jwt);
        if (count($parts) !== 3) {
            throw new \Exception('Invalid JWT format');
        }

        [$header, $payload, $signature] = $parts;

        // Verify signature (simplified)
        $expectedSignature = base64_encode(hash_hmac('sha256', $header . '.' . $payload, $key instanceof MockKey ? $key->getKey() : $key, true));
        if ($signature !== $expectedSignature) {
            throw new \Exception('Invalid JWT signature');
        }

        $decodedPayload = json_decode(base64_decode($payload), true);
        if (!$decodedPayload) {
            throw new \Exception('Invalid JWT payload');
        }

        // Check expiration
        if (isset($decodedPayload['exp']) && $decodedPayload['exp'] < time()) {
            throw new \Exception('JWT token has expired');
        }

        return (object) $decodedPayload;
    }
}

/**
 * Mock Key for Firebase\JWT\Key
 */
class MockKey
{
    private string $key;
    private string $algorithm;

    public function __construct(string $key, string $algorithm)
    {
        $this->key = $key;
        $this->algorithm = $algorithm;
    }

    public function getKey(): string
    {
        return $this->key;
    }

    public function getAlgorithm(): string
    {
        return $this->algorithm;
    }
}
