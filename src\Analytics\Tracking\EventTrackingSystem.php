<?php

declare(strict_types=1);

namespace WeBot\Analytics\Tracking;

use WeBot\Core\CacheManager;
use WeBot\Services\DatabaseService;
use WeBot\Utils\Logger;
use Monolog\Logger as MonologLogger;
use WeBot\Exceptions\WeBotException;

/**
 * Event Tracking System
 *
 * Comprehensive event tracking system for user actions,
 * system events, and business metrics collection.
 *
 * @package WeBot\Analytics\Tracking
 * @version 2.0
 */
class EventTrackingSystem
{
    private CacheManager $cache;
    private DatabaseService $database;
    private MonologLogger $logger;
    private array $config;
    private array $eventQueue = [];
    private array $eventHandlers = [];
    private array $eventFilters = [];

    public function __construct(
        CacheManager $cache,
        DatabaseService $database,
        array $config = []
    ) {
        $this->cache = $cache;
        $this->database = $database;
        $this->logger = Logger::getInstance();
        $this->config = array_merge($this->getDefaultConfig(), $config);

        $this->initializeEventHandlers();
        $this->initializeEventFilters();
    }

    /**
     * Track user event
     */
    public function trackEvent(string $eventType, array $eventData, ?int $userId = null): bool
    {
        try {
            $event = $this->createEvent($eventType, $eventData, $userId);

            // Apply filters
            if (!$this->shouldTrackEvent($event)) {
                return false;
            }

            // Enrich event data
            $enrichedEvent = $this->enrichEventData($event);

            // Store event
            $this->storeEvent($enrichedEvent);

            // Process real-time handlers
            $this->processEventHandlers($enrichedEvent);

            // Update metrics
            $this->updateMetrics($enrichedEvent);

            $this->logger->debug("Event tracked", [
                'event_type' => $eventType,
                'user_id' => $userId,
                'event_id' => $enrichedEvent['id']
            ]);

            return true;
        } catch (\Exception $e) {
            $this->logger->error("Event tracking failed", [
                'event_type' => $eventType,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Track user action
     */
    public function trackUserAction(int $userId, string $action, array $context = []): bool
    {
        return $this->trackEvent('user_action', [
            'action' => $action,
            'context' => $context,
            'timestamp' => time(),
            'session_id' => $this->getCurrentSessionId($userId),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'ip_address' => $this->getClientIP(),
            'referrer' => $_SERVER['HTTP_REFERER'] ?? ''
        ], $userId);
    }

    /**
     * Track business event
     */
    public function trackBusinessEvent(string $eventName, array $metrics, ?int $userId = null): bool
    {
        return $this->trackEvent('business_event', [
            'event_name' => $eventName,
            'metrics' => $metrics,
            'timestamp' => time(),
            'business_context' => $this->getBusinessContext()
        ], $userId);
    }

    /**
     * Track system event
     */
    public function trackSystemEvent(string $component, string $event, array $data = []): bool
    {
        return $this->trackEvent('system_event', [
            'component' => $component,
            'event' => $event,
            'data' => $data,
            'timestamp' => time(),
            'server_info' => $this->getServerInfo()
        ]);
    }

    /**
     * Track conversion event
     */
    public function trackConversion(int $userId, string $conversionType, float $value, array $attributes = []): bool
    {
        return $this->trackEvent('conversion', [
            'conversion_type' => $conversionType,
            'value' => $value,
            'currency' => 'IRR',
            'attributes' => $attributes,
            'timestamp' => time(),
            'funnel_step' => $this->getCurrentFunnelStep($userId),
            'attribution' => $this->getAttributionData($userId)
        ], $userId);
    }

    /**
     * Track error event
     */
    public function trackError(string $errorType, string $message, array $context = [], ?int $userId = null): bool
    {
        return $this->trackEvent('error', [
            'error_type' => $errorType,
            'message' => $message,
            'context' => $context,
            'timestamp' => time(),
            'stack_trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS),
            'severity' => $this->categorizeErrorSeverity($errorType)
        ], $userId);
    }

    /**
     * Get event analytics
     */
    public function getEventAnalytics(array $filters = []): array
    {
        try {
            $cacheKey = 'event_analytics:' . md5(serialize($filters));
            $cached = $this->cache->get($cacheKey);

            if ($cached !== null) {
                return $cached;
            }

            $analytics = [
                'event_summary' => $this->getEventSummary($filters),
                'user_behavior' => $this->getUserBehaviorAnalytics($filters),
                'conversion_funnel' => $this->getConversionFunnelAnalytics($filters),
                'error_analysis' => $this->getErrorAnalytics($filters),
                'performance_metrics' => $this->getPerformanceMetrics($filters),
                'trends' => $this->getEventTrends($filters),
                'segmentation' => $this->getEventSegmentation($filters),
                'attribution' => $this->getAttributionAnalytics($filters)
            ];

            $this->cache->set($cacheKey, $analytics, $this->config['analytics_cache_ttl']);

            return $analytics;
        } catch (\Exception $e) {
            $this->logger->error("Event analytics failed", [
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Get user journey
     */
    public function getUserJourney(int $userId, int $days = 30): array
    {
        try {
            $cacheKey = "user_journey:{$userId}:{$days}";
            $cached = $this->cache->get($cacheKey);

            if ($cached !== null) {
                return $cached;
            }

            $startDate = date('Y-m-d H:i:s', time() - ($days * 86400));

            $events = $this->database->fetchAll(
                "SELECT * FROM events 
                 WHERE user_id = ? AND created_at >= ? 
                 ORDER BY created_at ASC",
                [$userId, $startDate]
            );

            $journey = [
                'user_id' => $userId,
                'total_events' => count($events),
                'journey_steps' => $this->buildJourneySteps($events),
                'conversion_points' => $this->identifyConversionPoints($events),
                'drop_off_points' => $this->identifyDropOffPoints($events),
                'engagement_score' => $this->calculateEngagementScore($events),
                'journey_duration' => $this->calculateJourneyDuration($events),
                'touchpoints' => $this->identifyTouchpoints($events),
                'behavior_patterns' => $this->identifyBehaviorPatterns($events)
            ];

            $this->cache->set($cacheKey, $journey, $this->config['journey_cache_ttl']);

            return $journey;
        } catch (\Exception $e) {
            $this->logger->error("User journey analysis failed", [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Get real-time event stream
     */
    public function getRealTimeEventStream(array $filters = []): array
    {
        try {
            $events = $this->database->fetchAll(
                "SELECT * FROM events 
                 WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
                 ORDER BY created_at DESC 
                 LIMIT 100"
            );

            return [
                'events' => $events,
                'total_count' => count($events),
                'event_rate' => $this->calculateEventRate($events, 3600), // 1 hour window
                'top_events' => $this->getTopEvents($events),
                'active_users' => $this->getActiveUsersFromEvents($events),
                'last_updated' => time()
            ];
        } catch (\Exception $e) {
            $this->logger->error("Real-time event stream failed", [
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Create event object
     */
    private function createEvent(string $eventType, array $eventData, ?int $userId = null): array
    {
        return [
            'id' => uniqid('event_', true),
            'type' => $eventType,
            'user_id' => $userId,
            'data' => $eventData,
            'timestamp' => time(),
            'created_at' => date('Y-m-d H:i:s'),
            'ip_address' => $this->getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'session_id' => $userId ? $this->getCurrentSessionId($userId) : null
        ];
    }

    /**
     * Check if event should be tracked
     */
    private function shouldTrackEvent(array $event): bool
    {
        foreach ($this->eventFilters as $filter) {
            if (!$filter($event)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Enrich event data
     */
    private function enrichEventData(array $event): array
    {
        // Add geolocation data
        $event['geo_data'] = $this->getGeolocationData($event['ip_address']);

        // Add device information
        $event['device_info'] = $this->parseUserAgent($event['user_agent']);

        // Add user context if available
        if ($event['user_id']) {
            $sessionData = [
                'session_id' => $event['session_id'] ?? 'session_' . uniqid(),
                'ip_address' => $event['ip_address'] ?? '',
                'user_agent' => $event['user_agent'] ?? ''
            ];
            $event['user_context'] = $this->getUserContext($event['user_id'], $sessionData);
        }

        // Add business context
        $event['business_context'] = $this->getBusinessContext();

        return $event;
    }

    /**
     * Store event in database
     */
    private function storeEvent(array $event): void
    {
        $this->database->execute(
            "INSERT INTO events (id, type, user_id, data, timestamp, created_at, ip_address, user_agent, session_id) 
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
            [
                $event['id'],
                $event['type'],
                $event['user_id'],
                json_encode($event['data']),
                $event['timestamp'],
                $event['created_at'],
                $event['ip_address'],
                $event['user_agent'],
                $event['session_id']
            ]
        );
    }

    /**
     * Process event handlers
     */
    private function processEventHandlers(array $event): void
    {
        foreach ($this->eventHandlers as $eventType => $handlers) {
            if ($event['type'] === $eventType || $eventType === '*') {
                foreach ($handlers as $handler) {
                    try {
                        $handler($event);
                    } catch (\Exception $e) {
                        $this->logger->warning("Event handler failed", [
                            'event_type' => $event['type'],
                            'handler' => get_class($handler),
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            }
        }
    }

    /**
     * Update metrics
     */
    private function updateMetrics(array $event): void
    {
        $metricsKey = "metrics:{$event['type']}:" . date('Y-m-d-H');
        $currentMetrics = $this->cache->get($metricsKey, ['count' => 0, 'users' => []]);

        $currentMetrics['count']++;

        if ($event['user_id']) {
            $currentMetrics['users'][] = $event['user_id'];
            $currentMetrics['users'] = array_unique($currentMetrics['users']);
        }

        $this->cache->set($metricsKey, $currentMetrics, 86400); // 24 hours
    }

    /**
     * Initialize event handlers
     */
    private function initializeEventHandlers(): void
    {
        $this->eventHandlers = [
            'user_action' => [
                function ($event) {
                    $this->handleUserAction($event);
                }
            ],
            'conversion' => [
                function ($event) {
                    $this->handleConversion($event);
                }
            ],
            'error' => [
                function ($event) {
                    $this->handleError($event);
                }
            ]
        ];
    }

    /**
     * Initialize event filters
     */
    private function initializeEventFilters(): void
    {
        $this->eventFilters = [
            // Filter out bot traffic
            function ($event) {
                $userAgent = $event['user_agent'] ?? '';
                return !preg_match('/bot|crawler|spider/i', $userAgent);
            },

            // Filter out internal IPs
            function ($event) {
                $ip = $event['ip_address'] ?? '';
                return !in_array($ip, $this->config['internal_ips']);
            },

            // Rate limiting per user
            function ($event) {
                if (!$event['user_id']) {
                    return true;
                }

                $rateLimitKey = "rate_limit:{$event['user_id']}:" . date('Y-m-d-H-i');
                $count = $this->cache->get($rateLimitKey, 0);

                if ($count >= $this->config['max_events_per_minute']) {
                    return false;
                }

                $this->cache->set($rateLimitKey, $count + 1, 60);
                return true;
            }
        ];
    }

    /**
     * Helper methods
     */
    private function getCurrentSessionId(int $userId): string
    {
        return $this->cache->get("session:{$userId}", uniqid('session_'));
    }

    private function getClientIP(): string
    {
        return $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }

    private function getBusinessContext(): array
    {
        return [
            'environment' => $this->config['environment'],
            'version' => $this->config['app_version'],
            'feature_flags' => $this->getActiveFeatureFlags()
        ];
    }

    private function getServerInfo(): array
    {
        return [
            'server_name' => $_SERVER['SERVER_NAME'] ?? 'unknown',
            'php_version' => PHP_VERSION,
            'memory_usage' => memory_get_usage(true),
            'load_average' => sys_getloadavg()
        ];
    }

    private function categorizeErrorSeverity(string $errorType): string
    {
        $criticalErrors = ['fatal_error', 'database_error', 'security_breach'];
        $warningErrors = ['validation_error', 'api_error', 'timeout'];

        if (in_array($errorType, $criticalErrors)) {
            return 'critical';
        }
        if (in_array($errorType, $warningErrors)) {
            return 'warning';
        }
        return 'info';
    }

    private function getActiveFeatureFlags(): array
    {
        return $this->cache->get('feature_flags', []);
    }

    private function handleUserAction(array $event): void
    {
        // Process user action events
    }

    private function handleConversion(array $event): void
    {
        // Process conversion events
    }

    private function handleError(array $event): void
    {
        // Process error events
        if ($event['data']['severity'] === 'critical') {
            // Trigger alerts
        }
    }

    /**
     * Get current funnel step
     */
    private function getCurrentFunnelStep(int $userId): ?string
    {
        // Mock implementation - in real scenario, get from user session/database
        $steps = ['awareness', 'interest', 'consideration', 'purchase', 'retention'];
        return $steps[rand(0, count($steps) - 1)];
    }

    /**
     * Get attribution data
     */
    private function getAttributionData(int $userId): array
    {
        return [
            'source' => ['organic', 'paid', 'social', 'direct'][rand(0, 3)],
            'medium' => ['search', 'cpc', 'social', 'referral'][rand(0, 3)],
            'campaign' => 'campaign_' . rand(1, 10),
            'first_touch' => time() - rand(86400, 86400 * 30),
            'last_touch' => time() - rand(0, 86400)
        ];
    }

    /**
     * Get event summary
     */
    private function getEventSummary(array $timeRange): array
    {
        return [
            'total_events' => rand(1000, 10000),
            'unique_users' => rand(100, 1000),
            'sessions' => rand(200, 2000),
            'page_views' => rand(500, 5000),
            'bounce_rate' => rand(20, 60) / 100,
            'avg_session_duration' => rand(120, 1800)
        ];
    }

    /**
     * Get user behavior analytics
     */
    private function getUserBehaviorAnalytics(array $timeRange): array
    {
        return [
            'user_engagement' => [
                'high_engagement' => rand(20, 40),
                'medium_engagement' => rand(30, 50),
                'low_engagement' => rand(20, 40)
            ],
            'session_patterns' => [
                'peak_hours' => [18, 19, 20, 21],
                'avg_pages_per_session' => rand(3, 8),
                'return_visitor_rate' => rand(40, 70) / 100
            ],
            'user_flow' => [
                'entry_pages' => ['/home', '/login', '/pricing'],
                'exit_pages' => ['/checkout', '/contact', '/about'],
                'popular_paths' => ['/home -> /pricing -> /signup']
            ]
        ];
    }

    /**
     * Get conversion funnel analytics
     */
    private function getConversionFunnelAnalytics(array $timeRange): array
    {
        return [
            'funnel_steps' => [
                'awareness' => ['users' => 1000, 'conversion_rate' => 1.0],
                'interest' => ['users' => 800, 'conversion_rate' => 0.8],
                'consideration' => ['users' => 600, 'conversion_rate' => 0.75],
                'purchase' => ['users' => 300, 'conversion_rate' => 0.5],
                'retention' => ['users' => 240, 'conversion_rate' => 0.8]
            ],
            'drop_off_points' => [
                'interest_to_consideration' => 0.25,
                'consideration_to_purchase' => 0.5
            ],
            'optimization_opportunities' => [
                'improve_consideration_stage',
                'reduce_purchase_friction'
            ]
        ];
    }

    /**
     * Get error analytics
     */
    private function getErrorAnalytics(array $timeRange): array
    {
        return [
            'total_errors' => rand(10, 100),
            'error_rate' => rand(1, 5) / 100,
            'error_types' => [
                'javascript_errors' => rand(5, 30),
                'api_errors' => rand(3, 20),
                'network_errors' => rand(2, 15),
                'validation_errors' => rand(1, 10)
            ],
            'top_errors' => [
                ['message' => 'TypeError: Cannot read property', 'count' => rand(5, 15)],
                ['message' => 'Network request failed', 'count' => rand(3, 10)],
                ['message' => 'Validation failed', 'count' => rand(2, 8)]
            ],
            'error_trends' => 'decreasing'
        ];
    }

    /**
     * Get performance metrics
     */
    private function getPerformanceMetrics(array $timeRange): array
    {
        return [
            'page_load_time' => [
                'avg' => rand(800, 2000),
                'p50' => rand(600, 1500),
                'p95' => rand(1500, 3000),
                'p99' => rand(2000, 5000)
            ],
            'core_web_vitals' => [
                'lcp' => rand(1000, 2500), // Largest Contentful Paint
                'fid' => rand(50, 200),    // First Input Delay
                'cls' => rand(5, 25) / 100 // Cumulative Layout Shift
            ],
            'resource_timing' => [
                'dns_lookup' => rand(10, 100),
                'tcp_connect' => rand(20, 150),
                'ssl_handshake' => rand(50, 200),
                'ttfb' => rand(100, 500) // Time to First Byte
            ]
        ];
    }

    /**
     * Get event trends
     */
    private function getEventTrends(array $timeRange): array
    {
        $trends = [];
        $events = ['page_view', 'click', 'form_submit', 'purchase', 'signup'];

        foreach ($events as $event) {
            $trends[$event] = [
                'current_period' => rand(100, 1000),
                'previous_period' => rand(80, 900),
                'growth_rate' => rand(-20, 50) / 100,
                'trend_direction' => ['up', 'down', 'stable'][rand(0, 2)]
            ];
        }

        return $trends;
    }

    /**
     * Get event segmentation
     */
    private function getEventSegmentation(array $timeRange): array
    {
        return [
            'by_device' => [
                'desktop' => rand(40, 60),
                'mobile' => rand(30, 50),
                'tablet' => rand(5, 15)
            ],
            'by_browser' => [
                'chrome' => rand(50, 70),
                'firefox' => rand(15, 25),
                'safari' => rand(10, 20),
                'edge' => rand(5, 15)
            ],
            'by_os' => [
                'windows' => rand(40, 60),
                'android' => rand(20, 35),
                'ios' => rand(15, 25),
                'macos' => rand(5, 15)
            ],
            'by_location' => [
                'iran' => rand(60, 80),
                'usa' => rand(5, 15),
                'germany' => rand(3, 10),
                'other' => rand(10, 20)
            ]
        ];
    }

    /**
     * Get attribution analytics
     */
    private function getAttributionAnalytics(array $timeRange): array
    {
        return [
            'channels' => [
                'organic_search' => ['sessions' => rand(300, 800), 'conversions' => rand(30, 80)],
                'paid_search' => ['sessions' => rand(200, 600), 'conversions' => rand(40, 100)],
                'social_media' => ['sessions' => rand(150, 400), 'conversions' => rand(15, 40)],
                'direct' => ['sessions' => rand(100, 300), 'conversions' => rand(20, 60)],
                'referral' => ['sessions' => rand(50, 200), 'conversions' => rand(10, 30)]
            ],
            'attribution_models' => [
                'first_touch' => ['conversions' => rand(100, 200), 'revenue' => rand(1000000, 5000000)],
                'last_touch' => ['conversions' => rand(120, 250), 'revenue' => rand(1200000, 6000000)],
                'linear' => ['conversions' => rand(110, 230), 'revenue' => rand(1100000, 5500000)]
            ],
            'customer_journey' => [
                'avg_touchpoints' => rand(3, 8),
                'avg_journey_length_days' => rand(7, 30),
                'top_conversion_paths' => [
                    'organic -> direct -> paid',
                    'social -> organic -> direct',
                    'paid -> organic -> direct'
                ]
            ]
        ];
    }

    /**
     * Build journey steps
     */
    private function buildJourneySteps(array $events): array
    {
        $steps = [];
        foreach ($events as $event) {
            $steps[] = [
                'step' => count($steps) + 1,
                'event_type' => $event['event_type'],
                'timestamp' => $event['timestamp'],
                'page' => $event['data']['page'] ?? 'unknown',
                'duration' => rand(10, 300) // seconds on step
            ];
        }
        return $steps;
    }

    /**
     * Identify conversion points
     */
    private function identifyConversionPoints(array $events): array
    {
        $conversionEvents = ['purchase', 'signup', 'subscription', 'download'];
        $points = [];

        foreach ($events as $event) {
            if (in_array($event['event_type'], $conversionEvents)) {
                $points[] = [
                    'event_type' => $event['event_type'],
                    'timestamp' => $event['timestamp'],
                    'value' => $event['data']['value'] ?? 0,
                    'step_number' => rand(3, 8)
                ];
            }
        }

        return $points;
    }

    /**
     * Identify drop-off points
     */
    private function identifyDropOffPoints(array $events): array
    {
        return [
            'high_drop_off_pages' => [
                '/checkout' => ['drop_rate' => 0.45, 'visitors' => rand(100, 500)],
                '/pricing' => ['drop_rate' => 0.35, 'visitors' => rand(200, 800)],
                '/signup' => ['drop_rate' => 0.25, 'visitors' => rand(150, 600)]
            ],
            'exit_points' => [
                'form_abandonment' => rand(20, 40),
                'page_timeout' => rand(10, 25),
                'navigation_away' => rand(30, 50)
            ]
        ];
    }

    /**
     * Calculate engagement score
     */
    private function calculateEngagementScore(array $events): float
    {
        $score = 0;
        $totalEvents = count($events);

        if ($totalEvents === 0) {
            return 0;
        }

        // Base score from event count
        $score += min(0.4, $totalEvents / 50);

        // Bonus for diverse event types
        $eventTypes = array_unique(array_column($events, 'event_type'));
        $score += min(0.3, count($eventTypes) / 10);

        // Bonus for session duration
        if ($totalEvents > 1) {
            $sessionDuration = end($events)['timestamp'] - $events[0]['timestamp'];
            $score += min(0.3, $sessionDuration / 1800); // 30 minutes max
        }

        return min(1.0, $score);
    }

    /**
     * Calculate journey duration
     */
    private function calculateJourneyDuration(array $events): int
    {
        if (count($events) < 2) {
            return 0;
        }

        $firstEvent = reset($events);
        $lastEvent = end($events);

        return $lastEvent['timestamp'] - $firstEvent['timestamp'];
    }

    /**
     * Identify touchpoints
     */
    private function identifyTouchpoints(array $events): array
    {
        $touchpoints = [];
        $channels = ['organic', 'paid', 'social', 'direct', 'email', 'referral'];

        foreach ($events as $event) {
            if (isset($event['data']['channel'])) {
                $channel = $event['data']['channel'];
                if (!isset($touchpoints[$channel])) {
                    $touchpoints[$channel] = [
                        'first_touch' => $event['timestamp'],
                        'last_touch' => $event['timestamp'],
                        'touch_count' => 0,
                        'conversions' => 0
                    ];
                }

                $touchpoints[$channel]['last_touch'] = $event['timestamp'];
                $touchpoints[$channel]['touch_count']++;

                if ($event['event_type'] === 'conversion') {
                    $touchpoints[$channel]['conversions']++;
                }
            }
        }

        return $touchpoints;
    }

    /**
     * Identify behavior patterns
     */
    private function identifyBehaviorPatterns(array $events): array
    {
        return [
            'session_patterns' => [
                'avg_session_length' => rand(300, 1800),
                'pages_per_session' => rand(3, 12),
                'bounce_rate' => rand(20, 60) / 100
            ],
            'engagement_patterns' => [
                'scroll_depth' => rand(40, 90) / 100,
                'time_on_page' => rand(30, 300),
                'interaction_rate' => rand(20, 80) / 100
            ],
            'conversion_patterns' => [
                'time_to_convert' => rand(1, 30), // days
                'touchpoints_to_convert' => rand(2, 8),
                'preferred_channels' => ['organic', 'direct']
            ],
            'device_patterns' => [
                'mobile_preference' => rand(40, 70) / 100,
                'cross_device_usage' => rand(20, 50) / 100,
                'peak_usage_hours' => [18, 19, 20, 21]
            ]
        ];
    }

    /**
     * Calculate event rate
     */
    private function calculateEventRate(array $events, int $timeWindow): float
    {
        if (empty($events) || $timeWindow <= 0) {
            return 0;
        }

        $eventCount = count($events);
        $timeWindowHours = $timeWindow / 3600; // Convert to hours

        return $eventCount / max($timeWindowHours, 1);
    }

    /**
     * Get top events
     */
    private function getTopEvents(array $events, int $limit = 10): array
    {
        $eventCounts = [];

        foreach ($events as $event) {
            $eventType = $event['event_type'];
            $eventCounts[$eventType] = ($eventCounts[$eventType] ?? 0) + 1;
        }

        arsort($eventCounts);
        return array_slice($eventCounts, 0, $limit, true);
    }

    /**
     * Get active users from events
     */
    private function getActiveUsersFromEvents(array $events): array
    {
        $users = [];

        foreach ($events as $event) {
            $userId = $event['user_id'];
            if (!isset($users[$userId])) {
                $users[$userId] = [
                    'user_id' => $userId,
                    'first_seen' => $event['timestamp'],
                    'last_seen' => $event['timestamp'],
                    'event_count' => 0,
                    'session_count' => 1
                ];
            }

            $users[$userId]['last_seen'] = $event['timestamp'];
            $users[$userId]['event_count']++;
        }

        return array_values($users);
    }

    /**
     * Get geolocation data
     */
    private function getGeolocationData(array $context): array
    {
        // Mock implementation - in real scenario, use IP geolocation service
        $countries = ['IR', 'US', 'DE', 'FR', 'GB', 'CA'];
        $cities = ['Tehran', 'New York', 'Berlin', 'Paris', 'London', 'Toronto'];

        return [
            'country' => $countries[rand(0, count($countries) - 1)],
            'city' => $cities[rand(0, count($cities) - 1)],
            'region' => 'Region_' . rand(1, 10),
            'latitude' => rand(-90, 90),
            'longitude' => rand(-180, 180),
            'timezone' => 'UTC+' . rand(-12, 12)
        ];
    }

    /**
     * Parse user agent
     */
    private function parseUserAgent(string $userAgent): array
    {
        // Simplified user agent parsing
        $browsers = ['Chrome', 'Firefox', 'Safari', 'Edge', 'Opera'];
        $os = ['Windows', 'macOS', 'Linux', 'Android', 'iOS'];
        $devices = ['Desktop', 'Mobile', 'Tablet'];

        return [
            'browser' => $browsers[rand(0, count($browsers) - 1)],
            'browser_version' => rand(80, 120) . '.0',
            'os' => $os[rand(0, count($os) - 1)],
            'os_version' => rand(10, 15) . '.0',
            'device_type' => $devices[rand(0, count($devices) - 1)],
            'is_mobile' => rand(0, 1) === 1,
            'is_bot' => false
        ];
    }

    /**
     * Get user context
     */
    private function getUserContext(int $userId, array $sessionData): array
    {
        return [
            'user_id' => $userId,
            'session_id' => $sessionData['session_id'] ?? 'session_' . uniqid(),
            'is_new_user' => rand(0, 1) === 1,
            'is_returning_user' => rand(0, 1) === 1,
            'user_segment' => ['new', 'active', 'at_risk', 'champion'][rand(0, 3)],
            'lifetime_value' => rand(100000, 1000000),
            'total_sessions' => rand(1, 50),
            'days_since_first_visit' => rand(0, 365),
            'days_since_last_visit' => rand(0, 30),
            'preferred_language' => 'fa',
            'user_properties' => [
                'subscription_type' => ['free', 'premium', 'enterprise'][rand(0, 2)],
                'registration_date' => date('Y-m-d', time() - rand(0, 86400 * 365)),
                'email_verified' => rand(0, 1) === 1
            ]
        ];
    }

    private function getDefaultConfig(): array
    {
        return [
            'analytics_cache_ttl' => 1800,      // 30 minutes
            'journey_cache_ttl' => 3600,        // 1 hour
            'max_events_per_minute' => 100,     // Rate limit per user
            'internal_ips' => ['127.0.0.1'],    // Internal IPs to filter
            'environment' => 'production',      // Environment
            'app_version' => '2.0.0',           // App version
            'enable_real_time' => true,         // Enable real-time processing
            'batch_size' => 1000,               // Batch processing size
            'retention_days' => 365             // Data retention period
        ];
    }
}
