<?php
/**
 * Environment Configuration Checker for WeBot
 * 
 * This script validates all environment variables and configuration
 * settings required for WeBot to function properly.
 */

declare(strict_types=1);

echo "=== WeBot Environment Configuration Check ===\n\n";

// Load environment variables
if (file_exists('.env')) {
    $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line) || $line[0] === '#') {
            continue;
        }
        if (strpos($line, '=') !== false) {
            [$key, $value] = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value, '"\'');
            if (!empty($key)) {
                $_ENV[$key] = $value;
                putenv("{$key}={$value}");
            }
        }
    }
    echo "✅ .env file loaded successfully\n\n";
} else {
    echo "❌ .env file not found\n";
    exit(1);
}

// Critical environment variables
$criticalVars = [
    'APP_ENV' => ['required' => true, 'values' => ['development', 'testing', 'production']],
    'APP_DEBUG' => ['required' => true, 'values' => ['true', 'false']],
    'TELEGRAM_BOT_TOKEN' => ['required' => true, 'pattern' => '/^\d+:[A-Za-z0-9_-]+$/'],
    'DB_HOST' => ['required' => true],
    'DB_DATABASE' => ['required' => true],
    'DB_USERNAME' => ['required' => true]
];

// Important but optional variables
$optionalVars = [
    'WEBHOOK_URL' => ['pattern' => '/^https?:\/\/.+/'],
    'ADMIN_ID' => ['pattern' => '/^\d+$/'],
    'MARZBAN_MAIN_URL' => ['pattern' => '/^https?:\/\/.+/'],
    'ZARINPAL_MERCHANT_ID' => ['pattern' => '/^[a-f0-9-]+$/']
];

echo "1. Critical Environment Variables Check:\n";
$criticalOk = true;
foreach ($criticalVars as $var => $config) {
    $value = $_ENV[$var] ?? '';
    
    if (empty($value)) {
        if ($config['required']) {
            echo "   ❌ {$var}: Missing (required)\n";
            $criticalOk = false;
        } else {
            echo "   ⚠️  {$var}: Not set (optional)\n";
        }
        continue;
    }
    
    // Check allowed values
    if (isset($config['values']) && !in_array($value, $config['values'])) {
        echo "   ❌ {$var}: Invalid value '{$value}' (allowed: " . implode(', ', $config['values']) . ")\n";
        $criticalOk = false;
        continue;
    }
    
    // Check pattern
    if (isset($config['pattern']) && !preg_match($config['pattern'], $value)) {
        echo "   ❌ {$var}: Invalid format\n";
        $criticalOk = false;
        continue;
    }
    
    // Mask sensitive values
    $displayValue = in_array($var, ['TELEGRAM_BOT_TOKEN', 'DB_PASSWORD']) 
        ? str_repeat('*', min(strlen($value), 8)) 
        : $value;
    
    echo "   ✅ {$var}: {$displayValue}\n";
}

echo "\n2. Optional Environment Variables Check:\n";
foreach ($optionalVars as $var => $config) {
    $value = $_ENV[$var] ?? '';
    
    if (empty($value)) {
        echo "   ⚠️  {$var}: Not set (optional)\n";
        continue;
    }
    
    // Check pattern
    if (isset($config['pattern']) && !preg_match($config['pattern'], $value)) {
        echo "   ⚠️  {$var}: Invalid format (optional)\n";
        continue;
    }
    
    // Mask sensitive values
    $displayValue = strpos($var, 'PASSWORD') !== false || strpos($var, 'TOKEN') !== false || strpos($var, 'KEY') !== false
        ? str_repeat('*', min(strlen($value), 8))
        : $value;
    
    echo "   ✅ {$var}: {$displayValue}\n";
}

echo "\n3. Configuration Consistency Check:\n";
$consistencyOk = true;

// Check if debug mode matches environment
$appEnv = $_ENV['APP_ENV'] ?? 'production';
$appDebug = $_ENV['APP_DEBUG'] ?? 'false';

if ($appEnv === 'production' && $appDebug === 'true') {
    echo "   ⚠️  Debug mode enabled in production environment\n";
}

// Check if required panels are configured
$panelsConfigured = 0;
if (!empty($_ENV['MARZBAN_MAIN_URL'])) $panelsConfigured++;
if (!empty($_ENV['MARZNESHIN_URL'])) $panelsConfigured++;
if (!empty($_ENV['XUI_URL'])) $panelsConfigured++;

if ($panelsConfigured === 0) {
    echo "   ❌ No VPN panels configured\n";
    $consistencyOk = false;
} else {
    echo "   ✅ {$panelsConfigured} VPN panel(s) configured\n";
}

// Check payment gateways
$paymentsConfigured = 0;
if (!empty($_ENV['ZARINPAL_MERCHANT_ID'])) $paymentsConfigured++;
if (!empty($_ENV['NEXTPAY_API_KEY'])) $paymentsConfigured++;

echo "   ℹ️  {$paymentsConfigured} payment gateway(s) configured\n";

echo "\n=== Overall Status ===\n";
if ($criticalOk && $consistencyOk) {
    echo "✅ Environment configuration is valid!\n";
    echo "ℹ️  Environment: {$appEnv}\n";
    echo "ℹ️  Debug mode: {$appDebug}\n";
    exit(0);
} else {
    echo "❌ Environment configuration has issues.\n";
    echo "\n🔧 To fix configuration issues:\n";
    echo "   1. Copy .env.example to .env if not exists\n";
    echo "   2. Set all required variables\n";
    echo "   3. Configure at least one VPN panel\n";
    echo "   4. Set proper Telegram bot token\n";
    echo "   5. Configure database connection\n";
    exit(1);
}
