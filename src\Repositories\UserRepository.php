<?php

declare(strict_types=1);

namespace WeBot\Repositories;

use WeBot\Core\Database;
use WeBot\Models\User;
use WeBot\Exceptions\UserNotFoundException;
use WeBot\Exceptions\DatabaseException;

/**
 * User Repository
 *
 * Handles all database operations related to users
 * including CRUD operations, search, and statistics.
 *
 * @package WeBot\Repositories
 * @version 2.0
 */
class UserRepository
{
    private Database $database;
    private string $table = 'users';

    public function __construct(Database $database)
    {
        $this->database = $database;
    }

    /**
     * Find user by ID
     */
    public function findById(int $id): ?User
    {
        $sql = "SELECT * FROM {$this->table} WHERE id = ? AND deleted_at IS NULL";
        $result = $this->database->query($sql, [$id]);

        return $result ? $this->mapToUser($result[0]) : null;
    }

    /**
     * Find user by Telegram ID
     */
    public function findByTelegramId(int $telegramId): ?User
    {
        $sql = "SELECT * FROM {$this->table} WHERE telegram_id = ? AND deleted_at IS NULL";
        $result = $this->database->query($sql, [$telegramId]);

        return $result ? $this->mapToUser($result[0]) : null;
    }

    /**
     * Find user by username
     */
    public function findByUsername(string $username): ?User
    {
        $sql = "SELECT * FROM {$this->table} WHERE username = ? AND deleted_at IS NULL";
        $result = $this->database->query($sql, [$username]);

        return $result ? $this->mapToUser($result[0]) : null;
    }

    /**
     * Find user by referral code
     */
    public function findByReferralCode(string $referralCode): ?User
    {
        $sql = "SELECT * FROM {$this->table} WHERE referral_code = ? AND deleted_at IS NULL";
        $result = $this->database->query($sql, [$referralCode]);

        return $result ? $this->mapToUser($result[0]) : null;
    }

    /**
     * Create new user
     */
    public function create(array $data): User
    {
        $requiredFields = ['telegram_id'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                throw new \InvalidArgumentException("Required field '{$field}' is missing");
            }
        }

        // Generate referral code if not provided
        if (!isset($data['referral_code'])) {
            $data['referral_code'] = $this->generateReferralCode();
        }

        $fields = array_keys($data);
        $placeholders = str_repeat('?,', count($fields) - 1) . '?';

        $sql = "INSERT INTO {$this->table} (" . implode(',', $fields) . ") VALUES ({$placeholders})";

        try {
            $this->database->execute($sql, array_values($data));
            $userId = $this->database->lastInsertId();

            return $this->findById($userId);
        } catch (\Exception $e) {
            throw new DatabaseException("Failed to create user: " . $e->getMessage());
        }
    }

    /**
     * Update user
     */
    public function update(int $id, array $data): bool
    {
        if (empty($data)) {
            return true;
        }

        // Remove fields that shouldn't be updated directly
        unset($data['id'], $data['created_at'], $data['updated_at']);

        $fields = array_keys($data);
        $setClause = implode(' = ?, ', $fields) . ' = ?';

        $sql = "UPDATE {$this->table} SET {$setClause} WHERE id = ? AND deleted_at IS NULL";
        $params = array_merge(array_values($data), [$id]);

        try {
            return $this->database->execute($sql, $params) > 0;
        } catch (\Exception $e) {
            throw new DatabaseException("Failed to update user: " . $e->getMessage());
        }
    }

    /**
     * Updates last activity timestamp for a user
     */
    public function updateLastActivity(int $userId): bool
    {
        $sql = "UPDATE {$this->table} SET last_login_at = ? WHERE id = ?";
        try {
            return $this->database->execute($sql, [date('Y-m-d H:i:s'), $userId]) > 0;
        } catch (\Exception $e) {
            throw new DatabaseException("Failed to update last activity: " . $e->getMessage());
        }
    }

    /**
     * Soft delete user
     */
    public function delete(int $id): bool
    {
        $sql = "UPDATE {$this->table} SET deleted_at = CURRENT_TIMESTAMP WHERE id = ?";

        try {
            return $this->database->execute($sql, [$id]) > 0;
        } catch (\Exception $e) {
            throw new DatabaseException("Failed to delete user: " . $e->getMessage());
        }
    }

    /**
     * Get all users with pagination
     */
    public function getAll(int $page = 1, int $limit = 50, array $filters = []): array
    {
        $offset = ($page - 1) * $limit;
        $whereConditions = ['deleted_at IS NULL'];
        $params = [];

        // Apply filters
        if (!empty($filters['status'])) {
            $whereConditions[] = 'status = ?';
            $params[] = $filters['status'];
        }

        if (!empty($filters['role'])) {
            $whereConditions[] = 'role = ?';
            $params[] = $filters['role'];
        }

        if (!empty($filters['subscription_status'])) {
            $whereConditions[] = 'subscription_status = ?';
            $params[] = $filters['subscription_status'];
        }

        if (!empty($filters['search'])) {
            $whereConditions[] = '(username LIKE ? OR first_name LIKE ? OR last_name LIKE ?)';
            $searchTerm = '%' . $filters['search'] . '%';
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
        }

        $whereClause = implode(' AND ', $whereConditions);

        $sql = "SELECT * FROM {$this->table} WHERE {$whereClause} ORDER BY created_at DESC LIMIT ? OFFSET ?";
        $params = array_merge($params, [$limit, $offset]);

        $results = $this->database->query($sql, $params);

        return array_map([$this, 'mapToUser'], $results);
    }

    /**
     * Count users with filters
     */
    public function count(array $filters = []): int
    {
        $whereConditions = ['deleted_at IS NULL'];
        $params = [];

        // Apply same filters as getAll
        if (!empty($filters['status'])) {
            $whereConditions[] = 'status = ?';
            $params[] = $filters['status'];
        }

        if (!empty($filters['role'])) {
            $whereConditions[] = 'role = ?';
            $params[] = $filters['role'];
        }

        if (!empty($filters['subscription_status'])) {
            $whereConditions[] = 'subscription_status = ?';
            $params[] = $filters['subscription_status'];
        }

        if (!empty($filters['search'])) {
            $whereConditions[] = '(username LIKE ? OR first_name LIKE ? OR last_name LIKE ?)';
            $searchTerm = '%' . $filters['search'] . '%';
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
        }

        $whereClause = implode(' AND ', $whereConditions);

        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE {$whereClause}";
        $result = $this->database->query($sql, $params);

        return (int) $result[0]['count'];
    }

    /**
     * Update user balance
     */
    public function updateBalance(int $id, float $amount, string $operation = 'add'): bool
    {
        if ($operation === 'add') {
            $sql = "UPDATE {$this->table} SET balance = balance + ? WHERE id = ? AND deleted_at IS NULL";
        } elseif ($operation === 'subtract') {
            $sql = "UPDATE {$this->table} SET balance = balance - ? WHERE id = ? AND deleted_at IS NULL";
        } else {
            $sql = "UPDATE {$this->table} SET balance = ? WHERE id = ? AND deleted_at IS NULL";
        }

        try {
            return $this->database->execute($sql, [$amount, $id]) > 0;
        } catch (\Exception $e) {
            throw new DatabaseException("Failed to update user balance: " . $e->getMessage());
        }
    }

    /**
     * Update user usage statistics
     */
    public function updateUsage(int $id, int $usage): bool
    {
        $sql = "UPDATE {$this->table} SET total_usage = total_usage + ?, monthly_usage = monthly_usage + ? WHERE id = ? AND deleted_at IS NULL";

        try {
            return $this->database->execute($sql, [$usage, $usage, $id]) > 0;
        } catch (\Exception $e) {
            throw new DatabaseException("Failed to update user usage: " . $e->getMessage());
        }
    }

    /**
     * Reset monthly usage for all users
     */
    public function resetMonthlyUsage(): int
    {
        $sql = "UPDATE {$this->table} SET monthly_usage = 0, last_usage_reset = CURRENT_DATE WHERE deleted_at IS NULL";

        try {
            return $this->database->execute($sql);
        } catch (\Exception $e) {
            throw new DatabaseException("Failed to reset monthly usage: " . $e->getMessage());
        }
    }

    /**
     * Get user statistics
     */
    public function getStatistics(): array
    {
        $sql = "
            SELECT 
                COUNT(*) as total_users,
                COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
                COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_users,
                COUNT(CASE WHEN status = 'banned' THEN 1 END) as banned_users,
                COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_users,
                COUNT(CASE WHEN subscription_status = 'premium' THEN 1 END) as premium_users,
                COUNT(CASE WHEN subscription_status = 'vip' THEN 1 END) as vip_users,
                AVG(balance) as average_balance,
                SUM(total_usage) as total_usage,
                COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as new_users_30d
            FROM {$this->table} 
            WHERE deleted_at IS NULL
        ";

        $result = $this->database->query($sql);
        return $result[0];
    }

    /**
     * Get users by referrer
     */
    public function getReferrals(int $userId): array
    {
        $sql = "SELECT * FROM {$this->table} WHERE referred_by = ? AND deleted_at IS NULL ORDER BY created_at DESC";
        $results = $this->database->query($sql, [$userId]);

        return array_map([$this, 'mapToUser'], $results);
    }

    /**
     * Update referral count
     */
    public function updateReferralCount(int $userId): bool
    {
        $sql = "UPDATE {$this->table} SET referral_count = (SELECT COUNT(*) FROM {$this->table} r WHERE r.referred_by = ? AND r.deleted_at IS NULL) WHERE id = ?";

        try {
            return $this->database->execute($sql, [$userId, $userId]) > 0;
        } catch (\Exception $e) {
            throw new DatabaseException("Failed to update referral count: " . $e->getMessage());
        }
    }

    /**
     * Generate unique referral code
     */
    private function generateReferralCode(): string
    {
        do {
            $code = 'WB' . strtoupper(substr(md5(uniqid()), 0, 8));
            $exists = $this->findByReferralCode($code);
        } while ($exists);

        return $code;
    }

    /**
     * Get user statistics
     */
    public function getUserStats(int $userId): array
    {
        $sql = "
            SELECT
                COUNT(CASE WHEN s.status = 'active' THEN 1 END) as active_services,
                COUNT(s.id) as total_services,
                COALESCE(SUM(p.amount), 0) as total_spent,
                COUNT(p.id) as total_payments,
                MAX(p.created_at) as last_payment_date
            FROM {$this->table} u
            LEFT JOIN services s ON u.id = s.user_id AND s.deleted_at IS NULL
            LEFT JOIN payments p ON u.id = p.user_id AND p.status = 'completed'
            WHERE u.id = ? AND u.deleted_at IS NULL
            GROUP BY u.id
        ";

        $result = $this->database->query($sql, [$userId]);

        if (!$result) {
            return [
                'active_services' => 0,
                'total_services' => 0,
                'total_spent' => 0,
                'total_payments' => 0,
                'last_payment_date' => null
            ];
        }

        return [
            'active_services' => (int)$result[0]['active_services'],
            'total_services' => (int)$result[0]['total_services'],
            'total_spent' => (float)$result[0]['total_spent'],
            'total_payments' => (int)$result[0]['total_payments'],
            'last_payment_date' => $result[0]['last_payment_date']
        ];
    }

    /**
     * Search users by a query string
     */
    public function search(string $query, int $limit = 50, int $offset = 0): array
    {
        $conditions = ['deleted_at IS NULL'];
        $params = [];

        if (!empty($query)) {
            $conditions[] = '(username LIKE ? OR first_name LIKE ? OR last_name LIKE ? OR telegram_id = ?)';
            $searchTerm = '%' . $query . '%';
            $params = [$searchTerm, $searchTerm, $searchTerm, $query];
        }

        $whereClause = implode(' AND ', $conditions);
        $sql = "SELECT * FROM {$this->table} WHERE {$whereClause} ORDER BY created_at DESC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;

        $results = $this->database->query($sql, $params);

        return array_map([$this, 'mapToUser'], $results ?: []);
    }

    /**
     * Get user activity
     */
    public function getUserActivity(int $userId, int $days = 30): array
    {
        // Since we don't have user_sessions table, let's get activity from available tables
        $sql = "
            SELECT
                DATE(created_at) as date,
                COUNT(*) as activity_count,
                'service_creation' as activity_type
            FROM services
            WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY DATE(created_at)

            UNION ALL

            SELECT
                DATE(created_at) as date,
                COUNT(*) as activity_count,
                'payment' as activity_type
            FROM payments
            WHERE user_id = ? AND status = 'completed' AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY DATE(created_at)

            ORDER BY date DESC
        ";

        $params = [$userId, $days, $userId, $days];
        $results = $this->database->query($sql, $params);

        return $results ?: [];
    }

    /**
     * Map database row to User model
     */
    private function mapToUser(array $row): User
    {
        return new User([
            'id' => (int) $row['id'],
            'telegram_id' => (int) $row['telegram_id'],
            'username' => $row['username'],
            'first_name' => $row['first_name'],
            'last_name' => $row['last_name'],
            'phone' => $row['phone'],
            'email' => $row['email'],
            'language_code' => $row['language_code'],
            'status' => $row['status'],
            'role' => $row['role'],
            'notifications_enabled' => (bool) $row['notifications_enabled'],
            'timezone' => $row['timezone'],
            'subscription_status' => $row['subscription_status'],
            'subscription_expires_at' => $row['subscription_expires_at'],
            'total_usage' => (int) $row['total_usage'],
            'monthly_usage' => (int) $row['monthly_usage'],
            'last_usage_reset' => $row['last_usage_reset'],
            'balance' => (float) $row['balance'],
            'referral_code' => $row['referral_code'],
            'referred_by' => $row['referred_by'] ? (int) $row['referred_by'] : null,
            'referral_count' => (int) $row['referral_count'],
            'is_verified' => (bool) $row['is_verified'],
            'verification_code' => $row['verification_code'],
            'verification_expires_at' => $row['verification_expires_at'],
            'last_login_at' => $row['last_login_at'],
            'login_attempts' => (int) $row['login_attempts'],
            'locked_until' => $row['locked_until'],
            'created_at' => $row['created_at'],
            'updated_at' => $row['updated_at'],
            'deleted_at' => $row['deleted_at']
        ]);
    }
}
