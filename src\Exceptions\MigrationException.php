<?php

declare(strict_types=1);

namespace WeBot\Exceptions;

/**
 * Migration Exception
 *
 * Exception thrown when database migration operations fail
 *
 * @package WeBot\Exceptions
 * @version 2.0
 */
class MigrationException extends WeBotException
{
    /**
     * Migration file that caused the error
     */
    private ?string $migrationFile = null;

    /**
     * Migration version that failed
     */
    private ?string $migrationVersion = null;

    /**
     * SQL query that failed
     */
    private ?string $failedQuery = null;

    public function __construct(
        string $message = '',
        int $code = 0,
        ?\Throwable $previous = null,
        ?string $migrationFile = null,
        ?string $migrationVersion = null,
        ?string $failedQuery = null
    ) {
        parent::__construct($message, $code, $previous);

        $this->migrationFile = $migrationFile;
        $this->migrationVersion = $migrationVersion;
        $this->failedQuery = $failedQuery;
    }

    /**
     * Get migration file that caused the error
     */
    public function getMigrationFile(): ?string
    {
        return $this->migrationFile;
    }

    /**
     * Get migration version that failed
     */
    public function getMigrationVersion(): ?string
    {
        return $this->migrationVersion;
    }

    /**
     * Get SQL query that failed
     */
    public function getFailedQuery(): ?string
    {
        return $this->failedQuery;
    }

    /**
     * Get detailed error information
     */
    public function getDetailedError(): array
    {
        return [
            'message' => $this->getMessage(),
            'code' => $this->getCode(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'migration_file' => $this->migrationFile,
            'migration_version' => $this->migrationVersion,
            'failed_query' => $this->failedQuery,
            'trace' => $this->getTraceAsString()
        ];
    }

    /**
     * Create exception for migration file not found
     */
    public static function migrationFileNotFound(string $file): self
    {
        return new self(
            "Migration file not found: {$file}",
            404,
            null,
            $file
        );
    }

    /**
     * Create exception for invalid migration format
     */
    public static function invalidMigrationFormat(string $file): self
    {
        return new self(
            "Invalid migration format in file: {$file}",
            400,
            null,
            $file
        );
    }

    /**
     * Create exception for SQL execution failure
     */
    public static function sqlExecutionFailed(string $query, string $error, ?string $file = null): self
    {
        return new self(
            "SQL execution failed: {$error}",
            500,
            null,
            $file,
            null,
            $query
        );
    }

    /**
     * Create exception for migration version conflict
     */
    public static function versionConflict(string $version, string $file): self
    {
        return new self(
            "Migration version conflict: {$version} already exists",
            409,
            null,
            $file,
            $version
        );
    }

    /**
     * Create exception for rollback failure
     */
    public static function rollbackFailed(string $version, string $error): self
    {
        return new self(
            "Migration rollback failed for version {$version}: {$error}",
            500,
            null,
            null,
            $version
        );
    }
}
