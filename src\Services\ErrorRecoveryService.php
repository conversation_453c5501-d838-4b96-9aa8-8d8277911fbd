<?php

declare(strict_types=1);

namespace WeBot\Services;

use WeBot\Utils\Logger;
use WeBot\Exceptions\ErrorRecoveryException;
use WeBot\Core\CacheManager;
use WeBot\Services\DatabaseService;

/**
 * Error Recovery Service
 *
 * Provides automated error recovery mechanisms
 * for common failure scenarios.
 *
 * @package WeBot\Services
 * @version 2.0
 */
class ErrorRecoveryService
{
    private Logger $logger;
    private CacheManager $cache;
    private DatabaseService $database;
    private array $config;
    private array $recoveryHistory = [];

    public function __construct(
        CacheManager $cache,
        DatabaseService $database,
        array $config = []
    ) {
        $this->logger = Logger::getInstance();
        $this->cache = $cache;
        $this->database = $database;
        $this->config = array_merge($this->getDefaultConfig(), $config);
    }

    /**
     * Attempt to recover from an error
     */
    public function attemptRecovery(\Throwable $exception, array $context = []): bool
    {
        $recoveryId = $this->generateRecoveryId($exception);

        // Check if we've already tried to recover from this error recently
        if ($this->hasRecentRecoveryAttempt($recoveryId)) {
            $this->logger->warning('Skipping recovery attempt - recent attempt exists', [
                'recovery_id' => $recoveryId,
                'exception' => get_class($exception)
            ]);
            return false;
        }

        // Record recovery attempt
        $this->recordRecoveryAttempt($recoveryId, $exception, $context);

        // Determine recovery strategy based on exception type
        $strategy = $this->determineRecoveryStrategy($exception);

        if (!$strategy) {
            $this->logger->info('No recovery strategy available', [
                'exception' => get_class($exception),
                'message' => $exception->getMessage()
            ]);
            return false;
        }

        // Execute recovery strategy
        return $this->executeRecoveryStrategy($strategy, $exception, $context);
    }

    /**
     * Determine recovery strategy for exception
     */
    private function determineRecoveryStrategy(\Throwable $exception): ?string
    {
        $message = strtolower($exception->getMessage());
        $class = get_class($exception);

        // Database-related errors
        if (
            strpos($message, 'database') !== false ||
            strpos($message, 'connection') !== false ||
            strpos($message, 'mysql') !== false ||
            strpos($message, 'pdo') !== false
        ) {
            return 'database';
        }

        // API/Network errors
        if (
            strpos($message, 'api') !== false ||
            strpos($message, 'curl') !== false ||
            strpos($message, 'timeout') !== false ||
            strpos($message, 'network') !== false
        ) {
            return 'api';
        }

        // File system errors
        if (
            strpos($message, 'file') !== false ||
            strpos($message, 'directory') !== false ||
            strpos($message, 'permission') !== false ||
            strpos($message, 'disk') !== false
        ) {
            return 'filesystem';
        }

        // Memory errors
        if (
            strpos($message, 'memory') !== false ||
            strpos($message, 'allocation') !== false ||
            $class === 'OutOfMemoryError'
        ) {
            return 'memory';
        }

        // Cache errors
        if (
            strpos($message, 'cache') !== false ||
            strpos($message, 'redis') !== false ||
            strpos($message, 'memcache') !== false
        ) {
            return 'cache';
        }

        return null;
    }

    /**
     * Execute recovery strategy
     */
    private function executeRecoveryStrategy(string $strategy, \Throwable $exception, array $context): bool
    {
        $this->logger->info("Executing recovery strategy: {$strategy}", [
            'exception' => get_class($exception),
            'message' => $exception->getMessage()
        ]);

        try {
            switch ($strategy) {
                case 'database':
                    return $this->recoverDatabase($exception, $context);

                case 'api':
                    return $this->recoverApi($exception, $context);

                case 'filesystem':
                    return $this->recoverFilesystem($exception, $context);

                case 'memory':
                    return $this->recoverMemory($exception, $context);

                case 'cache':
                    return $this->recoverCache($exception, $context);

                default:
                    return false;
            }
        } catch (\Throwable $e) {
            $this->logger->error('Recovery strategy failed', [
                'strategy' => $strategy,
                'original_exception' => get_class($exception),
                'recovery_exception' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Recover from database errors
     */
    private function recoverDatabase(\Throwable $exception, array $context): bool
    {
        $this->logger->info('Attempting database recovery');

        // Strategy 1: Reconnect to database
        try {
            $this->database->reconnect();
            $this->logger->info('Database reconnection successful');
            return true;
        } catch (\Throwable $e) {
            $this->logger->warning('Database reconnection failed', ['error' => $e->getMessage()]);
        }

        // Strategy 2: Switch to read-only mode
        try {
            $this->database->enableReadOnlyMode();
            $this->logger->info('Switched to read-only database mode');
            return true;
        } catch (\Throwable $e) {
            $this->logger->warning('Failed to switch to read-only mode', ['error' => $e->getMessage()]);
        }

        // Strategy 3: Use cached data if available
        if (isset($context['cache_key'])) {
            $cachedData = $this->cache->get($context['cache_key']);
            if ($cachedData !== null) {
                $this->logger->info('Using cached data as fallback');
                return true;
            }
        }

        return false;
    }

    /**
     * Recover from API errors
     */
    private function recoverApi(\Throwable $exception, array $context): bool
    {
        $this->logger->info('Attempting API recovery');

        // Strategy 1: Retry with exponential backoff
        $retryCount = $context['retry_count'] ?? 0;
        if ($retryCount < $this->config['max_api_retries']) {
            $delay = pow(2, $retryCount) * $this->config['api_retry_delay'];
            sleep((int)$delay);
            $this->logger->info("Retrying API call after {$delay} seconds", ['retry_count' => $retryCount + 1]);
            return true;
        }

        // Strategy 2: Use cached response
        if (isset($context['cache_key'])) {
            $cachedResponse = $this->cache->get($context['cache_key']);
            if ($cachedResponse !== null) {
                $this->logger->info('Using cached API response');
                return true;
            }
        }

        // Strategy 3: Use fallback service
        if (isset($context['fallback_url'])) {
            $this->logger->info('Switching to fallback API service');
            return true;
        }

        return false;
    }

    /**
     * Recover from filesystem errors
     */
    private function recoverFilesystem(\Throwable $exception, array $context): bool
    {
        $this->logger->info('Attempting filesystem recovery');

        $path = $context['path'] ?? null;
        if (!$path) {
            return false;
        }

        // Strategy 1: Create missing directories
        $directory = dirname($path);
        if (!is_dir($directory)) {
            if (mkdir($directory, 0755, true)) {
                $this->logger->info("Created missing directory: {$directory}");
                return true;
            }
        }

        // Strategy 2: Fix permissions
        if (file_exists($path) && !is_writable($path)) {
            if (chmod($path, 0644)) {
                $this->logger->info("Fixed permissions for: {$path}");
                return true;
            }
        }

        // Strategy 3: Use temporary location
        $tempPath = sys_get_temp_dir() . '/' . basename($path);
        if (is_writable(sys_get_temp_dir())) {
            $this->logger->info("Using temporary location: {$tempPath}");
            return true;
        }

        return false;
    }

    /**
     * Recover from memory errors
     */
    private function recoverMemory(\Throwable $exception, array $context): bool
    {
        $this->logger->info('Attempting memory recovery');

        // Strategy 1: Force garbage collection
        $memoryBefore = memory_get_usage(true);
        gc_collect_cycles();
        $memoryAfter = memory_get_usage(true);
        $freed = $memoryBefore - $memoryAfter;

        if ($freed > 0) {
            $this->logger->info("Freed {$freed} bytes through garbage collection");
            return true;
        }

        // Strategy 2: Clear caches
        try {
            $this->cache->flush();
            $this->logger->info('Cleared cache to free memory');
            return true;
        } catch (\Throwable $e) {
            $this->logger->warning('Failed to clear cache', ['error' => $e->getMessage()]);
        }

        // Strategy 3: Increase memory limit (if possible)
        $currentLimit = ini_get('memory_limit');
        if ($currentLimit !== '-1') {
            $newLimit = (int)$currentLimit * 2;
            if (ini_set('memory_limit', $newLimit . 'M')) {
                $this->logger->info("Increased memory limit to {$newLimit}M");
                return true;
            }
        }

        return false;
    }

    /**
     * Recover from cache errors
     */
    private function recoverCache(\Throwable $exception, array $context): bool
    {
        $this->logger->info('Attempting cache recovery');

        // Strategy 1: Reconnect to cache server
        try {
            $this->cache->reconnect();
            $this->logger->info('Cache reconnection successful');
            return true;
        } catch (\Throwable $e) {
            $this->logger->warning('Cache reconnection failed', ['error' => $e->getMessage()]);
        }

        // Strategy 2: Disable cache temporarily
        try {
            $this->cache->disable();
            $this->logger->info('Disabled cache temporarily');
            return true;
        } catch (\Throwable $e) {
            $this->logger->warning('Failed to disable cache', ['error' => $e->getMessage()]);
        }

        return false;
    }

    /**
     * Generate recovery ID for tracking
     */
    private function generateRecoveryId(\Throwable $exception): string
    {
        return md5(get_class($exception) . $exception->getMessage() . $exception->getFile() . $exception->getLine());
    }

    /**
     * Check if there's a recent recovery attempt
     */
    private function hasRecentRecoveryAttempt(string $recoveryId): bool
    {
        $cutoff = time() - $this->config['recovery_cooldown'];

        return isset($this->recoveryHistory[$recoveryId]) &&
               $this->recoveryHistory[$recoveryId]['timestamp'] > $cutoff;
    }

    /**
     * Record recovery attempt
     */
    private function recordRecoveryAttempt(string $recoveryId, \Throwable $exception, array $context): void
    {
        $this->recoveryHistory[$recoveryId] = [
            'timestamp' => time(),
            'exception' => get_class($exception),
            'message' => $exception->getMessage(),
            'context' => $context
        ];

        // Clean old recovery history
        $cutoff = time() - ($this->config['recovery_cooldown'] * 2);
        foreach ($this->recoveryHistory as $id => $data) {
            if ($data['timestamp'] < $cutoff) {
                unset($this->recoveryHistory[$id]);
            }
        }
    }

    /**
     * Get recovery statistics
     */
    public function getRecoveryStats(): array
    {
        $stats = [
            'total_attempts' => count($this->recoveryHistory),
            'recent_attempts' => 0,
            'by_type' => []
        ];

        $recentCutoff = time() - 3600; // Last hour

        foreach ($this->recoveryHistory as $data) {
            if ($data['timestamp'] > $recentCutoff) {
                $stats['recent_attempts']++;
            }

            $type = $data['exception'];
            if (!isset($stats['by_type'][$type])) {
                $stats['by_type'][$type] = 0;
            }
            $stats['by_type'][$type]++;
        }

        return $stats;
    }

    /**
     * Clear recovery history
     */
    public function clearRecoveryHistory(): void
    {
        $this->recoveryHistory = [];
        $this->logger->info('Recovery history cleared');
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'recovery_cooldown' => 300, // 5 minutes
            'max_api_retries' => 3,
            'api_retry_delay' => 1, // seconds
            'max_memory_increase' => 2, // multiplier
            'enable_fallback_services' => true,
            'auto_create_directories' => true,
            'auto_fix_permissions' => true
        ];
    }
}
