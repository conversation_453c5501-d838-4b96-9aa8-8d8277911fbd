<?php

/**
 * WeBot v2.0 - Telegram Bot Core Class
 * کلاس اصلی مدیریت ربات تلگرام
 */

declare(strict_types=1);

namespace WeBot\Core;

use Psr\Log\LoggerInterface;
use WeBot\Core\Config;
use WeBot\Core\Database;
use WeBot\Core\CacheManager;
use WeBot\Microservices\Services\UserService;
use WeBot\Repositories\UserRepository;
use WeBot\Services\PanelService;
use WeBot\Services\PaymentService;
use WeBot\Services\DatabaseService;
use Exception;

// TODO: The handler classes (<PERSON><PERSON><PERSON><PERSON>, CallbackHandler, CommandHandler) were not found.
// The logic needs to be refactored, possibly moving routing into this class or controllers.
// use WeBot\Handlers\MessageHandler;
// use WeBot\Handlers\CallbackHandler;
// use WeBot\Handlers\CommandHandler;


class TelegramBot
{
    private Config $config;
    private Database $database;
    private LoggerInterface $logger;
    private string $botToken;
    private string $apiUrl;

    // Handlers have been removed/refactored.
    // private MessageHandler $messageHandler;
    // private CallbackHandler $callbackHandler;
    // private CommandHandler $commandHandler;

    // Services
    private UserService $userService;
    private PanelService $panelService;
    private PaymentService $paymentService;
    private DatabaseService $databaseService;

    public function __construct(Config $config, Database $database, LoggerInterface $logger)
    {
        $this->config = $config;
        $this->database = $database;
        $this->logger = $logger;

        $this->botToken = $config->get('telegram.bot_token');
        if (empty($this->botToken)) {
            throw new Exception('Telegram bot token is not configured');
        }

        $this->apiUrl = "https://api.telegram.org/bot{$this->botToken}/";

        $this->initializeServices();
        // $this->initializeHandlers(); // Handlers are removed
    }

    /**
     * راه‌اندازی سرویس‌ها
     */
    private function initializeServices(): void
    {
        $this->databaseService = new DatabaseService($this->config);

        $cacheManager = new CacheManager($this->config->get('cache', []));
        $userRepository = new UserRepository($this->database);

        // Assuming user service config is at 'services.user' key in config
        $this->userService = new UserService($this->databaseService, $cacheManager, $userRepository, $this->config->get('services.user', []));

        $this->panelService = new PanelService($this->config, $this->databaseService);
        $this->paymentService = new PaymentService($this->config, $this->databaseService);
    }

    /**
     * راه‌اندازی handlers (DEPRECATED)
     */
    // private function initializeHandlers(): void
    // {
    //     // This logic needs to be re-implemented as handlers are missing.
    //     // $this->messageHandler = new MessageHandler($this, $this->userService, $this->logger);
    //     // $this->callbackHandler = new CallbackHandler($this, $this->userService, $this->panelService, $this->logger);
    //     // $this->commandHandler = new CommandHandler($this, $this->userService, $this->panelService, $this->logger);
    // }

    /**
     * پردازش update دریافتی از تلگرام
     */
    public function processUpdate(array $update): bool
    {
        try {
            $this->logger->debug('Processing update', ['update_id' => $update['update_id']]);

            // پردازش انواع مختلف update
            if (isset($update['message'])) {
                return $this->processMessage($update['message']);
            }

            if (isset($update['callback_query'])) {
                return $this->processCallbackQuery($update['callback_query']);
            }

            if (isset($update['inline_query'])) {
                return $this->processInlineQuery($update['inline_query']);
            }

            if (isset($update['pre_checkout_query'])) {
                return $this->processPreCheckoutQuery($update['pre_checkout_query']);
            }

            if (isset($update['message']['successful_payment'])) { // successful_payment is inside message
                return $this->processSuccessfulPayment($update['message']['successful_payment']);
            }

            $this->logger->warning('Unknown update type', ['update' => $update]);
            return false;
        } catch (Exception $e) {
            $this->logger->error('Error processing update: ' . $e->getMessage(), [
                'update_id' => $update['update_id'] ?? 'unknown',
                'exception' => $e
            ]);
            return false;
        }
    }

    /**
     * پردازش پیام
     */
    private function processMessage(array $message): bool
    {
        // TODO: Re-implement routing logic as Handlers are missing.
        // For now, logging the message and returning true.
        $this->logger->info("Message received, but handler logic is missing.", ['message' => $message]);
        return true;

        // Old logic:
        // // بررسی نوع پیام
        // if (isset($message['text'])) {
        //     // بررسی اینکه آیا پیام یک command است
        //     if (str_starts_with($message['text'], '/')) {
        //         return $this->commandHandler->handle($message);
        //     } else {
        //         return $this->messageHandler->handle($message);
        //     }
        // }

        // // پردازش انواع دیگر پیام (عکس، فایل، و غیره)
        // return $this->messageHandler->handle($message);
    }

    /**
     * پردازش callback query
     */
    private function processCallbackQuery(array $callbackQuery): bool
    {
        // TODO: Re-implement routing logic as CallbackHandler is missing.
        $this->logger->info("Callback query received, but handler logic is missing.", ['callback' => $callbackQuery]);
        return true;
        // return $this->callbackHandler->handle($callbackQuery);
    }

    /**
     * پردازش inline query
     */
    private function processInlineQuery(array $inlineQuery): bool
    {
        // TODO: پیاده‌سازی inline query handler
        return true;
    }

    /**
     * پردازش pre checkout query
     */
    private function processPreCheckoutQuery(array $preCheckoutQuery): bool
    {
        // TODO: The method handlePreCheckout does not exist in PaymentService.
        // The bot must answer pre_checkout_query. For now, we approve it directly.
        // A proper implementation should call the payment service to validate the query.
        return (bool)$this->answerPreCheckoutQuery($preCheckoutQuery['id'], true);
    }

    /**
     * پردازش پرداخت موفق
     */
    private function processSuccessfulPayment(array $successfulPayment): bool
    {
        // TODO: The method handleSuccessfulPayment does not exist in PaymentService.
        // The logic should probably be to verify the payment and then credit the user.
        // For now, we just log it.
        $this->logger->info("Successful payment received, verification logic needed.", ['payment' => $successfulPayment]);

        $invoicePayload = $successfulPayment['invoice_payload'] ?? null;
        if ($invoicePayload) {
            // Assuming invoice_payload contains the paymentId.
            // This is an assumption and needs verification.
            // Example of what could be here:
            // return $this->paymentService->verifyPayment((int)$invoicePayload);
        }

        return true;
    }

    /**
     * ارسال پیام
     */
    public function sendMessage(
        int $chatId,
        string $text,
        ?array $replyMarkup = null,
        ?string $parseMode = 'HTML',
        ?int $replyToMessageId = null
    ): ?array {
        $data = [
            'chat_id' => $chatId,
            'text' => $text,
            'parse_mode' => $parseMode
        ];

        if ($replyMarkup) {
            $data['reply_markup'] = json_encode($replyMarkup);
        }

        if ($replyToMessageId) {
            $data['reply_to_message_id'] = $replyToMessageId;
        }

        return $this->makeApiCall('sendMessage', $data);
    }

    /**
     * ویرایش پیام
     */
    public function editMessageText(
        int $chatId,
        int $messageId,
        string $text,
        ?array $replyMarkup = null,
        ?string $parseMode = 'HTML'
    ): ?array {
        $data = [
            'chat_id' => $chatId,
            'message_id' => $messageId,
            'text' => $text,
            'parse_mode' => $parseMode
        ];

        if ($replyMarkup) {
            $data['reply_markup'] = json_encode($replyMarkup);
        }

        return $this->makeApiCall('editMessageText', $data);
    }

    /**
     * پاسخ به callback query
     */
    public function answerCallbackQuery(
        string $callbackQueryId,
        ?string $text = null,
        bool $showAlert = false,
        ?string $url = null
    ): ?array {
        $data = [
            'callback_query_id' => $callbackQueryId,
            'show_alert' => $showAlert
        ];

        if ($text) {
            $data['text'] = $text;
        }

        if ($url) {
            $data['url'] = $url;
        }

        return $this->makeApiCall('answerCallbackQuery', $data);
    }

    /**
     * Answer pre-checkout query
     */
    public function answerPreCheckoutQuery(string $preCheckoutQueryId, bool $ok, ?string $errorMessage = null): ?array
    {
        $data = [
            'pre_checkout_query_id' => $preCheckoutQueryId,
            'ok' => $ok
        ];

        if (!$ok && $errorMessage) {
            $data['error_message'] = $errorMessage;
        }

        return $this->makeApiCall('answerPreCheckoutQuery', $data);
    }

    /**
     * ارسال عکس
     */
    public function sendPhoto(
        int $chatId,
        string $photo,
        ?string $caption = null,
        ?array $replyMarkup = null
    ): ?array {
        $data = [
            'chat_id' => $chatId,
            'photo' => $photo
        ];

        if ($caption) {
            $data['caption'] = $caption;
            $data['parse_mode'] = 'HTML';
        }

        if ($replyMarkup) {
            $data['reply_markup'] = json_encode($replyMarkup);
        }

        return $this->makeApiCall('sendPhoto', $data);
    }

    /**
     * ارسال فایل
     */
    public function sendDocument(
        int $chatId,
        string $document,
        ?string $caption = null,
        ?array $replyMarkup = null
    ): ?array {
        $data = [
            'chat_id' => $chatId,
            'document' => $document
        ];

        if ($caption) {
            $data['caption'] = $caption;
            $data['parse_mode'] = 'HTML';
        }

        if ($replyMarkup) {
            $data['reply_markup'] = json_encode($replyMarkup);
        }

        return $this->makeApiCall('sendDocument', $data);
    }

    /**
     * دریافت اطلاعات کاربر
     */
    public function getChatMember(int $chatId, int $userId): ?array
    {
        return $this->makeApiCall('getChatMember', [
            'chat_id' => $chatId,
            'user_id' => $userId
        ]);
    }

    /**
     * تنظیم webhook
     */
    public function setWebhook(string $url, ?string $secretToken = null): ?array
    {
        $data = ['url' => $url];

        if ($secretToken) {
            $data['secret_token'] = $secretToken;
        }

        return $this->makeApiCall('setWebhook', $data);
    }

    /**
     * حذف webhook
     */
    public function deleteWebhook(): ?array
    {
        return $this->makeApiCall('deleteWebhook');
    }

    /**
     * دریافت اطلاعات webhook
     */
    public function getWebhookInfo(): ?array
    {
        return $this->makeApiCall('getWebhookInfo');
    }

    /**
     * فراخوانی API تلگرام
     */
    private function makeApiCall(string $method, array $data = []): ?array
    {
        try {
            $url = $this->apiUrl . $method;

            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => $data,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_CONNECTTIMEOUT => 10,
                CURLOPT_SSL_VERIFYPEER => true,
                CURLOPT_USERAGENT => 'WeBot/2.0'
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                $this->logger->error("cURL error in $method: $error");
                return null;
            }

            if ($httpCode !== 200) {
                $this->logger->error("HTTP error in $method: $httpCode", ['response' => $response]);
                return null;
            }

            $result = json_decode($response, true);

            if (!$result || !$result['ok']) {
                $this->logger->error("API error in $method", ['result' => $result]);
                return null;
            }

            return $result['result'];
        } catch (Exception $e) {
            $this->logger->error("Exception in $method: " . $e->getMessage());
            return null;
        }
    }

    /**
     * دریافت token ربات
     */
    public function getBotToken(): string
    {
        return $this->botToken;
    }

    /**
     * دریافت config
     */
    public function getConfig(): Config
    {
        return $this->config;
    }

    /**
     * دریافت database
     */
    public function getDatabase(): Database
    {
        return $this->database;
    }

    /**
     * دریافت logger
     */
    public function getLogger(): LoggerInterface
    {
        return $this->logger;
    }
}
