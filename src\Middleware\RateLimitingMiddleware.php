<?php

declare(strict_types=1);

namespace WeBot\Middleware;

use WeBot\Core\CacheManager;

/**
 * Rate Limiting Middleware
 *
 * Implements rate limiting and DDoS protection
 * using sliding window and token bucket algorithms.
 *
 * @package WeBot\Middleware
 * @version 2.0
 */
class RateLimitingMiddleware
{
    private CacheManager $cache;
    private array $config;

    public function __construct(CacheManager $cache, array $config = [])
    {
        $this->cache = $cache;
        $this->config = array_merge([
            'default_limit' => 60,
            'default_window' => 60, // seconds
            'burst_limit' => 10,
            'burst_window' => 1,
            'algorithm' => 'sliding_window', // 'sliding_window', 'token_bucket', 'fixed_window'
            'key_generator' => 'ip', // 'ip', 'user', 'custom'
            'whitelist' => [],
            'blacklist' => [],
            'headers' => [
                'limit' => 'X-RateLimit-Limit',
                'remaining' => 'X-RateLimit-Remaining',
                'reset' => 'X-RateLimit-Reset',
                'retry_after' => 'Retry-After'
            ],
            'skip_successful_requests' => false,
            'skip_failed_requests' => false
        ], $config);
    }

    /**
     * Handle rate limiting middleware
     */
    public function handle(array $request, callable $next): array
    {
        $key = $this->generateKey($request);

        // Check whitelist
        if ($this->isWhitelisted($key, $request)) {
            return $next($request);
        }

        // Check blacklist
        if ($this->isBlacklisted($key, $request)) {
            return $this->rateLimitExceeded($key, 0, 0);
        }

        // Apply rate limiting
        $result = $this->checkRateLimit($key, $request);

        if (!$result['allowed']) {
            return $this->rateLimitExceeded($key, $result['limit'], $result['reset_time']);
        }

        // Process request
        $response = $next($request);

        // Add rate limit headers
        $response = $this->addRateLimitHeaders($response, $result);

        // Update rate limit based on response (if configured)
        $this->updateRateLimitAfterResponse($key, $request, $response);

        return $response;
    }

    /**
     * Check rate limit using configured algorithm
     */
    private function checkRateLimit(string $key, array $request): array
    {
        switch ($this->config['algorithm']) {
            case 'token_bucket':
                return $this->tokenBucketCheck($key, $request);
            case 'fixed_window':
                return $this->fixedWindowCheck($key, $request);
            case 'sliding_window':
            default:
                return $this->slidingWindowCheck($key, $request);
        }
    }

    /**
     * Sliding window rate limiting
     */
    private function slidingWindowCheck(string $key, array $request): array
    {
        $limit = $this->getLimit($request);
        $window = $this->getWindow($request);
        $now = time();

        $cacheKey = "rate_limit:sliding:{$key}";
        $requests = $this->cache->get($cacheKey) ?? [];

        // Remove old requests outside the window
        $requests = array_filter($requests, fn($timestamp) => $now - $timestamp < $window);

        $currentCount = count($requests);

        if ($currentCount >= $limit) {
            $oldestRequest = min($requests);
            $resetTime = $oldestRequest + $window;

            return [
                'allowed' => false,
                'limit' => $limit,
                'remaining' => 0,
                'reset_time' => $resetTime,
                'current_count' => $currentCount
            ];
        }

        // Add current request
        $requests[] = $now;
        $this->cache->set($cacheKey, $requests, $window + 60);

        return [
            'allowed' => true,
            'limit' => $limit,
            'remaining' => $limit - count($requests),
            'reset_time' => $now + $window,
            'current_count' => count($requests)
        ];
    }

    /**
     * Token bucket rate limiting
     */
    private function tokenBucketCheck(string $key, array $request): array
    {
        $limit = $this->getLimit($request);
        $window = $this->getWindow($request);
        $now = time();

        $cacheKey = "rate_limit:bucket:{$key}";
        $bucket = $this->cache->get($cacheKey) ?? [
            'tokens' => $limit,
            'last_refill' => $now
        ];

        // Calculate tokens to add based on time passed
        $timePassed = $now - $bucket['last_refill'];
        $tokensToAdd = floor($timePassed * ($limit / $window));

        $bucket['tokens'] = min($limit, $bucket['tokens'] + $tokensToAdd);
        $bucket['last_refill'] = $now;

        if ($bucket['tokens'] < 1) {
            $resetTime = $now + ceil((1 - $bucket['tokens']) * ($window / $limit));

            return [
                'allowed' => false,
                'limit' => $limit,
                'remaining' => 0,
                'reset_time' => $resetTime,
                'current_count' => $limit - $bucket['tokens']
            ];
        }

        // Consume one token
        $bucket['tokens']--;
        $this->cache->set($cacheKey, $bucket, $window * 2);

        return [
            'allowed' => true,
            'limit' => $limit,
            'remaining' => (int) $bucket['tokens'],
            'reset_time' => $now + $window,
            'current_count' => $limit - $bucket['tokens']
        ];
    }

    /**
     * Fixed window rate limiting
     */
    private function fixedWindowCheck(string $key, array $request): array
    {
        $limit = $this->getLimit($request);
        $window = $this->getWindow($request);
        $now = time();

        $windowStart = floor($now / $window) * $window;
        $cacheKey = "rate_limit:fixed:{$key}:{$windowStart}";

        $currentCount = $this->cache->get($cacheKey) ?? 0;

        if ($currentCount >= $limit) {
            $resetTime = $windowStart + $window;

            return [
                'allowed' => false,
                'limit' => $limit,
                'remaining' => 0,
                'reset_time' => $resetTime,
                'current_count' => $currentCount
            ];
        }

        // Increment counter
        $newCount = $this->cache->increment($cacheKey, 1);
        if ($newCount === 1) {
            $this->cache->expire($cacheKey, $window);
        }

        return [
            'allowed' => true,
            'limit' => $limit,
            'remaining' => $limit - $newCount,
            'reset_time' => $windowStart + $window,
            'current_count' => $newCount
        ];
    }

    /**
     * Generate rate limiting key
     */
    private function generateKey(array $request): string
    {
        return match ($this->config['key_generator']) {
            'user' => 'user:' . ($request['user']['id'] ?? 'anonymous'),
            'ip' => 'ip:' . $this->getClientIP($request),
            'custom' => $this->generateCustomKey($request),
            default => 'ip:' . $this->getClientIP($request)
        };
    }

    /**
     * Generate custom key (can be overridden)
     */
    protected function generateCustomKey(array $request): string
    {
        $ip = $this->getClientIP($request);
        $userId = $request['user']['id'] ?? 'anonymous';
        $endpoint = $request['uri'] ?? '';

        return "custom:{$ip}:{$userId}:" . md5($endpoint);
    }

    /**
     * Get client IP address
     */
    private function getClientIP(array $request): string
    {
        $headers = $request['headers'] ?? [];

        $ipHeaders = [
            'HTTP_CF_CONNECTING_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        ];

        foreach ($ipHeaders as $header) {
            if (!empty($headers[$header])) {
                $ips = explode(',', $headers[$header]);
                $ip = trim($ips[0]);

                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $request['client_ip'] ?? '127.0.0.1';
    }

    /**
     * Get rate limit for request
     */
    private function getLimit(array $request): int
    {
        // Check for endpoint-specific limits
        $endpoint = $request['uri'] ?? '';
        $method = $request['method'] ?? 'GET';

        $endpointKey = strtolower($method) . ':' . $endpoint;

        if (isset($this->config['endpoints'][$endpointKey])) {
            return $this->config['endpoints'][$endpointKey]['limit'];
        }

        // Check for user role-based limits
        if (isset($request['user']['role'])) {
            $role = $request['user']['role'];
            if (isset($this->config['roles'][$role]['limit'])) {
                return $this->config['roles'][$role]['limit'];
            }
        }

        return $this->config['default_limit'];
    }

    /**
     * Get time window for request
     */
    private function getWindow(array $request): int
    {
        $endpoint = $request['uri'] ?? '';
        $method = $request['method'] ?? 'GET';

        $endpointKey = strtolower($method) . ':' . $endpoint;

        if (isset($this->config['endpoints'][$endpointKey])) {
            return $this->config['endpoints'][$endpointKey]['window'];
        }

        if (isset($request['user']['role'])) {
            $role = $request['user']['role'];
            if (isset($this->config['roles'][$role]['window'])) {
                return $this->config['roles'][$role]['window'];
            }
        }

        return $this->config['default_window'];
    }

    /**
     * Check if key is whitelisted
     */
    private function isWhitelisted(string $key, array $request): bool
    {
        $ip = $this->getClientIP($request);

        return in_array($ip, $this->config['whitelist']) ||
               in_array($key, $this->config['whitelist']);
    }

    /**
     * Check if key is blacklisted
     */
    private function isBlacklisted(string $key, array $request): bool
    {
        $ip = $this->getClientIP($request);

        return in_array($ip, $this->config['blacklist']) ||
               in_array($key, $this->config['blacklist']);
    }

    /**
     * Handle rate limit exceeded
     */
    private function rateLimitExceeded(string $key, int $limit, int $resetTime): array
    {
        $retryAfter = max(1, $resetTime - time());

        // Log rate limit violation
        $this->logRateLimitViolation($key, $limit);

        return [
            'success' => false,
            'error' => 'Rate limit exceeded',
            'status_code' => 429,
            'headers' => [
                $this->config['headers']['limit'] => (string) $limit,
                $this->config['headers']['remaining'] => '0',
                $this->config['headers']['reset'] => (string) $resetTime,
                $this->config['headers']['retry_after'] => (string) $retryAfter
            ]
        ];
    }

    /**
     * Add rate limit headers to response
     */
    private function addRateLimitHeaders(array $response, array $result): array
    {
        $headers = [
            $this->config['headers']['limit'] => (string) $result['limit'],
            $this->config['headers']['remaining'] => (string) $result['remaining'],
            $this->config['headers']['reset'] => (string) $result['reset_time']
        ];

        $response['headers'] = array_merge($response['headers'] ?? [], $headers);

        return $response;
    }

    /**
     * Update rate limit after response
     */
    private function updateRateLimitAfterResponse(string $key, array $request, array $response): void
    {
        // TODO: Implement rate limit updates based on response
        unset($key, $request); // Suppress unused variable warnings

        $statusCode = $response['status_code'] ?? 200;

        // Skip counting based on configuration
        if ($this->config['skip_successful_requests'] && $statusCode < 400) {
            return;
        }

        if ($this->config['skip_failed_requests'] && $statusCode >= 400) {
            return;
        }

        // Additional logic for response-based rate limiting can be added here
    }

    /**
     * Log rate limit violation
     */
    private function logRateLimitViolation(string $key, int $limit): void
    {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'key' => $key,
            'limit' => $limit,
            'algorithm' => $this->config['algorithm']
        ];

        error_log('RATE_LIMIT_EXCEEDED: ' . json_encode($logData));
    }

    /**
     * Clear rate limit for key
     */
    public function clearRateLimit(string $key): bool
    {
        $patterns = [
            "rate_limit:sliding:{$key}",
            "rate_limit:bucket:{$key}",
            "rate_limit:fixed:{$key}:*"
        ];

        foreach ($patterns as $pattern) {
            if (strpos($pattern, '*') !== false) {
                // Handle wildcard patterns - for now just skip or use full flush
                // TODO: Implement pattern-based cache clearing
                continue;
            } else {
                $this->cache->delete($pattern);
            }
        }

        return true;
    }

    /**
     * Get rate limit status for key
     */
    public function getRateLimitStatus(string $key): array
    {
        $dummyRequest = ['headers' => [], 'uri' => '', 'method' => 'GET'];
        return $this->checkRateLimit($key, $dummyRequest);
    }

    /**
     * Burst protection middleware
     */
    public function burstProtection(array $request, callable $next): array
    {
        $key = $this->generateKey($request) . ':burst';
        $burstLimit = $this->config['burst_limit'];
        $burstWindow = $this->config['burst_window'];

        $result = $this->slidingWindowCheck($key, array_merge($request, [
            '_burst_limit' => $burstLimit,
            '_burst_window' => $burstWindow
        ]));

        if (!$result['allowed']) {
            return $this->rateLimitExceeded($key, $burstLimit, $result['reset_time']);
        }

        return $next($request);
    }
}
