<?php

declare(strict_types=1);

namespace WeBot\Repositories;

use WeBot\Core\Database;
use WeBot\Models\Ticket;
use WeBot\Exceptions\WeBotException;

/**
 * Ticket Repository
 *
 * Handles database operations for Ticket model including
 * advanced queries, statistics, and ticket management.
 *
 * @package WeBot\Repositories
 * @version 2.0
 */
class TicketRepository
{
    private Database $database;

    public function __construct(Database $database)
    {
        $this->database = $database;
    }

    /**
     * Find ticket by ID
     */
    public function findById(int $id): ?Ticket
    {
        $data = $this->database->selectOne('tickets', ['id' => $id]);

        return $data ? new Ticket($this->database, $data) : null;
    }

    /**
     * Find user's ticket by ID
     */
    public function findUserTicket(int $ticketId, int $userId): ?Ticket
    {
        $data = $this->database->selectOne('tickets', ['id' => $ticketId, 'user_id' => $userId]);

        return $data ? new Ticket($this->database, $data) : null;
    }

    /**
     * Get all tickets
     */
    public function getAll(array $filters = [], int $limit = 50, int $offset = 0): array
    {
        $conditions = [];
        $params = [];
        $types = '';

        // Apply filters
        if (isset($filters['status'])) {
            $conditions[] = "status = ?";
            $params[] = $filters['status'];
            $types .= 's';
        }

        if (isset($filters['priority'])) {
            $conditions[] = "priority = ?";
            $params[] = $filters['priority'];
            $types .= 's';
        }

        if (isset($filters['category'])) {
            $conditions[] = "category = ?";
            $params[] = $filters['category'];
            $types .= 's';
        }

        if (isset($filters['assigned_to'])) {
            $conditions[] = "assigned_to = ?";
            $params[] = $filters['assigned_to'];
            $types .= 'i';
        }

        $sql = "SELECT * FROM tickets";
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }
        $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";

        $params[] = $limit;
        $params[] = $offset;
        $types .= 'ii';

        $results = $this->database->query($sql, $params);

        return array_map(function ($data) {
            return new Ticket($this->database, $data);
        }, $results);
    }

    /**
     * Get user tickets
     */
    public function getUserTickets(int $userId, int $limit = 20): array
    {
        $results = $this->database->select(
            'tickets',
            ['user_id' => $userId],
            ['*'],
            ['order_by' => 'created_at DESC', 'limit' => $limit]
        );

        return array_map(function ($data) {
            return new Ticket($this->database, $data);
        }, $results);
    }

    /**
     * Get open tickets
     */
    public function getOpenTickets(int $limit = 50): array
    {
        $sql = "
            SELECT * FROM tickets 
            WHERE status IN (?, ?, ?) 
            ORDER BY 
                CASE priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'medium' THEN 3 
                    WHEN 'low' THEN 4 
                END,
                created_at ASC
            LIMIT ?
        ";

        $params = [Ticket::STATUS_OPEN, Ticket::STATUS_IN_PROGRESS, Ticket::STATUS_WAITING, $limit];
        $results = $this->database->query($sql, $params);

        return array_map(function ($data) {
            return new Ticket($this->database, $data);
        }, $results);
    }

    /**
     * Get urgent tickets
     */
    public function getUrgentTickets(): array
    {
        $results = $this->database->select(
            'tickets',
            ['priority' => Ticket::PRIORITY_URGENT, 'status' => [Ticket::STATUS_OPEN, Ticket::STATUS_IN_PROGRESS]],
            ['*'],
            ['created_at' => 'ASC']
        );

        return array_map(function ($data) {
            return new Ticket($this->database, $data);
        }, $results);
    }

    /**
     * Get unassigned tickets
     */
    public function getUnassignedTickets(int $limit = 50): array
    {
        $sql = "
            SELECT * FROM tickets 
            WHERE assigned_to IS NULL 
            AND status IN (?, ?) 
            ORDER BY 
                CASE priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'medium' THEN 3 
                    WHEN 'low' THEN 4 
                END,
                created_at ASC
            LIMIT ?
        ";

        $params = [Ticket::STATUS_OPEN, Ticket::STATUS_IN_PROGRESS, $limit];
        $results = $this->database->query($sql, $params);

        return array_map(function ($data) {
            return new Ticket($this->database, $data);
        }, $results);
    }

    /**
     * Get assigned tickets for user
     */
    public function getAssignedTickets(int $userId, int $limit = 50): array
    {
        $results = $this->database->select(
            'tickets',
            ['assigned_to' => $userId],
            ['*'],
            ['order_by' => 'created_at DESC', 'limit' => $limit]
        );

        return array_map(function ($data) {
            return new Ticket($this->database, $data);
        }, $results);
    }

    /**
     * Create new ticket
     */
    public function create(array $data): Ticket
    {
        $ticket = new Ticket($this->database);
        $ticket->fill($data);

        if (!$ticket->save()) {
            throw new WeBotException('Failed to create ticket');
        }

        return $ticket;
    }

    /**
     * Update ticket
     */
    public function update(int $id, array $data): bool
    {
        $ticket = $this->findById($id);
        if (!$ticket) {
            throw new WeBotException('Ticket not found');
        }

        $ticket->fill($data);

        return $ticket->save();
    }

    /**
     * Delete ticket
     */
    public function delete(int $id): bool
    {
        // Delete ticket replies first
        $this->database->delete('ticket_replies', ['ticket_id' => $id]);

        // Delete ticket
        return $this->database->delete('tickets', ['id' => $id]) > 0;
    }

    /**
     * Search tickets
     */
    public function search(string $query, array $filters = [], int $limit = 50): array
    {
        $conditions = [];
        $params = [];
        $types = '';

        // Add search query
        if (!empty($query)) {
            $conditions[] = "(subject LIKE ? OR description LIKE ?)";
            $searchTerm = "%{$query}%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $types .= 'ss';
        }

        // Add filters
        if (isset($filters['status'])) {
            $conditions[] = "status = ?";
            $params[] = $filters['status'];
            $types .= 's';
        }

        if (isset($filters['priority'])) {
            $conditions[] = "priority = ?";
            $params[] = $filters['priority'];
            $types .= 's';
        }

        if (isset($filters['category'])) {
            $conditions[] = "category = ?";
            $params[] = $filters['category'];
            $types .= 's';
        }

        if (isset($filters['user_id'])) {
            $conditions[] = "user_id = ?";
            $params[] = $filters['user_id'];
            $types .= 'i';
        }

        $sql = "SELECT * FROM tickets";
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }
        $sql .= " ORDER BY created_at DESC LIMIT ?";

        $params[] = $limit;
        $types .= 'i';

        $results = $this->database->query($sql, $params);

        return array_map(function ($data) {
            return new Ticket($this->database, $data);
        }, $results);
    }

    /**
     * Get ticket statistics
     */
    public function getStatistics(): array
    {
        $sql = "
            SELECT 
                COUNT(*) as total_tickets,
                SUM(CASE WHEN status IN ('open', 'in_progress', 'waiting') THEN 1 ELSE 0 END) as open_tickets,
                SUM(CASE WHEN status IN ('resolved', 'closed') THEN 1 ELSE 0 END) as closed_tickets,
                SUM(CASE WHEN priority = 'urgent' THEN 1 ELSE 0 END) as urgent_tickets,
                SUM(CASE WHEN assigned_to IS NULL AND status IN ('open', 'in_progress') THEN 1 ELSE 0 END) as unassigned_tickets,
                AVG(CASE 
                    WHEN resolved_at IS NOT NULL THEN 
                        TIMESTAMPDIFF(HOUR, created_at, resolved_at) 
                    ELSE NULL 
                END) as avg_resolution_time_hours
            FROM tickets
        ";

        $results = $this->database->query($sql);
        $stats = $results[0] ?? [];

        return [
            'total_tickets' => (int) $stats['total_tickets'],
            'open_tickets' => (int) $stats['open_tickets'],
            'closed_tickets' => (int) $stats['closed_tickets'],
            'urgent_tickets' => (int) $stats['urgent_tickets'],
            'unassigned_tickets' => (int) $stats['unassigned_tickets'],
            'avg_resolution_time_hours' => round((float) ($stats['avg_resolution_time_hours'] ?? 0), 2)
        ];
    }

    /**
     * Get tickets by category statistics
     */
    public function getCategoryStatistics(): array
    {
        $sql = "
            SELECT 
                category,
                COUNT(*) as total,
                SUM(CASE WHEN status IN ('open', 'in_progress', 'waiting') THEN 1 ELSE 0 END) as open,
                SUM(CASE WHEN status IN ('resolved', 'closed') THEN 1 ELSE 0 END) as closed
            FROM tickets
            GROUP BY category
            ORDER BY total DESC
        ";

        $stats = $this->database->query($sql);

        return array_map(function ($row) {
            return [
                'category' => $row['category'],
                'total' => (int) $row['total'],
                'open' => (int) $row['open'],
                'closed' => (int) $row['closed']
            ];
        }, $stats);
    }

    /**
     * Get tickets by priority statistics
     */
    public function getPriorityStatistics(): array
    {
        $sql = "
            SELECT 
                priority,
                COUNT(*) as total,
                SUM(CASE WHEN status IN ('open', 'in_progress', 'waiting') THEN 1 ELSE 0 END) as open,
                SUM(CASE WHEN status IN ('resolved', 'closed') THEN 1 ELSE 0 END) as closed
            FROM tickets
            GROUP BY priority
            ORDER BY 
                CASE priority 
                    WHEN 'urgent' THEN 1 
                    WHEN 'high' THEN 2 
                    WHEN 'medium' THEN 3 
                    WHEN 'low' THEN 4 
                END
        ";

        $stats = $this->database->query($sql);

        return array_map(function ($row) {
            return [
                'priority' => $row['priority'],
                'total' => (int) $row['total'],
                'open' => (int) $row['open'],
                'closed' => (int) $row['closed']
            ];
        }, $stats);
    }

    /**
     * Get old unresolved tickets
     */
    public function getOldUnresolvedTickets(int $hoursOld = 24): array
    {
        $sql = "
            SELECT * FROM tickets 
            WHERE status IN ('open', 'in_progress', 'waiting') 
            AND created_at < DATE_SUB(NOW(), INTERVAL ? HOUR)
            ORDER BY created_at ASC
        ";

        $results = $this->database->query($sql, [$hoursOld]);

        return array_map(function ($data) {
            return new Ticket($this->database, $data);
        }, $results);
    }

    /**
     * Bulk assign tickets
     */
    public function bulkAssign(array $ticketIds, int $assignedTo): int
    {
        if (empty($ticketIds)) {
            return 0;
        }

        $placeholders = str_repeat('?,', count($ticketIds) - 1) . '?';
        $sql = "
            UPDATE tickets 
            SET assigned_to = ?, updated_at = NOW() 
            WHERE id IN ({$placeholders})
        ";

        $params = array_merge([$assignedTo], $ticketIds);
        return $this->database->execute($sql, $params);
    }

    /**
     * Bulk update status
     */
    public function bulkUpdateStatus(array $ticketIds, string $status): int
    {
        if (empty($ticketIds)) {
            return 0;
        }

        $placeholders = str_repeat('?,', count($ticketIds) - 1) . '?';
        $sql = "UPDATE tickets SET status = ?, updated_at = NOW() WHERE id IN ({$placeholders})";

        // Add resolved_at or closed_at timestamp if needed
        if ($status === Ticket::STATUS_RESOLVED) {
            $sql = "UPDATE tickets SET status = ?, resolved_at = NOW(), updated_at = NOW() WHERE id IN ({$placeholders})";
        } elseif ($status === Ticket::STATUS_CLOSED) {
            $sql = "UPDATE tickets SET status = ?, closed_at = NOW(), updated_at = NOW() WHERE id IN ({$placeholders})";
        }

        $params = array_merge([$status], $ticketIds);
        return $this->database->execute($sql, $params);
    }

    /**
     * Get response time statistics
     */
    public function getResponseTimeStatistics(): array
    {
        $sql = "
            SELECT 
                AVG(TIMESTAMPDIFF(HOUR, t.created_at, tr.created_at)) as avg_first_response_hours,
                MIN(TIMESTAMPDIFF(HOUR, t.created_at, tr.created_at)) as min_first_response_hours,
                MAX(TIMESTAMPDIFF(HOUR, t.created_at, tr.created_at)) as max_first_response_hours
            FROM tickets t
            INNER JOIN (
                SELECT ticket_id, MIN(created_at) as created_at
                FROM ticket_replies 
                WHERE is_staff = 1
                GROUP BY ticket_id
            ) tr ON t.id = tr.ticket_id
            WHERE t.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        ";

        $results = $this->database->query($sql);
        $stats = $results[0] ?? [];

        return [
            'avg_first_response_hours' => round((float) ($stats['avg_first_response_hours'] ?? 0), 2),
            'min_first_response_hours' => round((float) ($stats['min_first_response_hours'] ?? 0), 2),
            'max_first_response_hours' => round((float) ($stats['max_first_response_hours'] ?? 0), 2)
        ];
    }

    /**
     * Get tickets created in date range
     */
    public function getTicketsInDateRange(string $startDate, string $endDate): array
    {
        $sql = "
            SELECT * FROM tickets 
            WHERE created_at >= ? AND created_at <= ?
            ORDER BY created_at DESC
        ";

        $results = $this->database->query($sql, [$startDate, $endDate]);

        return array_map(function ($data) {
            return new Ticket($this->database, $data);
        }, $results);
    }
}
