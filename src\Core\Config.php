<?php

declare(strict_types=1);

namespace WeBot\Core;

use WeBot\Exceptions\WeBotException;

/**
 * WeBot Configuration Manager
 *
 * Manages application configuration from various sources
 * including environment variables and config files.
 *
 * @package WeBot\Core
 * @version 2.0
 */
class Config implements ConfigInterface
{
    private array $config = [];
    private array $cache = [];
    private string $configPath;

    public function __construct(string $configPath = null)
    {
        $this->configPath = $configPath ?? $this->getDefaultConfigPath();
        $this->loadConfiguration();
    }

    /**
     * Get default config path
     */
    private function getDefaultConfigPath(): string
    {
        if (defined('WEBOT_CONFIG')) {
            return WEBOT_CONFIG;
        }

        // Default to project root config directory
        $projectRoot = dirname(dirname(__DIR__));
        return $projectRoot . '/config';
    }

    /**
     * Get configuration value
     */
    public function get(string $key, $default = null)
    {
        // Check cache first
        if (isset($this->cache[$key])) {
            return $this->cache[$key];
        }

        // Parse dot notation
        $keys = explode('.', $key);
        $value = $this->config;

        foreach ($keys as $segment) {
            if (!is_array($value) || !array_key_exists($segment, $value)) {
                $this->cache[$key] = $default;
                return $default;
            }
            $value = $value[$segment];
        }

        $this->cache[$key] = $value;
        return $value;
    }

    /**
     * Set configuration value
     */
    public function set(string $key, $value): void
    {
        $keys = explode('.', $key);
        $config = &$this->config;

        foreach ($keys as $segment) {
            if (!isset($config[$segment]) || !is_array($config[$segment])) {
                $config[$segment] = [];
            }
            $config = &$config[$segment];
        }

        $config = $value;

        // Clear cache for this key
        unset($this->cache[$key]);
    }

    /**
     * Check if configuration key exists
     */
    public function has(string $key): bool
    {
        $keys = explode('.', $key);
        $value = $this->config;

        foreach ($keys as $segment) {
            if (!is_array($value) || !array_key_exists($segment, $value)) {
                return false;
            }
            $value = $value[$segment];
        }

        return true;
    }

    /**
     * Get all configuration
     */
    public function all(): array
    {
        return $this->config;
    }

    /**
     * Load configuration from files and environment
     */
    private function loadConfiguration(): void
    {
        // Load core configuration
        $this->loadCoreConfig();

        // Load database configuration
        $this->loadDatabaseConfig();

        // Load telegram configuration
        $this->loadTelegramConfig();

        // Load panel configurations
        $this->loadPanelConfigs();

        // Load payment configurations
        $this->loadPaymentConfigs();

        // Override with environment variables
        $this->loadEnvironmentOverrides();
    }

    /**
     * Load core application configuration
     */
    private function loadCoreConfig(): void
    {
        $this->config['app'] = [
            'name' => 'WeBot',
            'version' => defined('WEBOT_VERSION') ? WEBOT_VERSION : '2.0.0',
            'env' => env('APP_ENV', 'production'),
            'debug' => filter_var(env('APP_DEBUG', false), FILTER_VALIDATE_BOOLEAN),
            'timezone' => env('APP_TIMEZONE', 'Asia/Tehran'),
            'locale' => env('APP_LOCALE', 'fa'),
            'url' => env('APP_URL', 'https://localhost'),
            'memory_limit' => env('MEMORY_LIMIT', '256M'),
        ];
    }

    /**
     * Load database configuration
     */
    private function loadDatabaseConfig(): void
    {
        $configFile = $this->configPath . '/database/connection.php';

        if (file_exists($configFile)) {
            $this->config['database'] = require $configFile;
        } else {
            $this->config['database'] = [
                'host' => env('DB_HOST', 'localhost'),
                'port' => (int) env('DB_PORT', 3306),
                'database' => env('DB_DATABASE', 'webot'),
                'username' => env('DB_USERNAME', ''),
                'password' => env('DB_PASSWORD', ''),
                'charset' => 'utf8mb4',
                'collation' => 'utf8mb4_unicode_ci',
                'options' => [
                    \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                    \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                    \PDO::ATTR_EMULATE_PREPARES => false,
                ]
            ];
        }
    }

    /**
     * Load telegram configuration
     */
    private function loadTelegramConfig(): void
    {
        $configFile = $this->configPath . '/telegram/bot.php';

        if (file_exists($configFile)) {
            $this->config['telegram'] = require $configFile;
        } else {
            $this->config['telegram'] = [
                'token' => env('TELEGRAM_BOT_TOKEN', ''),
                'username' => env('TELEGRAM_BOT_USERNAME', ''),
                'webhook_url' => env('WEBHOOK_URL', ''),
                'admin_id' => (int) env('ADMIN_ID', 0),
                'channel_id' => env('CHANNEL_ID', ''),
                'api_url' => 'https://api.telegram.org/bot',
                'timeout' => 30,
                'retry_attempts' => 3,
            ];
        }
    }

    /**
     * Load panel configurations
     */
    private function loadPanelConfigs(): void
    {
        $panels = ['marzban', 'marzneshin', 'x-ui', 'hiddify'];

        foreach ($panels as $panel) {
            $configFile = $this->configPath . "/panels/{$panel}.php";

            if (file_exists($configFile)) {
                $this->config['panels'][$panel] = require $configFile;
            }
        }

        // Default panel configurations
        if (!isset($this->config['panels']['marzban'])) {
            $this->config['panels']['marzban'] = [
                'url' => env('MARZBAN_URL', ''),
                'username' => env('MARZBAN_USERNAME', ''),
                'password' => env('MARZBAN_PASSWORD', ''),
                'timeout' => 30,
            ];
        }
    }

    /**
     * Load payment configurations
     */
    private function loadPaymentConfigs(): void
    {
        $configFile = $this->configPath . '/payments/gateways.php';

        if (file_exists($configFile)) {
            $this->config['payments'] = require $configFile;
        } else {
            $this->config['payments'] = [
                'zarinpal' => [
                    'merchant_id' => env('ZARINPAL_MERCHANT_ID', ''),
                    'sandbox' => filter_var(env('ZARINPAL_SANDBOX', false), FILTER_VALIDATE_BOOLEAN),
                    'callback_url' => env('ZARINPAL_CALLBACK_URL', ''),
                ],
                'nowpayments' => [
                    'api_key' => env('NOWPAYMENTS_API_KEY', ''),
                    'sandbox' => filter_var(env('NOWPAYMENTS_SANDBOX', false), FILTER_VALIDATE_BOOLEAN),
                    'callback_url' => env('NOWPAYMENTS_CALLBACK_URL', ''),
                ],
            ];
        }
    }

    /**
     * Load environment variable overrides
     */
    private function loadEnvironmentOverrides(): void
    {
        // Override specific config values with environment variables
        $envOverrides = [
            'app.debug' => 'APP_DEBUG',
            'app.env' => 'APP_ENV',
            'app.timezone' => 'APP_TIMEZONE',
            'database.host' => 'DB_HOST',
            'database.port' => 'DB_PORT',
            'database.database' => 'DB_DATABASE',
            'database.username' => 'DB_USERNAME',
            'database.password' => 'DB_PASSWORD',
            'telegram.token' => 'TELEGRAM_BOT_TOKEN',
            'telegram.admin_id' => 'ADMIN_ID',
        ];

        foreach ($envOverrides as $configKey => $envKey) {
            $envValue = env($envKey);
            if ($envValue !== null) {
                $this->set($configKey, $envValue);
            }
        }
    }

    /**
     * Validate required configuration
     */
    public function validate(): void
    {
        $required = [
            'telegram.token' => 'Telegram bot token is required',
            'telegram.admin_id' => 'Admin ID is required',
            'database.host' => 'Database host is required',
            'database.database' => 'Database name is required',
        ];

        foreach ($required as $key => $message) {
            if (!$this->has($key) || empty($this->get($key))) {
                throw new WeBotException($message);
            }
        }
    }

    /**
     * Clear configuration cache
     */
    public function clearCache(): void
    {
        $this->cache = [];
    }
}
