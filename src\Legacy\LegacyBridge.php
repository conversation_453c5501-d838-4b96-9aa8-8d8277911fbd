<?php

declare(strict_types=1);

namespace WeBot\Legacy;

use WeBot\Core\Application;
use WeBot\Core\Database;
use WeBot\Controllers\UserController;
use WeBot\Controllers\AuthController;
use WeBot\Controllers\ServiceController;
use WeBot\Controllers\PaymentController;
use WeBot\Controllers\AdminController;
use WeBot\Controllers\TicketController;

/**
 * Legacy Bridge
 * 
 * Bridges old bot.php functionality with new WeBot 2.0 architecture.
 * Provides backward compatibility while gradually migrating to new system.
 * 
 * @package WeBot\Legacy
 * @version 2.0
 */
class LegacyBridge
{
    private Application $app;
    private Database $database;
    private array $controllers = [];
    private array $legacyData = [];
    private array $config = [];

    public function __construct(Application $app)
    {
        $this->app = $app;
        $this->database = $app->getContainer()->get('database');
        $this->initializeControllers();
        $this->loadLegacyConfig();
    }

    /**
     * Main entry point for legacy bot.php requests
     */
    public function handleLegacyRequest(array $update): array
    {
        try {
            // Extract message data in legacy format
            $this->extractLegacyData($update);
            
            // Route to appropriate controller based on legacy patterns
            return $this->routeLegacyRequest();
            
        } catch (\Exception $e) {
            error_log("Legacy Bridge Error: " . $e->getMessage());
            return $this->sendLegacyError('خطایی رخ داده است. لطفاً مجدداً تلاش کنید.');
        }
    }

    /**
     * Extract legacy data format from Telegram update
     */
    private function extractLegacyData(array $update): void
    {
        // Message data
        if (isset($update['message'])) {
            $message = $update['message'];
            $this->legacyData = [
                'message_id' => $message['message_id'],
                'from_id' => $message['from']['id'],
                'first_name' => $message['from']['first_name'] ?? '',
                'last_name' => $message['from']['last_name'] ?? '',
                'username' => $message['from']['username'] ?? '',
                'text' => $message['text'] ?? '',
                'chat_id' => $message['chat']['id'],
                'date' => $message['date'],
                'type' => 'message'
            ];
        }
        
        // Callback query data
        if (isset($update['callback_query'])) {
            $callback = $update['callback_query'];
            $this->legacyData = [
                'callback_query_id' => $callback['id'],
                'from_id' => $callback['from']['id'],
                'first_name' => $callback['from']['first_name'] ?? '',
                'last_name' => $callback['from']['last_name'] ?? '',
                'username' => $callback['from']['username'] ?? '',
                'data' => $callback['data'] ?? '',
                'message_id' => $callback['message']['message_id'] ?? 0,
                'chat_id' => $callback['message']['chat']['id'] ?? $callback['from']['id'],
                'type' => 'callback_query'
            ];
        }
    }

    /**
     * Route legacy request to appropriate controller
     */
    private function routeLegacyRequest(): array
    {
        $text = $this->legacyData['text'] ?? '';
        $data = $this->legacyData['data'] ?? '';
        $fromId = $this->legacyData['from_id'];

        // Check if user is banned or spam
        if ($this->isUserBanned($fromId)) {
            return $this->sendLegacyMessage('❌ | هی بهت گفتم آدم باش گوش نکردی ، الان مسدود شدی 😑😂');
        }

        // Route based on text/data patterns (legacy bot.php logic)
        
        // Start command
        if (preg_match('/^\/([Ss]tart)/', $text) || $data === 'mainMenu') {
            return $this->handleStart();
        }
        
        // Admin commands
        if ($this->isAdmin($fromId)) {
            if ($data === 'botSettings' || preg_match('/^changeBot(\w+)/', $data)) {
                return $this->handleAdminSettings();
            }
            
            if (preg_match('/^sendMessageToUser(\d+)/', $data)) {
                return $this->handleAdminMessage();
            }
        }
        
        // User profile and services
        if ($data === 'profile' || $text === 'پروفایل من') {
            return $this->handleProfile();
        }
        
        if ($data === 'myServices' || $text === 'سرویس‌های من') {
            return $this->handleMyServices();
        }
        
        // Buy service
        if ($data === 'buyService' || $text === 'خرید سرویس') {
            return $this->handleBuyService();
        }
        
        // Support/Tickets
        if ($text === '/support' || $data === 'support') {
            return $this->handleSupport();
        }
        
        // Payment related
        if (preg_match('/^payment_/', $data) || preg_match('/^wallet/', $data)) {
            return $this->handlePayment();
        }
        
        // Service management
        if (preg_match('/^service_/', $data) || preg_match('/^config_/', $data)) {
            return $this->handleServiceManagement();
        }
        
        // Default fallback
        return $this->handleDefault();
    }

    /**
     * Handle start command
     */
    private function handleStart(): array
    {
        if ($this->legacyData['type'] === 'message') {
            $message = [
                'message_id' => $this->legacyData['message_id'],
                'from' => [
                    'id' => $this->legacyData['from_id'],
                    'first_name' => $this->legacyData['first_name'],
                    'last_name' => $this->legacyData['last_name'],
                    'username' => $this->legacyData['username']
                ],
                'text' => $this->legacyData['text'],
                'date' => $this->legacyData['date']
            ];
            
            return $this->controllers['auth']->register($message);
        } else {
            $callback = [
                'id' => $this->legacyData['callback_query_id'],
                'from' => [
                    'id' => $this->legacyData['from_id'],
                    'first_name' => $this->legacyData['first_name']
                ],
                'data' => 'main_menu',
                'message' => [
                    'message_id' => $this->legacyData['message_id'],
                    'chat' => ['id' => $this->legacyData['chat_id']]
                ]
            ];
            
            return $this->controllers['user']->handleCallback($callback);
        }
    }

    /**
     * Handle profile request
     */
    private function handleProfile(): array
    {
        // Legacy profile handling - callback data not needed for new implementation
        return $this->controllers['auth']->viewProfile();
    }

    /**
     * Handle my services request
     */
    private function handleMyServices(): array
    {
        $callback = [
            'id' => $this->legacyData['callback_query_id'] ?? 'legacy_' . time(),
            'from' => [
                'id' => $this->legacyData['from_id'],
                'first_name' => $this->legacyData['first_name']
            ],
            'data' => 'my_services',
            'message' => [
                'message_id' => $this->legacyData['message_id'] ?? 0,
                'chat' => ['id' => $this->legacyData['chat_id']]
            ]
        ];
        
        return $this->controllers['service']->handleCallback($callback);
    }

    /**
     * Handle buy service request
     */
    private function handleBuyService(): array
    {
        $callback = [
            'id' => $this->legacyData['callback_query_id'] ?? 'legacy_' . time(),
            'from' => [
                'id' => $this->legacyData['from_id'],
                'first_name' => $this->legacyData['first_name']
            ],
            'data' => 'buy_service',
            'message' => [
                'message_id' => $this->legacyData['message_id'] ?? 0,
                'chat' => ['id' => $this->legacyData['chat_id']]
            ]
        ];
        
        return $this->controllers['service']->handleCallback($callback);
    }

    /**
     * Handle support request
     */
    private function handleSupport(): array
    {
        if ($this->legacyData['type'] === 'message') {
            $message = [
                'message_id' => $this->legacyData['message_id'],
                'from' => [
                    'id' => $this->legacyData['from_id'],
                    'first_name' => $this->legacyData['first_name'],
                    'username' => $this->legacyData['username']
                ],
                'text' => $this->legacyData['text'],
                'date' => $this->legacyData['date']
            ];
            
            return $this->controllers['ticket']->handleSupport($message);
        } else {
            $callback = [
                'id' => $this->legacyData['callback_query_id'],
                'from' => [
                    'id' => $this->legacyData['from_id'],
                    'first_name' => $this->legacyData['first_name']
                ],
                'data' => $this->legacyData['data'],
                'message' => [
                    'message_id' => $this->legacyData['message_id'],
                    'chat' => ['id' => $this->legacyData['chat_id']]
                ]
            ];
            
            return $this->controllers['ticket']->handleCallback($callback);
        }
    }

    /**
     * Handle payment requests
     */
    private function handlePayment(): array
    {
        $callback = [
            'id' => $this->legacyData['callback_query_id'] ?? 'legacy_' . time(),
            'from' => [
                'id' => $this->legacyData['from_id'],
                'first_name' => $this->legacyData['first_name']
            ],
            'data' => $this->legacyData['data'] ?? 'wallet',
            'message' => [
                'message_id' => $this->legacyData['message_id'] ?? 0,
                'chat' => ['id' => $this->legacyData['chat_id']]
            ]
        ];
        
        return $this->controllers['payment']->handleCallback($callback);
    }

    /**
     * Handle service management
     */
    private function handleServiceManagement(): array
    {
        $callback = [
            'id' => $this->legacyData['callback_query_id'] ?? 'legacy_' . time(),
            'from' => [
                'id' => $this->legacyData['from_id'],
                'first_name' => $this->legacyData['first_name']
            ],
            'data' => $this->legacyData['data'],
            'message' => [
                'message_id' => $this->legacyData['message_id'] ?? 0,
                'chat' => ['id' => $this->legacyData['chat_id']]
            ]
        ];
        
        return $this->controllers['service']->handleCallback($callback);
    }

    /**
     * Handle admin settings
     */
    private function handleAdminSettings(): array
    {
        $callback = [
            'id' => $this->legacyData['callback_query_id'] ?? 'legacy_' . time(),
            'from' => [
                'id' => $this->legacyData['from_id'],
                'first_name' => $this->legacyData['first_name']
            ],
            'data' => $this->legacyData['data'],
            'message' => [
                'message_id' => $this->legacyData['message_id'] ?? 0,
                'chat' => ['id' => $this->legacyData['chat_id']]
            ]
        ];
        
        return $this->controllers['admin']->handleCallback($callback);
    }

    /**
     * Handle admin message
     */
    private function handleAdminMessage(): array
    {
        // Implementation for admin messaging
        return $this->sendLegacyMessage('قابلیت ارسال پیام ادمین در حال توسعه است.');
    }

    /**
     * Handle default/unknown requests
     */
    private function handleDefault(): array
    {
        return $this->sendLegacyMessage('دستور شناخته نشد. لطفاً از منو اصلی استفاده کنید.');
    }

    /**
     * Check if user is banned
     */
    private function isUserBanned(int $userId): bool
    {
        $user = $this->database->selectOne('users', ['userid' => $userId]);
        return $user && ($user['step'] === 'banned' || $user['status'] === 'banned');
    }

    /**
     * Check if user is admin
     */
    private function isAdmin(int $userId): bool
    {
        $user = $this->database->selectOne('users', ['userid' => $userId]);
        return $user && ($user['isAdmin'] == 1 || $user['role'] === 'admin');
    }

    /**
     * Send legacy format message
     */
    private function sendLegacyMessage(string $text, array $keyboard = []): array
    {
        return [
            'method' => 'sendMessage',
            'chat_id' => $this->legacyData['chat_id'],
            'text' => $text,
            'reply_markup' => $keyboard ? json_encode($keyboard) : null,
            'parse_mode' => 'HTML'
        ];
    }

    /**
     * Send legacy format error
     */
    private function sendLegacyError(string $error): array
    {
        return $this->sendLegacyMessage($error);
    }

    /**
     * Initialize controllers
     */
    private function initializeControllers(): void
    {
        $container = $this->app->getContainer();
        
        $this->controllers = [
            'user' => new UserController($container),
            'auth' => new AuthController($container),
            'service' => new ServiceController($container),
            'payment' => new PaymentController($container),
            'admin' => new AdminController($container),
            'ticket' => new TicketController($container)
        ];
    }

    /**
     * Load legacy configuration
     */
    private function loadLegacyConfig(): void
    {
        // Legacy config.php has been removed - use environment variables instead
        $this->config = [
            'admin_id' => (int) ($_ENV['ADMIN_ID'] ?? 123456789),
            'bot_token' => $_ENV['BOT_TOKEN'] ?? ''
        ];
    }

    // parseLegacyConfig method removed - legacy config.php no longer exists

    /**
     * Get legacy configuration value
     */
    public function getLegacyConfig(string $key, $default = null)
    {
        return $this->config[$key] ?? $default;
    }
}
