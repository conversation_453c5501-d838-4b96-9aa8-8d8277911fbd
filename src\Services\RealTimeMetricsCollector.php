<?php

declare(strict_types=1);

namespace WeBot\Services;

use WeBot\Core\CacheManager;
use WeBot\Utils\Logger;
use WeBot\Events\EventDispatcher;

/**
 * Real-time Metrics Collector
 *
 * Collects and processes real-time metrics for monitoring
 * and analytics with high-frequency data updates.
 *
 * @package WeBot\Services
 * @version 2.0
 */
class RealTimeMetricsCollector
{
    private CacheManager $cache;
    private Logger $logger;
    private EventDispatcher $eventDispatcher;
    private array $config;
    private array $metrics = [];
    private array $counters = [];
    private array $timers = [];

    public function __construct(
        CacheManager $cache,
        EventDispatcher $eventDispatcher,
        array $config = []
    ) {
        $this->cache = $cache;
        $this->logger = Logger::getInstance();
        $this->eventDispatcher = $eventDispatcher;
        $this->config = array_merge($this->getDefaultConfig(), $config);
    }

    /**
     * Record a metric
     */
    public function recordMetric(string $name, $value, array $tags = []): void
    {
        $timestamp = microtime(true);

        $metric = [
            'name' => $name,
            'value' => $value,
            'tags' => $tags,
            'timestamp' => $timestamp,
            'date' => date('Y-m-d'),
            'hour' => date('H'),
            'minute' => date('i')
        ];

        // Store in memory for immediate access
        $this->metrics[] = $metric;

        // Store in cache for persistence
        $this->storeMetricInCache($metric);

        // Update aggregated counters
        $this->updateCounters($name, $value, $tags);

        // Check for alerts
        $this->checkMetricAlerts($metric);

        // Limit memory usage
        if (count($this->metrics) > $this->config['max_memory_metrics']) {
            $this->metrics = array_slice($this->metrics, -($this->config['max_memory_metrics'] / 2));
        }
    }

    /**
     * Increment a counter
     */
    public function increment(string $name, int $value = 1, array $tags = []): void
    {
        $key = $this->buildCounterKey($name, $tags);

        if (!isset($this->counters[$key])) {
            $this->counters[$key] = [
                'name' => $name,
                'value' => 0,
                'tags' => $tags,
                'last_updated' => microtime(true)
            ];
        }

        $this->counters[$key]['value'] += $value;
        $this->counters[$key]['last_updated'] = microtime(true);

        // Update cache
        $cacheKey = "metrics:counter:{$key}";
        $this->cache->increment($cacheKey, $value);
        $this->cache->expire($cacheKey, $this->config['counter_ttl']);

        // Record as metric
        $this->recordMetric($name, $this->counters[$key]['value'], $tags);
    }

    /**
     * Record timing metric
     */
    public function timing(string $name, float $duration, array $tags = []): void
    {
        $this->recordMetric($name, $duration, array_merge($tags, ['type' => 'timing']));

        // Update timing statistics
        $this->updateTimingStats($name, $duration, $tags);
    }

    /**
     * Start a timer
     */
    public function startTimer(string $name, array $tags = []): string
    {
        $timerId = uniqid("{$name}_", true);

        $this->timers[$timerId] = [
            'name' => $name,
            'tags' => $tags,
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true)
        ];

        return $timerId;
    }

    /**
     * Stop a timer and record timing
     */
    public function stopTimer(string $timerId): ?float
    {
        if (!isset($this->timers[$timerId])) {
            return null;
        }

        $timer = $this->timers[$timerId];
        $duration = microtime(true) - $timer['start_time'];
        $memoryUsed = memory_get_usage(true) - $timer['start_memory'];

        // Record timing metric
        $this->timing($timer['name'], $duration, $timer['tags']);

        // Record memory usage
        $this->recordMetric(
            $timer['name'] . '.memory',
            $memoryUsed,
            array_merge($timer['tags'], ['type' => 'memory'])
        );

        unset($this->timers[$timerId]);

        return $duration;
    }

    /**
     * Record gauge metric (current value)
     */
    public function gauge(string $name, $value, array $tags = []): void
    {
        $this->recordMetric($name, $value, array_merge($tags, ['type' => 'gauge']));

        // Store current value in cache
        $cacheKey = "metrics:gauge:{$name}:" . md5(serialize($tags));
        $this->cache->set($cacheKey, $value, $this->config['gauge_ttl']);
    }

    /**
     * Record histogram metric
     */
    public function histogram(string $name, $value, array $tags = []): void
    {
        $this->recordMetric($name, $value, array_merge($tags, ['type' => 'histogram']));

        // Update histogram buckets
        $this->updateHistogramBuckets($name, $value, $tags);
    }

    /**
     * Get real-time metrics
     */
    public function getRealTimeMetrics(int $seconds = 60): array
    {
        $cutoff = microtime(true) - $seconds;

        $recentMetrics = array_filter($this->metrics, function ($metric) use ($cutoff) {
            return $metric['timestamp'] >= $cutoff;
        });

        return [
            'timestamp' => microtime(true),
            'period_seconds' => $seconds,
            'total_metrics' => count($recentMetrics),
            'metrics_per_second' => count($recentMetrics) / $seconds,
            'metrics' => $this->aggregateMetrics($recentMetrics),
            'counters' => $this->getActiveCounters(),
            'gauges' => $this->getCurrentGauges(),
            'timings' => $this->getTimingStats($seconds)
        ];
    }

    /**
     * Get metrics summary
     */
    public function getMetricsSummary(string $period = '1h'): array
    {
        $cacheKey = "metrics:summary:{$period}";
        $cached = $this->cache->get($cacheKey);

        if ($cached) {
            return $cached;
        }

        $seconds = $this->parsePeriod($period);
        $summary = $this->calculateMetricsSummary($seconds);

        $this->cache->set($cacheKey, $summary, min($seconds / 4, 300)); // Cache for 1/4 of period or 5 min max

        return $summary;
    }

    /**
     * Get top metrics
     */
    public function getTopMetrics(int $limit = 10, string $orderBy = 'frequency'): array
    {
        $metricCounts = [];

        foreach ($this->metrics as $metric) {
            $name = $metric['name'];
            if (!isset($metricCounts[$name])) {
                $metricCounts[$name] = [
                    'name' => $name,
                    'count' => 0,
                    'total_value' => 0,
                    'avg_value' => 0,
                    'min_value' => null,
                    'max_value' => null,
                    'last_value' => null,
                    'last_timestamp' => null
                ];
            }

            $metricCounts[$name]['count']++;
            $metricCounts[$name]['total_value'] += $metric['value'];
            $metricCounts[$name]['last_value'] = $metric['value'];
            $metricCounts[$name]['last_timestamp'] = $metric['timestamp'];

            if ($metricCounts[$name]['min_value'] === null || $metric['value'] < $metricCounts[$name]['min_value']) {
                $metricCounts[$name]['min_value'] = $metric['value'];
            }

            if ($metricCounts[$name]['max_value'] === null || $metric['value'] > $metricCounts[$name]['max_value']) {
                $metricCounts[$name]['max_value'] = $metric['value'];
            }
        }

        // Calculate averages
        foreach ($metricCounts as &$metric) {
            $metric['avg_value'] = $metric['count'] > 0 ? $metric['total_value'] / $metric['count'] : 0;
        }

        // Sort by specified criteria
        switch ($orderBy) {
            case 'frequency':
                uasort($metricCounts, fn($a, $b) => $b['count'] <=> $a['count']);
                break;
            case 'value':
                uasort($metricCounts, fn($a, $b) => $b['total_value'] <=> $a['total_value']);
                break;
            case 'recent':
                uasort($metricCounts, fn($a, $b) => $b['last_timestamp'] <=> $a['last_timestamp']);
                break;
        }

        return array_slice(array_values($metricCounts), 0, $limit);
    }

    /**
     * Clear old metrics
     */
    public function cleanup(): void
    {
        $cutoff = microtime(true) - $this->config['metric_retention'];

        // Clean memory metrics
        $this->metrics = array_filter($this->metrics, function ($metric) use ($cutoff) {
            return $metric['timestamp'] >= $cutoff;
        });

        // Clean old counters
        foreach ($this->counters as $key => $counter) {
            if ($counter['last_updated'] < $cutoff) {
                unset($this->counters[$key]);
            }
        }

        // Clean old timers
        foreach ($this->timers as $timerId => $timer) {
            if ($timer['start_time'] < $cutoff) {
                unset($this->timers[$timerId]);
            }
        }

        $this->logger->debug('Metrics cleanup completed', [
            'metrics_count' => count($this->metrics),
            'counters_count' => count($this->counters),
            'timers_count' => count($this->timers)
        ]);
    }

    /**
     * Store metric in cache
     */
    private function storeMetricInCache(array $metric): void
    {
        $minute = date('Y-m-d-H-i', (int)$metric['timestamp']);
        $cacheKey = "metrics:minute:{$minute}";

        $minuteMetrics = $this->cache->get($cacheKey, []);
        $minuteMetrics[] = $metric;

        $this->cache->set($cacheKey, $minuteMetrics, $this->config['cache_ttl']);
    }

    /**
     * Update counters
     */
    private function updateCounters(string $name, $value, array $tags): void
    {
        // Update per-minute counters
        $minute = date('Y-m-d-H-i');
        $counterKey = "counter:{$name}:{$minute}";

        if (is_numeric($value)) {
            $this->cache->increment($counterKey, (int)$value);
            $this->cache->expire($counterKey, 3600);
        }
    }

    /**
     * Build counter key
     */
    private function buildCounterKey(string $name, array $tags): string
    {
        ksort($tags);
        return $name . ':' . md5(serialize($tags));
    }

    /**
     * Check metric alerts
     */
    private function checkMetricAlerts(array $metric): void
    {
        if (!$this->config['alerts_enabled']) {
            return;
        }

        // Check if metric exceeds thresholds
        $thresholds = $this->config['alert_thresholds'][$metric['name']] ?? null;

        if ($thresholds && is_numeric($metric['value'])) {
            if (isset($thresholds['max']) && $metric['value'] > $thresholds['max']) {
                $this->triggerAlert('metric_threshold_exceeded', $metric, $thresholds);
            }

            if (isset($thresholds['min']) && $metric['value'] < $thresholds['min']) {
                $this->triggerAlert('metric_threshold_below', $metric, $thresholds);
            }
        }
    }

    /**
     * Trigger alert
     */
    private function triggerAlert(string $type, array $metric, array $thresholds): void
    {
        $alert = [
            'type' => $type,
            'metric' => $metric,
            'thresholds' => $thresholds,
            'timestamp' => microtime(true)
        ];

        $this->logger->warning("Metric alert triggered", $alert);

        // Dispatch alert event
        $this->eventDispatcher->dispatchByName('metric.alert', $alert);
    }

    /**
     * Parse period string to seconds
     */
    private function parsePeriod(string $period): int
    {
        $matches = [];
        if (preg_match('/^(\d+)([smhd])$/', $period, $matches)) {
            $value = (int)$matches[1];
            $unit = $matches[2];

            return match ($unit) {
                's' => $value,
                'm' => $value * 60,
                'h' => $value * 3600,
                'd' => $value * 86400,
                default => 3600
            };
        }

        return 3600; // Default to 1 hour
    }

    /**
     * Aggregate metrics
     */
    private function aggregateMetrics(array $metrics): array
    {
        $aggregated = [];

        foreach ($metrics as $metric) {
            $name = $metric['name'];

            if (!isset($aggregated[$name])) {
                $aggregated[$name] = [
                    'name' => $name,
                    'count' => 0,
                    'sum' => 0,
                    'min' => null,
                    'max' => null,
                    'avg' => 0
                ];
            }

            $aggregated[$name]['count']++;
            $aggregated[$name]['sum'] += $metric['value'];

            if ($aggregated[$name]['min'] === null || $metric['value'] < $aggregated[$name]['min']) {
                $aggregated[$name]['min'] = $metric['value'];
            }

            if ($aggregated[$name]['max'] === null || $metric['value'] > $aggregated[$name]['max']) {
                $aggregated[$name]['max'] = $metric['value'];
            }

            $aggregated[$name]['avg'] = $aggregated[$name]['sum'] / $aggregated[$name]['count'];
        }

        return array_values($aggregated);
    }

    /**
     * Get active counters
     */
    private function getActiveCounters(): array
    {
        return array_values($this->counters);
    }

    /**
     * Get current gauges
     */
    private function getCurrentGauges(): array
    {
        $gauges = [];

        // Get all gauge keys from cache
        try {
            // This is a simplified implementation
            // In production, you'd use cache->keys() or similar method
            $cacheKeys = $this->cache->get('metrics:gauge_keys', []);

            foreach ($cacheKeys as $key) {
                $value = $this->cache->get($key);
                if ($value !== null) {
                    $gauges[] = [
                        'name' => str_replace('metrics:gauge:', '', $key),
                        'value' => $value,
                        'timestamp' => time()
                    ];
                }
            }
        } catch (\Exception $e) {
            $this->logger->error('Failed to get current gauges', ['error' => $e->getMessage()]);
        }

        return $gauges;
    }

    /**
     * Get timing statistics
     */
    private function getTimingStats(int $seconds): array
    {
        $cutoff = microtime(true) - $seconds;

        $timingMetrics = array_filter($this->metrics, function ($metric) use ($cutoff) {
            return $metric['timestamp'] >= $cutoff &&
                   isset($metric['tags']['type']) &&
                   $metric['tags']['type'] === 'timing';
        });

        return $this->aggregateMetrics($timingMetrics);
    }

    /**
     * Update timing statistics
     */
    private function updateTimingStats(string $name, float $duration, array $tags): void
    {
        $cacheKey = "metrics:timing_stats:{$name}";
        $stats = $this->cache->get($cacheKey, [
            'count' => 0,
            'sum' => 0,
            'min' => null,
            'max' => null,
            'avg' => 0
        ]);

        $stats['count']++;
        $stats['sum'] += $duration;

        if ($stats['min'] === null || $duration < $stats['min']) {
            $stats['min'] = $duration;
        }

        if ($stats['max'] === null || $duration > $stats['max']) {
            $stats['max'] = $duration;
        }

        $stats['avg'] = $stats['sum'] / $stats['count'];

        $this->cache->set($cacheKey, $stats, $this->config['cache_ttl']);
    }

    /**
     * Update histogram buckets
     */
    private function updateHistogramBuckets(string $name, $value, array $tags): void
    {
        $buckets = [0.1, 0.5, 1.0, 2.5, 5.0, 10.0, 25.0, 50.0, 100.0];

        foreach ($buckets as $bucket) {
            if ($value <= $bucket) {
                $bucketKey = "metrics:histogram:{$name}:bucket_{$bucket}";
                $this->cache->increment($bucketKey);
                $this->cache->expire($bucketKey, $this->config['cache_ttl']);
                break;
            }
        }
    }

    /**
     * Calculate metrics summary
     */
    private function calculateMetricsSummary(int $seconds): array
    {
        $cutoff = microtime(true) - $seconds;

        $recentMetrics = array_filter($this->metrics, function ($metric) use ($cutoff) {
            return $metric['timestamp'] >= $cutoff;
        });

        return [
            'period_seconds' => $seconds,
            'total_metrics' => count($recentMetrics),
            'unique_metrics' => count(array_unique(array_column($recentMetrics, 'name'))),
            'metrics_per_second' => count($recentMetrics) / $seconds,
            'top_metrics' => $this->getTopMetrics(5),
            'summary_by_type' => $this->summarizeByType($recentMetrics)
        ];
    }

    /**
     * Summarize metrics by type
     */
    private function summarizeByType(array $metrics): array
    {
        $byType = [];

        foreach ($metrics as $metric) {
            $type = $metric['tags']['type'] ?? 'default';

            if (!isset($byType[$type])) {
                $byType[$type] = [
                    'count' => 0,
                    'total_value' => 0,
                    'avg_value' => 0
                ];
            }

            $byType[$type]['count']++;
            $byType[$type]['total_value'] += $metric['value'];
            $byType[$type]['avg_value'] = $byType[$type]['total_value'] / $byType[$type]['count'];
        }

        return $byType;
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'max_memory_metrics' => 10000,
            'metric_retention' => 3600, // 1 hour
            'cache_ttl' => 3600,
            'counter_ttl' => 86400,
            'gauge_ttl' => 300,
            'alerts_enabled' => true,
            'alert_thresholds' => [],
            'real_time_updates' => true
        ];
    }
}
