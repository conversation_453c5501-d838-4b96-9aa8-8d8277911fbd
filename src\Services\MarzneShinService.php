<?php

declare(strict_types=1);

namespace WeBot\Services;

use WeBot\Adapters\MarzneShinAdapter;
use WeBot\Core\CacheManager;

/**
 * Marzneshin Service
 *
 * Enhanced service for Marzneshin panel operations
 * with advanced features like templates, user groups, and multi-node support.
 *
 * @package WeBot\Services
 * @version 2.0
 */
class MarzneShinService
{
    private MarzneShinAdapter $adapter;
    private CacheManager $cache;
    private array $config;

    public function __construct(MarzneShinAdapter $adapter, CacheManager $cache, array $config = [])
    {
        $this->adapter = $adapter;
        $this->cache = $cache;
        $this->config = $config;
    }

    /**
     * Create user with template support
     */
    public function createUser(array $userData, ?string $templateId = null): array
    {
        try {
            if ($templateId) {
                return $this->adapter->createUserFromTemplate($templateId, $userData);
            }

            return $this->adapter->createUserAdvanced($userData);
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to create user: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get user with caching
     */
    public function getUser(string $username, bool $useCache = true): array
    {
        $cacheKey = "marzneshin:user:{$username}";

        if ($useCache) {
            $cached = $this->cache->get($cacheKey);
            if ($cached) {
                return $cached;
            }
        }

        $result = $this->adapter->getUser($username);

        if ($result['success'] && $useCache) {
            $this->cache->set($cacheKey, $result, 300); // 5 minutes
        }

        return $result;
    }

    /**
     * Update user with cache invalidation
     */
    public function updateUser(string $username, array $updateData): array
    {
        $result = $this->adapter->updateUser($username, $updateData);

        if ($result['success']) {
            // Invalidate cache
            $this->cache->delete("marzneshin:user:{$username}");
        }

        return $result;
    }

    /**
     * Delete user with cleanup
     */
    public function deleteUser(string $username): array
    {
        $result = $this->adapter->deleteUser($username);

        if ($result['success']) {
            // Cleanup cache
            $this->cache->delete("marzneshin:user:{$username}");
            $this->cache->delete("marzneshin:stats:{$username}");
        }

        return $result;
    }

    /**
     * Get user statistics with caching
     */
    public function getUserStats(string $username, bool $useCache = true): array
    {
        $cacheKey = "marzneshin:stats:{$username}";

        if ($useCache) {
            $cached = $this->cache->get($cacheKey);
            if ($cached) {
                return $cached;
            }
        }

        $result = $this->adapter->getUserStats($username);

        if ($result['success'] && $useCache) {
            $this->cache->set($cacheKey, $result, 60); // 1 minute
        }

        return $result;
    }

    /**
     * Generate configuration with client type support
     */
    public function generateConfig(string $username, string $clientType = 'v2ray'): array
    {
        return $this->adapter->generateConfig($username, $clientType);
    }

    /**
     * Get available templates
     */
    public function getTemplates(bool $useCache = true): array
    {
        $cacheKey = 'marzneshin:templates';

        if ($useCache) {
            $cached = $this->cache->get($cacheKey);
            if ($cached) {
                return $cached;
            }
        }

        $result = $this->adapter->getUserTemplates();

        if ($result['success'] && $useCache) {
            $this->cache->set($cacheKey, $result, 3600); // 1 hour
        }

        return $result;
    }

    /**
     * Get user groups
     */
    public function getUserGroups(bool $useCache = true): array
    {
        $cacheKey = 'marzneshin:groups';

        if ($useCache) {
            $cached = $this->cache->get($cacheKey);
            if ($cached) {
                return $cached;
            }
        }

        $result = $this->adapter->getUserGroups();

        if ($result['success'] && $useCache) {
            $this->cache->set($cacheKey, $result, 3600); // 1 hour
        }

        return $result;
    }

    /**
     * Assign user to group
     */
    public function assignUserToGroup(string $username, int $groupId): array
    {
        $result = $this->adapter->assignUserToGroup($username, $groupId);

        if ($result['success']) {
            // Invalidate user cache
            $this->cache->delete("marzneshin:user:{$username}");
        }

        return $result;
    }

    /**
     * Get subscription information
     */
    public function getSubscriptionInfo(string $username): array
    {
        return $this->adapter->getSubscriptionInfo($username);
    }

    /**
     * Reset user subscription
     */
    public function resetSubscription(string $username): array
    {
        $result = $this->adapter->resetUserSubscription($username);

        if ($result['success']) {
            // Invalidate related caches
            $this->cache->delete("marzneshin:user:{$username}");
            $this->cache->delete("marzneshin:stats:{$username}");
        }

        return $result;
    }

    /**
     * Get nodes information
     */
    public function getNodes(bool $useCache = true): array
    {
        $cacheKey = 'marzneshin:nodes';

        if ($useCache) {
            $cached = $this->cache->get($cacheKey);
            if ($cached) {
                return $cached;
            }
        }

        $result = $this->adapter->getNodes();

        if ($result['success'] && $useCache) {
            $this->cache->set($cacheKey, $result, 300); // 5 minutes
        }

        return $result;
    }

    /**
     * Get node usage statistics
     */
    public function getNodeUsage(int $nodeId): array
    {
        return $this->adapter->getNodeUsage($nodeId);
    }

    /**
     * Get system information with nodes
     */
    public function getSystemInfo(bool $useCache = true): array
    {
        $cacheKey = 'marzneshin:system';

        if ($useCache) {
            $cached = $this->cache->get($cacheKey);
            if ($cached) {
                return $cached;
            }
        }

        $result = $this->adapter->getSystemInfoAdvanced();

        if ($result['success'] && $useCache) {
            $this->cache->set($cacheKey, $result, 60); // 1 minute
        }

        return $result;
    }

    /**
     * Bulk operations with progress tracking
     */
    public function bulkUpdateUsers(array $usernames, array $updateData, callable $progressCallback = null): array
    {
        $results = [];
        $total = count($usernames);
        $processed = 0;

        foreach ($usernames as $username) {
            $result = $this->updateUser($username, $updateData);
            $results[$username] = $result;
            $processed++;

            if ($progressCallback) {
                $progressCallback($processed, $total, $username, $result);
            }
        }

        $successCount = count(array_filter($results, fn($r) => $r['success']));

        return [
            'success' => $successCount > 0,
            'total' => $total,
            'successful' => $successCount,
            'failed' => $total - $successCount,
            'results' => $results
        ];
    }

    /**
     * Health check with node status
     */
    public function healthCheck(): array
    {
        $panelHealth = $this->adapter->healthCheck();
        $systemInfo = $this->getSystemInfo();
        $nodes = $this->getNodes();

        return [
            'success' => $panelHealth['success'],
            'panel_status' => $panelHealth['success'] ? 'online' : 'offline',
            'system_info' => $systemInfo['success'] ? $systemInfo : null,
            'nodes_count' => $nodes['success'] ? count($nodes['nodes']) : 0,
            'nodes_status' => $nodes['success'] ? 'available' : 'unavailable',
            'timestamp' => time()
        ];
    }

    /**
     * Clear all caches
     */
    public function clearCache(): bool
    {
        $patterns = [
            'marzneshin:user:*',
            'marzneshin:stats:*',
            'marzneshin:templates',
            'marzneshin:groups',
            'marzneshin:nodes',
            'marzneshin:system'
        ];

        foreach ($patterns as $pattern) {
            $this->cache->deletePattern($pattern);
        }

        return true;
    }

    /**
     * Get adapter instance
     */
    public function getAdapter(): MarzneShinAdapter
    {
        return $this->adapter;
    }
}
