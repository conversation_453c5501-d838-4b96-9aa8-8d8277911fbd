<?php

declare(strict_types=1);

namespace WeBot\Services;

use WeBot\Core\Config;
use WeBot\Utils\Logger;
use Monolog\Logger as MonologLogger;
use WeBot\Exceptions\WeBotException;

/**
 * Telegram Service
 *
 * Handles all Telegram Bot API operations including
 * sending messages, photos, handling callbacks, and webhooks.
 *
 * @package WeBot\Services
 * @version 2.0
 */
class TelegramService
{
    private Config $config;
    private MonologLogger $logger;
    private string $botToken;
    private string $apiUrl;
    private int $timeout;
    private int $retryAttempts;

    public function __construct(Config $config)
    {
        $this->config = $config;
        $this->logger = Logger::getInstance();

        $this->botToken = $config->get('telegram.token');
        $this->apiUrl = $config->get('telegram.api_url', 'https://api.telegram.org/bot');
        $this->timeout = $config->get('telegram.timeout', 30);
        $this->retryAttempts = $config->get('telegram.retry_attempts', 3);

        if (empty($this->botToken)) {
            throw new WeBotException('Telegram bot token is required');
        }
    }

    /**
     * Send message
     */
    public function sendMessage(array $params): array
    {
        $defaultParams = [
            'parse_mode' => 'HTML',
            'disable_web_page_preview' => true
        ];

        $params = array_merge($defaultParams, $params);

        return $this->makeRequest('sendMessage', $params);
    }

    /**
     * Edit message text
     */
    public function editMessageText(array $params): array
    {
        $defaultParams = [
            'parse_mode' => 'HTML',
            'disable_web_page_preview' => true
        ];

        $params = array_merge($defaultParams, $params);

        return $this->makeRequest('editMessageText', $params);
    }

    /**
     * Delete message
     */
    public function deleteMessage(array $params): array
    {
        return $this->makeRequest('deleteMessage', $params);
    }

    /**
     * Send photo
     */
    public function sendPhoto(array $params): array
    {
        $defaultParams = [
            'parse_mode' => 'HTML'
        ];

        $params = array_merge($defaultParams, $params);

        return $this->makeRequest('sendPhoto', $params);
    }

    /**
     * Send document
     */
    public function sendDocument(array $params): array
    {
        $defaultParams = [
            'parse_mode' => 'HTML'
        ];

        $params = array_merge($defaultParams, $params);

        return $this->makeRequest('sendDocument', $params);
    }

    /**
     * Answer callback query
     */
    public function answerCallbackQuery(array $params): array
    {
        return $this->makeRequest('answerCallbackQuery', $params);
    }

    /**
     * Get chat member
     */
    public function getChatMember(int $chatId, int $userId): array
    {
        return $this->makeRequest('getChatMember', [
            'chat_id' => $chatId,
            'user_id' => $userId
        ]);
    }

    /**
     * Ban chat member
     */
    public function banChatMember(int $chatId, int $userId, ?int $untilDate = null): array
    {
        $params = [
            'chat_id' => $chatId,
            'user_id' => $userId
        ];

        if ($untilDate) {
            $params['until_date'] = $untilDate;
        }

        return $this->makeRequest('banChatMember', $params);
    }

    /**
     * Unban chat member
     */
    public function unbanChatMember(int $chatId, int $userId): array
    {
        return $this->makeRequest('unbanChatMember', [
            'chat_id' => $chatId,
            'user_id' => $userId
        ]);
    }

    /**
     * Set webhook
     */
    public function setWebhook(string $url, array $options = []): array
    {
        $params = array_merge(['url' => $url], $options);
        return $this->makeRequest('setWebhook', $params);
    }

    /**
     * Delete webhook
     */
    public function deleteWebhook(): array
    {
        return $this->makeRequest('deleteWebhook');
    }

    /**
     * Get webhook info
     */
    public function getWebhookInfo(): array
    {
        return $this->makeRequest('getWebhookInfo');
    }

    /**
     * Get bot info
     */
    public function getMe(): array
    {
        return $this->makeRequest('getMe');
    }

    /**
     * Send message to multiple users
     */
    public function broadcast(array $userIds, string $text, ?array $keyboard = null): array
    {
        $results = [];
        $successCount = 0;
        $failCount = 0;

        foreach ($userIds as $userId) {
            try {
                $params = [
                    'chat_id' => $userId,
                    'text' => $text
                ];

                if ($keyboard) {
                    $params['reply_markup'] = json_encode($keyboard);
                }

                $result = $this->sendMessage($params);

                if ($result['ok'] ?? false) {
                    $successCount++;
                } else {
                    $failCount++;
                }

                $results[] = [
                    'user_id' => $userId,
                    'success' => $result['ok'] ?? false,
                    'error' => $result['description'] ?? null
                ];

                // Small delay to avoid rate limiting
                usleep(50000); // 50ms
            } catch (\Exception $e) {
                $failCount++;
                $results[] = [
                    'user_id' => $userId,
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }

        $this->logger->info('Broadcast completed', [
            'total_users' => count($userIds),
            'success_count' => $successCount,
            'fail_count' => $failCount
        ]);

        return [
            'total' => count($userIds),
            'success' => $successCount,
            'failed' => $failCount,
            'results' => $results
        ];
    }

    /**
     * Make HTTP request to Telegram API
     */
    private function makeRequest(string $method, array $params = []): array
    {
        $url = $this->apiUrl . $this->botToken . '/' . $method;

        // Check if we're in testing mode
        if (env('DISABLE_EXTERNAL_APIS', false)) {
            return $this->getMockResponse($method, $params);
        }

        $attempt = 0;
        $lastError = null;

        while ($attempt < $this->retryAttempts) {
            try {
                $result = $this->executeRequest($url, $params);

                $this->logger->debug('Telegram API request', [
                    'method' => $method,
                    'params' => $this->sanitizeParams($params),
                    'response' => $result,
                    'attempt' => $attempt + 1
                ]);

                return $result;
            } catch (\Exception $e) {
                $lastError = $e;
                $attempt++;

                if ($attempt < $this->retryAttempts) {
                    $delay = (int) (pow(2, $attempt) * 1000000); // Exponential backoff in microseconds
                    usleep($delay);
                }
            }
        }

        $this->logger->error('Telegram API request failed', [
            'method' => $method,
            'params' => $this->sanitizeParams($params),
            'attempts' => $this->retryAttempts,
            'error' => $lastError->getMessage()
        ]);

        throw new WeBotException(
            "Telegram API request failed after {$this->retryAttempts} attempts: " . $lastError->getMessage()
        );
    }

    /**
     * Execute HTTP request
     */
    private function executeRequest(string $url, array $params): array
    {
        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $params,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->timeout,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2,
            CURLOPT_USERAGENT => 'WeBot/2.0',
            CURLOPT_HTTPHEADER => [
                'Accept: application/json',
                'Content-Type: application/x-www-form-urlencoded'
            ]
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);

        curl_close($ch);

        if ($response === false) {
            throw new WeBotException("cURL error: {$error}");
        }

        if ($httpCode !== 200) {
            throw new WeBotException("HTTP error: {$httpCode}");
        }

        $decoded = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new WeBotException("JSON decode error: " . json_last_error_msg());
        }

        return $decoded;
    }

    /**
     * Get mock response for testing
     */
    private function getMockResponse(string $method, array $params): array
    {
        $mockResponses = $GLOBALS['mock_telegram_responses'] ?? [];

        if (isset($mockResponses[$method])) {
            return $mockResponses[$method];
        }

        // Default mock response
        return [
            'ok' => true,
            'result' => [
                'message_id' => rand(1000, 9999),
                'date' => time(),
                'chat' => ['id' => $params['chat_id'] ?? 123456789],
                'text' => $params['text'] ?? 'Mock response'
            ]
        ];
    }

    /**
     * Sanitize parameters for logging (remove sensitive data)
     */
    private function sanitizeParams(array $params): array
    {
        $sanitized = $params;

        // Remove or mask sensitive fields
        $sensitiveFields = ['photo', 'document', 'video', 'audio'];

        foreach ($sensitiveFields as $field) {
            if (isset($sanitized[$field])) {
                $sanitized[$field] = '[BINARY_DATA]';
            }
        }

        return $sanitized;
    }

    /**
     * Check if user is member of channel
     */
    public function isChannelMember(int $userId, string $channelId): bool
    {
        try {
            $result = $this->getChatMember((int) $channelId, $userId);

            if (!($result['ok'] ?? false)) {
                return false;
            }

            $status = $result['result']['status'] ?? '';

            return in_array($status, ['member', 'administrator', 'creator']);
        } catch (\Exception $e) {
            $this->logger->warning('Failed to check channel membership', [
                'user_id' => $userId,
                'channel_id' => $channelId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Format text for Telegram HTML
     */
    public function formatText(string $text): string
    {
        // Escape HTML special characters
        $text = htmlspecialchars($text, ENT_QUOTES, 'UTF-8');

        // Convert markdown-like formatting to HTML
        $text = preg_replace('/\*\*(.*?)\*\*/', '<b>$1</b>', $text);
        $text = preg_replace('/\*(.*?)\*/', '<i>$1</i>', $text);
        $text = preg_replace('/`(.*?)`/', '<code>$1</code>', $text);

        return $text;
    }

    /**
     * Create inline keyboard
     */
    public function createInlineKeyboard(array $buttons): array
    {
        return ['inline_keyboard' => $buttons];
    }

    /**
     * Create reply keyboard
     */
    public function createReplyKeyboard(array $buttons, bool $resize = true, bool $oneTime = false): array
    {
        return [
            'keyboard' => $buttons,
            'resize_keyboard' => $resize,
            'one_time_keyboard' => $oneTime
        ];
    }

    /**
     * Remove keyboard
     */
    public function removeKeyboard(): array
    {
        return ['remove_keyboard' => true];
    }

    /**
     * Get bot token (for debugging)
     */
    public function getBotToken(): string
    {
        return substr($this->botToken, 0, 10) . '...';
    }

    /**
     * Get API URL
     */
    public function getApiUrl(): string
    {
        return $this->apiUrl;
    }

    /**
     * Get recent notifications (for testing)
     */
    public function getRecentNotifications(int $userId, int $limit = 10): array
    {
        try {
            // Mock recent notifications
            return [
                [
                    'id' => 1,
                    'user_id' => $userId,
                    'type' => 'payment_success',
                    'message' => 'Payment completed successfully',
                    'created_at' => date('Y-m-d H:i:s', time() - 3600)
                ],
                [
                    'id' => 2,
                    'user_id' => $userId,
                    'type' => 'service_activated',
                    'message' => 'Your VPN service has been activated',
                    'created_at' => date('Y-m-d H:i:s', time() - 7200)
                ]
            ];
        } catch (\Exception $e) {
            $this->logger->error('Failed to get recent notifications', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [];
        }
    }
}
