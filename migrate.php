<?php

declare(strict_types=1);

/**
 * WeBot Migration Command Line Tool
 * 
 * Usage:
 * php migrate.php migrate          - Run all pending migrations
 * php migrate.php rollback         - Rollback last migration
 * php migrate.php status           - Show migration status
 * php migrate.php reset            - Reset all migrations (dangerous!)
 * php migrate.php create <name>    - Create new migration file
 * 
 * @package WeBot
 * @version 2.0
 */

require_once __DIR__ . '/vendor/autoload.php';

use WeBot\Core\Database;
use WeBot\Core\MigrationRunner;

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value, '"\'');
        }
    }
}

// Database configuration
$config = [
    'default' => $_ENV['DB_CONNECTION'] ?? 'mysql',
    'connections' => [
        'mysql' => [
            'driver' => 'mysql',
            'host' => $_ENV['DB_HOST'] ?? 'localhost',
            'port' => (int)($_ENV['DB_PORT'] ?? 3306),
            'database' => $_ENV['DB_DATABASE'] ?? 'webot',
            'username' => $_ENV['DB_USERNAME'] ?? 'root',
            'password' => $_ENV['DB_PASSWORD'] ?? '',
            'charset' => $_ENV['DB_CHARSET'] ?? 'utf8mb4',
            'collation' => $_ENV['DB_COLLATION'] ?? 'utf8mb4_unicode_ci',
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ]
        ]
    ],
    'pool' => [
        'max_connections' => 10
    ]
];

try {
    // Initialize database and migration runner
    $database = new Database($config);
    $migrationRunner = new MigrationRunner($database, __DIR__ . '/migrations');

    // Parse command line arguments
    $command = $argv[1] ?? 'help';
    $argument = $argv[2] ?? null;

    echo "🤖 WeBot Migration Tool\n";
    echo "========================\n\n";

    switch ($command) {
        case 'migrate':
            echo "🔄 Running migrations...\n\n";
            $result = $migrationRunner->migrate();
            echo "\n✅ " . $result['message'] . "\n";
            
            if (isset($result['executed']) && !empty($result['executed'])) {
                echo "📋 Executed migrations:\n";
                foreach ($result['executed'] as $migration) {
                    echo "   - {$migration}\n";
                }
            }
            break;

        case 'rollback':
            echo "⏪ Rolling back last migration...\n\n";
            $result = $migrationRunner->rollback();
            echo "\n✅ " . $result['message'] . "\n";
            
            if (isset($result['rolled_back'])) {
                echo "📋 Rolled back: {$result['rolled_back']}\n";
            }
            break;

        case 'status':
            echo "📊 Migration Status:\n\n";
            $status = $migrationRunner->status();
            
            if (empty($status)) {
                echo "No migrations found.\n";
            } else {
                foreach ($status as $migration) {
                    $statusIcon = $migration['status'] === 'executed' ? '✅' : '⏳';
                    $executedAt = $migration['executed_at'] ? " (executed: {$migration['executed_at']})" : '';
                    echo "{$statusIcon} {$migration['file']} - {$migration['status']}{$executedAt}\n";
                }
            }
            break;

        case 'reset':
            echo "⚠️  WARNING: This will drop all tables and reset migrations!\n";
            echo "Are you sure you want to continue? (yes/no): ";
            
            $handle = fopen("php://stdin", "r");
            $confirmation = trim(fgets($handle));
            fclose($handle);
            
            if (strtolower($confirmation) === 'yes') {
                echo "\n🔄 Resetting database...\n\n";
                $result = $migrationRunner->reset();
                echo "\n✅ " . $result['message'] . "\n";
                echo "📋 Reset {$result['reset_migrations']} migrations\n";
            } else {
                echo "\n❌ Reset cancelled.\n";
            }
            break;

        case 'create':
            if (!$argument) {
                echo "❌ Error: Migration name is required.\n";
                echo "Usage: php migrate.php create <migration_name>\n";
                exit(1);
            }
            
            echo "📝 Creating new migration: {$argument}\n\n";
            $filepath = $migrationRunner->create($argument);
            echo "✅ Migration created: {$filepath}\n";
            echo "📝 Edit the file to add your migration SQL.\n";
            break;

        case 'help':
        default:
            echo "📖 Available Commands:\n\n";
            echo "  migrate              Run all pending migrations\n";
            echo "  rollback             Rollback the last migration\n";
            echo "  status               Show migration status\n";
            echo "  reset                Reset all migrations (dangerous!)\n";
            echo "  create <name>        Create a new migration file\n";
            echo "  help                 Show this help message\n\n";
            echo "📋 Examples:\n";
            echo "  php migrate.php migrate\n";
            echo "  php migrate.php create create_users_table\n";
            echo "  php migrate.php status\n";
            break;
    }

    echo "\n";

} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    
    if ($_ENV['APP_DEBUG'] ?? false) {
        echo "\n🔍 Stack Trace:\n";
        echo $e->getTraceAsString() . "\n";
    }
    
    exit(1);
}
