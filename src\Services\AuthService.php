<?php

declare(strict_types=1);

namespace WeBot\Services;

use WeBot\Core\Database;
use WeBot\Core\CacheManager;
use WeBot\Core\SecurityManager;
use WeBot\Exceptions\WeBotException;

/**
 * Authentication Service
 *
 * Handles user authentication, registration, session management,
 * and security features for the bot.
 *
 * @package WeBot\Services
 * @version 2.0
 */
class AuthService
{
    private Database $database;
    private CacheManager $cache;
    private SecurityManager $security;
    private array $config;

    public function __construct(Database $database, CacheManager $cache, SecurityManager $security, array $config = [])
    {
        $this->database = $database;
        $this->cache = $cache;
        $this->security = $security;
        $this->config = array_merge([
            'session_lifetime' => 86400, // 24 hours
            'max_login_attempts' => 5,
            'lockout_duration' => 900, // 15 minutes
            'password_min_length' => 8,
            'require_phone_verification' => false,
            'cache_user_data' => true,
            'cache_ttl' => 3600, // 1 hour
        ], $config);
    }

    /**
     * Register new user
     */
    public function registerUser(array $userData): array
    {
        try {
            // Validate required fields
            $required = ['telegram_id', 'first_name'];
            foreach ($required as $field) {
                if (empty($userData[$field])) {
                    throw new WeBotException("Missing required field: {$field}");
                }
            }

            $telegramId = (int) $userData['telegram_id'];

            // Check if user already exists
            if ($this->userExists($telegramId)) {
                return [
                    'success' => false,
                    'error' => 'User already exists'
                ];
            }

            // Prepare user data
            $userRecord = [
                'userid' => $telegramId,
                'first_name' => $this->security->sanitizeInput($userData['first_name']),
                'last_name' => $this->security->sanitizeInput($userData['last_name'] ?? ''),
                'username' => $this->security->sanitizeInput($userData['username'] ?? ''),
                'language_code' => $userData['language_code'] ?? 'fa',
                'status' => 'active',
                'role' => 'user',
                'wallet' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'last_login_at' => date('Y-m-d H:i:s')
            ];

            // Insert user
            $userInsertData = [
                'userid' => $userRecord['userid'],
                'first_name' => $userRecord['first_name'],
                'last_name' => $userRecord['last_name'],
                'username' => $userRecord['username'],
                'language_code' => $userRecord['language_code'],
                'status' => $userRecord['status'],
                'role' => $userRecord['role'],
                'wallet' => $userRecord['wallet'],
                'created_at' => $userRecord['created_at'],
                'last_login_at' => $userRecord['last_login_at']
            ];

            $userId = $this->database->insert('users', $userInsertData);
            $success = $userId > 0;

            if (!$success) {
                throw new WeBotException('Failed to create user record');
            }

            // Create session
            $this->createSession($telegramId);

            // Clear cache
            $this->clearUserCache($telegramId);

            return [
                'success' => true,
                'user_id' => $userId,
                'telegram_id' => $telegramId,
                'message' => 'User registered successfully'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Authenticate user
     */
    public function authenticateUser(int $telegramId): array
    {
        try {
            // Check rate limiting
            if ($this->isRateLimited($telegramId)) {
                return [
                    'success' => false,
                    'error' => 'Too many login attempts. Please try again later.',
                    'locked_until' => $this->getLockoutTime($telegramId)
                ];
            }

            // Get user data
            $user = $this->getUserByTelegramId($telegramId);
            if (!$user) {
                $this->recordFailedAttempt($telegramId);
                return [
                    'success' => false,
                    'error' => 'User not found'
                ];
            }

            // Check user status
            if ($user['status'] !== 'active') {
                return [
                    'success' => false,
                    'error' => 'Account is not active',
                    'status' => $user['status']
                ];
            }

            // Update last login
            $this->updateLastLogin($telegramId);

            // Create/update session
            $this->createSession($telegramId);

            // Clear failed attempts
            $this->clearFailedAttempts($telegramId);

            return [
                'success' => true,
                'user' => $user,
                'message' => 'Authentication successful'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Update last login time
     */
    public function updateLastLogin(int $telegramId): bool
    {
        try {
            $success = $this->database->update(
                'users',
                ['last_login_at' => date('Y-m-d H:i:s')],
                ['userid' => $telegramId]
            ) > 0;

            if ($success) {
                $this->clearUserCache($telegramId);
            }

            return $success;
        } catch (\Exception $e) {
            error_log("Failed to update last login: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update user language
     */
    public function updateUserLanguage(int $telegramId, string $language): array
    {
        try {
            // Validate language
            $supportedLanguages = ['fa', 'en', 'ar'];
            if (!in_array($language, $supportedLanguages)) {
                throw new WeBotException('Unsupported language');
            }

            $success = $this->database->update(
                'users',
                ['language_code' => $language],
                ['userid' => $telegramId]
            ) > 0;

            if ($success) {
                $this->clearUserCache($telegramId);
            }

            return [
                'success' => $success,
                'message' => $success ? 'Language updated successfully' : 'Failed to update language'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Create user session
     */
    public function createSession(int $telegramId): string
    {
        $sessionId = bin2hex(random_bytes(32));
        $expiresAt = time() + $this->config['session_lifetime'];

        try {
            // Store in database
            $sessionData = [
                'user_id' => $telegramId,
                'session_id' => $sessionId,
                'expires_at' => date('Y-m-d H:i:s', $expiresAt),
                'created_at' => date('Y-m-d H:i:s')
            ];

            // Use upsert logic
            $existing = $this->database->selectOne('user_sessions', ['user_id' => $telegramId]);
            if ($existing) {
                $this->database->update('user_sessions', $sessionData, ['user_id' => $telegramId]);
            } else {
                $this->database->insert('user_sessions', $sessionData);
            }

            // Store in cache
            $this->cache->set("session:{$sessionId}", [
                'user_id' => $telegramId,
                'expires_at' => $expiresAt
            ], $this->config['session_lifetime']);

            return $sessionId;
        } catch (\Exception $e) {
            error_log("Failed to create session: " . $e->getMessage());
            return '';
        }
    }

    /**
     * Validate session
     */
    public function validateSession(string $sessionId): ?array
    {
        // Check cache first
        $cached = $this->cache->get("session:{$sessionId}");
        if ($cached && $cached['expires_at'] > time()) {
            return $cached;
        }

        // Check database
        try {
            $sql = "SELECT user_id, expires_at FROM user_sessions WHERE session_id = ? AND expires_at > NOW()";
            $results = $this->database->query($sql, [$sessionId]);
            $session = $results[0] ?? null;

            if ($session) {
                // Update cache
                $this->cache->set("session:{$sessionId}", [
                    'user_id' => (int) $session['user_id'],
                    'expires_at' => strtotime($session['expires_at'])
                ], $this->config['session_lifetime']);

                return [
                    'user_id' => (int) $session['user_id'],
                    'expires_at' => strtotime($session['expires_at'])
                ];
            }

            return null;
        } catch (\Exception $e) {
            error_log("Failed to validate session: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Clear user session
     */
    public function clearUserSession(int $telegramId): bool
    {
        try {
            // Get session ID first
            $sql = "SELECT session_id FROM user_sessions WHERE user_id = ?";
            $results = $this->database->query($sql, [$telegramId]);
            $session = $results[0] ?? null;

            if ($session) {
                // Clear from cache
                $this->cache->delete("session:{$session['session_id']}");
            }

            // Clear from database
            $success = $this->database->delete('user_sessions', ['user_id' => $telegramId]) > 0;

            return $success;
        } catch (\Exception $e) {
            error_log("Failed to clear user session: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if user exists
     */
    public function userExists(int $telegramId): bool
    {
        try {
            $sql = "SELECT COUNT(*) as count FROM users WHERE userid = ?";
            $results = $this->database->query($sql, [$telegramId]);
            $row = $results[0] ?? ['count' => 0];

            return $row['count'] > 0;
        } catch (\Exception $e) {
            error_log("Failed to check user existence: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get user by Telegram ID
     */
    public function getUserByTelegramId(int $telegramId): ?array
    {
        // Check cache first
        if ($this->config['cache_user_data']) {
            $cacheKey = "user:{$telegramId}";
            $cached = $this->cache->get($cacheKey);
            if ($cached) {
                return $cached;
            }
        }

        try {
            $user = $this->database->selectOne('users', ['userid' => $telegramId]);

            if ($user && $this->config['cache_user_data']) {
                $this->cache->set("user:{$telegramId}", $user, $this->config['cache_ttl']);
            }

            return $user ?: null;
        } catch (\Exception $e) {
            error_log("Failed to get user: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Check rate limiting
     */
    private function isRateLimited(int $telegramId): bool
    {
        $cacheKey = "failed_attempts:{$telegramId}";
        $attempts = $this->cache->get($cacheKey);

        if (!$attempts) {
            return false;
        }

        return $attempts['count'] >= $this->config['max_login_attempts'] &&
               (time() - $attempts['last_attempt']) < $this->config['lockout_duration'];
    }

    /**
     * Record failed login attempt
     */
    private function recordFailedAttempt(int $telegramId): void
    {
        $cacheKey = "failed_attempts:{$telegramId}";
        $attempts = $this->cache->get($cacheKey) ?: ['count' => 0, 'last_attempt' => 0];

        $attempts['count']++;
        $attempts['last_attempt'] = time();

        $this->cache->set($cacheKey, $attempts, $this->config['lockout_duration']);
    }

    /**
     * Clear failed attempts
     */
    private function clearFailedAttempts(int $telegramId): void
    {
        $cacheKey = "failed_attempts:{$telegramId}";
        $this->cache->delete($cacheKey);
    }

    /**
     * Get lockout time
     */
    private function getLockoutTime(int $telegramId): int
    {
        $cacheKey = "failed_attempts:{$telegramId}";
        $attempts = $this->cache->get($cacheKey);

        if (!$attempts) {
            return 0;
        }

        return $attempts['last_attempt'] + $this->config['lockout_duration'];
    }

    /**
     * Clear user cache
     */
    private function clearUserCache(int $telegramId): void
    {
        if ($this->config['cache_user_data']) {
            $this->cache->delete("user:{$telegramId}");
        }
    }

    /**
     * Clean expired sessions
     */
    public function cleanExpiredSessions(): int
    {
        try {
            $sql = "DELETE FROM user_sessions WHERE expires_at < NOW()";
            $deletedCount = $this->database->execute($sql);

            return $deletedCount;
        } catch (\Exception $e) {
            error_log("Failed to clean expired sessions: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get user statistics
     */
    public function getUserStats(): array
    {
        try {
            $sql = "SELECT
                COUNT(*) as total_users,
                SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
                SUM(CASE WHEN last_login_at > DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 ELSE 0 END) as daily_active,
                SUM(CASE WHEN created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 ELSE 0 END) as new_today
                FROM users";
            $results = $this->database->query($sql);
            $stats = $results[0] ?? [];

            return [
                'success' => true,
                'stats' => $stats
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
