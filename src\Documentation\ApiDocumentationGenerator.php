<?php

declare(strict_types=1);

namespace WeBot\Documentation;

use WeBot\Core\ConfigInterface;
use WeBot\Utils\Logger;

/**
 * API Documentation Generator
 *
 * Generates comprehensive API documentation including:
 * - OpenAPI specification
 * - Markdown documentation
 * - Postman collection
 * - Code examples
 *
 * @package WeBot\Documentation
 * @version 2.0
 */
class ApiDocumentationGenerator
{
    private ConfigInterface $config;
    private Logger $logger;
    private OpenApiGenerator $openApiGenerator;
    private string $outputDir;

    public function __construct(ConfigInterface $config, string $outputDir = 'docs/api')
    {
        $this->config = $config;
        $this->logger = Logger::getInstance();
        $this->openApiGenerator = new OpenApiGenerator();
        $this->outputDir = $outputDir;

        $this->ensureOutputDirectory();
    }

    /**
     * Generate all documentation formats
     */
    public function generateAll(): array
    {
        $results = [];

        try {
            $this->logger->info('Starting API documentation generation');

            // Generate OpenAPI specification
            $results['openapi_json'] = $this->generateOpenApiJson();
            $results['openapi_yaml'] = $this->generateOpenApiYaml();

            // Generate Markdown documentation
            $results['markdown'] = $this->generateMarkdownDocs();

            // Generate Postman collection
            $results['postman'] = $this->generatePostmanCollection();

            // Generate code examples
            $results['examples'] = $this->generateCodeExamples();

            // Generate authentication guide
            $results['auth_guide'] = $this->generateAuthenticationGuide();

            // Generate error codes documentation
            $results['error_codes'] = $this->generateErrorCodesDoc();

            // Generate rate limiting documentation
            $results['rate_limiting'] = $this->generateRateLimitingDoc();

            $this->logger->info('API documentation generation completed', $results);

            return [
                'success' => true,
                'message' => 'Documentation generated successfully',
                'files' => $results
            ];
        } catch (\Exception $e) {
            $this->logger->error('Documentation generation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Generate OpenAPI JSON specification
     */
    public function generateOpenApiJson(): string
    {
        $filename = $this->outputDir . '/openapi.json';
        $this->openApiGenerator->exportToFile($filename);

        return $filename;
    }

    /**
     * Generate OpenAPI YAML specification
     */
    public function generateOpenApiYaml(): string
    {
        $filename = $this->outputDir . '/openapi.yaml';
        $this->openApiGenerator->exportToYaml($filename);

        return $filename;
    }

    /**
     * Generate comprehensive Markdown documentation
     */
    public function generateMarkdownDocs(): string
    {
        $filename = $this->outputDir . '/API_DOCUMENTATION.md';

        $content = $this->buildMarkdownContent();
        file_put_contents($filename, $content);

        return $filename;
    }

    /**
     * Build Markdown documentation content
     */
    private function buildMarkdownContent(): string
    {
        $content = <<<'MD'
# WeBot API Documentation

## Overview

WeBot API provides comprehensive endpoints for managing VPN services, users, payments, and administrative functions. This RESTful API uses JSON for data exchange and supports multiple authentication methods.

### Base URL
```
Production: https://api.webot.com
Staging: https://staging-api.webot.com
Development: http://localhost:8000
```

### API Version
Current version: **2.0.0**

## Authentication

WeBot API supports multiple authentication methods:

### 1. JWT Bearer Token
```http
Authorization: Bearer <jwt_token>
```

### 2. Telegram Bot API Secret Token
```http
X-Telegram-Bot-Api-Secret-Token: <secret_token>
```

### 3. Session-based Authentication
```http
Cookie: WEBOT_SESSION=<session_id>
```

## Rate Limiting

API requests are rate-limited to ensure fair usage:

- **General endpoints**: 100 requests per minute per IP
- **Authentication endpoints**: 10 requests per minute per IP
- **Webhook endpoints**: 1000 requests per minute per IP

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

## Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": "ERROR_CODE",
  "message": "Human-readable error message",
  "details": {
    // Additional error details
  }
}
```

## Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `VALIDATION_ERROR` | 400 | Invalid input data |
| `UNAUTHORIZED` | 401 | Authentication required |
| `FORBIDDEN` | 403 | Insufficient permissions |
| `NOT_FOUND` | 404 | Resource not found |
| `RATE_LIMITED` | 429 | Rate limit exceeded |
| `INTERNAL_ERROR` | 500 | Internal server error |

## Endpoints

### Authentication

#### POST /api/auth/login
Authenticate user and receive JWT token.

**Request:**
```json
{
  "telegram_id": 123456789,
  "username": "john_doe",
  "first_name": "John",
  "auth_data": "telegram_auth_string"
}
```

**Response:**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "telegram_id": 123456789,
    "username": "john_doe",
    "first_name": "John",
    "status": "active",
    "balance": 0
  }
}
```

#### POST /api/auth/refresh
Refresh expired JWT token.

**Response:**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### User Management

#### GET /api/users/profile
Get current user profile information.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "telegram_id": 123456789,
    "username": "john_doe",
    "first_name": "John",
    "last_name": "Doe",
    "phone": "+1234567890",
    "email": "<EMAIL>",
    "status": "active",
    "balance": 25.50,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-15T12:30:00Z"
  }
}
```

#### PUT /api/users/profile
Update user profile information.

**Request:**
```json
{
  "first_name": "John",
  "last_name": "Doe",
  "phone": "+1234567890",
  "email": "<EMAIL>"
}
```

### Service Management

#### GET /api/services
List user VPN services with pagination.

**Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20, max: 100)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "user_id": 1,
      "plan_id": 1,
      "server_id": 1,
      "uuid": "550e8400-e29b-41d4-a716-************",
      "username": "user_123",
      "status": "active",
      "traffic_used": 1073741824,
      "traffic_limit": 10737418240,
      "expires_at": "2024-02-01T00:00:00Z",
      "created_at": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 5
  }
}
```

#### POST /api/services
Create new VPN service.

**Request:**
```json
{
  "plan_id": 1,
  "server_id": 1,
  "username": "custom_username",
  "duration_days": 30
}
```

#### GET /api/services/{service_id}/config
Get VPN configuration for service.

**Response:**
```json
{
  "success": true,
  "data": {
    "config_url": "https://api.webot.com/config/user_123",
    "qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "subscription_url": "https://api.webot.com/sub/user_123"
  }
}
```

### Payment Processing

#### POST /api/payments
Create new payment for wallet top-up.

**Request:**
```json
{
  "amount": 25000,
  "gateway": "zarinpal",
  "currency": "IRR",
  "description": "Wallet top-up"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "payment_id": 123,
    "payment_url": "https://gateway.zarinpal.com/pay/123456",
    "amount": 25000
  }
}
```

#### GET /api/payments/{payment_id}
Check payment status.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 123,
    "user_id": 1,
    "amount": 25000,
    "currency": "IRR",
    "gateway": "zarinpal",
    "status": "completed",
    "transaction_id": "TXN123456",
    "created_at": "2024-01-15T10:00:00Z",
    "completed_at": "2024-01-15T10:05:00Z"
  }
}
```

### Webhook Handling

#### POST /webhook/telegram
Handle Telegram webhook updates.

**Headers:**
```http
X-Telegram-Bot-Api-Secret-Token: <secret_token>
Content-Type: application/json
```

**Request:**
```json
{
  "update_id": 123456,
  "message": {
    "message_id": 1,
    "from": {
      "id": 123456789,
      "is_bot": false,
      "first_name": "John",
      "username": "john_doe"
    },
    "chat": {
      "id": 123456789,
      "first_name": "John",
      "username": "john_doe",
      "type": "private"
    },
    "date": **********,
    "text": "/start"
  }
}
```

### Health Check

#### GET /api/health
Check API health status.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T12:00:00Z",
  "version": "2.0.0",
  "uptime": 86400
}
```

## Code Examples

### cURL Examples

#### Authentication
```bash
curl -X POST https://api.webot.com/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "telegram_id": 123456789,
    "username": "john_doe",
    "first_name": "John"
  }'
```

#### Get User Profile
```bash
curl -X GET https://api.webot.com/api/users/profile \
  -H "Authorization: Bearer <jwt_token>"
```

#### Create Payment
```bash
curl -X POST https://api.webot.com/api/payments \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 25000,
    "gateway": "zarinpal",
    "currency": "IRR"
  }'
```

### JavaScript Examples

#### Using Fetch API
```javascript
// Authentication
const loginResponse = await fetch('https://api.webot.com/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    telegram_id: 123456789,
    username: 'john_doe',
    first_name: 'John'
  })
});

const { token } = await loginResponse.json();

// Get user profile
const profileResponse = await fetch('https://api.webot.com/api/users/profile', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

const profile = await profileResponse.json();
```

### PHP Examples

#### Using cURL
```php
// Authentication
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api.webot.com/api/auth/login');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'telegram_id' => 123456789,
    'username' => 'john_doe',
    'first_name' => 'John'
]));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$data = json_decode($response, true);
$token = $data['token'];

curl_close($ch);

// Get user profile
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api.webot.com/api/users/profile');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    "Authorization: Bearer {$token}"
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$profile = json_decode(curl_exec($ch), true);
curl_close($ch);
```

## SDKs and Libraries

### Official SDKs
- **PHP SDK**: `composer require webot/php-sdk`
- **JavaScript SDK**: `npm install @webot/js-sdk`
- **Python SDK**: `pip install webot-sdk`

### Community Libraries
- **Go**: `go get github.com/webot/go-sdk`
- **Java**: Available on Maven Central
- **C#**: Available on NuGet

## Support

For API support and questions:
- **Documentation**: https://docs.webot.com
- **Support Email**: <EMAIL>
- **GitHub Issues**: https://github.com/webot/webot-api/issues
- **Telegram Channel**: @webot_support

## Changelog

### Version 2.0.0 (2024-01-15)
- Complete API redesign with RESTful principles
- Added JWT authentication
- Improved error handling and response format
- Added comprehensive rate limiting
- Enhanced security features

### Version 1.x (Legacy)
- Basic webhook-only API
- Limited functionality
- Deprecated - migration required
MD;

        return $content;
    }

    /**
     * Generate Postman collection
     */
    public function generatePostmanCollection(): string
    {
        $filename = $this->outputDir . '/WeBot_API.postman_collection.json';

        $collection = [
            'info' => [
                'name' => 'WeBot API',
                'description' => 'Complete WeBot API collection for testing and development',
                'version' => '2.0.0',
                'schema' => 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
            ],
            'auth' => [
                'type' => 'bearer',
                'bearer' => [
                    [
                        'key' => 'token',
                        'value' => '{{jwt_token}}',
                        'type' => 'string'
                    ]
                ]
            ],
            'variable' => [
                [
                    'key' => 'base_url',
                    'value' => 'https://api.webot.com',
                    'type' => 'string'
                ],
                [
                    'key' => 'jwt_token',
                    'value' => '',
                    'type' => 'string'
                ]
            ],
            'item' => [
                [
                    'name' => 'Authentication',
                    'item' => [
                        [
                            'name' => 'Login',
                            'request' => [
                                'method' => 'POST',
                                'header' => [
                                    [
                                        'key' => 'Content-Type',
                                        'value' => 'application/json'
                                    ]
                                ],
                                'body' => [
                                    'mode' => 'raw',
                                    'raw' => json_encode([
                                        'telegram_id' => 123456789,
                                        'username' => 'john_doe',
                                        'first_name' => 'John'
                                    ], JSON_PRETTY_PRINT)
                                ],
                                'url' => [
                                    'raw' => '{{base_url}}/api/auth/login',
                                    'host' => ['{{base_url}}'],
                                    'path' => ['api', 'auth', 'login']
                                ]
                            ]
                        ],
                        [
                            'name' => 'Refresh Token',
                            'request' => [
                                'method' => 'POST',
                                'header' => [],
                                'url' => [
                                    'raw' => '{{base_url}}/api/auth/refresh',
                                    'host' => ['{{base_url}}'],
                                    'path' => ['api', 'auth', 'refresh']
                                ]
                            ]
                        ]
                    ]
                ],
                [
                    'name' => 'Users',
                    'item' => [
                        [
                            'name' => 'Get Profile',
                            'request' => [
                                'method' => 'GET',
                                'header' => [],
                                'url' => [
                                    'raw' => '{{base_url}}/api/users/profile',
                                    'host' => ['{{base_url}}'],
                                    'path' => ['api', 'users', 'profile']
                                ]
                            ]
                        ],
                        [
                            'name' => 'Update Profile',
                            'request' => [
                                'method' => 'PUT',
                                'header' => [
                                    [
                                        'key' => 'Content-Type',
                                        'value' => 'application/json'
                                    ]
                                ],
                                'body' => [
                                    'mode' => 'raw',
                                    'raw' => json_encode([
                                        'first_name' => 'John',
                                        'last_name' => 'Doe',
                                        'email' => '<EMAIL>'
                                    ], JSON_PRETTY_PRINT)
                                ],
                                'url' => [
                                    'raw' => '{{base_url}}/api/users/profile',
                                    'host' => ['{{base_url}}'],
                                    'path' => ['api', 'users', 'profile']
                                ]
                            ]
                        ]
                    ]
                ],
                [
                    'name' => 'Services',
                    'item' => [
                        [
                            'name' => 'List Services',
                            'request' => [
                                'method' => 'GET',
                                'header' => [],
                                'url' => [
                                    'raw' => '{{base_url}}/api/services?page=1&limit=20',
                                    'host' => ['{{base_url}}'],
                                    'path' => ['api', 'services'],
                                    'query' => [
                                        ['key' => 'page', 'value' => '1'],
                                        ['key' => 'limit', 'value' => '20']
                                    ]
                                ]
                            ]
                        ],
                        [
                            'name' => 'Create Service',
                            'request' => [
                                'method' => 'POST',
                                'header' => [
                                    [
                                        'key' => 'Content-Type',
                                        'value' => 'application/json'
                                    ]
                                ],
                                'body' => [
                                    'mode' => 'raw',
                                    'raw' => json_encode([
                                        'plan_id' => 1,
                                        'server_id' => 1,
                                        'duration_days' => 30
                                    ], JSON_PRETTY_PRINT)
                                ],
                                'url' => [
                                    'raw' => '{{base_url}}/api/services',
                                    'host' => ['{{base_url}}'],
                                    'path' => ['api', 'services']
                                ]
                            ]
                        ]
                    ]
                ],
                [
                    'name' => 'Payments',
                    'item' => [
                        [
                            'name' => 'Create Payment',
                            'request' => [
                                'method' => 'POST',
                                'header' => [
                                    [
                                        'key' => 'Content-Type',
                                        'value' => 'application/json'
                                    ]
                                ],
                                'body' => [
                                    'mode' => 'raw',
                                    'raw' => json_encode([
                                        'amount' => 25000,
                                        'gateway' => 'zarinpal',
                                        'currency' => 'IRR'
                                    ], JSON_PRETTY_PRINT)
                                ],
                                'url' => [
                                    'raw' => '{{base_url}}/api/payments',
                                    'host' => ['{{base_url}}'],
                                    'path' => ['api', 'payments']
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        file_put_contents($filename, json_encode($collection, JSON_PRETTY_PRINT));
        return $filename;
    }

    /**
     * Generate code examples
     */
    public function generateCodeExamples(): string
    {
        $filename = $this->outputDir . '/CODE_EXAMPLES.md';

        $content = <<<'MD'
# WeBot API Code Examples

## Table of Contents
- [Authentication](#authentication)
- [User Management](#user-management)
- [Service Management](#service-management)
- [Payment Processing](#payment-processing)
- [Error Handling](#error-handling)

## Authentication

### JavaScript (Node.js)
```javascript
const axios = require('axios');

class WeBotAPI {
    constructor(baseURL = 'https://api.webot.com') {
        this.baseURL = baseURL;
        this.token = null;
        this.client = axios.create({
            baseURL: this.baseURL,
            timeout: 10000
        });

        // Add request interceptor for auth
        this.client.interceptors.request.use(config => {
            if (this.token) {
                config.headers.Authorization = `Bearer ${this.token}`;
            }
            return config;
        });
    }

    async login(telegramId, username, firstName) {
        try {
            const response = await this.client.post('/api/auth/login', {
                telegram_id: telegramId,
                username: username,
                first_name: firstName
            });

            this.token = response.data.token;
            return response.data;
        } catch (error) {
            throw new Error(`Login failed: ${error.response?.data?.message || error.message}`);
        }
    }

    async getUserProfile() {
        const response = await this.client.get('/api/users/profile');
        return response.data;
    }

    async createPayment(amount, gateway, currency = 'IRR') {
        const response = await this.client.post('/api/payments', {
            amount,
            gateway,
            currency
        });
        return response.data;
    }
}

// Usage
const api = new WeBotAPI();

async function example() {
    try {
        // Login
        await api.login(123456789, 'john_doe', 'John');
        console.log('Logged in successfully');

        // Get profile
        const profile = await api.getUserProfile();
        console.log('User profile:', profile.data);

        // Create payment
        const payment = await api.createPayment(25000, 'zarinpal');
        console.log('Payment created:', payment.data);

    } catch (error) {
        console.error('Error:', error.message);
    }
}

example();
```

### PHP
```php
<?php

class WeBotAPI {
    private $baseURL;
    private $token;
    private $client;

    public function __construct($baseURL = 'https://api.webot.com') {
        $this->baseURL = $baseURL;
        $this->client = curl_init();

        curl_setopt_array($this->client, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_HTTPHEADER => ['Content-Type: application/json']
        ]);
    }

    public function login($telegramId, $username, $firstName) {
        $data = [
            'telegram_id' => $telegramId,
            'username' => $username,
            'first_name' => $firstName
        ];

        $response = $this->makeRequest('POST', '/api/auth/login', $data);

        if ($response['success']) {
            $this->token = $response['token'];
        }

        return $response;
    }

    public function getUserProfile() {
        return $this->makeRequest('GET', '/api/users/profile');
    }

    public function createPayment($amount, $gateway, $currency = 'IRR') {
        $data = [
            'amount' => $amount,
            'gateway' => $gateway,
            'currency' => $currency
        ];

        return $this->makeRequest('POST', '/api/payments', $data);
    }

    private function makeRequest($method, $endpoint, $data = null) {
        $url = $this->baseURL . $endpoint;

        curl_setopt($this->client, CURLOPT_URL, $url);
        curl_setopt($this->client, CURLOPT_CUSTOMREQUEST, $method);

        $headers = ['Content-Type: application/json'];
        if ($this->token) {
            $headers[] = 'Authorization: Bearer ' . $this->token;
        }
        curl_setopt($this->client, CURLOPT_HTTPHEADER, $headers);

        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($this->client, CURLOPT_POSTFIELDS, json_encode($data));
        }

        $response = curl_exec($this->client);
        $httpCode = curl_getinfo($this->client, CURLINFO_HTTP_CODE);

        if (curl_error($this->client)) {
            throw new Exception('cURL Error: ' . curl_error($this->client));
        }

        $decodedResponse = json_decode($response, true);

        if ($httpCode >= 400) {
            throw new Exception($decodedResponse['message'] ?? 'API Error');
        }

        return $decodedResponse;
    }

    public function __destruct() {
        if ($this->client) {
            curl_close($this->client);
        }
    }
}

// Usage
try {
    $api = new WeBotAPI();

    // Login
    $loginResult = $api->login(123456789, 'john_doe', 'John');
    echo "Logged in successfully\n";

    // Get profile
    $profile = $api->getUserProfile();
    echo "User profile: " . json_encode($profile['data']) . "\n";

    // Create payment
    $payment = $api->createPayment(25000, 'zarinpal');
    echo "Payment created: " . json_encode($payment['data']) . "\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
```

### Python
```python
import requests
import json
from typing import Optional, Dict, Any

class WeBotAPI:
    def __init__(self, base_url: str = 'https://api.webot.com'):
        self.base_url = base_url
        self.token: Optional[str] = None
        self.session = requests.Session()
        self.session.timeout = 10

    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[Any, Any]:
        url = f"{self.base_url}{endpoint}"
        headers = {'Content-Type': 'application/json'}

        if self.token:
            headers['Authorization'] = f'Bearer {self.token}'

        response = self.session.request(
            method=method,
            url=url,
            headers=headers,
            json=data if data else None
        )

        if response.status_code >= 400:
            error_data = response.json() if response.content else {}
            raise Exception(f"API Error: {error_data.get('message', 'Unknown error')}")

        return response.json()

    def login(self, telegram_id: int, username: str, first_name: str) -> Dict[Any, Any]:
        data = {
            'telegram_id': telegram_id,
            'username': username,
            'first_name': first_name
        }

        response = self._make_request('POST', '/api/auth/login', data)

        if response.get('success'):
            self.token = response.get('token')

        return response

    def get_user_profile(self) -> Dict[Any, Any]:
        return self._make_request('GET', '/api/users/profile')

    def create_payment(self, amount: int, gateway: str, currency: str = 'IRR') -> Dict[Any, Any]:
        data = {
            'amount': amount,
            'gateway': gateway,
            'currency': currency
        }

        return self._make_request('POST', '/api/payments', data)

# Usage
if __name__ == '__main__':
    try:
        api = WeBotAPI()

        # Login
        login_result = api.login(123456789, 'john_doe', 'John')
        print("Logged in successfully")

        # Get profile
        profile = api.get_user_profile()
        print(f"User profile: {json.dumps(profile['data'], indent=2)}")

        # Create payment
        payment = api.create_payment(25000, 'zarinpal')
        print(f"Payment created: {json.dumps(payment['data'], indent=2)}")

    except Exception as e:
        print(f"Error: {e}")
```

## Error Handling

### JavaScript Error Handling
```javascript
class APIError extends Error {
    constructor(message, code, details) {
        super(message);
        this.name = 'APIError';
        this.code = code;
        this.details = details;
    }
}

async function handleAPICall(apiFunction) {
    try {
        return await apiFunction();
    } catch (error) {
        if (error.response) {
            const { data } = error.response;
            throw new APIError(
                data.message || 'API Error',
                data.error || 'UNKNOWN_ERROR',
                data.details || {}
            );
        }
        throw error;
    }
}

// Usage
try {
    const result = await handleAPICall(() => api.getUserProfile());
    console.log('Success:', result);
} catch (error) {
    if (error instanceof APIError) {
        console.error(`API Error [${error.code}]: ${error.message}`);
        if (error.details) {
            console.error('Details:', error.details);
        }
    } else {
        console.error('Network Error:', error.message);
    }
}
```

### PHP Error Handling
```php
class APIException extends Exception {
    private $errorCode;
    private $details;

    public function __construct($message, $errorCode = null, $details = null) {
        parent::__construct($message);
        $this->errorCode = $errorCode;
        $this->details = $details;
    }

    public function getErrorCode() {
        return $this->errorCode;
    }

    public function getDetails() {
        return $this->details;
    }
}

// Enhanced makeRequest method with better error handling
private function makeRequest($method, $endpoint, $data = null) {
    // ... existing code ...

    $decodedResponse = json_decode($response, true);

    if ($httpCode >= 400) {
        throw new APIException(
            $decodedResponse['message'] ?? 'API Error',
            $decodedResponse['error'] ?? 'UNKNOWN_ERROR',
            $decodedResponse['details'] ?? null
        );
    }

    return $decodedResponse;
}

// Usage
try {
    $profile = $api->getUserProfile();
    echo "Success: " . json_encode($profile) . "\n";
} catch (APIException $e) {
    echo "API Error [{$e->getErrorCode()}]: {$e->getMessage()}\n";
    if ($e->getDetails()) {
        echo "Details: " . json_encode($e->getDetails()) . "\n";
    }
} catch (Exception $e) {
    echo "Network Error: {$e->getMessage()}\n";
}
```

## Rate Limiting Handling

### JavaScript with Retry Logic
```javascript
class RateLimitHandler {
    constructor(maxRetries = 3, baseDelay = 1000) {
        this.maxRetries = maxRetries;
        this.baseDelay = baseDelay;
    }

    async executeWithRetry(apiCall) {
        let lastError;

        for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
            try {
                return await apiCall();
            } catch (error) {
                lastError = error;

                if (error.response?.status === 429) {
                    const retryAfter = error.response.headers['retry-after'];
                    const delay = retryAfter ? parseInt(retryAfter) * 1000 : this.baseDelay * Math.pow(2, attempt);

                    console.log(`Rate limited. Retrying after ${delay}ms...`);
                    await this.sleep(delay);
                    continue;
                }

                throw error;
            }
        }

        throw lastError;
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Usage
const rateLimitHandler = new RateLimitHandler();

try {
    const result = await rateLimitHandler.executeWithRetry(() => api.getUserProfile());
    console.log('Success:', result);
} catch (error) {
    console.error('Failed after retries:', error.message);
}
```
MD;

        file_put_contents($filename, $content);
        return $filename;
    }

    /**
     * Generate authentication guide
     */
    public function generateAuthenticationGuide(): string
    {
        $filename = $this->outputDir . '/AUTHENTICATION_GUIDE.md';

        $content = <<<'MD'
# WeBot API Authentication Guide

## Overview

WeBot API supports multiple authentication methods to accommodate different use cases and security requirements.

## Authentication Methods

### 1. JWT Bearer Token (Recommended)

JWT (JSON Web Token) is the primary authentication method for API access.

#### How to Obtain JWT Token

```http
POST /api/auth/login
Content-Type: application/json

{
  "telegram_id": 123456789,
  "username": "john_doe",
  "first_name": "John",
  "auth_data": "telegram_auth_string"
}
```

#### Response
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": 3600,
  "user": {
    "id": 1,
    "telegram_id": 123456789,
    "status": "active"
  }
}
```

#### Using JWT Token
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 2. Telegram Bot API Secret Token

For webhook endpoints, use the Telegram Bot API secret token.

```http
X-Telegram-Bot-Api-Secret-Token: your_secret_token_here
```

### 3. Session-based Authentication

For web applications, session-based authentication is supported.

```http
Cookie: WEBOT_SESSION=session_id_here
```

## Token Management

### Token Expiration

JWT tokens expire after 1 hour by default. Use the refresh endpoint to get a new token.

```http
POST /api/auth/refresh
Authorization: Bearer expired_token
```

### Token Refresh

```json
{
  "success": true,
  "token": "new_jwt_token_here",
  "expires_in": 3600
}
```

## Security Best Practices

1. **Store tokens securely** - Never expose tokens in client-side code
2. **Use HTTPS** - Always use encrypted connections
3. **Implement token refresh** - Handle token expiration gracefully
4. **Validate tokens** - Check token validity before making requests
5. **Rotate secrets** - Regularly update API secrets

## Error Handling

### Authentication Errors

| Error Code | Description | Solution |
|------------|-------------|----------|
| `UNAUTHORIZED` | Missing or invalid token | Provide valid authentication |
| `TOKEN_EXPIRED` | JWT token has expired | Refresh the token |
| `INVALID_CREDENTIALS` | Login credentials are invalid | Check Telegram auth data |
| `ACCOUNT_SUSPENDED` | User account is suspended | Contact support |

### Example Error Response
```json
{
  "success": false,
  "error": "UNAUTHORIZED",
  "message": "Authentication required",
  "details": {
    "required_auth": ["bearer", "telegram_secret"]
  }
}
```

## Implementation Examples

### JavaScript
```javascript
class AuthManager {
    constructor() {
        this.token = localStorage.getItem('webot_token');
        this.refreshTimer = null;
    }

    async login(telegramData) {
        const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(telegramData)
        });

        const data = await response.json();

        if (data.success) {
            this.token = data.token;
            localStorage.setItem('webot_token', this.token);
            this.scheduleRefresh(data.expires_in);
        }

        return data;
    }

    scheduleRefresh(expiresIn) {
        // Refresh 5 minutes before expiration
        const refreshTime = (expiresIn - 300) * 1000;

        this.refreshTimer = setTimeout(() => {
            this.refreshToken();
        }, refreshTime);
    }

    async refreshToken() {
        try {
            const response = await fetch('/api/auth/refresh', {
                method: 'POST',
                headers: { 'Authorization': `Bearer ${this.token}` }
            });

            const data = await response.json();

            if (data.success) {
                this.token = data.token;
                localStorage.setItem('webot_token', this.token);
                this.scheduleRefresh(data.expires_in);
            }
        } catch (error) {
            console.error('Token refresh failed:', error);
            this.logout();
        }
    }

    logout() {
        this.token = null;
        localStorage.removeItem('webot_token');
        if (this.refreshTimer) {
            clearTimeout(this.refreshTimer);
        }
    }

    getAuthHeaders() {
        return this.token ? { 'Authorization': `Bearer ${this.token}` } : {};
    }
}
```

### PHP
```php
class AuthManager {
    private $token;
    private $tokenExpiry;

    public function login($telegramData) {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => 'https://api.webot.com/api/auth/login',
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($telegramData),
            CURLOPT_HTTPHEADER => ['Content-Type: application/json'],
            CURLOPT_RETURNTRANSFER => true
        ]);

        $response = curl_exec($ch);
        $data = json_decode($response, true);
        curl_close($ch);

        if ($data['success']) {
            $this->token = $data['token'];
            $this->tokenExpiry = time() + $data['expires_in'];

            // Store in session
            $_SESSION['webot_token'] = $this->token;
            $_SESSION['webot_token_expiry'] = $this->tokenExpiry;
        }

        return $data;
    }

    public function refreshTokenIfNeeded() {
        // Refresh if token expires in next 5 minutes
        if ($this->tokenExpiry && $this->tokenExpiry - time() < 300) {
            return $this->refreshToken();
        }
        return true;
    }

    private function refreshToken() {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => 'https://api.webot.com/api/auth/refresh',
            CURLOPT_POST => true,
            CURLOPT_HTTPHEADER => [
                'Authorization: Bearer ' . $this->token
            ],
            CURLOPT_RETURNTRANSFER => true
        ]);

        $response = curl_exec($ch);
        $data = json_decode($response, true);
        curl_close($ch);

        if ($data['success']) {
            $this->token = $data['token'];
            $this->tokenExpiry = time() + $data['expires_in'];

            $_SESSION['webot_token'] = $this->token;
            $_SESSION['webot_token_expiry'] = $this->tokenExpiry;

            return true;
        }

        return false;
    }

    public function getAuthHeaders() {
        $this->refreshTokenIfNeeded();
        return $this->token ? ['Authorization: Bearer ' . $this->token] : [];
    }
}
```
MD;

        file_put_contents($filename, $content);
        return $filename;
    }

    /**
     * Generate error codes documentation
     */
    public function generateErrorCodesDoc(): string
    {
        $filename = $this->outputDir . '/ERROR_CODES.md';

        $content = <<<'MD'
# WeBot API Error Codes

## Overview

WeBot API uses standard HTTP status codes along with specific error codes to provide detailed information about failures.

## HTTP Status Codes

| Status | Description |
|--------|-------------|
| 200 | OK - Request successful |
| 201 | Created - Resource created successfully |
| 400 | Bad Request - Invalid request data |
| 401 | Unauthorized - Authentication required |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 409 | Conflict - Resource conflict |
| 422 | Unprocessable Entity - Validation failed |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server error |
| 502 | Bad Gateway - Upstream server error |
| 503 | Service Unavailable - Service temporarily unavailable |

## Error Response Format

```json
{
  "success": false,
  "error": "ERROR_CODE",
  "message": "Human-readable error message",
  "details": {
    "field": "Additional error details",
    "validation_errors": []
  },
  "timestamp": "2024-01-15T12:00:00Z",
  "request_id": "req_123456789"
}
```

## Authentication Errors (401)

| Error Code | Description | Solution |
|------------|-------------|----------|
| `UNAUTHORIZED` | No authentication provided | Include valid authentication header |
| `TOKEN_EXPIRED` | JWT token has expired | Refresh token or login again |
| `TOKEN_INVALID` | JWT token is malformed or invalid | Obtain new token |
| `TOKEN_REVOKED` | Token has been revoked | Login again |
| `INVALID_CREDENTIALS` | Login credentials are invalid | Check authentication data |

## Authorization Errors (403)

| Error Code | Description | Solution |
|------------|-------------|----------|
| `FORBIDDEN` | Insufficient permissions | Contact admin for access |
| `ACCOUNT_SUSPENDED` | User account is suspended | Contact support |
| `ACCOUNT_BANNED` | User account is banned | Appeal ban or create new account |
| `FEATURE_DISABLED` | Feature is disabled for user | Upgrade account or contact support |

## Validation Errors (400, 422)

| Error Code | Description | Solution |
|------------|-------------|----------|
| `VALIDATION_ERROR` | Input validation failed | Check request data format |
| `MISSING_REQUIRED_FIELD` | Required field is missing | Include all required fields |
| `INVALID_FORMAT` | Field format is invalid | Use correct format (email, phone, etc.) |
| `VALUE_TOO_LONG` | Field value exceeds maximum length | Reduce field length |
| `VALUE_TOO_SHORT` | Field value below minimum length | Increase field length |
| `INVALID_ENUM_VALUE` | Invalid enum value | Use valid enum values |

## Resource Errors (404, 409)

| Error Code | Description | Solution |
|------------|-------------|----------|
| `NOT_FOUND` | Resource not found | Check resource ID |
| `USER_NOT_FOUND` | User not found | Verify user ID |
| `SERVICE_NOT_FOUND` | Service not found | Verify service ID |
| `PAYMENT_NOT_FOUND` | Payment not found | Verify payment ID |
| `DUPLICATE_RESOURCE` | Resource already exists | Use different identifier |
| `RESOURCE_CONFLICT` | Resource state conflict | Check resource status |

## Business Logic Errors (400)

| Error Code | Description | Solution |
|------------|-------------|----------|
| `INSUFFICIENT_BALANCE` | User balance too low | Top up wallet |
| `SERVICE_LIMIT_EXCEEDED` | Service limit reached | Upgrade plan or delete services |
| `INVALID_PLAN` | Plan not available | Choose different plan |
| `SERVER_UNAVAILABLE` | Server not available | Choose different server |
| `PAYMENT_FAILED` | Payment processing failed | Try different payment method |
| `GATEWAY_ERROR` | Payment gateway error | Contact support |

## Rate Limiting Errors (429)

| Error Code | Description | Solution |
|------------|-------------|----------|
| `RATE_LIMITED` | Rate limit exceeded | Wait before retrying |
| `DAILY_LIMIT_EXCEEDED` | Daily API limit exceeded | Wait until next day |
| `CONCURRENT_LIMIT_EXCEEDED` | Too many concurrent requests | Reduce concurrent requests |

## Server Errors (500, 502, 503)

| Error Code | Description | Solution |
|------------|-------------|----------|
| `INTERNAL_ERROR` | Internal server error | Retry request or contact support |
| `DATABASE_ERROR` | Database connection error | Retry request |
| `EXTERNAL_SERVICE_ERROR` | External service error | Retry request |
| `MAINTENANCE_MODE` | Service under maintenance | Wait for maintenance completion |

## Error Handling Best Practices

### 1. Implement Retry Logic
```javascript
async function apiCallWithRetry(apiCall, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            return await apiCall();
        } catch (error) {
            if (error.status === 429) {
                // Rate limited - wait before retry
                const retryAfter = error.headers['retry-after'] || Math.pow(2, attempt);
                await sleep(retryAfter * 1000);
                continue;
            }

            if (error.status >= 500 && attempt < maxRetries) {
                // Server error - retry with exponential backoff
                await sleep(Math.pow(2, attempt) * 1000);
                continue;
            }

            throw error;
        }
    }
}
```

### 2. Handle Specific Error Codes
```javascript
function handleAPIError(error) {
    switch (error.code) {
        case 'TOKEN_EXPIRED':
            return refreshTokenAndRetry();

        case 'INSUFFICIENT_BALANCE':
            return redirectToTopUp();

        case 'VALIDATION_ERROR':
            return showValidationErrors(error.details.validation_errors);

        case 'RATE_LIMITED':
            return showRateLimitMessage(error.details.retry_after);

        default:
            return showGenericError(error.message);
    }
}
```

### 3. Log Errors for Debugging
```javascript
function logError(error, context) {
    console.error('API Error:', {
        code: error.code,
        message: error.message,
        details: error.details,
        request_id: error.request_id,
        timestamp: error.timestamp,
        context: context
    });
}
```

## Testing Error Scenarios

### Using cURL
```bash
# Test authentication error
curl -X GET https://api.webot.com/api/users/profile
# Expected: 401 UNAUTHORIZED

# Test validation error
curl -X POST https://api.webot.com/api/payments \
  -H "Authorization: Bearer valid_token" \
  -H "Content-Type: application/json" \
  -d '{"amount": -100}'
# Expected: 400 VALIDATION_ERROR

# Test rate limiting
for i in {1..200}; do
  curl -X GET https://api.webot.com/api/health
done
# Expected: 429 RATE_LIMITED after 100 requests
```

## Support

If you encounter errors not documented here:

1. Check the `request_id` in the error response
2. Include the request ID when contacting support
3. Provide the full error response
4. Include steps to reproduce the error

Contact: <EMAIL>
MD;

        file_put_contents($filename, $content);
        return $filename;
    }

    /**
     * Generate rate limiting documentation
     */
    public function generateRateLimitingDoc(): string
    {
        $filename = $this->outputDir . '/RATE_LIMITING.md';

        $content = <<<'MD'
# WeBot API Rate Limiting

## Overview

WeBot API implements rate limiting to ensure fair usage and maintain service quality for all users.

## Rate Limits

### General API Endpoints
- **Limit**: 100 requests per minute per IP address
- **Window**: 60 seconds (sliding window)
- **Scope**: Per IP address

### Authentication Endpoints
- **Limit**: 10 requests per minute per IP address
- **Window**: 60 seconds (sliding window)
- **Scope**: Per IP address
- **Endpoints**: `/api/auth/*`

### Webhook Endpoints
- **Limit**: 1000 requests per minute per IP address
- **Window**: 60 seconds (sliding window)
- **Scope**: Per IP address
- **Endpoints**: `/webhook/*`

### User-specific Limits
- **Limit**: 1000 requests per hour per authenticated user
- **Window**: 3600 seconds (sliding window)
- **Scope**: Per user account

## Rate Limit Headers

All API responses include rate limit information in headers:

```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
X-RateLimit-Window: 60
```

### Header Descriptions

| Header | Description |
|--------|-------------|
| `X-RateLimit-Limit` | Maximum requests allowed in the time window |
| `X-RateLimit-Remaining` | Remaining requests in current window |
| `X-RateLimit-Reset` | Unix timestamp when the window resets |
| `X-RateLimit-Window` | Time window in seconds |

## Rate Limit Exceeded Response

When rate limit is exceeded, the API returns:

```http
HTTP/1.1 429 Too Many Requests
Content-Type: application/json
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 0
X-RateLimit-Reset: **********
Retry-After: 60

{
  "success": false,
  "error": "RATE_LIMITED",
  "message": "Rate limit exceeded",
  "details": {
    "limit": 100,
    "window": 60,
    "retry_after": 60
  }
}
```

## Best Practices

### 1. Monitor Rate Limit Headers
```javascript
function checkRateLimit(response) {
    const remaining = parseInt(response.headers['x-ratelimit-remaining']);
    const reset = parseInt(response.headers['x-ratelimit-reset']);

    if (remaining < 10) {
        const waitTime = reset - Math.floor(Date.now() / 1000);
        console.warn(`Rate limit low. ${remaining} requests remaining. Resets in ${waitTime}s`);
    }
}
```

### 2. Implement Exponential Backoff
```javascript
async function apiCallWithBackoff(apiCall, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const response = await apiCall();
            checkRateLimit(response);
            return response;
        } catch (error) {
            if (error.status === 429) {
                const retryAfter = error.headers['retry-after'] || Math.pow(2, attempt);
                console.log(`Rate limited. Waiting ${retryAfter}s before retry ${attempt}/${maxRetries}`);
                await sleep(retryAfter * 1000);
                continue;
            }
            throw error;
        }
    }
    throw new Error('Max retries exceeded');
}
```

### 3. Batch Requests When Possible
```javascript
// Instead of multiple individual requests
const users = await Promise.all([
    api.getUser(1),
    api.getUser(2),
    api.getUser(3)
]);

// Use batch endpoint if available
const users = await api.getUsers([1, 2, 3]);
```

### 4. Cache Responses
```javascript
class CachedAPIClient {
    constructor() {
        this.cache = new Map();
        this.cacheTTL = 60000; // 1 minute
    }

    async get(endpoint) {
        const cacheKey = endpoint;
        const cached = this.cache.get(cacheKey);

        if (cached && Date.now() - cached.timestamp < this.cacheTTL) {
            return cached.data;
        }

        const response = await this.apiCall(endpoint);
        this.cache.set(cacheKey, {
            data: response,
            timestamp: Date.now()
        });

        return response;
    }
}
```

## Rate Limit Strategies

### 1. Token Bucket Algorithm
The API uses a token bucket algorithm for rate limiting:

- Each user/IP has a bucket with a maximum capacity
- Tokens are added to the bucket at a fixed rate
- Each request consumes one token
- Requests are rejected when bucket is empty

### 2. Sliding Window
Rate limits use a sliding window approach:

- Window moves continuously rather than fixed intervals
- More accurate than fixed windows
- Prevents burst traffic at window boundaries

## Increasing Rate Limits

### Premium Plans
Higher rate limits are available for premium users:

| Plan | General API | Authentication | Webhooks |
|------|-------------|----------------|----------|
| Free | 100/min | 10/min | 1000/min |
| Pro | 500/min | 50/min | 5000/min |
| Enterprise | 2000/min | 200/min | 20000/min |

### Custom Limits
For enterprise customers, custom rate limits can be configured:

- Contact <EMAIL>
- Provide usage requirements
- Custom SLA available

## Monitoring and Alerts

### Rate Limit Monitoring
```javascript
class RateLimitMonitor {
    constructor() {
        this.alerts = [];
    }

    checkLimits(response) {
        const remaining = parseInt(response.headers['x-ratelimit-remaining']);
        const limit = parseInt(response.headers['x-ratelimit-limit']);
        const percentage = (remaining / limit) * 100;

        if (percentage < 20) {
            this.sendAlert('Rate limit warning', {
                remaining,
                limit,
                percentage
            });
        }
    }

    sendAlert(message, data) {
        console.warn(message, data);
        // Send to monitoring service
    }
}
```

### Metrics to Track
- Requests per minute/hour
- Rate limit hit frequency
- Average response time
- Error rates by endpoint

## Testing Rate Limits

### Load Testing Script
```bash
#!/bin/bash

# Test rate limiting
echo "Testing rate limits..."

for i in {1..150}; do
    response=$(curl -s -w "%{http_code}" -o /dev/null \
        -H "Authorization: Bearer $TOKEN" \
        https://api.webot.com/api/users/profile)

    echo "Request $i: HTTP $response"

    if [ "$response" = "429" ]; then
        echo "Rate limit hit at request $i"
        break
    fi

    sleep 0.1
done
```

### Rate Limit Testing Tool
```javascript
async function testRateLimit(endpoint, expectedLimit) {
    const results = [];
    let rateLimitHit = false;

    for (let i = 1; i <= expectedLimit + 10; i++) {
        try {
            const start = Date.now();
            const response = await fetch(endpoint);
            const duration = Date.now() - start;

            results.push({
                request: i,
                status: response.status,
                duration,
                remaining: response.headers.get('x-ratelimit-remaining')
            });

            if (response.status === 429) {
                rateLimitHit = true;
                console.log(`Rate limit hit at request ${i}`);
                break;
            }

        } catch (error) {
            console.error(`Request ${i} failed:`, error.message);
        }

        // Small delay to avoid overwhelming
        await sleep(10);
    }

    return { results, rateLimitHit };
}
```

## Troubleshooting

### Common Issues

1. **Unexpected 429 errors**
   - Check if multiple applications share the same IP
   - Verify rate limit headers in responses
   - Consider using different API keys for different applications

2. **Rate limits too restrictive**
   - Implement caching to reduce API calls
   - Use batch endpoints when available
   - Consider upgrading to higher plan

3. **Inconsistent rate limiting**
   - Rate limits use sliding windows
   - Previous requests affect current limits
   - Check system clock synchronization

### Debug Rate Limiting
```javascript
function debugRateLimit(response) {
    console.log('Rate Limit Debug:', {
        limit: response.headers.get('x-ratelimit-limit'),
        remaining: response.headers.get('x-ratelimit-remaining'),
        reset: new Date(response.headers.get('x-ratelimit-reset') * 1000),
        window: response.headers.get('x-ratelimit-window')
    });
}
```

## Contact Support

For rate limiting issues:
- Email: <EMAIL>
- Include your API key or user ID
- Provide rate limit headers from responses
- Describe your use case and requirements
MD;

        file_put_contents($filename, $content);
        return $filename;
    }



    /**
     * Ensure output directory exists
     */
    private function ensureOutputDirectory(): void
    {
        if (!is_dir($this->outputDir)) {
            mkdir($this->outputDir, 0755, true);
        }
    }
}
