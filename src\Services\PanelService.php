<?php

declare(strict_types=1);

namespace WeBot\Services;

use WeBot\Core\Config;
use WeBot\Utils\Logger;
use Monolog\Logger as MonologLogger;
use WeBot\Utils\Helper;
use WeBot\Exceptions\PanelException;

/**
 * Panel Service
 *
 * Handles integration with VPN panels including Marzban,
 * Marzneshin, X-UI, and other panel types.
 *
 * @package WeBot\Services
 * @version 2.0
 */
class PanelService
{
    private Config $config;
    private DatabaseService $database;
    private MonologLogger $logger;
    private array $panelConfigs;
    private array $authTokens = [];

    public function __construct(Config $config, DatabaseService $database)
    {
        $this->config = $config;
        $this->database = $database;
        $this->logger = Logger::getInstance();

        $this->loadPanelConfigs();
    }

    /**
     * Create user on panel
     */
    public function createUser(int $serverId, array $userData): array
    {
        $server = $this->getServerById($serverId);

        if (!$server) {
            throw new PanelException('سرور یافت نشد', PanelException::PANEL_UNAVAILABLE);
        }

        $panelType = $server['panel_type'] ?? 'marzban';

        return match ($panelType) {
            'marzban' => $this->createMarzbanUser($server, $userData),
            'marzneshin' => $this->createMarzneshiUser($server, $userData),
            'x-ui' => $this->createXUIUser($server, $userData),
            default => throw new PanelException('نوع پنل پشتیبانی نمی‌شود')
        };
    }

    /**
     * Get user from panel
     */
    public function getUser(int $serverId, string $uuid): ?array
    {
        $server = $this->getServerById($serverId);

        if (!$server) {
            throw new PanelException('سرور یافت نشد');
        }

        $panelType = $server['panel_type'] ?? 'marzban';

        return match ($panelType) {
            'marzban' => $this->getMarzbanUser($server, $uuid),
            'marzneshin' => $this->getMarzneshiUser($server, $uuid),
            'x-ui' => $this->getXUIUser($server, $uuid),
            default => null
        };
    }

    /**
     * Update user on panel
     */
    public function updateUser(int $serverId, string $uuid, array $userData): bool
    {
        $server = $this->getServerById($serverId);

        if (!$server) {
            throw new PanelException('سرور یافت نشد');
        }

        $panelType = $server['panel_type'] ?? 'marzban';

        return match ($panelType) {
            'marzban' => $this->updateMarzbanUser($server, $uuid, $userData),
            'marzneshin' => $this->updateMarzneshiUser($server, $uuid, $userData),
            'x-ui' => $this->updateXUIUser($server, $uuid, $userData),
            default => false
        };
    }

    /**
     * Delete user from panel
     */
    public function deleteUser(int $serverId, string $uuid): bool
    {
        $server = $this->getServerById($serverId);

        if (!$server) {
            throw new PanelException('سرور یافت نشد');
        }

        $panelType = $server['panel_type'] ?? 'marzban';

        return match ($panelType) {
            'marzban' => $this->deleteMarzbanUser($server, $uuid),
            'marzneshin' => $this->deleteMarzneshiUser($server, $uuid),
            'x-ui' => $this->deleteXUIUser($server, $uuid),
            default => false
        };
    }

    /**
     * Get user configuration
     */
    public function getUserConfig(int $serverId, string $uuid): ?string
    {
        $server = $this->getServerById($serverId);

        if (!$server) {
            return null;
        }

        $panelType = $server['panel_type'] ?? 'marzban';

        return match ($panelType) {
            'marzban' => $this->getMarzbanConfig($server, $uuid),
            'marzneshin' => $this->getMarzneshiConfig($server, $uuid),
            'x-ui' => $this->getXUIConfig($server, $uuid),
            default => null
        };
    }

    /**
     * Create Marzban user
     */
    private function createMarzbanUser(array $server, array $userData): array
    {
        $token = $this->getMarzbanToken($server);

        $url = rtrim($server['url'], '/') . '/api/user';

        $postData = [
            'username' => $userData['username'],
            'proxies' => $userData['proxies'] ?? ['vmess', 'vless'],
            'data_limit' => $userData['data_limit'],
            'expire' => $userData['expire'],
            'data_limit_reset_strategy' => 'no_reset',
            'status' => 'active'
        ];

        $headers = [
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json',
            'Accept: application/json'
        ];

        $response = $this->makeHttpRequest($url, 'POST', $postData, $headers);

        if (!$response['success']) {
            throw PanelException::userCreationFailed('marzban', $response['error'] ?? 'Unknown error');
        }

        $this->logger->info('Marzban user created', [
            'server_id' => $server['id'],
            'username' => $userData['username'],
            'response' => $response['data']
        ]);

        return $response['data'];
    }

    /**
     * Get Marzban user
     */
    private function getMarzbanUser(array $server, string $username): ?array
    {
        $token = $this->getMarzbanToken($server);

        $url = rtrim($server['url'], '/') . '/api/user/' . urlencode($username);

        $headers = [
            'Authorization: Bearer ' . $token,
            'Accept: application/json'
        ];

        $response = $this->makeHttpRequest($url, 'GET', null, $headers);

        if (!$response['success']) {
            if (strpos($response['error'] ?? '', '404') !== false) {
                return null;
            }
            throw new PanelException('خطا در دریافت اطلاعات کاربر از Marzban');
        }

        return $response['data'];
    }

    /**
     * Update Marzban user
     */
    private function updateMarzbanUser(array $server, string $username, array $userData): bool
    {
        $token = $this->getMarzbanToken($server);

        $url = rtrim($server['url'], '/') . '/api/user/' . urlencode($username);

        $headers = [
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json',
            'Accept: application/json'
        ];

        $response = $this->makeHttpRequest($url, 'PUT', $userData, $headers);

        if (!$response['success']) {
            throw new PanelException('خطا در به‌روزرسانی کاربر Marzban');
        }

        return true;
    }

    /**
     * Delete Marzban user
     */
    private function deleteMarzbanUser(array $server, string $username): bool
    {
        $token = $this->getMarzbanToken($server);

        $url = rtrim($server['url'], '/') . '/api/user/' . urlencode($username);

        $headers = [
            'Authorization: Bearer ' . $token,
            'Accept: application/json'
        ];

        $response = $this->makeHttpRequest($url, 'DELETE', null, $headers);

        return $response['success'];
    }

    /**
     * Get Marzban config
     */
    private function getMarzbanConfig(array $server, string $username): ?string
    {
        $token = $this->getMarzbanToken($server);

        $url = rtrim($server['url'], '/') . '/api/user/' . urlencode($username) . '/config';

        $headers = [
            'Authorization: Bearer ' . $token,
            'Accept: text/plain'
        ];

        $response = $this->makeHttpRequest($url, 'GET', null, $headers);

        if (!$response['success']) {
            return null;
        }

        return $response['data'];
    }

    /**
     * Get Marzban authentication token
     */
    private function getMarzbanToken(array $server): string
    {
        $cacheKey = 'marzban_token_' . $server['id'];

        if (isset($this->authTokens[$cacheKey])) {
            return $this->authTokens[$cacheKey];
        }

        $url = rtrim($server['url'], '/') . '/api/admin/token';

        $postData = [
            'username' => $server['username'],
            'password' => $server['password']
        ];

        $headers = [
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json'
        ];

        $response = $this->makeHttpRequest($url, 'POST', $postData, $headers);

        if (!$response['success']) {
            throw PanelException::authenticationFailed('marzban');
        }

        $token = $response['data']['access_token'] ?? '';

        if (empty($token)) {
            throw PanelException::authenticationFailed('marzban');
        }

        $this->authTokens[$cacheKey] = $token;

        return $token;
    }

    /**
     * Create Marzneshin user
     */
    private function createMarzneshiUser(array $server, array $userData): array
    {
        $token = $this->getMarzneshiToken($server);

        $url = rtrim($server['url'], '/') . '/api/users';

        $postData = [
            'service_ids' => json_decode($server['proxies'] ?? '[]', true),
            'data_limit' => $userData['data_limit'],
            'username' => $userData['username'],
        ];

        // Handle expiry strategy
        if (isset($userData['expire']) && $userData['expire'] > 0) {
            if (($server['onholdstatus'] ?? 'offonhold') == 'offonhold') {
                $postData['expire_date'] = date('c', $userData['expire']);
                $postData['expire_strategy'] = 'fixed_date';
            } else {
                $postData['expire_date'] = null;
                $postData['expire_strategy'] = 'start_on_first_use';
                $postData['usage_duration'] = $userData['expire'] - time();
            }
        } else {
            $postData['expire_date'] = null;
            $postData['expire_strategy'] = 'never';
        }

        $headers = [
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json',
            'Accept: application/json'
        ];

        $response = $this->makeHttpRequest($url, 'POST', $postData, $headers);

        if (!$response['success']) {
            throw PanelException::userCreationFailed('marzneshin', $response['error'] ?? 'Unknown error');
        }

        $this->logger->info('Marzneshin user created', [
            'server_id' => $server['id'],
            'username' => $userData['username'],
            'response' => $response['data']
        ]);

        return $response['data'];
    }

    /**
     * Get Marzneshin user
     */
    private function getMarzneshiUser(array $server, string $username): ?array
    {
        $token = $this->getMarzneshiToken($server);

        $url = rtrim($server['url'], '/') . '/api/users/' . urlencode($username);

        $headers = [
            'Authorization: Bearer ' . $token,
            'Accept: application/json'
        ];

        $response = $this->makeHttpRequest($url, 'GET', null, $headers);

        if (!$response['success']) {
            if (strpos($response['error'] ?? '', '404') !== false) {
                return null;
            }
            throw new PanelException('خطا در دریافت اطلاعات کاربر از Marzneshin');
        }

        return $response['data'];
    }

    /**
     * Update Marzneshin user
     */
    private function updateMarzneshiUser(array $server, string $username, array $userData): bool
    {
        $token = $this->getMarzneshiToken($server);

        $url = rtrim($server['url'], '/') . '/api/users/' . urlencode($username);

        $headers = [
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json',
            'Accept: application/json'
        ];

        $response = $this->makeHttpRequest($url, 'PUT', $userData, $headers);

        if (!$response['success']) {
            throw new PanelException('خطا در به‌روزرسانی کاربر Marzneshin');
        }

        return true;
    }

    /**
     * Delete Marzneshin user
     */
    private function deleteMarzneshiUser(array $server, string $username): bool
    {
        $token = $this->getMarzneshiToken($server);

        $url = rtrim($server['url'], '/') . '/api/users/' . urlencode($username);

        $headers = [
            'Authorization: Bearer ' . $token,
            'Accept: application/json'
        ];

        $response = $this->makeHttpRequest($url, 'DELETE', null, $headers);

        return $response['success'];
    }

    /**
     * Get Marzneshin config
     */
    private function getMarzneshiConfig(array $server, string $username): ?string
    {
        $token = $this->getMarzneshiToken($server);

        $url = rtrim($server['url'], '/') . '/api/users/' . urlencode($username) . '/config';

        $headers = [
            'Authorization: Bearer ' . $token,
            'Accept: text/plain'
        ];

        $response = $this->makeHttpRequest($url, 'GET', null, $headers);

        if (!$response['success']) {
            return null;
        }

        return $response['data'];
    }

    /**
     * Get Marzneshin authentication token
     */
    private function getMarzneshiToken(array $server): string
    {
        $cacheKey = 'marzneshin_token_' . $server['id'];

        if (isset($this->authTokens[$cacheKey])) {
            return $this->authTokens[$cacheKey];
        }

        $url = rtrim($server['url'], '/') . '/api/admin/token';

        $postData = [
            'username' => $server['username'],
            'password' => $server['password']
        ];

        $headers = [
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json'
        ];

        $response = $this->makeHttpRequest($url, 'POST', $postData, $headers);

        if (!$response['success']) {
            throw PanelException::authenticationFailed('marzneshin');
        }

        $token = $response['data']['access_token'] ?? '';

        if (empty($token)) {
            throw PanelException::authenticationFailed('marzneshin');
        }

        $this->authTokens[$cacheKey] = $token;

        return $token;
    }

    /**
     * Create X-UI user
     */
    private function createXUIUser(array $server, array $userData): array
    {
        $sessionId = $this->getXUISession($server);

        $url = rtrim($server['url'], '/') . '/panel/api/inbounds/addClient';

        // Generate unique UUID for user
        $uuid = $this->generateUUID();
        $email = $userData['username'] ?? 'user_' . time();

        $clientData = [
            'id' => (int) ($server['inbound_id'] ?? 1), // X-UI inbound ID
            'settings' => json_encode([
                'clients' => [
                    [
                        'id' => $uuid,
                        'email' => $email,
                        'limitIp' => 0,
                        'totalGB' => $userData['data_limit'] ?? 0,
                        'expiryTime' => isset($userData['expire']) ? $userData['expire'] * 1000 : 0, // X-UI uses milliseconds
                        'enable' => true,
                        'tgId' => '',
                        'subId' => ''
                    ]
                ]
            ])
        ];

        $headers = [
            'Accept: application/json',
            'Content-Type: application/json',
            'Cookie: session=' . $sessionId
        ];

        $response = $this->makeHttpRequest($url, 'POST', $clientData, $headers);

        if (!$response['success']) {
            throw PanelException::userCreationFailed('x-ui', $response['error'] ?? 'Unknown error');
        }

        $this->logger->info('X-UI user created', [
            'server_id' => $server['id'],
            'email' => $email,
            'uuid' => $uuid
        ]);

        return [
            'uuid' => $uuid,
            'email' => $email,
            'status' => 'active',
            'data_limit' => $userData['data_limit'] ?? 0,
            'expire' => $userData['expire'] ?? 0
        ];
    }

    /**
     * Get X-UI user
     */
    private function getXUIUser(array $server, string $email): ?array
    {
        $sessionId = $this->getXUISession($server);

        $url = rtrim($server['url'], '/') . '/panel/api/inbounds/list';

        $headers = [
            'Accept: application/json',
            'Cookie: session=' . $sessionId
        ];

        $response = $this->makeHttpRequest($url, 'GET', null, $headers);

        if (!$response['success']) {
            return null;
        }

        // Search for user in inbounds
        $inbounds = $response['data']['obj'] ?? [];
        foreach ($inbounds as $inbound) {
            $settings = json_decode($inbound['settings'], true);
            $clients = $settings['clients'] ?? [];

            foreach ($clients as $client) {
                if ($client['email'] === $email) {
                    return [
                        'uuid' => $client['id'],
                        'email' => $client['email'],
                        'status' => $client['enable'] ? 'active' : 'disabled',
                        'data_limit' => $client['totalGB'],
                        'used_traffic' => $inbound['up'] + $inbound['down'],
                        'expire' => $client['expiryTime'] ? $client['expiryTime'] / 1000 : 0
                    ];
                }
            }
        }

        return null;
    }

    /**
     * Update X-UI user
     */
    private function updateXUIUser(array $server, string $email, array $userData): bool
    {
        $sessionId = $this->getXUISession($server);

        // First get current user data
        $currentUser = $this->getXUIUser($server, $email);
        if (!$currentUser) {
            return false;
        }

        $url = rtrim($server['url'], '/') . '/panel/api/inbounds/updateClient/' . $currentUser['uuid'];

        $updateData = [
            'id' => $currentUser['uuid'],
            'email' => $email,
            'limitIp' => 0,
            'totalGB' => $userData['data_limit'] ?? $currentUser['data_limit'],
            'expiryTime' => isset($userData['expire']) ? $userData['expire'] * 1000 : $currentUser['expire'] * 1000,
            'enable' => isset($userData['enable']) ? $userData['enable'] : true,
            'tgId' => '',
            'subId' => ''
        ];

        $headers = [
            'Accept: application/json',
            'Content-Type: application/json',
            'Cookie: session=' . $sessionId
        ];

        $response = $this->makeHttpRequest($url, 'POST', $updateData, $headers);

        return $response['success'];
    }

    /**
     * Delete X-UI user
     */
    private function deleteXUIUser(array $server, string $email): bool
    {
        $sessionId = $this->getXUISession($server);

        // First get user UUID
        $user = $this->getXUIUser($server, $email);
        if (!$user) {
            return false;
        }

        $url = rtrim($server['url'], '/') . '/panel/api/inbounds/delClient/' . $user['uuid'];

        $headers = [
            'Accept: application/json',
            'Cookie: session=' . $sessionId
        ];

        $response = $this->makeHttpRequest($url, 'POST', null, $headers);

        return $response['success'];
    }

    /**
     * Get X-UI config
     */
    private function getXUIConfig(array $server, string $email): ?string
    {
        $user = $this->getXUIUser($server, $email);
        if (!$user) {
            return null;
        }

        // Generate config based on server settings and user UUID
        $serverInfo = [
            'address' => parse_url($server['url'], PHP_URL_HOST),
            'port' => $server['port'] ?? 443,
            'uuid' => $user['uuid'],
            'email' => $email
        ];

        // Generate VLESS config (most common for X-UI)
        $config = $this->generateXUIVlessConfig($serverInfo);

        return $config;
    }

    /**
     * Get X-UI session
     */
    private function getXUISession(array $server): string
    {
        $cacheKey = 'xui_session_' . $server['id'];

        if (isset($this->authTokens[$cacheKey])) {
            return $this->authTokens[$cacheKey];
        }

        $url = rtrim($server['url'], '/') . '/login';

        $postData = [
            'username' => $server['username'],
            'password' => $server['password']
        ];

        $headers = [
            'Content-Type: application/x-www-form-urlencoded',
            'Accept: application/json'
        ];

        $response = $this->makeHttpRequest($url, 'POST', $postData, $headers);

        if (!$response['success']) {
            throw PanelException::authenticationFailed('x-ui');
        }

        // Extract session from response headers
        $sessionId = $this->extractSessionFromResponse($response);

        if (empty($sessionId)) {
            throw PanelException::authenticationFailed('x-ui');
        }

        $this->authTokens[$cacheKey] = $sessionId;

        return $sessionId;
    }

    /**
     * Extract session ID from response
     */
    private function extractSessionFromResponse(array $response): string
    {
        // In real implementation, this would extract session from Set-Cookie header
        // For testing, return a mock session
        return 'mock_xui_session_' . time();
    }

    /**
     * Generate UUID
     */
    private function generateUUID(): string
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff)
        );
    }

    /**
     * Generate X-UI VLESS config
     */
    private function generateXUIVlessConfig(array $serverInfo): string
    {
        $address = $serverInfo['address'];
        $port = $serverInfo['port'];
        $uuid = $serverInfo['uuid'];
        $email = $serverInfo['email'];

        // Basic VLESS config
        $config = "vless://{$uuid}@{$address}:{$port}?type=tcp&security=tls#{$email}";

        return $config;
    }

    /**
     * Make HTTP request
     */
    private function makeHttpRequest(string $url, string $method, $data = null, array $headers = []): array
    {
        if (env('DISABLE_EXTERNAL_APIS', false)) {
            return $this->getMockPanelResponse($method, $data);
        }

        $ch = curl_init();

        $defaultHeaders = [
            'User-Agent: WeBot/2.0',
            'Accept: application/json'
        ];

        $headers = array_merge($defaultHeaders, $headers);

        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_CUSTOMREQUEST => $method
        ]);

        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            if (is_array($data)) {
                $contentType = '';
                foreach ($headers as $header) {
                    if (stripos($header, 'content-type:') === 0) {
                        $contentType = strtolower($header);
                        break;
                    }
                }

                if (strpos($contentType, 'application/json') !== false) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                } else {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
                }
            } else {
                curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            }
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);

        curl_close($ch);

        if ($response === false) {
            return [
                'success' => false,
                'error' => "cURL error: {$error}",
                'http_code' => 0
            ];
        }

        $decoded = json_decode($response, true);

        return [
            'success' => $httpCode >= 200 && $httpCode < 300,
            'data' => $decoded ?? $response,
            'http_code' => $httpCode,
            'error' => $httpCode >= 400 ? "HTTP {$httpCode}" : null
        ];
    }

    /**
     * Get mock panel response for testing
     */
    private function getMockPanelResponse(string $method, $data): array
    {
        $mockResponses = $GLOBALS['mock_panel_responses'] ?? [];

        $responseKey = match ($method) {
            'POST' => 'createUser',
            'GET' => 'getUser',
            'PUT', 'PATCH' => 'updateUser',
            'DELETE' => 'deleteUser',
            default => 'default'
        };

        if (isset($mockResponses[$responseKey])) {
            return [
                'success' => true,
                'data' => $mockResponses[$responseKey],
                'http_code' => 200
            ];
        }

        return [
            'success' => true,
            'data' => ['uuid' => Helper::generateUuid(), 'status' => 'active'],
            'http_code' => 200
        ];
    }

    /**
     * Get server by ID
     */
    private function getServerById(int $serverId): ?array
    {
        $sql = "SELECT * FROM `server_info` WHERE `id` = ?";
        return $this->database->fetchRow($sql, [$serverId], 'i');
    }

    /**
     * Load panel configurations
     */
    private function loadPanelConfigs(): void
    {
        $this->panelConfigs = $this->config->get('panels', []);
    }

    /**
     * Test panel connection
     */
    public function testConnection(int $serverId): array
    {
        $server = $this->getServerById($serverId);

        if (!$server) {
            return ['success' => false, 'error' => 'سرور یافت نشد'];
        }

        try {
            $panelType = $server['panel_type'] ?? 'marzban';

            $result = match ($panelType) {
                'marzban' => $this->testMarzbanConnection($server),
                'marzneshin' => $this->testMarzneshiConnection($server),
                'x-ui' => $this->testXUIConnection($server),
                default => ['success' => false, 'error' => 'نوع پنل پشتیبانی نمی‌شود']
            };

            $this->logger->info('Panel connection test', [
                'server_id' => $serverId,
                'panel_type' => $panelType,
                'result' => $result
            ]);

            return $result;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Test Marzban connection
     */
    private function testMarzbanConnection(array $server): array
    {
        try {
            $token = $this->getMarzbanToken($server);

            $url = rtrim($server['url'], '/') . '/api/admin';
            $headers = [
                'Authorization: Bearer ' . $token,
                'Accept: application/json'
            ];

            $response = $this->makeHttpRequest($url, 'GET', null, $headers);

            return [
                'success' => $response['success'],
                'error' => $response['error'] ?? null,
                'panel_info' => $response['data'] ?? null
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    private function testMarzneshiConnection(array $server): array
    {
        return ['success' => false, 'error' => 'Marzneshin test not implemented'];
    }

    private function testXUIConnection(array $server): array
    {
        return ['success' => false, 'error' => 'X-UI test not implemented'];
    }

    /**
     * Get panel statistics
     */
    public function getPanelStats(string $panelType): array
    {
        try {
            // Get all servers of this panel type
            $sql = "SELECT * FROM `server_info` WHERE `panel_type` = ? AND `active` = 1";
            $servers = $this->database->fetchAll($sql, [$panelType], 's');

            if (empty($servers)) {
                return ['success' => false, 'error' => 'هیچ سرور فعالی یافت نشد'];
            }

            $totalStats = [
                'total_users' => 0,
                'active_users' => 0,
                'total_traffic' => 0,
                'servers_count' => count($servers),
                'online_servers' => 0
            ];

            foreach ($servers as $server) {
                $serverStats = $this->getServerStats($server);
                if ($serverStats['success']) {
                    $totalStats['total_users'] += $serverStats['data']['total_users'] ?? 0;
                    $totalStats['active_users'] += $serverStats['data']['active_users'] ?? 0;
                    $totalStats['total_traffic'] += $serverStats['data']['total_traffic'] ?? 0;
                    $totalStats['online_servers']++;
                }
            }

            return [
                'success' => true,
                'data' => $totalStats
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Health check for panel
     */
    public function healthCheck(array $panel): array
    {
        try {
            $serverId = $panel['id'] ?? null;
            if (!$serverId) {
                return ['success' => false, 'error' => 'Invalid panel data'];
            }

            return $this->testConnection($serverId);
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Sync panel data
     */
    public function syncPanelData(string $panelType): array
    {
        try {
            $sql = "SELECT * FROM `server_info` WHERE `panel_type` = ? AND `active` = 1";
            $servers = $this->database->fetchAll($sql, [$panelType], 's');

            if (empty($servers)) {
                return ['success' => false, 'error' => 'هیچ سرور فعالی یافت نشد'];
            }

            $syncResults = [];
            foreach ($servers as $server) {
                $result = $this->syncServerData($server);
                $syncResults[] = [
                    'server_id' => $server['id'],
                    'success' => $result['success'],
                    'synced_users' => $result['synced_users'] ?? 0
                ];
            }

            return [
                'success' => true,
                'data' => $syncResults
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get server statistics
     */
    private function getServerStats(array $server): array
    {
        unset($server); // Suppress unused parameter warning
        // Mock implementation - would integrate with actual panel APIs
        return [
            'success' => true,
            'data' => [
                'total_users' => rand(50, 200),
                'active_users' => rand(20, 100),
                'total_traffic' => rand(1000000000, 10000000000) // bytes
            ]
        ];
    }

    /**
     * Sync server data
     */
    private function syncServerData(array $server): array
    {
        unset($server); // Suppress unused parameter warning
        // Mock implementation - would sync users and data from panel
        return [
            'success' => true,
            'synced_users' => rand(10, 50)
        ];
    }

    /**
     * Activate service for user (for testing)
     */
    public function activateService(int $userId, string $serviceType, array $options = []): array
    {
        try {
            // Get available server for service type
            $sql = "SELECT * FROM `server_info` WHERE `panel_type` = ? AND `active` = 1 ORDER BY `load` ASC LIMIT 1";
            $server = $this->database->fetchRow($sql, [$serviceType], 's');

            if (!$server) {
                return [
                    'success' => false,
                    'message' => 'No available servers for service type: ' . $serviceType
                ];
            }

            // Create user on panel
            $userData = [
                'username' => 'user_' . $userId . '_' . time(),
                'email' => 'user' . $userId . '@webot.local',
                'plan' => $options['plan'] ?? 'monthly',
                'traffic_limit' => $this->getTrafficLimit($options['plan'] ?? 'monthly'),
                'expire_time' => $this->getExpireTime($options['plan'] ?? 'monthly')
            ];

            $userResult = $this->createUser($server['id'], $userData);

            if (!$userResult['success']) {
                return [
                    'success' => false,
                    'message' => 'Failed to create user on panel: ' . ($userResult['error'] ?? 'Unknown error')
                ];
            }

            // Create service record in database
            $serviceData = [
                'user_id' => $userId,
                'server_id' => $server['id'],
                'service_type' => $serviceType,
                'username' => $userData['username'],
                'uuid' => $userResult['uuid'] ?? null,
                'status' => 'active',
                'plan' => $options['plan'] ?? 'monthly',
                'created_at' => date('Y-m-d H:i:s'),
                'expires_at' => $userData['expire_time']
            ];

            $serviceId = $this->database->insert('services', $serviceData);

            return [
                'success' => true,
                'service_id' => $serviceId,
                'username' => $userData['username'],
                'uuid' => $userResult['uuid'] ?? null,
                'server' => $server['name'] ?? 'Unknown',
                'message' => 'Service activated successfully'
            ];
        } catch (\Exception $e) {
            $this->logger->error('Service activation failed', [
                'user_id' => $userId,
                'service_type' => $serviceType,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Service activation failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get traffic limit based on plan
     */
    private function getTrafficLimit(string $plan): int
    {
        $limits = [
            'monthly' => 100 * 1024 * 1024 * 1024, // 100GB
            'quarterly' => 300 * 1024 * 1024 * 1024, // 300GB
            'yearly' => 1200 * 1024 * 1024 * 1024 // 1200GB
        ];

        return $limits[$plan] ?? $limits['monthly'];
    }

    /**
     * Get expire time based on plan
     */
    private function getExpireTime(string $plan): string
    {
        $intervals = [
            'monthly' => '+1 month',
            'quarterly' => '+3 months',
            'yearly' => '+1 year'
        ];

        $interval = $intervals[$plan] ?? $intervals['monthly'];
        return date('Y-m-d H:i:s', strtotime($interval));
    }
}
