# WeBot MySQL Optimization Configuration
# Optimized for production workloads with moderate traffic

[mysqld]
# Basic Settings
default-storage-engine = InnoDB
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

# Connection Settings
max_connections = 200
max_user_connections = 50
max_connect_errors = 100000
connect_timeout = 10
wait_timeout = 600
interactive_timeout = 600

# Thread Settings
thread_cache_size = 16
thread_stack = 256K

# Query Cache (disabled in MySQL 8.0+, but kept for compatibility)
query_cache_type = 0
query_cache_size = 0

# Table Settings
table_open_cache = 4000
table_definition_cache = 2000
open_files_limit = 65535

# Temporary Tables
tmp_table_size = 64M
max_heap_table_size = 64M

# Sort and Group Settings
sort_buffer_size = 2M
read_buffer_size = 1M
read_rnd_buffer_size = 2M
join_buffer_size = 2M

# InnoDB Settings
innodb_buffer_pool_size = 1G
innodb_buffer_pool_instances = 4
innodb_log_file_size = 256M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
innodb_file_per_table = 1
innodb_open_files = 2000

# InnoDB Performance
innodb_read_io_threads = 4
innodb_write_io_threads = 4
innodb_thread_concurrency = 0
innodb_lock_wait_timeout = 50
innodb_rollback_on_timeout = 1

# InnoDB Monitoring
innodb_monitor_enable = all
innodb_print_all_deadlocks = 1

# Binary Logging (for replication and point-in-time recovery)
log_bin = mysql-bin
binlog_format = ROW
binlog_expire_logs_seconds = 604800
max_binlog_size = 100M
sync_binlog = 1

# Slow Query Log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 1
log_queries_not_using_indexes = 1
min_examined_row_limit = 1000

# Error Log
log_error = /var/log/mysql/error.log
log_error_verbosity = 2

# General Log (disabled by default for performance)
general_log = 0
general_log_file = /var/log/mysql/general.log

# Character Set
character_set_server = utf8mb4
collation_server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'

# Security Settings
local_infile = 0
skip_show_database = 1

# Performance Schema
performance_schema = ON
performance_schema_max_table_instances = 12500
performance_schema_max_table_handles = 4000

# MyISAM Settings (for system tables)
key_buffer_size = 32M
myisam_sort_buffer_size = 8M
myisam_max_sort_file_size = 2G
myisam_repair_threads = 1

# Network Settings
max_allowed_packet = 64M
net_buffer_length = 32K
net_read_timeout = 30
net_write_timeout = 60

# Replication Settings (if using replication)
server_id = 1
relay_log = mysql-relay-bin
relay_log_recovery = 1
slave_skip_errors = 1062,1053,1146

[mysql]
default_character_set = utf8mb4

[mysqldump]
quick
quote_names
max_allowed_packet = 64M

[isamchk]
key_buffer_size = 16M
sort_buffer_size = 20M
read_buffer = 2M
write_buffer = 2M

[myisamchk]
key_buffer_size = 20M
sort_buffer_size = 20M
read_buffer = 2M
write_buffer = 2M

[mysqlhotcopy]
interactive_timeout
