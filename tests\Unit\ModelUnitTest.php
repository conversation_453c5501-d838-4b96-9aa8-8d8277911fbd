<?php

declare(strict_types=1);

namespace WeBot\Tests\Unit;

use WeBot\Models\User;
use WeBot\Models\Payment;
use WeBot\Models\Service;
use WeBot\Models\BaseModel;

/**
 * Model Unit Tests
 * 
 * Unit tests for all WeBot models including
 * User, Payment, Service, and BaseModel functionality.
 * 
 * @package WeBot\Tests\Unit
 * @version 2.0
 */
class ModelUnitTest extends BaseTestCase
{
    protected function setUp(): void
    {
        parent::setUp();
    }
    
    /**
     * Test User model basic functionality
     */
    public function testUserModelBasics(): void
    {
        // Test that User class exists and can be instantiated
        $this->assertTrue(class_exists(\WeBot\Models\User::class));

        // Test basic properties with mock database
        $mockDb = $this->createMock(\WeBot\Services\DatabaseService::class);
        $user = new \WeBot\Models\User($mockDb);
        $this->assertInstanceOf(\WeBot\Models\User::class, $user);
    }
    
    /**
     * Test Payment model basic functionality
     */
    public function testPaymentModelBasics(): void
    {
        // Test that Payment class exists and can be instantiated
        $this->assertTrue(class_exists(\WeBot\Models\Payment::class));

        // Test basic properties with mock database
        $mockDb = $this->createMock(\WeBot\Services\DatabaseService::class);
        $payment = new \WeBot\Models\Payment($mockDb);
        $this->assertInstanceOf(\WeBot\Models\Payment::class, $payment);
    }
    
    /**
     * Test Service model basic functionality
     */
    public function testServiceModelBasics(): void
    {
        // Test that Service class exists and can be instantiated
        $this->assertTrue(class_exists(\WeBot\Models\Service::class));

        // Test basic properties with mock database
        $mockDb = $this->createMock(\WeBot\Services\DatabaseService::class);
        $service = new \WeBot\Models\Service($mockDb);
        $this->assertInstanceOf(\WeBot\Models\Service::class, $service);
    }
    
    /**
     * Test BaseModel basic functionality
     */
    public function testBaseModelBasics(): void
    {
        // Test that BaseModel class exists
        $this->assertTrue(class_exists(\WeBot\Models\BaseModel::class));

        // BaseModel is abstract, so we test through a concrete implementation
        $mockDb = $this->createMock(\WeBot\Services\DatabaseService::class);
        $user = new \WeBot\Models\User($mockDb);
        $this->assertInstanceOf(\WeBot\Models\BaseModel::class, $user);
    }
}
