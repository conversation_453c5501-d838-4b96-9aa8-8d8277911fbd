<?php

declare(strict_types=1);

namespace WeBot\Contracts;

/**
 * HTTP Client Interface
 *
 * Defines the contract for HTTP clients to ensure compatibility
 * between GuzzleHttp\Client and MockHttpClient.
 *
 * @package WeBot\Contracts
 * @version 2.0
 */
interface HttpClientInterface
{
    /**
     * Send HTTP request
     *
     * @param string $method
     * @param string $uri
     * @param array $options
     * @return mixed
     */
    public function request(string $method, string $uri, array $options = []): mixed;

    /**
     * Send GET request
     *
     * @param string $uri
     * @param array $options
     * @return mixed
     */
    public function get(string $uri, array $options = []): mixed;

    /**
     * Send POST request
     *
     * @param string $uri
     * @param array $options
     * @return mixed
     */
    public function post(string $uri, array $options = []): mixed;

    /**
     * Send PUT request
     *
     * @param string $uri
     * @param array $options
     * @return mixed
     */
    public function put(string $uri, array $options = []): mixed;

    /**
     * Send DELETE request
     *
     * @param string $uri
     * @param array $options
     * @return mixed
     */
    public function delete(string $uri, array $options = []): mixed;
}
