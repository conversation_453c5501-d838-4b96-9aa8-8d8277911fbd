<?php

declare(strict_types=1);

namespace WeBot\Models;

use WeBot\Services\DatabaseService;

/**
 * User Model
 *
 * Represents a user in the WeBot system with all related
 * functionality including authentication, wallet, and services.
 *
 * @package WeBot\Models
 * @version 2.0
 */
class User extends BaseModel
{
    protected string $table = 'users';
    protected string $primaryKey = 'userid';

    protected array $fillable = [
        'userid',
        'first_name',
        'last_name',
        'username',
        'phone',
        'step',
        'temp',
        'wallet',
        'isAdmin',
        'banned',
        'referrer_id',
        'total_spent',
        'last_activity'
    ];

    protected array $hidden = [
        'step',
        'temp'
    ];

    protected array $casts = [
        'userid' => 'int',
        'wallet' => 'int',
        'isAdmin' => 'bool',
        'banned' => 'bool',
        'referrer_id' => 'int',
        'total_spent' => 'int'
    ];

    /**
     * Get validation rules
     */
    protected function getValidationRules(): array
    {
        $rules = [
            'userid' => 'required|integer|min:1',
            'first_name' => 'required|string|max:255',
            'username' => 'string|max:255',
            'phone' => 'string|max:20',
            'wallet' => 'integer|min:0',
            'isAdmin' => 'boolean',
            'banned' => 'boolean'
        ];

        // If updating, userid is not required
        if ($this->exists()) {
            unset($rules['userid']);
        }

        return $rules;
    }

    /**
     * Check if user is admin
     */
    public function isAdmin(): bool
    {
        return (bool) $this->getAttribute('isAdmin');
    }

    /**
     * Check if user is banned
     */
    public function isBanned(): bool
    {
        return (bool) $this->getAttribute('banned');
    }

    /**
     * Get full name
     */
    public function getFullName(): string
    {
        $firstName = $this->getAttribute('first_name') ?? '';
        $lastName = $this->getAttribute('last_name') ?? '';

        return trim($firstName . ' ' . $lastName);
    }

    /**
     * Get display name
     */
    public function getDisplayName(): string
    {
        $fullName = $this->getFullName();

        if (!empty($fullName)) {
            return $fullName;
        }

        $username = $this->getAttribute('username');
        if (!empty($username)) {
            return '@' . $username;
        }

        return 'کاربر #' . $this->getAttribute('userid');
    }

    /**
     * Get username
     */
    public function getUsername(): ?string
    {
        return $this->getAttribute('username');
    }

    /**
     * Get phone number
     */
    public function getPhone(): ?string
    {
        return $this->getAttribute('phone');
    }

    /**
     * Get wallet balance
     */
    public function getWalletBalance(): int
    {
        return (int) ($this->getAttribute('wallet') ?? 0);
    }

    /**
     * Add to wallet
     */
    public function addToWallet(int $amount): bool
    {
        if ($amount <= 0) {
            return false;
        }

        $currentBalance = $this->getWalletBalance();
        $this->setAttribute('wallet', $currentBalance + $amount);

        return $this->save();
    }

    /**
     * Deduct from wallet
     */
    public function deductFromWallet(int $amount): bool
    {
        if ($amount <= 0) {
            return false;
        }

        $currentBalance = $this->getWalletBalance();

        if ($currentBalance < $amount) {
            return false;
        }

        $this->setAttribute('wallet', $currentBalance - $amount);

        return $this->save();
    }

    /**
     * Check if user has enough balance
     */
    public function hasEnoughBalance(int $amount): bool
    {
        return $this->getWalletBalance() >= $amount;
    }

    /**
     * Update user step
     */
    public function setStep(string $step): bool
    {
        $this->setAttribute('step', $step);
        return $this->save();
    }

    /**
     * Get user step
     */
    public function getStep(): string
    {
        return $this->getAttribute('step') ?? '';
    }

    /**
     * Update temp data
     */
    public function setTemp(string $temp): bool
    {
        $this->setAttribute('temp', $temp);
        return $this->save();
    }

    /**
     * Get temp data
     */
    public function getTemp(): string
    {
        return $this->getAttribute('temp') ?? '';
    }

    /**
     * Ban user
     */
    public function ban(): bool
    {
        $this->setAttribute('banned', true);
        return $this->save();
    }

    /**
     * Unban user
     */
    public function unban(): bool
    {
        $this->setAttribute('banned', false);
        return $this->save();
    }

    /**
     * Make user admin
     */
    public function makeAdmin(): bool
    {
        $this->setAttribute('isAdmin', true);
        return $this->save();
    }

    /**
     * Remove admin privileges
     */
    public function removeAdmin(): bool
    {
        $this->setAttribute('isAdmin', false);
        return $this->save();
    }

    /**
     * Update last activity
     */
    public function updateLastActivity(): bool
    {
        $this->setAttribute('last_activity', date('Y-m-d H:i:s'));
        return $this->save();
    }

    /**
     * Get user services
     */
    public function getServices(): array
    {
        $sql = "SELECT * FROM `services` WHERE `user_id` = ? ORDER BY `created_at` DESC";
        $results = $this->database->fetchAll($sql, [$this->getKey()], 'i');

        $services = [];
        foreach ($results as $serviceData) {
            $services[] = new Service($this->database, $serviceData);
        }

        return $services;
    }

    /**
     * Get active services
     */
    public function getActiveServices(): array
    {
        $sql = "SELECT * FROM `services` WHERE `user_id` = ? AND `status` = 'active' ORDER BY `created_at` DESC";
        $results = $this->database->fetchAll($sql, [$this->getKey()], 'i');

        $services = [];
        foreach ($results as $serviceData) {
            $services[] = new Service($this->database, $serviceData);
        }

        return $services;
    }

    /**
     * Get user payments
     */
    public function getPayments(): array
    {
        $sql = "SELECT * FROM `payments` WHERE `user_id` = ? ORDER BY `created_at` DESC";
        $results = $this->database->fetchAll($sql, [$this->getKey()], 'i');

        $payments = [];
        foreach ($results as $paymentData) {
            $payments[] = new Payment($this->database, $paymentData);
        }

        return $payments;
    }

    /**
     * Get successful payments
     */
    public function getSuccessfulPayments(): array
    {
        $sql = "SELECT * FROM `payments` WHERE `user_id` = ? AND `status` = 'completed' ORDER BY `created_at` DESC";
        $results = $this->database->fetchAll($sql, [$this->getKey()], 'i');

        $payments = [];
        foreach ($results as $paymentData) {
            $payments[] = new Payment($this->database, $paymentData);
        }

        return $payments;
    }

    /**
     * Get user transactions
     */
    public function getTransactions(int $limit = 20): array
    {
        $sql = "SELECT * FROM `transactions` WHERE `user_id` = ? ORDER BY `created_at` DESC LIMIT ?";
        return $this->database->fetchAll($sql, [$this->getKey(), $limit], 'ii');
    }

    /**
     * Get total spent amount
     */
    public function getTotalSpent(): int
    {
        $sql = "SELECT COALESCE(SUM(amount), 0) FROM `payments` WHERE `user_id` = ? AND `status` = 'completed'";
        return (int) $this->database->fetchValue($sql, [$this->getKey()], 'i');
    }

    /**
     * Update total spent
     */
    public function updateTotalSpent(): bool
    {
        $totalSpent = $this->getTotalSpent();
        $this->setAttribute('total_spent', $totalSpent);
        return $this->save();
    }

    /**
     * Get referrer
     */
    public function getReferrer(): ?User
    {
        $referrerId = $this->getAttribute('referrer_id');

        if (!$referrerId) {
            return null;
        }

        return self::find($this->database, $referrerId);
    }

    /**
     * Get referred users
     */
    public function getReferredUsers(): array
    {
        return self::where($this->database, ['referrer_id' => $this->getKey()]);
    }

    /**
     * Count referred users
     */
    public function countReferredUsers(): int
    {
        $sql = "SELECT COUNT(*) FROM `users` WHERE `referrer_id` = ?";
        return (int) $this->database->fetchValue($sql, [$this->getKey()], 'i');
    }

    /**
     * Check if phone is verified
     */
    public function isPhoneVerified(): bool
    {
        return !empty($this->getAttribute('phone'));
    }

    /**
     * Check if phone is Iranian
     */
    public function hasIranianPhone(): bool
    {
        $phone = $this->getAttribute('phone');

        if (empty($phone)) {
            return false;
        }

        return preg_match('/^(\+98|98|0098)/', $phone) === 1;
    }

    /**
     * Get user statistics
     */
    public function getStatistics(): array
    {
        return [
            'total_services' => count($this->getServices()),
            'active_services' => count($this->getActiveServices()),
            'total_payments' => count($this->getPayments()),
            'successful_payments' => count($this->getSuccessfulPayments()),
            'total_spent' => $this->getTotalSpent(),
            'wallet_balance' => $this->getWalletBalance(),
            'referred_users' => $this->countReferredUsers(),
            'is_admin' => $this->isAdmin(),
            'is_banned' => $this->isBanned(),
            'phone_verified' => $this->isPhoneVerified(),
            'join_date' => $this->getAttribute('created_at'),
            'last_activity' => $this->getAttribute('last_activity')
        ];
    }

    /**
     * Find user by Telegram ID
     */
    public static function findByTelegramId(DatabaseService $database, int $telegramId): ?User
    {
        $sql = "SELECT * FROM `users` WHERE `userid` = ?";
        $attributes = $database->fetchRow($sql, [$telegramId], 'i');

        if ($attributes) {
            return new self($database, $attributes);
        }

        return null;
    }

    /**
     * Find user by username
     */
    public static function findByUsername(DatabaseService $database, string $username): ?User
    {
        $sql = "SELECT * FROM `users` WHERE `username` = ?";
        $attributes = $database->fetchRow($sql, [$username], 's');

        if ($attributes) {
            return new self($database, $attributes);
        }

        return null;
    }

    /**
     * Find user by phone
     */
    public static function findByPhone(DatabaseService $database, string $phone): ?User
    {
        $sql = "SELECT * FROM `users` WHERE `phone` = ?";
        $attributes = $database->fetchRow($sql, [$phone], 's');

        if ($attributes) {
            return new self($database, $attributes);
        }

        return null;
    }

    /**
     * Get all admins
     */
    public static function getAdmins(DatabaseService $database): array
    {
        return self::where($database, ['isAdmin' => true]);
    }

    /**
     * Get banned users
     */
    public static function getBannedUsers(DatabaseService $database): array
    {
        return self::where($database, ['banned' => true]);
    }

    /**
     * Get users registered today
     */
    public static function getTodayUsers(DatabaseService $database): array
    {
        $sql = "SELECT * FROM `users` WHERE DATE(created_at) = CURDATE()";
        $results = $database->fetchAll($sql);

        $users = [];
        foreach ($results as $attributes) {
            $users[] = new self($database, $attributes);
        }

        return $users;
    }

    /**
     * Search users
     */
    public static function search(DatabaseService $database, string $query): array
    {
        $searchTerm = '%' . $query . '%';

        $sql = "SELECT * FROM `users` WHERE 
                `first_name` LIKE ? OR 
                `last_name` LIKE ? OR 
                `username` LIKE ? OR 
                `phone` LIKE ? OR
                `userid` = ?
                ORDER BY `created_at` DESC";

        $userId = is_numeric($query) ? (int) $query : 0;
        $results = $database->fetchAll($sql, [$searchTerm, $searchTerm, $searchTerm, $searchTerm, $userId], 'ssssi');

        $users = [];
        foreach ($results as $attributes) {
            $users[] = new self($database, $attributes);
        }

        return $users;
    }
}
