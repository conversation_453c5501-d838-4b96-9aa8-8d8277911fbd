<?php

declare(strict_types=1);

namespace WeBot\Core\Cache\Drivers;

use WeBot\Core\Cache\Contracts\CacheInterface;
use Redis;
use Exception;

/**
 * Redis Cache Driver
 *
 * Redis implementation of the cache interface.
 *
 * @package WeBot\Core\Cache\Drivers
 * @version 2.0
 */
class RedisDriver implements CacheInterface
{
    private ?Redis $redis = null;
    private array $config;
    private bool $connected = false;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'host' => '127.0.0.1',
            'port' => 6379,
            'password' => null,
            'database' => 0,
            'timeout' => 5.0,
            'read_timeout' => 5.0,
            'retry_interval' => 100,
        ], $config);

        $this->connect();
    }

    /**
     * Connect to Redis
     */
    private function connect(): bool
    {
        try {
            $this->redis = new Redis();

            $connected = $this->redis->connect(
                $this->config['host'],
                $this->config['port'],
                $this->config['timeout'],
                null,
                $this->config['retry_interval'],
                $this->config['read_timeout']
            );

            if (!$connected) {
                throw new Exception('Failed to connect to Redis');
            }

            if ($this->config['password']) {
                $this->redis->auth($this->config['password']);
            }

            $this->redis->select($this->config['database']);
            $this->connected = true;

            return true;
        } catch (Exception $e) {
            $this->connected = false;
            error_log("Redis connection failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get cached value
     */
    public function get(string $key, $default = null)
    {
        if (!$this->isConnected()) {
            return $default;
        }

        try {
            $value = $this->redis->get($key);

            if ($value === false) {
                return $default;
            }

            return $this->unserialize($value);
        } catch (Exception $e) {
            error_log("Cache get error: " . $e->getMessage());
            return $default;
        }
    }

    /**
     * Set cached value
     */
    public function set(string $key, $value, ?int $ttl = null): bool
    {
        if (!$this->isConnected()) {
            return false;
        }

        try {
            $serialized = $this->serialize($value);

            if ($ttl) {
                return $this->redis->setex($key, $ttl, $serialized);
            }

            return $this->redis->set($key, $serialized);
        } catch (Exception $e) {
            error_log("Cache set error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete cached value
     */
    public function delete(string $key): bool
    {
        if (!$this->isConnected()) {
            return false;
        }

        try {
            return $this->redis->del($key) > 0;
        } catch (Exception $e) {
            error_log("Cache delete error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if key exists
     */
    public function exists(string $key): bool
    {
        if (!$this->isConnected()) {
            return false;
        }

        try {
            return $this->redis->exists($key) > 0;
        } catch (Exception $e) {
            error_log("Cache exists error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Increment value
     */
    public function increment(string $key, int $value = 1): int
    {
        if (!$this->isConnected()) {
            return 0;
        }

        try {
            return $this->redis->incrBy($key, $value);
        } catch (Exception $e) {
            error_log("Cache increment error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Decrement value
     */
    public function decrement(string $key, int $value = 1): int
    {
        if (!$this->isConnected()) {
            return 0;
        }

        try {
            return $this->redis->decrBy($key, $value);
        } catch (Exception $e) {
            error_log("Cache decrement error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Set multiple values
     */
    public function setMultiple(array $values, ?int $ttl = null): bool
    {
        if (!$this->isConnected()) {
            return false;
        }

        try {
            $serializedValues = [];
            foreach ($values as $key => $value) {
                $serializedValues[$key] = $this->serialize($value);
            }

            $result = $this->redis->mset($serializedValues);

            if ($ttl && $result) {
                foreach (array_keys($values) as $key) {
                    $this->redis->expire($key, $ttl);
                }
            }

            return $result;
        } catch (Exception $e) {
            error_log("Cache setMultiple error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get multiple values
     */
    public function getMultiple(array $keys, $default = null): array
    {
        if (!$this->isConnected()) {
            return array_fill_keys($keys, $default);
        }

        try {
            $values = $this->redis->mget($keys);
            $result = [];

            foreach ($keys as $index => $key) {
                $value = $values[$index] ?? false;
                $result[$key] = $value !== false ? $this->unserialize($value) : $default;
            }

            return $result;
        } catch (Exception $e) {
            error_log("Cache getMultiple error: " . $e->getMessage());
            return array_fill_keys($keys, $default);
        }
    }

    /**
     * Flush all cache
     */
    public function flush(): bool
    {
        if (!$this->isConnected()) {
            return false;
        }

        try {
            return $this->redis->flushDB();
        } catch (Exception $e) {
            error_log("Cache flush error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if connected to Redis
     */
    public function isConnected(): bool
    {
        if (!$this->connected || !$this->redis) {
            return false;
        }

        try {
            $this->redis->ping();
            return true;
        } catch (Exception $e) {
            $this->connected = false;
            return false;
        }
    }

    /**
     * Get Redis instance
     */
    public function getRedis(): ?Redis
    {
        return $this->redis;
    }

    /**
     * Serialize value for storage
     */
    private function serialize($value): string
    {
        return serialize($value);
    }

    /**
     * Unserialize value from storage
     */
    private function unserialize(string $value)
    {
        return unserialize($value);
    }

    /**
     * Reconnect to Redis
     */
    public function reconnect(): bool
    {
        if ($this->redis) {
            try {
                $this->redis->close();
            } catch (Exception $e) {
                // Ignore close errors
            }
        }

        return $this->connect();
    }
}
