<?php

declare(strict_types=1);

namespace WeBot\Tests\Unit\Core\Cache;

use WeBot\Tests\Unit\BaseTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use WeBot\Core\Cache\CacheManager;
use WeBot\Core\Cache\Contracts\CacheInterface;
use WeBot\Core\Cache\Drivers\RedisDriver;

/**
 * Cache Manager Test
 * 
 * Unit tests for the new modular CacheManager class.
 * 
 * @package WeBot\Tests\Unit\Core\Cache
 * @version 2.0
 */
class CacheManagerTest extends BaseTestCase
{
    private CacheManager $cacheManager;
    /** @var MockObject&CacheInterface */
    private MockObject $mockDriver;

    protected function setUp(): void
    {
        parent::setUp();

        // Create mock driver
        /** @var MockObject&CacheInterface $mockDriver */
        $mockDriver = $this->createMock(CacheInterface::class);
        $this->mockDriver = $mockDriver;
        
        // Create CacheManager with test config
        $config = [
            'prefix' => 'test:',
            'default_ttl' => 3600,
            'enabled' => true
        ];
        
        $this->cacheManager = new CacheManager($config);
        
        // Inject mock driver using reflection
        $reflection = new \ReflectionClass($this->cacheManager);
        $driverProperty = $reflection->getProperty('driver');
        $driverProperty->setAccessible(true);
        $driverProperty->setValue($this->cacheManager, $this->mockDriver);
    }

    /**
     * Test get method
     */
    public function testGet(): void
    {
        // Arrange
        $key = 'test_key';
        $expectedValue = 'test_value';
        $default = 'default_value';

        $this->mockDriver
            ->expects($this->once())
            ->method('get')
            ->with('test:' . $key, $default)
            ->willReturn($expectedValue);

        // Act
        $result = $this->cacheManager->get($key, $default);

        // Assert
        $this->assertEquals($expectedValue, $result);
    }

    /**
     * Test set method
     */
    public function testSet(): void
    {
        // Arrange
        $key = 'test_key';
        $value = 'test_value';
        $ttl = 1800;

        $this->mockDriver
            ->expects($this->once())
            ->method('set')
            ->with('test:' . $key, $value, $ttl)
            ->willReturn(true);

        // Act
        $result = $this->cacheManager->set($key, $value, $ttl);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test remember method with cache hit
     */
    public function testRememberWithCacheHit(): void
    {
        // Arrange
        $key = 'test_key';
        $cachedValue = 'cached_value';
        $callback = function () {
            return 'callback_value';
        };

        $this->mockDriver
            ->expects($this->once())
            ->method('get')
            ->with('test:' . $key, null)
            ->willReturn($cachedValue);

        $this->mockDriver
            ->expects($this->never())
            ->method('set');

        // Act
        $result = $this->cacheManager->remember($key, $callback);

        // Assert
        $this->assertEquals($cachedValue, $result);
    }

    /**
     * Test remember method with cache miss
     */
    public function testRememberWithCacheMiss(): void
    {
        // Arrange
        $key = 'test_key';
        $callbackValue = 'callback_value';
        $callback = function () use ($callbackValue) {
            return $callbackValue;
        };

        $this->mockDriver
            ->expects($this->once())
            ->method('get')
            ->with('test:' . $key, null)
            ->willReturn(null);

        $this->mockDriver
            ->expects($this->once())
            ->method('set')
            ->with('test:' . $key, $callbackValue, 3600)
            ->willReturn(true);

        // Act
        $result = $this->cacheManager->remember($key, $callback);

        // Assert
        $this->assertEquals($callbackValue, $result);
    }

    /**
     * Test delete method
     */
    public function testDelete(): void
    {
        // Arrange
        $key = 'test_key';

        $this->mockDriver
            ->expects($this->once())
            ->method('delete')
            ->with('test:' . $key)
            ->willReturn(true);

        // Act
        $result = $this->cacheManager->delete($key);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test exists method
     */
    public function testExists(): void
    {
        // Arrange
        $key = 'test_key';

        $this->mockDriver
            ->expects($this->once())
            ->method('exists')
            ->with('test:' . $key)
            ->willReturn(true);

        // Act
        $result = $this->cacheManager->exists($key);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test increment method
     */
    public function testIncrement(): void
    {
        // Arrange
        $key = 'test_key';
        $value = 5;
        $expectedResult = 10;

        $this->mockDriver
            ->expects($this->once())
            ->method('increment')
            ->with('test:' . $key, $value)
            ->willReturn($expectedResult);

        // Act
        $result = $this->cacheManager->increment($key, $value);

        // Assert
        $this->assertEquals($expectedResult, $result);
    }

    /**
     * Test decrement method
     */
    public function testDecrement(): void
    {
        // Arrange
        $key = 'test_key';
        $value = 3;
        $expectedResult = 7;

        $this->mockDriver
            ->expects($this->once())
            ->method('decrement')
            ->with('test:' . $key, $value)
            ->willReturn($expectedResult);

        // Act
        $result = $this->cacheManager->decrement($key, $value);

        // Assert
        $this->assertEquals($expectedResult, $result);
    }

    /**
     * Test setMultiple method
     */
    public function testSetMultiple(): void
    {
        // Arrange
        $values = [
            'key1' => 'value1',
            'key2' => 'value2'
        ];
        $ttl = 1800;

        $expectedPrefixedValues = [
            'test:key1' => 'value1',
            'test:key2' => 'value2'
        ];

        $this->mockDriver
            ->expects($this->once())
            ->method('setMultiple')
            ->with($expectedPrefixedValues, $ttl)
            ->willReturn(true);

        // Act
        $result = $this->cacheManager->setMultiple($values, $ttl);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test getMultiple method
     */
    public function testGetMultiple(): void
    {
        // Arrange
        $keys = ['key1', 'key2'];
        $default = 'default';
        
        $driverResults = [
            'test:key1' => 'value1',
            'test:key2' => 'value2'
        ];

        $expectedResults = [
            'key1' => 'value1',
            'key2' => 'value2'
        ];

        $this->mockDriver
            ->expects($this->once())
            ->method('getMultiple')
            ->with(['test:key1', 'test:key2'], $default)
            ->willReturn($driverResults);

        // Act
        $result = $this->cacheManager->getMultiple($keys, $default);

        // Assert
        $this->assertEquals($expectedResults, $result);
    }

    /**
     * Test flush method
     */
    public function testFlush(): void
    {
        // Arrange
        $this->mockDriver
            ->expects($this->once())
            ->method('flush')
            ->willReturn(true);

        // Act
        $result = $this->cacheManager->flush();

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test disabled cache
     */
    public function testDisabledCache(): void
    {
        // Arrange
        $this->cacheManager->disable();

        // Act & Assert
        $this->assertNull($this->cacheManager->get('test_key', null));
        $this->assertFalse($this->cacheManager->set('test_key', 'value'));
        $this->assertFalse($this->cacheManager->delete('test_key'));
        $this->assertFalse($this->cacheManager->exists('test_key'));
    }

    /**
     * Test enable/disable functionality
     */
    public function testEnableDisable(): void
    {
        // Test disable
        $this->assertTrue($this->cacheManager->disable());
        $this->assertFalse($this->cacheManager->isEnabled());

        // Test enable
        $this->assertTrue($this->cacheManager->enable());
        $this->assertTrue($this->cacheManager->isEnabled());
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        
        unset($this->cacheManager, $this->mockDriver);
    }
}
