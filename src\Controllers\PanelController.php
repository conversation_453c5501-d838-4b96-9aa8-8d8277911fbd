<?php

declare(strict_types=1);

namespace WeBot\Controllers;

use WeBot\Exceptions\WeBotException;
use WeBot\Services\PanelService;

/**
 * Panel Controller
 *
 * Handles panel management operations including
 * panel selection, configuration, and monitoring.
 *
 * @package WeBot\Controllers
 * @version 2.0
 */
class PanelController extends BaseController
{
    private PanelService $panelService;

    public function __construct($container)
    {
        parent::__construct($container);
        $this->panelService = $container->get('panelService');
    }

    /**
     * Handle panel selection
     */
    public function selectPanel(array $callbackQuery): array
    {
        try {
            $this->initialize(['callback_query' => $callbackQuery]);
            $this->logAction('panel_selection');

            if (!$this->isAdmin()) {
                return $this->answerCallback('شما دسترسی لازم را ندارید.');
            }

            $text = "🔧 **انتخاب پنل**\n\n";
            $text .= "لطفاً نوع پنل مورد نظر خود را انتخاب کنید:\n\n";
            $text .= "• **Marzban**: پنل اصلی با OAuth2\n";
            $text .= "• **Marzneshin**: نسخه پیشرفته با ویژگی‌های اضافی\n";
            $text .= "• **X-UI**: پنل ساده با مدیریت inbound";

            $keyboard = $this->createInlineKeyboard([
                [
                    ['text' => '🟢 Marzban', 'callback_data' => 'panel_marzban'],
                    ['text' => '🔵 Marzneshin', 'callback_data' => 'panel_marzneshin']
                ],
                [
                    ['text' => '🟡 X-UI', 'callback_data' => 'panel_xui']
                ],
                [
                    ['text' => '📊 وضعیت پنل‌ها', 'callback_data' => 'panel_status'],
                    ['text' => '⚙️ تنظیمات', 'callback_data' => 'panel_settings']
                ],
                [
                    ['text' => '🔙 بازگشت', 'callback_data' => 'admin_menu']
                ]
            ]);

            return $this->editMessage($text, $keyboard);
        } catch (\Throwable $e) {
            return $this->handleError($e, 'خطا در انتخاب پنل.');
        }
    }

    /**
     * Handle panel configuration
     */
    public function configurePanel(array $callbackQuery): array
    {
        try {
            $this->initialize(['callback_query' => $callbackQuery]);

            if (!$this->isAdmin()) {
                return $this->answerCallback('شما دسترسی لازم را ندارید.');
            }

            $panelType = str_replace('panel_', '', $this->data);

            $text = "⚙️ **تنظیمات پنل {$panelType}**\n\n";
            $text .= "عملیات مورد نظر خود را انتخاب کنید:";

            $keyboard = $this->createInlineKeyboard([
                [
                    ['text' => '🔗 تست اتصال', 'callback_data' => "test_{$panelType}"],
                    ['text' => '📊 آمار پنل', 'callback_data' => "stats_{$panelType}"]
                ],
                [
                    ['text' => '👥 مدیریت کاربران', 'callback_data' => "users_{$panelType}"],
                    ['text' => '⚙️ تنظیمات', 'callback_data' => "config_{$panelType}"]
                ],
                [
                    ['text' => '🔄 همگام‌سازی', 'callback_data' => "sync_{$panelType}"],
                    ['text' => '🧹 پاکسازی', 'callback_data' => "cleanup_{$panelType}"]
                ],
                [
                    ['text' => '🔙 بازگشت', 'callback_data' => 'select_panel']
                ]
            ]);

            return $this->editMessage($text, $keyboard);
        } catch (\Throwable $e) {
            return $this->handleError($e, 'خطا در تنظیمات پنل.');
        }
    }

    /**
     * Test panel connection
     */
    public function testConnection(array $callbackQuery): array
    {
        try {
            $this->initialize(['callback_query' => $callbackQuery]);

            if (!$this->isAdmin()) {
                return $this->answerCallback('شما دسترسی لازم را ندارید.');
            }

            $panelType = str_replace('test_', '', $this->data);

            $this->answerCallback('در حال تست اتصال...');

            // Get first server ID for the panel type (for testing purposes)
            $serverId = $this->getServerIdByPanelType($panelType);
            if (!$serverId) {
                return $this->editMessage('❌ هیچ سرور فعالی برای این نوع پنل یافت نشد.');
            }

            $result = $this->panelService->testConnection($serverId);

            if ($result['success']) {
                $text = "✅ **اتصال موفق**\n\n";
                $text .= "🔗 پنل: **{$panelType}**\n";
                $text .= "⏱️ زمان پاسخ: {$result['response_time']}ms\n";
                $text .= "📊 وضعیت: آنلاین\n";
                $text .= "🔢 نسخه: " . ($result['version'] ?? 'نامشخص');
            } else {
                $text = "❌ **خطا در اتصال**\n\n";
                $text .= "🔗 پنل: **{$panelType}**\n";
                $text .= "❌ خطا: {$result['error']}\n";
                $text .= "📊 وضعیت: آفلاین";
            }

            $keyboard = $this->createInlineKeyboard([
                [
                    ['text' => '🔄 تست مجدد', 'callback_data' => "test_{$panelType}"]
                ],
                [
                    ['text' => '🔙 بازگشت', 'callback_data' => "panel_{$panelType}"]
                ]
            ]);

            return $this->sendMessage($text, $keyboard);
        } catch (\Throwable $e) {
            return $this->handleError($e, 'خطا در تست اتصال.');
        }
    }

    /**
     * Show panel statistics
     */
    public function showPanelStats(array $callbackQuery): array
    {
        try {
            $this->initialize(['callback_query' => $callbackQuery]);

            if (!$this->isAdmin()) {
                return $this->answerCallback('شما دسترسی لازم را ندارید.');
            }

            $panelType = str_replace('stats_', '', $this->data);

            $this->answerCallback('در حال دریافت آمار...');

            $stats = $this->panelService->getPanelStats($panelType);

            if ($stats['success']) {
                $text = "📊 **آمار پنل {$panelType}**\n\n";
                $text .= "👥 تعداد کاربران: " . number_format($stats['users_count'] ?? 0) . "\n";
                $text .= "🔄 کاربران فعال: " . number_format($stats['active_users'] ?? 0) . "\n";
                $text .= "📈 ترافیک کل: " . $this->formatBytes($stats['total_traffic'] ?? 0) . "\n";
                $text .= "📊 ترافیک امروز: " . $this->formatBytes($stats['today_traffic'] ?? 0) . "\n";
                $text .= "💾 استفاده حافظه: " . ($stats['memory_usage'] ?? 0) . "%\n";
                $text .= "🖥️ استفاده CPU: " . ($stats['cpu_usage'] ?? 0) . "%\n";
                $text .= "⏰ آپتایم: " . $this->formatUptime($stats['uptime'] ?? 0);
            } else {
                $text = "❌ **خطا در دریافت آمار**\n\n";
                $text .= "خطا: {$stats['error']}";
            }

            $keyboard = $this->createInlineKeyboard([
                [
                    ['text' => '🔄 بروزرسانی', 'callback_data' => "stats_{$panelType}"]
                ],
                [
                    ['text' => '📈 آمار تفصیلی', 'callback_data' => "detailed_stats_{$panelType}"],
                    ['text' => '📊 نمودار', 'callback_data' => "chart_{$panelType}"]
                ],
                [
                    ['text' => '🔙 بازگشت', 'callback_data' => "panel_{$panelType}"]
                ]
            ]);

            return $this->sendMessage($text, $keyboard);
        } catch (\Throwable $e) {
            return $this->handleError($e, 'خطا در نمایش آمار.');
        }
    }

    /**
     * Show all panels status
     */
    public function showPanelsStatus(array $callbackQuery): array
    {
        try {
            $this->initialize(['callback_query' => $callbackQuery]);

            if (!$this->isAdmin()) {
                return $this->answerCallback('شما دسترسی لازم را ندارید.');
            }

            $this->answerCallback('در حال بررسی وضعیت پنل‌ها...');

            $panelTypes = ['marzban', 'marzneshin', 'xui'];
            $text = "📊 **وضعیت کلی پنل‌ها**\n\n";

            foreach ($panelTypes as $panelType) {
                // Get servers for this panel type
                $servers = $this->getServersByPanelType($panelType);

                if (empty($servers)) {
                    $text .= "⚪ **{$panelType}**: هیچ سرور فعالی یافت نشد\n\n";
                    continue;
                }

                $onlineCount = 0;
                $totalCount = count($servers);

                foreach ($servers as $server) {
                    $status = $this->panelService->healthCheck($server);
                    if ($status['success']) {
                        $onlineCount++;
                    }
                }

                $statusEmoji = $onlineCount > 0 ? '🟢' : '🔴';
                $statusText = "{$onlineCount}/{$totalCount} آنلاین";

                $text .= "{$statusEmoji} **{$panelType}**: {$statusText}\n";

                if ($status['success']) {
                    $text .= "   ⏱️ پاسخ: " . ($status['response_time'] ?? 'N/A') . "ms\n";
                    $text .= "   👥 کاربران: " . ($status['users_count'] ?? 'N/A') . "\n";
                } else {
                    $text .= "   ❌ خطا: " . ($status['error'] ?? 'نامشخص') . "\n";
                }
                $text .= "\n";
            }

            $keyboard = $this->createInlineKeyboard([
                [
                    ['text' => '🔄 بروزرسانی', 'callback_data' => 'panel_status']
                ],
                [
                    ['text' => '🟢 Marzban', 'callback_data' => 'panel_marzban'],
                    ['text' => '🔵 Marzneshin', 'callback_data' => 'panel_marzneshin']
                ],
                [
                    ['text' => '🟡 X-UI', 'callback_data' => 'panel_xui']
                ],
                [
                    ['text' => '🔙 بازگشت', 'callback_data' => 'select_panel']
                ]
            ]);

            return $this->sendMessage($text, $keyboard);
        } catch (\Throwable $e) {
            return $this->handleError($e, 'خطا در نمایش وضعیت پنل‌ها.');
        }
    }

    /**
     * Sync panel data
     */
    public function syncPanel(array $callbackQuery): array
    {
        try {
            $this->initialize(['callback_query' => $callbackQuery]);

            if (!$this->isAdmin()) {
                return $this->answerCallback('شما دسترسی لازم را ندارید.');
            }

            $panelType = str_replace('sync_', '', $this->data);

            $this->answerCallback('در حال همگام‌سازی...');

            $result = $this->panelService->syncPanelData($panelType);

            if ($result['success']) {
                $text = "✅ **همگام‌سازی موفق**\n\n";
                $text .= "🔗 پنل: **{$panelType}**\n";
                $text .= "👥 کاربران همگام‌سازی شده: " . ($result['synced_users'] ?? 0) . "\n";
                $text .= "📊 سرویس‌های بروزرسانی شده: " . ($result['updated_services'] ?? 0) . "\n";
                $text .= "⏰ زمان: " . date('Y-m-d H:i:s');
            } else {
                $text = "❌ **خطا در همگام‌سازی**\n\n";
                $text .= "خطا: {$result['error']}";
            }

            $keyboard = $this->createInlineKeyboard([
                [
                    ['text' => '🔄 همگام‌سازی مجدد', 'callback_data' => "sync_{$panelType}"]
                ],
                [
                    ['text' => '🔙 بازگشت', 'callback_data' => "panel_{$panelType}"]
                ]
            ]);

            return $this->sendMessage($text, $keyboard);
        } catch (\Throwable $e) {
            return $this->handleError($e, 'خطا در همگام‌سازی.');
        }
    }

    /**
     * Format bytes to human readable
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= (1 << (10 * $pow));

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Format uptime to human readable
     */
    private function formatUptime(int $seconds): string
    {
        $days = floor($seconds / 86400);
        $hours = floor(($seconds % 86400) / 3600);
        $minutes = floor(($seconds % 3600) / 60);

        if ($days > 0) {
            return "{$days} روز، {$hours} ساعت";
        } elseif ($hours > 0) {
            return "{$hours} ساعت، {$minutes} دقیقه";
        } else {
            return "{$minutes} دقیقه";
        }
    }

    /**
     * Get server ID by panel type
     */
    private function getServerIdByPanelType(string $panelType): ?int
    {
        $sql = "SELECT id FROM `server_info` WHERE `panel_type` = ? AND `active` = 1 LIMIT 1";
        $result = $this->database->fetchRow($sql, [$panelType], 's');

        return $result ? (int) $result['id'] : null;
    }

    /**
     * Get all servers by panel type
     */
    private function getServersByPanelType(string $panelType): array
    {
        $sql = "SELECT * FROM `server_info` WHERE `panel_type` = ? AND `active` = 1";
        return $this->database->fetchAll($sql, [$panelType], 's');
    }
}
