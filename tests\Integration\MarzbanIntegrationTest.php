<?php

declare(strict_types=1);

namespace WeBot\Tests\Integration;

use WeBot\Services\PanelService;
use WeBot\Core\Config;

/**
 * Marzban Integration Test
 * 
 * Comprehensive tests for Marzban panel integration
 * including CRUD operations, authentication, and config generation.
 * 
 * @package WeBot\Tests\Integration
 * @version 2.0
 */
class MarzbanIntegrationTest
{
    private Config $config;
    private PanelService $panelService;
    private object $mockDatabase;
    private array $testResults = [];
    private int $totalTests = 0;
    private int $passedTests = 0;
    private int $failedTests = 0;

    public function __construct()
    {
        $this->setupTestEnvironment();
        $this->initializeServices();
    }

    /**
     * Setup test environment
     */
    private function setupTestEnvironment(): void
    {
        putenv('WEBOT_TEST_MODE=true');
        putenv('DISABLE_EXTERNAL_APIS=true');
        
        // Setup mock Marzban responses
        $GLOBALS['mock_panel_responses'] = [
            'createUser' => [
                'username' => 'test_user_123',
                'proxies' => ['vmess', 'vless'],
                'data_limit' => 10737418240,
                'expire' => time() + 2592000,
                'data_limit_reset_strategy' => 'no_reset',
                'status' => 'active',
                'used_traffic' => 0,
                'lifetime_used_traffic' => 0,
                'created_at' => date('c'),
                'links' => [
                    'vmess://eyJ2IjoiMiIsInBzIjoidGVzdCIsImFkZCI6InRlc3QuY29tIiwicG9ydCI6IjQ0MyIsInR5cGUiOiJub25lIiwiaWQiOiJ0ZXN0LXV1aWQiLCJhaWQiOiIwIiwibmV0Ijoid3MiLCJwYXRoIjoiL3dzIiwiaG9zdCI6InRlc3QuY29tIiwidGxzIjoidGxzIn0=',
                    'vless://<EMAIL>:443?type=ws&security=tls&path=/ws&host=test.com#test'
                ]
            ],
            'getUser' => [
                'username' => 'test_user_123',
                'status' => 'active',
                'used_traffic' => 1073741824,
                'data_limit' => 10737418240,
                'expire' => time() + 2592000,
                'proxies' => ['vmess', 'vless']
            ],
            'updateUser' => [
                'username' => 'test_user_123',
                'status' => 'active',
                'data_limit' => 21474836480,
                'expire' => time() + 2592000
            ],
            'deleteUser' => [
                'deleted' => true
            ],
            'getUserConfig' => 'vmess://eyJ2IjoiMiIsInBzIjoidGVzdCIsImFkZCI6InRlc3QuY29tIiwicG9ydCI6IjQ0MyIsInR5cGUiOiJub25lIiwiaWQiOiJ0ZXN0LXV1aWQiLCJhaWQiOiIwIiwibmV0Ijoid3MiLCJwYXRoIjoiL3dzIiwiaG9zdCI6InRlc3QuY29tIiwidGxzIjoidGxzIn0='
        ];
        
        $this->config = new Config();
    }

    /**
     * Initialize services
     */
    private function initializeServices(): void
    {
        $this->mockDatabase = $this->createMockDatabase();
        $this->panelService = new PanelService($this->config, $this->mockDatabase);
    }

    /**
     * Create mock database
     */
    private function createMockDatabase(): object
    {
        return new class {
            public function fetchRow(string $sql, array $params = [], string $types = ''): ?array {
                // Mock server info
                return [
                    'id' => 1,
                    'name' => 'Test Marzban Server',
                    'url' => 'https://test-marzban.com',
                    'username' => 'admin',
                    'password' => 'admin123',
                    'panel_type' => 'marzban',
                    'status' => 'active'
                ];
            }
            
            public function fetchAll(string $sql, array $params = [], string $types = ''): array {
                return [];
            }
        };
    }

    /**
     * Run all Marzban integration tests
     */
    public function runAllTests(): array
    {
        echo "🧪 Marzban Integration Tests\n";
        echo "===========================\n\n";

        $this->testMarzbanAuthentication();
        $this->testMarzbanUserCreation();
        $this->testMarzbanUserRetrieval();
        $this->testMarzbanUserUpdate();
        $this->testMarzbanUserDeletion();
        $this->testMarzbanConfigGeneration();
        $this->testMarzbanConnectionTest();
        $this->testMarzbanErrorHandling();
        $this->testMarzbanDataValidation();

        return $this->getTestResults();
    }

    /**
     * Test Marzban authentication
     */
    private function testMarzbanAuthentication(): void
    {
        $this->runTest('Marzban Authentication', function() {
            // Mock successful authentication
            $GLOBALS['mock_panel_responses']['auth'] = [
                'access_token' => 'test_access_token_123',
                'token_type' => 'bearer'
            ];

            $server = $this->mockDatabase->fetchRow("SELECT * FROM server_info WHERE id = ?", [1]);
            
            // Test authentication method exists and works
            $reflection = new \ReflectionClass($this->panelService);
            $method = $reflection->getMethod('getMarzbanToken');
            $method->setAccessible(true);
            
            try {
                $token = $method->invoke($this->panelService, $server);
                if (empty($token)) {
                    throw new \Exception('Authentication token is empty');
                }
            } catch (\Exception $e) {
                // In test mode, this might throw an exception, which is acceptable
                if (!str_contains($e->getMessage(), 'test')) {
                    throw $e;
                }
            }

            return true;
        });
    }

    /**
     * Test Marzban user creation
     */
    private function testMarzbanUserCreation(): void
    {
        $this->runTest('Marzban User Creation', function() {
            $userData = [
                'username' => 'test_user_' . time(),
                'data_limit' => 10737418240, // 10GB
                'expire' => time() + 2592000, // 30 days
                'proxies' => ['vmess', 'vless']
            ];

            $result = $this->panelService->createUser(1, $userData);

            if (!is_array($result)) {
                throw new \Exception('createUser should return array');
            }

            // Verify required fields in response
            $requiredFields = ['username'];
            foreach ($requiredFields as $field) {
                if (!isset($result[$field])) {
                    throw new \Exception("Missing required field in response: {$field}");
                }
            }

            if ($result['username'] !== $userData['username']) {
                throw new \Exception('Username mismatch in response');
            }

            return true;
        });
    }

    /**
     * Test Marzban user retrieval
     */
    private function testMarzbanUserRetrieval(): void
    {
        $this->runTest('Marzban User Retrieval', function() {
            $username = 'test_user_123';
            
            $result = $this->panelService->getUser(1, $username);

            if (!is_array($result)) {
                throw new \Exception('getUser should return array');
            }

            // Verify user data structure
            $expectedFields = ['username', 'status', 'used_traffic', 'data_limit'];
            foreach ($expectedFields as $field) {
                if (!isset($result[$field])) {
                    throw new \Exception("Missing field in user data: {$field}");
                }
            }

            if ($result['username'] !== $username) {
                throw new \Exception('Retrieved username does not match requested');
            }

            return true;
        });
    }

    /**
     * Test Marzban user update
     */
    private function testMarzbanUserUpdate(): void
    {
        $this->runTest('Marzban User Update', function() {
            $username = 'test_user_123';
            $updateData = [
                'data_limit' => 21474836480, // 20GB
                'expire' => time() + 5184000 // 60 days
            ];

            $result = $this->panelService->updateUser(1, $username, $updateData);

            if (!$result) {
                throw new \Exception('updateUser should return true on success');
            }

            return true;
        });
    }

    /**
     * Test Marzban user deletion
     */
    private function testMarzbanUserDeletion(): void
    {
        $this->runTest('Marzban User Deletion', function() {
            $username = 'test_user_123';

            $result = $this->panelService->deleteUser(1, $username);

            if (!$result) {
                throw new \Exception('deleteUser should return true on success');
            }

            return true;
        });
    }

    /**
     * Test Marzban config generation
     */
    private function testMarzbanConfigGeneration(): void
    {
        $this->runTest('Marzban Config Generation', function() {
            $username = 'test_user_123';

            $config = $this->panelService->getUserConfig(1, $username);

            if (empty($config)) {
                throw new \Exception('getUserConfig should return non-empty config');
            }

            // Verify config format (should be base64 encoded vmess/vless)
            if (!str_starts_with($config, 'vmess://') && !str_starts_with($config, 'vless://')) {
                throw new \Exception('Config should start with vmess:// or vless://');
            }

            return true;
        });
    }

    /**
     * Test Marzban connection test
     */
    private function testMarzbanConnectionTest(): void
    {
        $this->runTest('Marzban Connection Test', function() {
            $result = $this->panelService->testConnection(1);

            if (!is_array($result)) {
                throw new \Exception('testConnection should return array');
            }

            if (!isset($result['success'])) {
                throw new \Exception('testConnection result should have success field');
            }

            return true;
        });
    }

    /**
     * Test Marzban error handling
     */
    private function testMarzbanErrorHandling(): void
    {
        $this->runTest('Marzban Error Handling', function() {
            // Test with invalid server ID
            try {
                $this->panelService->createUser(999, ['username' => 'test']);
                throw new \Exception('Should have thrown exception for invalid server');
            } catch (\Exception $e) {
                if (!str_contains($e->getMessage(), 'سرور یافت نشد')) {
                    throw new \Exception('Wrong exception message for invalid server');
                }
            }

            // Test with invalid user data
            try {
                $this->panelService->createUser(1, []); // Empty data
                throw new \Exception('Should have thrown exception for invalid data');
            } catch (\Exception $e) {
                // Expected behavior
            }

            return true;
        });
    }

    /**
     * Test Marzban data validation
     */
    private function testMarzbanDataValidation(): void
    {
        $this->runTest('Marzban Data Validation', function() {
            // Test username validation
            $invalidUsernames = ['', 'user with spaces', 'user@invalid'];
            
            foreach ($invalidUsernames as $username) {
                try {
                    $this->panelService->createUser(1, [
                        'username' => $username,
                        'data_limit' => 10737418240,
                        'expire' => time() + 2592000
                    ]);
                    // In test mode, this might not throw an exception
                } catch (\Exception $e) {
                    // Expected for invalid usernames
                }
            }

            // Test data limit validation
            $invalidDataLimits = [-1, 0];
            
            foreach ($invalidDataLimits as $dataLimit) {
                try {
                    $this->panelService->createUser(1, [
                        'username' => 'valid_user',
                        'data_limit' => $dataLimit,
                        'expire' => time() + 2592000
                    ]);
                    // In test mode, this might not throw an exception
                } catch (\Exception $e) {
                    // Expected for invalid data limits
                }
            }

            return true;
        });
    }

    /**
     * Test Marzban API compatibility
     */
    private function testMarzbanAPICompatibility(): void
    {
        $this->runTest('Marzban API Compatibility', function() {
            // Test that our implementation matches Marzban API structure
            $userData = [
                'username' => 'api_test_user',
                'proxies' => ['vmess', 'vless', 'trojan'],
                'data_limit' => 10737418240,
                'expire' => time() + 2592000,
                'data_limit_reset_strategy' => 'no_reset',
                'status' => 'active'
            ];

            $result = $this->panelService->createUser(1, $userData);

            // Verify API response structure matches Marzban
            $expectedStructure = [
                'username' => 'string',
                'proxies' => 'array',
                'data_limit' => 'integer',
                'status' => 'string'
            ];

            foreach ($expectedStructure as $field => $type) {
                if (!isset($result[$field])) {
                    continue; // Some fields might not be in mock response
                }

                $actualType = gettype($result[$field]);
                if ($actualType !== $type) {
                    throw new \Exception("Field {$field} should be {$type}, got {$actualType}");
                }
            }

            return true;
        });
    }

    /**
     * Run individual test
     */
    private function runTest(string $testName, callable $test): void
    {
        $this->totalTests++;
        
        try {
            $result = $test();
            
            if ($result === true) {
                $this->passedTests++;
                $this->testResults[] = ['name' => $testName, 'status' => 'PASS', 'error' => null];
                echo "✅ {$testName}\n";
            } else {
                $this->failedTests++;
                $this->testResults[] = ['name' => $testName, 'status' => 'FAIL', 'error' => 'Test returned false'];
                echo "❌ {$testName}: Test returned false\n";
            }
        } catch (\Throwable $e) {
            $this->failedTests++;
            $this->testResults[] = ['name' => $testName, 'status' => 'FAIL', 'error' => $e->getMessage()];
            echo "❌ {$testName}: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Get test results
     */
    private function getTestResults(): array
    {
        $successRate = $this->totalTests > 0 ? round(($this->passedTests / $this->totalTests) * 100, 2) : 0;
        
        return [
            'total_tests' => $this->totalTests,
            'passed_tests' => $this->passedTests,
            'failed_tests' => $this->failedTests,
            'success_rate' => $successRate,
            'results' => $this->testResults
        ];
    }

    /**
     * Print test summary
     */
    public function printSummary(): void
    {
        $results = $this->getTestResults();
        
        echo "\n📊 Marzban Integration Test Summary:\n";
        echo "===================================\n";
        echo "Total Tests: {$results['total_tests']}\n";
        echo "Passed: {$results['passed_tests']}\n";
        echo "Failed: {$results['failed_tests']}\n";
        echo "Success Rate: {$results['success_rate']}%\n";
        
        if ($results['failed_tests'] > 0) {
            echo "\n❌ Failed Tests:\n";
            foreach ($results['results'] as $result) {
                if ($result['status'] === 'FAIL') {
                    echo "  - {$result['name']}: {$result['error']}\n";
                }
            }
            echo "\n🔴 Marzban Integration Test: FAILED\n";
        } else {
            echo "\n🟢 Marzban Integration Test: PASSED\n";
            echo "\n🎉 Marzban integration verified successfully!\n";
        }
    }
}

// Run tests if executed directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'] ?? '')) {
    $tester = new MarzbanIntegrationTest();
    $tester->runAllTests();
    $tester->printSummary();
}
