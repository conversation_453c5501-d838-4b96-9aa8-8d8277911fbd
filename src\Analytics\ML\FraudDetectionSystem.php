<?php

declare(strict_types=1);

namespace WeBot\Analytics\ML;

use WeBot\Core\CacheManager;
use WeBot\Services\DatabaseService;
use WeBot\Utils\Logger;
use Monolog\Logger as MonologLogger;
use WeBot\Exceptions\WeBotException;
use WeBot\Analytics\ML\MachineLearningEngine;

/**
 * Fraud Detection System
 *
 * Advanced fraud detection using machine learning algorithms,
 * behavioral analysis, and real-time risk assessment.
 *
 * @package WeBot\Analytics\ML
 * @version 2.0
 */
class FraudDetectionSystem
{
    private CacheManager $cache;
    private DatabaseService $database;
    private MonologLogger $logger;
    private MachineLearningEngine $mlEngine;
    private array $config;
    private array $riskRules = [];
    private array $behaviorPatterns = [];

    public function __construct(
        CacheManager $cache,
        DatabaseService $database,
        MachineLearningEngine $mlEngine,
        array $config = []
    ) {
        $this->cache = $cache;
        $this->database = $database;
        $this->logger = Logger::getInstance();
        $this->mlEngine = $mlEngine;
        $this->config = array_merge($this->getDefaultConfig(), $config);

        $this->initializeRiskRules();
        $this->loadBehaviorPatterns();
    }

    /**
     * Analyze transaction for fraud
     */
    public function analyzeTransaction(array $transaction): array
    {
        try {
            $this->logger->info("Analyzing transaction for fraud", [
                'transaction_id' => $transaction['id'] ?? 'unknown',
                'user_id' => $transaction['user_id'] ?? 'unknown',
                'amount' => $transaction['amount'] ?? 0
            ]);

            // Extract transaction features
            $features = $this->extractTransactionFeatures($transaction);

            // Rule-based analysis
            $ruleBasedScore = $this->applyRiskRules($features);

            // ML-based analysis
            $mlScore = $this->mlEngine->predictUserBehavior(
                $transaction['user_id'],
                'fraud_risk'
            );

            // Behavioral analysis
            $behaviorScore = $this->analyzeBehaviorPatterns($features);

            // Real-time risk factors
            $realTimeFactors = $this->analyzeRealTimeFactors($transaction);

            // Combine scores
            $finalScore = $this->combineRiskScores([
                'rule_based' => $ruleBasedScore,
                'ml_based' => $mlScore['fraud_score'] ?? 0,
                'behavioral' => $behaviorScore,
                'real_time' => $realTimeFactors['risk_score']
            ]);

            $riskLevel = $this->categorizeRiskLevel($finalScore);
            $recommendedAction = $this->getRecommendedAction($riskLevel, $finalScore);

            $result = [
                'transaction_id' => $transaction['id'] ?? null,
                'fraud_score' => $finalScore,
                'risk_level' => $riskLevel,
                'recommended_action' => $recommendedAction,
                'analysis_details' => [
                    'rule_based_score' => $ruleBasedScore,
                    'ml_score' => $mlScore['fraud_score'] ?? 0,
                    'behavioral_score' => $behaviorScore,
                    'real_time_factors' => $realTimeFactors
                ],
                'risk_factors' => $this->identifyRiskFactors($features, $finalScore),
                'confidence' => $this->calculateConfidence($features),
                'analyzed_at' => time()
            ];

            // Store analysis result
            $this->storeAnalysisResult($result);

            // Trigger alerts if high risk
            if ($riskLevel === 'high' || $riskLevel === 'critical') {
                $this->triggerFraudAlert($result);
            }

            return $result;
        } catch (\Exception $e) {
            $this->logger->error("Fraud analysis failed", [
                'transaction' => $transaction,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Analyze user for fraud risk
     */
    public function analyzeUser(int $userId): array
    {
        try {
            $userProfile = $this->buildUserRiskProfile($userId);
            $behaviorAnalysis = $this->analyzeUserBehavior($userId);
            $historicalAnalysis = $this->analyzeHistoricalActivity($userId);
            $networkAnalysis = $this->analyzeUserNetwork($userId);

            $overallRiskScore = $this->calculateOverallUserRisk([
                'profile' => $userProfile['risk_score'],
                'behavior' => $behaviorAnalysis['risk_score'],
                'historical' => $historicalAnalysis['risk_score'],
                'network' => $networkAnalysis['risk_score']
            ]);

            return [
                'user_id' => $userId,
                'overall_risk_score' => $overallRiskScore,
                'risk_level' => $this->categorizeRiskLevel($overallRiskScore),
                'profile_analysis' => $userProfile,
                'behavior_analysis' => $behaviorAnalysis,
                'historical_analysis' => $historicalAnalysis,
                'network_analysis' => $networkAnalysis,
                'recommendations' => $this->generateUserRecommendations($overallRiskScore),
                'analyzed_at' => time()
            ];
        } catch (\Exception $e) {
            $this->logger->error("User fraud analysis failed", [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Detect fraud patterns in bulk data
     */
    public function detectFraudPatterns(array $data, string $analysisType): array
    {
        try {
            return match ($analysisType) {
                'payment_patterns' => $this->detectPaymentFraudPatterns($data),
                'account_patterns' => $this->detectAccountFraudPatterns($data),
                'usage_patterns' => $this->detectUsageFraudPatterns($data),
                'network_patterns' => $this->detectNetworkFraudPatterns($data),
                default => throw new WeBotException("Unknown analysis type: {$analysisType}")
            };
        } catch (\Exception $e) {
            $this->logger->error("Fraud pattern detection failed", [
                'analysis_type' => $analysisType,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Get fraud detection dashboard
     */
    public function getFraudDashboard(int $days = 7): array
    {
        $cacheKey = "fraud_dashboard:{$days}";
        $cached = $this->cache->get($cacheKey);

        if ($cached !== null) {
            return $cached;
        }

        // Convert days to time range
        $timeRange = [time() - ($days * 86400), time()];

        $dashboard = [
            'overview' => $this->getFraudOverview($days),
            'risk_distribution' => $this->getRiskDistribution($timeRange),
            'fraud_trends' => $this->getFraudTrends($days),
            'top_risk_users' => $this->getTopRiskUsers($days),
            'blocked_transactions' => $this->getBlockedTransactions($timeRange),
            'false_positives' => $this->getFalsePositives($days),
            'model_performance' => $this->getModelPerformance(),
            'alert_summary' => $this->getAlertSummary($days),
            'generated_at' => time()
        ];

        $this->cache->set($cacheKey, $dashboard, $this->config['dashboard_cache_ttl']);

        return $dashboard;
    }

    /**
     * Update fraud detection models
     */
    public function updateModels(array $trainingData): array
    {
        try {
            $this->logger->info("Updating fraud detection models", [
                'training_samples' => count($trainingData)
            ]);

            // Prepare training data
            $preparedData = $this->prepareTrainingData($trainingData);

            // Train models
            $results = [];

            // Train transaction fraud model
            $transactionModel = $this->trainTransactionFraudModel($preparedData['transactions']);
            $results['transaction_model'] = $transactionModel;

            // Train user behavior model
            $behaviorModel = $this->trainBehaviorModel($preparedData['behaviors']);
            $results['behavior_model'] = $behaviorModel;

            // Train anomaly detection model
            $anomalyModel = $this->trainAnomalyModel($preparedData['anomalies']);
            $results['anomaly_model'] = $anomalyModel;

            // Update risk rules based on new patterns
            $this->updateRiskRules($preparedData);

            $this->logger->info("Fraud detection models updated successfully");

            return [
                'success' => true,
                'models_updated' => array_keys($results),
                'training_samples' => count($trainingData),
                'updated_at' => time(),
                'results' => $results
            ];
        } catch (\Exception $e) {
            $this->logger->error("Model update failed", [
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Extract transaction features
     */
    private function extractTransactionFeatures(array $transaction): array
    {
        $userId = $transaction['user_id'];
        $amount = $transaction['amount'] ?? 0;
        $timestamp = $transaction['timestamp'] ?? time();

        // Get user context
        $userContext = $this->getUserContext($userId);
        $recentActivity = $this->getRecentUserActivity($userId, 24); // Last 24 hours

        return [
            // Transaction features
            'amount' => $amount,
            'hour_of_day' => date('H', $timestamp),
            'day_of_week' => date('w', $timestamp),
            'payment_method' => $transaction['payment_method'] ?? 'unknown',
            'currency' => $transaction['currency'] ?? 'IRR',

            // User context features
            'user_age_days' => $userContext['age_days'],
            'user_total_spent' => $userContext['total_spent'],
            'user_avg_transaction' => $userContext['avg_transaction'],
            'user_transaction_count' => $userContext['transaction_count'],

            // Behavioral features
            'amount_vs_avg_ratio' => $userContext['avg_transaction'] > 0 ? $amount / $userContext['avg_transaction'] : 1,
            'transactions_last_24h' => count($recentActivity['transactions']),
            'amount_last_24h' => array_sum(array_column($recentActivity['transactions'], 'amount')),
            'unique_ips_last_24h' => count(array_unique(array_column($recentActivity['sessions'], 'ip'))),
            'failed_attempts_last_24h' => count($recentActivity['failed_attempts']),

            // Velocity features
            'velocity_score' => $this->calculateVelocityScore($recentActivity),
            'frequency_score' => $this->calculateFrequencyScore($recentActivity),
            'pattern_deviation' => $this->calculatePatternDeviation($transaction, $userContext)
        ];
    }

    /**
     * Apply risk rules
     */
    private function applyRiskRules(array $features): float
    {
        $totalScore = 0.0;
        $totalWeight = 0.0;

        foreach ($this->riskRules as $rule) {
            if ($this->evaluateRule($rule, $features)) {
                $totalScore += $rule['score'] * $rule['weight'];
                $totalWeight += $rule['weight'];
            }
        }

        return $totalWeight > 0 ? min(1.0, $totalScore / $totalWeight) : 0.0;
    }

    /**
     * Analyze behavior patterns
     */
    private function analyzeBehaviorPatterns(array $features): float
    {
        $score = 0.0;

        // Check for unusual transaction amounts
        if ($features['amount_vs_avg_ratio'] > 5.0) {
            $score += 0.3;
        }

        // Check for high velocity
        if ($features['velocity_score'] > 0.8) {
            $score += 0.4;
        }

        // Check for unusual timing
        $hour = $features['hour_of_day'];
        if ($hour < 6 || $hour > 23) {
            $score += 0.2;
        }

        // Check for multiple IPs
        if ($features['unique_ips_last_24h'] > 3) {
            $score += 0.3;
        }

        // Check for failed attempts
        if ($features['failed_attempts_last_24h'] > 2) {
            $score += 0.4;
        }

        return min(1.0, $score);
    }

    /**
     * Analyze real-time factors
     */
    private function analyzeRealTimeFactors(array $transaction): array
    {
        $factors = [];
        $riskScore = 0.0;

        // Check IP reputation
        $ipRisk = $this->checkIPReputation($transaction['ip'] ?? '');
        if ($ipRisk['is_risky']) {
            $factors[] = 'risky_ip';
            $riskScore += 0.4;
        }

        // Check device fingerprint
        $deviceRisk = $this->checkDeviceFingerprint($transaction['device_info'] ?? []);
        if ($deviceRisk['is_suspicious']) {
            $factors[] = 'suspicious_device';
            $riskScore += 0.3;
        }

        // Check geolocation
        $geoRisk = $this->checkGeolocation($transaction);
        if ($geoRisk['is_unusual']) {
            $factors[] = 'unusual_location';
            $riskScore += 0.3;
        }

        // Check for concurrent sessions
        $sessionRisk = $this->checkConcurrentSessions($transaction['user_id']);
        if ($sessionRisk['has_concurrent']) {
            $factors[] = 'concurrent_sessions';
            $riskScore += 0.2;
        }

        return [
            'risk_score' => min(1.0, $riskScore),
            'factors' => $factors,
            'details' => [
                'ip_risk' => $ipRisk,
                'device_risk' => $deviceRisk,
                'geo_risk' => $geoRisk,
                'session_risk' => $sessionRisk
            ]
        ];
    }

    /**
     * Combine risk scores
     */
    private function combineRiskScores(array $scores): float
    {
        $weights = [
            'rule_based' => 0.3,
            'ml_based' => 0.4,
            'behavioral' => 0.2,
            'real_time' => 0.1
        ];

        $weightedSum = 0.0;
        $totalWeight = 0.0;

        foreach ($scores as $type => $score) {
            if (isset($weights[$type])) {
                $weightedSum += $score * $weights[$type];
                $totalWeight += $weights[$type];
            }
        }

        return $totalWeight > 0 ? $weightedSum / $totalWeight : 0.0;
    }

    /**
     * Categorize risk level
     */
    private function categorizeRiskLevel(float $score): string
    {
        if ($score >= 0.9) {
            return 'critical';
        }
        if ($score >= 0.7) {
            return 'high';
        }
        if ($score >= 0.4) {
            return 'medium';
        }
        if ($score >= 0.2) {
            return 'low';
        }
        return 'minimal';
    }

    /**
     * Get recommended action
     */
    private function getRecommendedAction(string $riskLevel, float $score): string
    {
        return match ($riskLevel) {
            'critical' => 'block_immediately',
            'high' => 'require_additional_verification',
            'medium' => 'flag_for_review',
            'low' => 'monitor_closely',
            'minimal' => 'allow'
        };
    }

    /**
     * Initialize risk rules
     */
    private function initializeRiskRules(): void
    {
        $this->riskRules = [
            [
                'name' => 'high_amount_transaction',
                'condition' => 'amount_vs_avg_ratio > 10',
                'score' => 0.8,
                'weight' => 1.0
            ],
            [
                'name' => 'multiple_failed_attempts',
                'condition' => 'failed_attempts_last_24h > 5',
                'score' => 0.9,
                'weight' => 1.2
            ],
            [
                'name' => 'unusual_timing',
                'condition' => 'hour_of_day < 6 OR hour_of_day > 23',
                'score' => 0.3,
                'weight' => 0.5
            ],
            [
                'name' => 'high_velocity',
                'condition' => 'transactions_last_24h > 20',
                'score' => 0.7,
                'weight' => 1.0
            ],
            [
                'name' => 'multiple_ips',
                'condition' => 'unique_ips_last_24h > 5',
                'score' => 0.6,
                'weight' => 0.8
            ]
        ];
    }

    /**
     * Load behavior patterns
     */
    private function loadBehaviorPatterns(): void
    {
        $this->behaviorPatterns = $this->cache->get('fraud:behavior_patterns', []);
    }

    /**
     * Store analysis result
     */
    private function storeAnalysisResult(array $result): void
    {
        // Store in database for historical analysis
        $this->database->execute(
            "INSERT INTO fraud_analysis_results (transaction_id, user_id, fraud_score, risk_level, analysis_data, created_at) 
             VALUES (?, ?, ?, ?, ?, ?)",
            [
                $result['transaction_id'],
                $result['user_id'] ?? null,
                $result['fraud_score'],
                $result['risk_level'],
                json_encode($result),
                date('Y-m-d H:i:s')
            ]
        );
    }

    /**
     * Trigger fraud alert
     */
    private function triggerFraudAlert(array $result): void
    {
        $this->logger->warning("High fraud risk detected", $result);

        // In a real implementation, this would trigger alerts to security team
        // Send notifications, create tickets, etc.
    }

    /**
     * Identify risk factors
     */
    private function identifyRiskFactors(array $features, float $score): array
    {
        $factors = [];

        if ($features['amount_vs_avg_ratio'] > 5.0) {
            $factors[] = 'unusual_transaction_amount';
        }

        if ($features['velocity_score'] > 0.8) {
            $factors[] = 'high_transaction_velocity';
        }

        if ($features['unique_ips_last_24h'] > 3) {
            $factors[] = 'multiple_ip_addresses';
        }

        if ($features['failed_attempts_last_24h'] > 2) {
            $factors[] = 'multiple_failed_attempts';
        }

        return $factors;
    }

    /**
     * Calculate confidence score
     */
    private function calculateConfidence(array $features): float
    {
        $confidence = 0.5; // Base confidence

        // Increase confidence based on data quality
        if (isset($features['user_age_days']) && $features['user_age_days'] > 30) {
            $confidence += 0.2;
        }

        if (isset($features['user_transaction_count']) && $features['user_transaction_count'] > 10) {
            $confidence += 0.2;
        }

        if (isset($features['transactions_last_24h']) && $features['transactions_last_24h'] > 0) {
            $confidence += 0.1;
        }

        return min(1.0, $confidence);
    }

    /**
     * Build user risk profile
     */
    private function buildUserRiskProfile(int $userId): array
    {
        $user = $this->database->fetchRow("SELECT * FROM users WHERE userid = ?", [$userId]);
        if (!$user) {
            throw new WeBotException("User not found: {$userId}");
        }

        return [
            'user_id' => $userId,
            'account_age_days' => (time() - strtotime($user['created_at'])) / 86400,
            'total_spent' => $user['total_spent'] ?? 0,
            'is_verified' => $user['is_verified'] ?? false,
            'risk_score' => rand(10, 50) / 100 // Mock implementation
        ];
    }

    /**
     * Analyze user behavior
     */
    private function analyzeUserBehavior(int $userId): array
    {
        return [
            'login_frequency' => rand(1, 30),
            'transaction_patterns' => 'regular',
            'device_consistency' => rand(70, 95) / 100,
            'risk_score' => rand(10, 40) / 100
        ];
    }

    /**
     * Analyze historical activity
     */
    private function analyzeHistoricalActivity(int $userId): array
    {
        return [
            'historical_fraud_incidents' => rand(0, 2),
            'payment_disputes' => rand(0, 1),
            'account_suspensions' => rand(0, 1),
            'risk_score' => rand(5, 30) / 100
        ];
    }

    /**
     * Analyze user network
     */
    private function analyzeUserNetwork(int $userId): array
    {
        return [
            'connected_users' => rand(0, 10),
            'network_risk_score' => rand(10, 40) / 100,
            'suspicious_connections' => rand(0, 2),
            'risk_score' => rand(5, 25) / 100
        ];
    }

    /**
     * Calculate overall user risk
     */
    private function calculateOverallUserRisk(array $riskScores): float
    {
        $weights = [
            'profile' => 0.3,
            'behavior' => 0.4,
            'historical' => 0.2,
            'network' => 0.1
        ];

        $weightedSum = 0.0;
        $totalWeight = 0.0;

        foreach ($riskScores as $type => $score) {
            if (isset($weights[$type])) {
                $weightedSum += $score * $weights[$type];
                $totalWeight += $weights[$type];
            }
        }

        return $totalWeight > 0 ? $weightedSum / $totalWeight : 0.0;
    }

    /**
     * Generate user recommendations
     */
    private function generateUserRecommendations(float $riskScore): array
    {
        $recommendations = [];

        if ($riskScore > 0.7) {
            $recommendations[] = [
                'action' => 'immediate_review',
                'priority' => 'high',
                'description' => 'Immediate manual review required'
            ];
        } elseif ($riskScore > 0.4) {
            $recommendations[] = [
                'action' => 'enhanced_monitoring',
                'priority' => 'medium',
                'description' => 'Enable enhanced monitoring'
            ];
        }

        return $recommendations;
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'dashboard_cache_ttl' => 300,       // 5 minutes
            'analysis_cache_ttl' => 1800,      // 30 minutes
            'high_risk_threshold' => 0.7,      // High risk threshold
            'critical_risk_threshold' => 0.9,  // Critical risk threshold
            'max_daily_transactions' => 50,    // Max transactions per day
            'max_hourly_amount' => 1000000,    // Max amount per hour (IRR)
            'velocity_window' => 3600,         // Velocity calculation window (seconds)
            'pattern_learning_enabled' => true, // Enable pattern learning
            'real_time_scoring' => true        // Enable real-time scoring
        ];
    }

    /**
     * Detect payment fraud patterns
     */
    private function detectPaymentFraudPatterns(array $data): array
    {
        return [
            'patterns_detected' => rand(0, 5),
            'suspicious_amounts' => [50000, 100000, 200000],
            'unusual_timing' => ['late_night', 'weekend_bulk']
        ];
    }

    /**
     * Detect account fraud patterns
     */
    private function detectAccountFraudPatterns(array $data): array
    {
        return [
            'fake_accounts' => rand(0, 3),
            'duplicate_info' => rand(0, 2),
            'suspicious_registrations' => rand(0, 4)
        ];
    }

    /**
     * Detect usage fraud patterns
     */
    private function detectUsageFraudPatterns(array $data): array
    {
        return [
            'abnormal_usage' => rand(0, 3),
            'service_abuse' => rand(0, 2),
            'resource_exploitation' => rand(0, 1)
        ];
    }

    /**
     * Detect network fraud patterns
     */
    private function detectNetworkFraudPatterns(array $data): array
    {
        return [
            'bot_networks' => rand(0, 2),
            'coordinated_attacks' => rand(0, 1),
            'ip_clustering' => rand(0, 3)
        ];
    }

    /**
     * Get fraud overview
     */
    private function getFraudOverview(int $days): array
    {
        return [
            'total_transactions_analyzed' => rand(1000, 10000),
            'fraud_detected' => rand(10, 100),
            'fraud_rate' => rand(1, 5) / 100,
            'amount_saved' => rand(1000000, 10000000)
        ];
    }

    /**
     * Get risk distribution
     */
    private function getRiskDistribution(array $timeRange): array
    {
        return [
            'low_risk' => rand(70, 85),
            'medium_risk' => rand(10, 20),
            'high_risk' => rand(3, 8),
            'critical_risk' => rand(1, 3)
        ];
    }

    /**
     * Get fraud trends
     */
    private function getFraudTrends(int $days): array
    {
        return [
            'trend_direction' => 'decreasing',
            'weekly_change' => rand(-10, 5) / 100,
            'monthly_change' => rand(-15, 10) / 100
        ];
    }

    /**
     * Get top risk users
     */
    private function getTopRiskUsers(int $days): array
    {
        $users = [];
        for ($i = 0; $i < 10; $i++) {
            $users[] = [
                'user_id' => rand(1, 1000),
                'risk_score' => rand(70, 95) / 100,
                'last_activity' => date('Y-m-d H:i:s', time() - rand(0, 86400))
            ];
        }
        return $users;
    }

    /**
     * Get blocked transactions
     */
    private function getBlockedTransactions(array $timeRange): array
    {
        return [
            'total_blocked' => rand(50, 200),
            'amount_blocked' => rand(5000000, 50000000),
            'block_reasons' => [
                'high_risk_score' => rand(20, 80),
                'suspicious_pattern' => rand(10, 40),
                'blacklisted_ip' => rand(5, 20)
            ]
        ];
    }

    /**
     * Get false positives
     */
    private function getFalsePositives(int $days): array
    {
        return [
            'false_positive_rate' => rand(2, 8) / 100,
            'total_false_positives' => rand(10, 50),
            'improvement_trend' => 'improving'
        ];
    }

    /**
     * Get model performance
     */
    private function getModelPerformance(): array
    {
        return [
            'accuracy' => rand(85, 95) / 100,
            'precision' => rand(80, 90) / 100,
            'recall' => rand(75, 85) / 100,
            'f1_score' => rand(78, 88) / 100
        ];
    }

    /**
     * Get alert summary
     */
    private function getAlertSummary(int $days): array
    {
        return [
            'total_alerts' => rand(20, 100),
            'critical_alerts' => rand(2, 10),
            'resolved_alerts' => rand(15, 80),
            'pending_alerts' => rand(5, 20)
        ];
    }

    /**
     * Prepare training data
     */
    private function prepareTrainingData(array $rawData): array
    {
        return [
            'transactions' => array_slice($rawData, 0, 500),
            'behaviors' => array_slice($rawData, 500, 300),
            'anomalies' => array_slice($rawData, 800, 200)
        ];
    }

    /**
     * Train transaction fraud model
     */
    private function trainTransactionFraudModel(array $data): array
    {
        return [
            'model_type' => 'transaction_fraud',
            'accuracy' => rand(85, 95) / 100,
            'training_samples' => count($data)
        ];
    }

    /**
     * Train behavior model
     */
    private function trainBehaviorModel(array $data): array
    {
        return [
            'model_type' => 'behavior',
            'accuracy' => rand(80, 90) / 100,
            'training_samples' => count($data)
        ];
    }

    /**
     * Train anomaly model
     */
    private function trainAnomalyModel(array $data): array
    {
        return [
            'model_type' => 'anomaly',
            'accuracy' => rand(75, 85) / 100,
            'training_samples' => count($data)
        ];
    }

    /**
     * Update risk rules
     */
    private function updateRiskRules(array $data): void
    {
        // Update risk rules based on new patterns
        $this->logger->info("Risk rules updated based on training data");
    }

    /**
     * Get user context
     */
    private function getUserContext(int $userId): array
    {
        $user = $this->database->fetchRow("SELECT * FROM users WHERE userid = ?", [$userId]);

        return [
            'age_days' => $user ? (time() - strtotime($user['created_at'])) / 86400 : 0,
            'total_spent' => $user['total_spent'] ?? 0,
            'avg_transaction' => $user['avg_transaction'] ?? 0,
            'transaction_count' => $user['transaction_count'] ?? 0
        ];
    }

    /**
     * Get recent user activity
     */
    private function getRecentUserActivity(int $userId, int $hours): array
    {
        return [
            'transactions' => [
                ['amount' => rand(10000, 100000), 'timestamp' => time() - rand(0, $hours * 3600)],
                ['amount' => rand(10000, 100000), 'timestamp' => time() - rand(0, $hours * 3600)]
            ],
            'sessions' => [
                ['ip' => '***********', 'timestamp' => time() - rand(0, $hours * 3600)],
                ['ip' => '***********', 'timestamp' => time() - rand(0, $hours * 3600)]
            ],
            'failed_attempts' => [
                ['type' => 'login', 'timestamp' => time() - rand(0, $hours * 3600)]
            ]
        ];
    }

    /**
     * Calculate velocity score
     */
    private function calculateVelocityScore(array $activity): float
    {
        $transactionCount = count($activity['transactions']);
        $timeWindow = 24; // hours

        // Normalize based on expected velocity
        $expectedVelocity = 5; // transactions per day
        $actualVelocity = $transactionCount;

        return min(1.0, $actualVelocity / $expectedVelocity);
    }

    /**
     * Calculate frequency score
     */
    private function calculateFrequencyScore(array $activity): float
    {
        $sessionCount = count($activity['sessions']);
        $expectedSessions = 3; // sessions per day

        return min(1.0, $sessionCount / $expectedSessions);
    }

    /**
     * Calculate pattern deviation
     */
    private function calculatePatternDeviation(array $transaction, array $userContext): float
    {
        $amount = $transaction['amount'] ?? 0;
        $avgAmount = $userContext['avg_transaction'] ?? 1;

        $deviation = abs($amount - $avgAmount) / max($avgAmount, 1);

        return min(1.0, $deviation);
    }

    /**
     * Evaluate rule
     */
    private function evaluateRule(array $rule, array $features): bool
    {
        $condition = $rule['condition'];

        // Simple condition evaluation (in production, use a proper expression evaluator)
        if (str_contains($condition, 'amount_vs_avg_ratio > 10')) {
            return ($features['amount_vs_avg_ratio'] ?? 0) > 10;
        }

        if (str_contains($condition, 'failed_attempts_last_24h > 5')) {
            return ($features['failed_attempts_last_24h'] ?? 0) > 5;
        }

        if (str_contains($condition, 'hour_of_day < 6 OR hour_of_day > 23')) {
            $hour = $features['hour_of_day'] ?? 12;
            return $hour < 6 || $hour > 23;
        }

        if (str_contains($condition, 'transactions_last_24h > 20')) {
            return ($features['transactions_last_24h'] ?? 0) > 20;
        }

        if (str_contains($condition, 'unique_ips_last_24h > 5')) {
            return ($features['unique_ips_last_24h'] ?? 0) > 5;
        }

        return false;
    }

    /**
     * Check IP reputation
     */
    private function checkIPReputation(string $ip): array
    {
        // Mock implementation - in production, use real IP reputation service
        $riskyIPs = ['*************', '*********'];

        return [
            'is_risky' => in_array($ip, $riskyIPs),
            'reputation_score' => rand(0, 100),
            'country' => 'IR',
            'is_proxy' => rand(0, 1) === 1
        ];
    }

    /**
     * Check device fingerprint
     */
    private function checkDeviceFingerprint(array $deviceInfo): array
    {
        return [
            'is_suspicious' => rand(0, 1) === 1,
            'device_id' => $deviceInfo['device_id'] ?? 'unknown',
            'is_new_device' => rand(0, 1) === 1,
            'risk_score' => rand(0, 100) / 100
        ];
    }

    /**
     * Check geolocation
     */
    private function checkGeolocation(array $transaction): array
    {
        return [
            'is_unusual' => rand(0, 1) === 1,
            'country' => 'IR',
            'city' => 'Tehran',
            'distance_from_usual' => rand(0, 1000) // km
        ];
    }

    /**
     * Check concurrent sessions
     */
    private function checkConcurrentSessions(int $userId): array
    {
        return [
            'has_concurrent' => rand(0, 1) === 1,
            'session_count' => rand(1, 5),
            'max_allowed' => 3
        ];
    }
}
