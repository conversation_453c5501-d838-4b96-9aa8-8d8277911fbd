<?php

declare(strict_types=1);

namespace WeBot\Exceptions;

/**
 * Error Recovery Exception
 *
 * Exception that includes recovery mechanisms and suggestions
 * for handling recoverable errors.
 *
 * @package WeBot\Exceptions
 * @version 2.0
 */
class ErrorRecoveryException extends WeBotException
{
    private array $recoveryActions = [];
    private bool $isRecoverable = true;
    private int $retryCount = 0;
    private int $maxRetries = 3;
    private array $recoveryContext = [];

    public function __construct(
        string $message = '',
        int $code = 0,
        ?\Throwable $previous = null,
        array $context = [],
        array $recoveryActions = [],
        bool $isRecoverable = true
    ) {
        parent::__construct($message, $code, $previous, $context);

        $this->recoveryActions = $recoveryActions;
        $this->isRecoverable = $isRecoverable;
    }

    /**
     * Get recovery actions
     */
    public function getRecoveryActions(): array
    {
        return $this->recoveryActions;
    }

    /**
     * Add recovery action
     */
    public function addRecoveryAction(string $action, callable $callback, array $params = []): self
    {
        $this->recoveryActions[] = [
            'action' => $action,
            'callback' => $callback,
            'params' => $params,
            'priority' => count($this->recoveryActions)
        ];

        return $this;
    }

    /**
     * Check if error is recoverable
     */
    public function isRecoverable(): bool
    {
        return $this->isRecoverable && $this->retryCount < $this->maxRetries;
    }

    /**
     * Set recoverable status
     */
    public function setRecoverable(bool $recoverable): self
    {
        $this->isRecoverable = $recoverable;
        return $this;
    }

    /**
     * Get retry count
     */
    public function getRetryCount(): int
    {
        return $this->retryCount;
    }

    /**
     * Increment retry count
     */
    public function incrementRetryCount(): self
    {
        $this->retryCount++;
        return $this;
    }

    /**
     * Get max retries
     */
    public function getMaxRetries(): int
    {
        return $this->maxRetries;
    }

    /**
     * Set max retries
     */
    public function setMaxRetries(int $maxRetries): self
    {
        $this->maxRetries = $maxRetries;
        return $this;
    }

    /**
     * Get recovery context
     */
    public function getRecoveryContext(): array
    {
        return $this->recoveryContext;
    }

    /**
     * Set recovery context
     */
    public function setRecoveryContext(array $context): self
    {
        $this->recoveryContext = $context;
        return $this;
    }

    /**
     * Execute recovery actions
     */
    public function executeRecovery(): bool
    {
        if (!$this->isRecoverable()) {
            return false;
        }

        // Sort recovery actions by priority
        usort($this->recoveryActions, fn($a, $b) => $a['priority'] <=> $b['priority']);

        foreach ($this->recoveryActions as $action) {
            try {
                $result = call_user_func($action['callback'], $action['params'], $this);

                if ($result === true) {
                    // Recovery successful
                    return true;
                }
            } catch (\Throwable $e) {
                // Recovery action failed, continue to next
                continue;
            }
        }

        return false;
    }

    /**
     * Create database connection recovery exception
     */
    public static function databaseConnectionFailed(string $host, int $port, ?\Throwable $previous = null): self
    {
        $exception = new self(
            "Database connection failed to {$host}:{$port}",
            5001,
            $previous,
            ['host' => $host, 'port' => $port]
        );

        $exception->addRecoveryAction('retry_connection', function ($params, $exception) {
            // Retry database connection
            sleep(1); // Wait before retry
            return false; // Placeholder - would implement actual retry logic
        });

        $exception->addRecoveryAction('use_backup_database', function ($params, $exception) {
            // Switch to backup database
            return false; // Placeholder - would implement backup database logic
        });

        return $exception;
    }

    /**
     * Create API service recovery exception
     */
    public static function apiServiceUnavailable(string $service, string $endpoint, ?\Throwable $previous = null): self
    {
        $exception = new self(
            "API service '{$service}' unavailable at {$endpoint}",
            5002,
            $previous,
            ['service' => $service, 'endpoint' => $endpoint]
        );

        $exception->addRecoveryAction('retry_request', function ($params, $exception) {
            // Retry API request with exponential backoff
            $retryCount = $exception->getRetryCount();
            sleep(pow(2, $retryCount)); // Exponential backoff
            return false; // Placeholder
        });

        $exception->addRecoveryAction('use_cache', function ($params, $exception) {
            // Use cached response if available
            return false; // Placeholder
        });

        $exception->addRecoveryAction('use_fallback_service', function ($params, $exception) {
            // Use alternative service
            return false; // Placeholder
        });

        return $exception;
    }

    /**
     * Create file system recovery exception
     */
    public static function fileSystemError(string $operation, string $path, ?\Throwable $previous = null): self
    {
        $exception = new self(
            "File system error during {$operation} on {$path}",
            5003,
            $previous,
            ['operation' => $operation, 'path' => $path]
        );

        $exception->addRecoveryAction('retry_operation', function ($params, $exception) {
            // Retry file operation
            return false; // Placeholder
        });

        $exception->addRecoveryAction('create_directory', function ($params, $exception) {
            // Create missing directories
            $path = $exception->getContext()['path'];
            $dir = dirname($path);
            if (!is_dir($dir)) {
                return mkdir($dir, 0755, true);
            }
            return false;
        });

        $exception->addRecoveryAction('use_temp_location', function ($params, $exception) {
            // Use temporary location
            return false; // Placeholder
        });

        return $exception;
    }

    /**
     * Create memory limit recovery exception
     */
    public static function memoryLimitExceeded(int $currentUsage, int $limit, ?\Throwable $previous = null): self
    {
        $exception = new self(
            "Memory limit exceeded: {$currentUsage} bytes (limit: {$limit} bytes)",
            5004,
            $previous,
            ['current_usage' => $currentUsage, 'limit' => $limit]
        );

        $exception->addRecoveryAction('garbage_collection', function ($params, $exception) {
            // Force garbage collection
            gc_collect_cycles();
            return memory_get_usage() < $exception->getContext()['limit'] * 0.9;
        });

        $exception->addRecoveryAction('increase_memory_limit', function ($params, $exception) {
            // Increase memory limit if possible
            $currentLimit = ini_get('memory_limit');
            $newLimit = (int)$currentLimit * 2;
            return ini_set('memory_limit', $newLimit . 'M') !== false;
        });

        return $exception;
    }
}
