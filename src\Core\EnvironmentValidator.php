<?php

/**
 * Environment Variables Validator
 *
 * Validates environment variables according to defined rules
 * and ensures proper application configuration.
 *
 * @package WeBot\Core
 * @version 2.0
 */

declare(strict_types=1);

namespace WeBot\Core;

class EnvironmentValidator
{
    private array $config;
    private array $errors = [];
    private array $warnings = [];

    public function __construct()
    {
        $this->config = require __DIR__ . '/../../config/validation.php';
    }

    /**
     * Validate all environment variables
     */
    public function validate(): array
    {
        $this->errors = [];
        $this->warnings = [];

        // Check required variables
        $this->validateRequired();

        // Validate individual variables
        $this->validateRules();

        // Environment specific validations
        $this->validateEnvironmentSpecific();

        // Security validations
        $this->validateSecurity();

        // Check for warnings
        $this->checkWarnings();

        return [
            'valid' => empty($this->errors),
            'errors' => $this->errors,
            'warnings' => $this->warnings
        ];
    }

    /**
     * Validate required environment variables
     */
    private function validateRequired(): void
    {
        foreach ($this->config['required'] as $var) {
            if (!isset($_ENV[$var]) || empty($_ENV[$var])) {
                $this->errors[] = "Required environment variable '{$var}' is missing or empty";
            }
        }
    }

    /**
     * Validate environment variables against rules
     */
    private function validateRules(): void
    {
        foreach ($this->config['rules'] as $var => $rules) {
            $value = $_ENV[$var] ?? null;

            // Skip if not required and not set
            if (!isset($rules['required']) && $value === null) {
                continue;
            }

            // Check if required
            if (isset($rules['required']) && $rules['required'] && empty($value)) {
                $this->errors[] = "Required environment variable '{$var}' is missing";
                continue;
            }

            // Skip further validation if empty and not required
            if (empty($value)) {
                continue;
            }

            // Type validation
            if (isset($rules['type'])) {
                if (!$this->validateType($value, $rules['type'])) {
                    $this->errors[] = "Environment variable '{$var}' must be of type {$rules['type']}";
                    continue;
                }
            }

            // Pattern validation
            if (isset($rules['pattern'])) {
                if (!preg_match($rules['pattern'], $value)) {
                    $this->errors[] = "Environment variable '{$var}' does not match required pattern";
                }
            }

            // Allowed values validation
            if (isset($rules['allowed'])) {
                if (!in_array($value, $rules['allowed'])) {
                    $allowedStr = implode(', ', $rules['allowed']);
                    $this->errors[] = "Environment variable '{$var}' must be one of: {$allowedStr}";
                }
            }

            // Min/Max validation for integers
            if ($rules['type'] === 'integer') {
                $intValue = (int) $value;

                if (isset($rules['min']) && $intValue < $rules['min']) {
                    $this->errors[] = "Environment variable '{$var}' must be at least {$rules['min']}";
                }

                if (isset($rules['max']) && $intValue > $rules['max']) {
                    $this->errors[] = "Environment variable '{$var}' must be at most {$rules['max']}";
                }
            }

            // Min length validation for strings
            if ($rules['type'] === 'string' && isset($rules['min_length'])) {
                if (strlen($value) < $rules['min_length']) {
                    $this->errors[] = "Environment variable '{$var}' must be at least {$rules['min_length']} characters long";
                }
            }
        }
    }

    /**
     * Validate environment specific rules
     */
    private function validateEnvironmentSpecific(): void
    {
        $env = $_ENV['APP_ENV'] ?? 'production';

        if (!isset($this->config['environment_specific'][$env])) {
            return;
        }

        $envConfig = $this->config['environment_specific'][$env];

        // Check environment specific required variables
        if (isset($envConfig['required'])) {
            foreach ($envConfig['required'] as $var) {
                if (!isset($_ENV[$var]) || empty($_ENV[$var])) {
                    $this->errors[] = "Environment variable '{$var}' is required for {$env} environment";
                }
            }
        }

        // Check environment specific rules
        if (isset($envConfig['rules'])) {
            foreach ($envConfig['rules'] as $var => $rules) {
                $value = $_ENV[$var] ?? null;

                if (isset($rules['value']) && $value !== $rules['value']) {
                    $this->errors[] = "Environment variable '{$var}' must be '{$rules['value']}' in {$env} environment";
                }
            }
        }
    }

    /**
     * Validate security requirements
     */
    private function validateSecurity(): void
    {
        $security = $this->config['security'];

        // Check for weak passwords
        foreach (['DB_PASSWORD', 'REDIS_PASSWORD'] as $var) {
            $value = $_ENV[$var] ?? '';
            if (in_array(strtolower($value), $security['weak_passwords'])) {
                $this->errors[] = "Environment variable '{$var}' contains a weak password";
            }
        }

        // Check for insecure tokens
        foreach (['TELEGRAM_BOT_TOKEN', 'APP_KEY'] as $var) {
            $value = $_ENV[$var] ?? '';
            foreach ($security['insecure_tokens'] as $insecure) {
                if (stripos($value, $insecure) !== false) {
                    $this->errors[] = "Environment variable '{$var}' contains insecure default value";
                }
            }
        }

        // Production specific security checks
        if (($_ENV['APP_ENV'] ?? 'production') === 'production') {
            foreach ($security['production_requirements'] as $var => $requirement) {
                $value = $_ENV[$var] ?? '';

                switch ($var) {
                    case 'APP_KEY':
                        if (strlen($value) < 32) {
                            $this->errors[] = "APP_KEY must be at least 32 characters in production";
                        }
                        break;

                    case 'DB_PASSWORD':
                        if (empty($value)) {
                            $this->errors[] = "DB_PASSWORD must not be empty in production";
                        }
                        break;

                    case 'SSL_ENABLED':
                        if ($value !== 'true') {
                            $this->errors[] = "SSL_ENABLED must be true in production";
                        }
                        break;

                    case 'APP_DEBUG':
                        if ($value === 'true') {
                            $this->errors[] = "APP_DEBUG must be false in production";
                        }
                        break;
                }
            }
        }
    }

    /**
     * Check for warning conditions
     */
    private function checkWarnings(): void
    {
        $warnings = $this->config['warnings'];

        // Check for missing optional variables
        foreach ($warnings['missing_optional'] as $var) {
            if (!isset($_ENV[$var]) || empty($_ENV[$var])) {
                $this->warnings[] = "Optional environment variable '{$var}' is not set";
            }
        }

        // Check for development values in production
        if (($_ENV['APP_ENV'] ?? 'production') === 'production') {
            foreach ($_ENV as $key => $value) {
                foreach ($warnings['development_values_in_production'] as $devValue) {
                    if (stripos($value, $devValue) !== false) {
                        $this->warnings[] = "Environment variable '{$key}' contains development value '{$devValue}' in production";
                    }
                }
            }
        }
    }

    /**
     * Validate variable type
     */
    private function validateType(string $value, string $type): bool
    {
        switch ($type) {
            case 'boolean':
                return in_array(strtolower($value), ['true', 'false', '1', '0']);

            case 'integer':
                return ctype_digit($value) || (is_numeric($value) && (int) $value == $value);

            case 'string':
                return is_string($value);

            default:
                return true;
        }
    }

    /**
     * Get validation errors
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Get validation warnings
     */
    public function getWarnings(): array
    {
        return $this->warnings;
    }

    /**
     * Check if validation passed
     */
    public function isValid(): bool
    {
        return empty($this->errors);
    }

    /**
     * Generate validation report
     */
    public function generateReport(): string
    {
        $report = "Environment Variables Validation Report\n";
        $report .= "==========================================\n\n";

        if (empty($this->errors) && empty($this->warnings)) {
            $report .= "✅ All environment variables are properly configured!\n";
        } else {
            if (!empty($this->errors)) {
                $report .= "❌ ERRORS (" . count($this->errors) . "):\n";
                foreach ($this->errors as $error) {
                    $report .= "   - {$error}\n";
                }
                $report .= "\n";
            }

            if (!empty($this->warnings)) {
                $report .= "⚠️  WARNINGS (" . count($this->warnings) . "):\n";
                foreach ($this->warnings as $warning) {
                    $report .= "   - {$warning}\n";
                }
                $report .= "\n";
            }
        }

        return $report;
    }
}
