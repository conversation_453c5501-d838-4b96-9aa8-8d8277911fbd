<?php

declare(strict_types=1);

/**
 * WeBot Bootstrap File
 *
 * Initializes the WeBot application environment including
 * autoloading, configuration, error handling, and core services.
 *
 * @package WeBot\Core
 * @version 2.0
 */

// Define application constants
if (!defined('WEBOT_ROOT')) {
    define('WEBOT_ROOT', dirname(__DIR__, 2));
}

if (!defined('WEBOT_SRC')) {
    define('WEBOT_SRC', WEBOT_ROOT . '/src');
}

if (!defined('WEBOT_CONFIG')) {
    define('WEBOT_CONFIG', WEBOT_ROOT . '/config');
}

if (!defined('WEBOT_STORAGE')) {
    define('WEBOT_STORAGE', WEBOT_ROOT . '/storage');
}

if (!defined('WEBOT_LOGS')) {
    define('WEBOT_LOGS', WEBOT_STORAGE . '/logs');
}

// Set timezone
date_default_timezone_set('UTC');

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', '1');
ini_set('log_errors', '1');

// Load environment variables if .env file exists
if (file_exists(WEBOT_ROOT . '/.env')) {
    $envFile = WEBOT_ROOT . '/.env';
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);

    foreach ($lines as $line) {
        if (str_starts_with(trim($line), '#')) {
            continue;
        }

        if (str_contains($line, '=')) {
            [$key, $value] = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value, " \t\n\r\0\x0B\"'");

            if (!array_key_exists($key, $_ENV)) {
                $_ENV[$key] = $value;
                putenv("{$key}={$value}");
            }
        }
    }
}

// Set default environment variables
$defaults = [
    'APP_ENV' => 'development',
    'APP_DEBUG' => 'true',
    'APP_URL' => 'http://localhost:8000',
    'DB_CONNECTION' => 'sqlite',
    'DB_DATABASE' => WEBOT_STORAGE . '/database.sqlite',
    'LOG_LEVEL' => 'debug'
];

foreach ($defaults as $key => $value) {
    if (!isset($_ENV[$key])) {
        $_ENV[$key] = $value;
        putenv("{$key}={$value}");
    }
}

// Create necessary directories
$directories = [
    WEBOT_STORAGE,
    WEBOT_LOGS,
    WEBOT_STORAGE . '/cache',
    WEBOT_STORAGE . '/sessions',
    dirname($_ENV['DB_DATABASE'])
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Simple autoloader for WeBot classes
spl_autoload_register(function ($class) {
    // Only handle WeBot namespace
    if (!str_starts_with($class, 'WeBot\\')) {
        return;
    }

    // Convert namespace to file path
    $relativePath = str_replace('WeBot\\', '', $class);
    $relativePath = str_replace('\\', DIRECTORY_SEPARATOR, $relativePath);
    $file = WEBOT_SRC . DIRECTORY_SEPARATOR . $relativePath . '.php';

    if (file_exists($file)) {
        require_once $file;
    }
});

// Helper functions
if (!function_exists('env')) {
    /**
     * Get environment variable value
     */
    function env(string $key, $default = null)
    {
        return $_ENV[$key] ?? $default;
    }
}

if (!function_exists('config_path')) {
    /**
     * Get config file path
     */
    function config_path(string $path = ''): string
    {
        return WEBOT_CONFIG . ($path ? DIRECTORY_SEPARATOR . $path : '');
    }
}

if (!function_exists('storage_path')) {
    /**
     * Get storage file path
     */
    function storage_path(string $path = ''): string
    {
        return WEBOT_STORAGE . ($path ? DIRECTORY_SEPARATOR . $path : '');
    }
}

if (!function_exists('base_path')) {
    /**
     * Get base application path
     */
    function base_path(string $path = ''): string
    {
        return WEBOT_ROOT . ($path ? DIRECTORY_SEPARATOR . $path : '');
    }
}

// Set up error handler
set_error_handler(function ($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return false;
    }

    $errorType = match ($severity) {
        E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR => 'ERROR',
        E_WARNING, E_CORE_WARNING, E_COMPILE_WARNING, E_USER_WARNING => 'WARNING',
        E_NOTICE, E_USER_NOTICE => 'NOTICE',
        E_STRICT => 'STRICT',
        E_DEPRECATED, E_USER_DEPRECATED => 'DEPRECATED',
        default => 'UNKNOWN'
    };

    $logMessage = "[{$errorType}] {$message} in {$file} on line {$line}";

    // Log to file
    $logFile = WEBOT_LOGS . '/error.log';
    error_log(date('Y-m-d H:i:s') . " {$logMessage}\n", 3, $logFile);

    // Display in development
    if (env('APP_DEBUG', false)) {
        echo "<pre>{$logMessage}</pre>\n";
    }

    return true;
});

// Set up exception handler
set_exception_handler(function ($exception) {
    $message = "Uncaught exception: " . $exception->getMessage();
    $file = $exception->getFile();
    $line = $exception->getLine();
    $trace = $exception->getTraceAsString();

    $logMessage = "[EXCEPTION] {$message} in {$file} on line {$line}\nStack trace:\n{$trace}";

    // Log to file
    $logFile = WEBOT_LOGS . '/error.log';
    error_log(date('Y-m-d H:i:s') . " {$logMessage}\n", 3, $logFile);

    // Display in development
    if (env('APP_DEBUG', false)) {
        echo "<pre>{$logMessage}</pre>\n";
    } else {
        echo "An error occurred. Please try again later.\n";
    }

    exit(1);
});

// Initialize application
if (!defined('WEBOT_BOOTSTRAPPED')) {
    define('WEBOT_BOOTSTRAPPED', true);
}
