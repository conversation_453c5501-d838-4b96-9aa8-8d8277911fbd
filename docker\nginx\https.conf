# WeBot HTTPS Server Configuration
# Secure HTTPS server block with SSL/TLS best practices

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name _;
    
    # Security headers even for redirects
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    
    # Redirect all HTTP traffic to HTTPS
    return 301 https://$host$request_uri;
}

# Main HTTPS Server
server {
    listen 443 ssl http2;
    server_name _;
    root /var/www/html/public;
    index index.php index.html;

    # Include SSL configuration
    include /etc/nginx/ssl.conf;

    # Logging
    access_log /var/log/nginx/https_access.log;
    error_log /var/log/nginx/https_error.log;

    # Security
    server_tokens off;
    
    # Disable access to hidden files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Webhook endpoint with enhanced security
    location /webhook {
        # Rate limiting
        limit_req zone=webhook burst=20 nodelay;
        
        # Additional security for webhook
        add_header X-Webhook-Secure "true" always;
        
        try_files $uri $uri/ /index.php?$query_string;
        
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # Telegram webhook specific headers
        fastcgi_param HTTP_X_TELEGRAM_BOT_API_SECRET_TOKEN $http_x_telegram_bot_api_secret_token;
        
        # Enhanced security for webhook
        fastcgi_param HTTPS on;
        fastcgi_param SERVER_PORT 443;
        fastcgi_param REQUEST_SCHEME https;
    }

    # API endpoints with SSL verification
    location /api/ {
        # Rate limiting
        limit_req zone=api burst=5 nodelay;
        
        # API security headers
        add_header X-API-Secure "true" always;
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        
        try_files $uri $uri/ /index.php?$query_string;
        
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # SSL parameters for API
        fastcgi_param HTTPS on;
        fastcgi_param SSL_PROTOCOL $ssl_protocol;
        fastcgi_param SSL_CIPHER $ssl_cipher;
    }

    # Health check endpoint (allow HTTP for monitoring)
    location /health {
        access_log off;
        add_header Content-Type text/plain;
        return 200 "healthy\n";
    }

    # SSL health check
    location /ssl-health {
        access_log off;
        add_header Content-Type application/json;
        add_header X-SSL-Protocol $ssl_protocol always;
        add_header X-SSL-Cipher $ssl_cipher always;
        return 200 '{"status":"healthy","ssl":"enabled","protocol":"$ssl_protocol","cipher":"$ssl_cipher"}';
    }

    # Static files with long-term caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
        access_log off;
        
        # Security headers for static files
        add_header X-Content-Type-Options nosniff always;
        add_header X-Frame-Options DENY always;
    }

    # PHP files with SSL context
    location ~ \.php$ {
        try_files $uri =404;
        
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # SSL context for PHP
        fastcgi_param HTTPS on;
        fastcgi_param SERVER_PORT 443;
        fastcgi_param REQUEST_SCHEME https;
        fastcgi_param SSL_PROTOCOL $ssl_protocol;
        fastcgi_param SSL_CIPHER $ssl_cipher;
        fastcgi_param SSL_SESSION_ID $ssl_session_id;
        fastcgi_param SSL_SESSION_REUSED $ssl_session_reused;
        
        # Security
        fastcgi_param HTTP_PROXY "";
        fastcgi_param SERVER_NAME $host;
        
        # Timeouts
        fastcgi_connect_timeout 60s;
        fastcgi_send_timeout 60s;
        fastcgi_read_timeout 60s;
        
        # Buffer sizes
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
    }

    # Default location
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # Deny access to sensitive files
    location ~ /\.(env|git|svn) {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ /(config|storage|tests|vendor)/ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Block common attack patterns
    location ~* (wp-admin|wp-login|xmlrpc|phpmyadmin) {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Custom error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    error_page 495 496 497 /ssl_error.html;
    
    location = /50x.html {
        root /var/www/html/public;
    }
    
    location = /ssl_error.html {
        root /var/www/html/public;
        add_header Content-Type text/html;
    }

    # Security.txt for responsible disclosure
    location = /.well-known/security.txt {
        add_header Content-Type text/plain;
        return 200 "Contact: <EMAIL>\nExpires: 2025-12-31T23:59:59.000Z\nPreferred-Languages: en, fa\n";
    }

    # Certificate transparency monitoring
    location = /.well-known/ct-logs {
        add_header Content-Type application/json;
        return 200 '{"logs":["https://ct.googleapis.com/logs/argon2024/","https://oak.ct.letsencrypt.org/2024h1/"]}';
    }
}

# Additional HTTPS server for specific domains (if needed)
# server {
#     listen 443 ssl http2;
#     server_name your-domain.com www.your-domain.com;
#     
#     # Domain-specific SSL certificate
#     ssl_certificate /etc/nginx/ssl/your-domain.com.crt;
#     ssl_certificate_key /etc/nginx/ssl/your-domain.com.key;
#     
#     # Include common SSL settings
#     include /etc/nginx/ssl.conf;
#     
#     # Domain-specific configuration
#     root /var/www/html/public;
#     index index.php index.html;
#     
#     # Include common locations
#     include /etc/nginx/common-locations.conf;
# }
