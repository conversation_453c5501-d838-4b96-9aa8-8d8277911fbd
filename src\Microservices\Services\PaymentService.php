<?php

declare(strict_types=1);

namespace WeBot\Microservices\Services;

use WeBot\Services\DatabaseService;
use WeBot\Core\CacheManager;
use WeBot\Utils\Logger;
use WeBot\Exceptions\WeBotException;
use WeBot\Models\Payment;
use WeBot\Repositories\PaymentRepository;

/**
 * Payment Microservice
 *
 * Handles all payment-related operations including payment processing,
 * wallet management, transaction history, and payment gateway integration.
 *
 * @package WeBot\Microservices\Services
 * @version 2.0
 */
class PaymentService
{
    private DatabaseService $database;
    private CacheManager $cache;
    private Logger $logger;
    private PaymentRepository $paymentRepository;
    private array $config;
    private array $gateways = [];

    public function __construct(
        DatabaseService $database,
        CacheManager $cache,
        PaymentRepository $paymentRepository,
        array $config = []
    ) {
        $this->database = $database;
        $this->cache = $cache;
        $this->logger = Logger::getInstance();
        $this->paymentRepository = $paymentRepository;
        $this->config = array_merge($this->getDefaultConfig(), $config);

        $this->initializeGateways();
    }

    /**
     * Handle service requests
     */
    public function handleRequest(array $request): array
    {
        $action = $request['action'] ?? '';
        $data = $request['data'] ?? [];

        try {
            return match ($action) {
                'create_payment' => $this->createPayment($data),
                'process_payment' => $this->processPayment($data),
                'verify_payment' => $this->verifyPayment($data),
                'get_payment' => $this->getPayment($data),
                'get_user_payments' => $this->getUserPayments($data),
                'refund_payment' => $this->refundPayment($data),
                'get_payment_stats' => $this->getPaymentStats($data),
                'update_wallet' => $this->updateWallet($data),
                'get_wallet_balance' => $this->getWalletBalance($data),
                'get_wallet_transactions' => $this->getWalletTransactions($data),
                'get_gateways' => $this->getAvailableGateways($data),
                'health' => $this->healthCheck(),
                default => throw new WeBotException("Unknown action: {$action}")
            };
        } catch (\Exception $e) {
            $this->logger->error("Payment service error", [
                'action' => $action,
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            throw $e;
        }
    }

    /**
     * Create new payment
     */
    public function createPayment(array $data): array
    {
        $requiredFields = ['user_id', 'amount', 'gateway'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                throw new WeBotException("Field {$field} is required");
            }
        }

        $userId = $data['user_id'];
        $amount = $data['amount'];
        $gateway = $data['gateway'];
        $description = $data['description'] ?? 'Payment';

        // Validate amount
        if ($amount <= 0) {
            throw new WeBotException("Amount must be greater than 0");
        }

        // Validate gateway
        if (!isset($this->gateways[$gateway])) {
            throw new WeBotException("Invalid payment gateway");
        }

        // Create payment record
        $paymentData = [
            'user_id' => $userId,
            'amount' => $amount,
            'gateway' => $gateway,
            'description' => $description,
            'status' => 'pending',
            'transaction_id' => $this->generateTransactionId(),
            'gateway_transaction_id' => null,
            'created_at' => date('Y-m-d H:i:s'),
            'metadata' => json_encode($data['metadata'] ?? [])
        ];

        $payment = $this->paymentRepository->create($paymentData);

        $this->logger->info("Payment created", [
            'payment_id' => $payment->id,
            'user_id' => $userId,
            'amount' => $amount,
            'gateway' => $gateway
        ]);

        return [
            'success' => true,
            'payment' => $payment,
            'message' => 'Payment created successfully'
        ];
    }

    /**
     * Process payment through gateway
     */
    public function processPayment(array $data): array
    {
        $paymentId = $data['payment_id'] ?? null;

        if (!$paymentId) {
            throw new WeBotException("Payment ID is required");
        }

        $payment = $this->paymentRepository->findById($paymentId);
        if (!$payment) {
            throw new WeBotException("Payment not found", 404);
        }

        if ($payment->status !== 'pending') {
            throw new WeBotException("Payment is not in pending status");
        }

        $gateway = $this->gateways[$payment->gateway];

        try {
            // Process payment through gateway
            $result = $this->processGatewayPayment($gateway, $payment);

            // Update payment status
            $updateData = [
                'status' => $result['status'],
                'gateway_transaction_id' => $result['transaction_id'] ?? null,
                'gateway_response' => json_encode($result),
                'processed_at' => date('Y-m-d H:i:s')
            ];

            $this->paymentRepository->update($payment->id, $updateData);

            // If successful, update user wallet
            if ($result['status'] === 'completed') {
                $this->addToWallet($payment->user_id, $payment->amount);
            }

            $this->logger->info("Payment processed", [
                'payment_id' => $paymentId,
                'status' => $result['status'],
                'gateway_transaction_id' => $result['transaction_id'] ?? null
            ]);

            return [
                'success' => true,
                'payment_id' => $paymentId,
                'status' => $result['status'],
                'gateway_url' => $result['gateway_url'] ?? null,
                'message' => 'Payment processed successfully'
            ];
        } catch (\Exception $e) {
            // Update payment status to failed
            $this->paymentRepository->update($paymentId, [
                'status' => 'failed',
                'error_message' => $e->getMessage(),
                'processed_at' => date('Y-m-d H:i:s')
            ]);

            throw $e;
        }
    }

    /**
     * Verify payment status
     */
    public function verifyPayment(array $data): array
    {
        $paymentId = $data['payment_id'] ?? null;
        $gatewayTransactionId = $data['gateway_transaction_id'] ?? null;

        if (!$paymentId && !$gatewayTransactionId) {
            throw new WeBotException("Payment ID or Gateway Transaction ID is required");
        }

        if ($paymentId) {
            $payment = $this->paymentRepository->findById($paymentId);
        } else {
            $payment = $this->paymentRepository->findByGatewayTransactionId($gatewayTransactionId);
        }

        if (!$payment) {
            throw new WeBotException("Payment not found", 404);
        }

        $gateway = $this->gateways[$payment->gateway];

        // Verify payment with gateway
        $verificationResult = $this->verifyGatewayPayment($gateway, $payment);

        // Update payment if status changed
        if ($verificationResult['status'] !== $payment->status) {
            $updateData = [
                'status' => $verificationResult['status'],
                'verified_at' => date('Y-m-d H:i:s'),
                'verification_response' => json_encode($verificationResult)
            ];

            $this->paymentRepository->update($payment->id, $updateData);

            // If payment was completed, update wallet
            if ($verificationResult['status'] === 'completed' && $payment->status !== 'completed') {
                $this->addToWallet($payment->user_id, $payment->amount);
            }
        }

        return [
            'success' => true,
            'payment_id' => $payment->id,
            'status' => $verificationResult['status'],
            'verified' => true,
            'message' => 'Payment verified successfully'
        ];
    }

    /**
     * Get payment by ID
     */
    public function getPayment(array $data): array
    {
        $paymentId = $data['payment_id'] ?? null;

        if (!$paymentId) {
            throw new WeBotException("Payment ID is required");
        }

        $payment = $this->paymentRepository->findById($paymentId);

        if (!$payment) {
            throw new WeBotException("Payment not found", 404);
        }

        return [
            'success' => true,
            'payment' => $payment
        ];
    }

    /**
     * Get user payments
     */
    public function getUserPayments(array $data): array
    {
        $userId = $data['user_id'] ?? null;
        $limit = $data['limit'] ?? 50;
        $offset = $data['offset'] ?? 0;
        $status = $data['status'] ?? null;

        if (!$userId) {
            throw new WeBotException("User ID is required");
        }

        $payments = $this->paymentRepository->findByUserId($userId, $limit, $offset, $status);

        return [
            'success' => true,
            'payments' => $payments,
            'user_id' => $userId,
            'limit' => $limit,
            'offset' => $offset
        ];
    }

    /**
     * Refund payment
     */
    public function refundPayment(array $data): array
    {
        $paymentId = $data['payment_id'] ?? null;
        $reason = $data['reason'] ?? 'Refund requested';

        if (!$paymentId) {
            throw new WeBotException("Payment ID is required");
        }

        $payment = $this->paymentRepository->findById($paymentId);
        if (!$payment) {
            throw new WeBotException("Payment not found", 404);
        }

        if ($payment->status !== 'completed') {
            throw new WeBotException("Only completed payments can be refunded");
        }

        $gateway = $this->gateways[$payment->gateway];

        try {
            // Process refund through gateway
            $refundResult = $this->processGatewayRefund($gateway, $payment, $reason);

            // Update payment status
            $updateData = [
                'status' => 'refunded',
                'refund_reason' => $reason,
                'refunded_at' => date('Y-m-d H:i:s'),
                'refund_response' => json_encode($refundResult)
            ];

            $this->paymentRepository->update($payment->id, $updateData);

            // Deduct from user wallet
            $this->subtractFromWallet($payment->user_id, $payment->amount);

            $this->logger->info("Payment refunded", [
                'payment_id' => $payment->id,
                'amount' => $payment->amount,
                'reason' => $reason
            ]);

            return [
                'success' => true,
                'payment_id' => $payment->id,
                'refund_amount' => $payment->amount,
                'message' => 'Payment refunded successfully'
            ];
        } catch (\Exception $e) {
            $this->logger->error("Refund failed", [
                'payment_id' => $paymentId,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Get payment statistics
     */
    public function getPaymentStats(array $data): array
    {
        $userId = $data['user_id'] ?? null;
        $startDate = $data['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
        $endDate = $data['end_date'] ?? date('Y-m-d');

        $cacheKey = "payment_stats:" . md5(serialize($data));
        $cached = $this->cache->get($cacheKey);

        if ($cached !== null) {
            return [
                'success' => true,
                'stats' => $cached,
                'from_cache' => true
            ];
        }

        $stats = $this->paymentRepository->getPaymentStats($userId, $startDate, $endDate);

        // Cache stats
        $this->cache->set($cacheKey, $stats, $this->config['stats_cache_ttl']);

        return [
            'success' => true,
            'stats' => $stats,
            'from_cache' => false
        ];
    }

    /**
     * Update wallet balance
     */
    public function updateWallet(array $data): array
    {
        $userId = $data['user_id'] ?? null;
        $amount = $data['amount'] ?? null;
        $operation = $data['operation'] ?? 'add';
        $description = $data['description'] ?? 'Wallet update';

        if (!$userId || $amount === null) {
            throw new WeBotException("User ID and amount are required");
        }

        try {
            $this->database->beginTransaction();

            if ($operation === 'add') {
                $newBalance = $this->addToWallet($userId, $amount);
            } elseif ($operation === 'subtract') {
                $newBalance = $this->subtractFromWallet($userId, $amount);
            } else {
                throw new WeBotException("Invalid operation");
            }

            // Record wallet transaction
            $this->recordWalletTransaction($userId, $amount, $operation, $description);

            $this->database->commit();

            return [
                'success' => true,
                'new_balance' => $newBalance,
                'operation' => $operation,
                'amount' => $amount,
                'message' => 'Wallet updated successfully'
            ];
        } catch (\Exception $e) {
            $this->database->rollback();
            throw $e;
        }
    }

    /**
     * Get wallet balance
     */
    public function getWalletBalance(array $data): array
    {
        $userId = $data['user_id'] ?? null;

        if (!$userId) {
            throw new WeBotException("User ID is required");
        }

        $cacheKey = "wallet_balance:{$userId}";
        $cached = $this->cache->get($cacheKey);

        if ($cached !== null) {
            return [
                'success' => true,
                'balance' => $cached,
                'from_cache' => true
            ];
        }

        $balance = $this->paymentRepository->getWalletBalance($userId);

        // Cache balance
        $this->cache->set($cacheKey, $balance, $this->config['balance_cache_ttl']);

        return [
            'success' => true,
            'balance' => $balance,
            'from_cache' => false
        ];
    }

    /**
     * Get wallet transactions
     */
    public function getWalletTransactions(array $data): array
    {
        $userId = $data['user_id'] ?? null;
        $limit = $data['limit'] ?? 50;
        $offset = $data['offset'] ?? 0;

        if (!$userId) {
            throw new WeBotException("User ID is required");
        }

        $transactions = $this->paymentRepository->getWalletTransactions($userId, $limit, $offset);

        return [
            'success' => true,
            'transactions' => $transactions,
            'user_id' => $userId
        ];
    }

    /**
     * Get available payment gateways
     */
    public function getAvailableGateways(array $data): array
    {
        $gateways = [];

        foreach ($this->gateways as $key => $gateway) {
            if ($gateway['enabled']) {
                $gateways[] = [
                    'key' => $key,
                    'name' => $gateway['name'],
                    'description' => $gateway['description'],
                    'min_amount' => $gateway['min_amount'],
                    'max_amount' => $gateway['max_amount'],
                    'fee_percentage' => $gateway['fee_percentage'],
                    'fee_fixed' => $gateway['fee_fixed']
                ];
            }
        }

        return [
            'success' => true,
            'gateways' => $gateways
        ];
    }

    /**
     * Health check
     */
    public function healthCheck(): array
    {
        try {
            // Test database connection
            $this->database->query("SELECT 1");

            // Test cache connection
            $this->cache->set('health_check', time(), 10);
            $this->cache->get('health_check');

            return [
                'success' => true,
                'status' => 'healthy',
                'service' => 'payment-service',
                'timestamp' => time(),
                'version' => '1.0.0'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'status' => 'unhealthy',
                'service' => 'payment-service',
                'error' => $e->getMessage(),
                'timestamp' => time()
            ];
        }
    }

    /**
     * Initialize payment gateways
     */
    private function initializeGateways(): void
    {
        $this->gateways = [
            'zarinpal' => [
                'name' => 'ZarinPal',
                'description' => 'ZarinPal Payment Gateway',
                'enabled' => true,
                'min_amount' => 1000,
                'max_amount' => 50000000,
                'fee_percentage' => 1.5,
                'fee_fixed' => 0,
                'config' => $this->config['gateways']['zarinpal'] ?? []
            ],
            'idpay' => [
                'name' => 'IDPay',
                'description' => 'IDPay Payment Gateway',
                'enabled' => true,
                'min_amount' => 1000,
                'max_amount' => 50000000,
                'fee_percentage' => 1.8,
                'fee_fixed' => 0,
                'config' => $this->config['gateways']['idpay'] ?? []
            ]
        ];
    }

    /**
     * Process payment through gateway
     */
    private function processGatewayPayment(array $gateway, Payment $payment): array
    {
        // Mock implementation - replace with actual gateway integration
        return [
            'status' => 'pending',
            'transaction_id' => 'gw_' . uniqid(),
            'gateway_url' => 'https://gateway.example.com/pay/' . $payment->transaction_id
        ];
    }

    /**
     * Verify payment with gateway
     */
    private function verifyGatewayPayment(array $gateway, Payment $payment): array
    {
        // Mock implementation - replace with actual gateway verification
        return [
            'status' => 'completed',
            'verified' => true
        ];
    }

    /**
     * Process refund through gateway
     */
    private function processGatewayRefund(array $gateway, Payment $payment, string $reason): array
    {
        // Mock implementation - replace with actual gateway refund
        return [
            'refund_id' => 'ref_' . uniqid(),
            'status' => 'refunded'
        ];
    }

    /**
     * Add amount to wallet
     */
    private function addToWallet(int $userId, float $amount): float
    {
        $currentBalance = $this->paymentRepository->getWalletBalance($userId);
        $newBalance = $currentBalance + $amount;

        $this->paymentRepository->updateWalletBalance($userId, $newBalance);

        // Clear cache
        $this->cache->delete("wallet_balance:{$userId}");

        return $newBalance;
    }

    /**
     * Subtract amount from wallet
     */
    private function subtractFromWallet(int $userId, float $amount): float
    {
        $currentBalance = $this->paymentRepository->getWalletBalance($userId);

        if ($currentBalance < $amount) {
            throw new WeBotException("Insufficient wallet balance");
        }

        $newBalance = $currentBalance - $amount;

        $this->paymentRepository->updateWalletBalance($userId, $newBalance);

        // Clear cache
        $this->cache->delete("wallet_balance:{$userId}");

        return $newBalance;
    }

    /**
     * Record wallet transaction
     */
    private function recordWalletTransaction(int $userId, float $amount, string $operation, string $description): void
    {
        $this->paymentRepository->recordWalletTransaction([
            'user_id' => $userId,
            'amount' => $amount,
            'operation' => $operation,
            'description' => $description,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Generate transaction ID
     */
    private function generateTransactionId(): string
    {
        return 'TXN_' . time() . '_' . uniqid();
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'stats_cache_ttl' => 1800,     // 30 minutes
            'balance_cache_ttl' => 300,    // 5 minutes
            'gateways' => [
                'zarinpal' => [
                    'merchant_id' => '',
                    'sandbox' => true
                ],
                'idpay' => [
                    'api_key' => '',
                    'sandbox' => true
                ]
            ]
        ];
    }
}
