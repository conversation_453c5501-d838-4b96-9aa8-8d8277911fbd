<?php

declare(strict_types=1);

namespace WeBot\Repositories;

use WeBot\Core\Database;
use WeBot\Models\Panel;
use WeBot\Exceptions\WeBotException;

/**
 * Panel Repository
 *
 * Handles database operations for Panel model including
 * advanced queries, statistics, and panel management.
 *
 * @package WeBot\Repositories
 * @version 2.0
 */
class PanelRepository
{
    private Database $database;

    public function __construct(Database $database)
    {
        $this->database = $database;
    }

    /**
     * Find panel by ID
     */
    public function findById(int $id): ?Panel
    {
        $data = $this->database->selectOne('panels', ['id' => $id]);

        return $data ? new Panel($this->database, $data) : null;
    }

    /**
     * Find panel by name
     */
    public function findByName(string $name): ?Panel
    {
        $data = $this->database->selectOne('panels', ['name' => $name]);

        return $data ? new Panel($this->database, $data) : null;
    }

    /**
     * Find panel by URL
     */
    public function findByUrl(string $url): ?Panel
    {
        $data = $this->database->selectOne('panels', ['url' => $url]);

        return $data ? new Panel($this->database, $data) : null;
    }

    /**
     * Get all panels
     */
    public function getAll(): array
    {
        $results = $this->database->select('panels', [], ['*'], ['priority' => 'ASC', 'name' => 'ASC']);

        return array_map(function ($data) {
            return new Panel($this->database, $data);
        }, $results);
    }

    /**
     * Get active panels
     */
    public function getActive(): array
    {
        $results = $this->database->select(
            'panels',
            ['is_active' => true],
            ['*'],
            ['priority' => 'ASC', 'name' => 'ASC']
        );

        return array_map(function ($data) {
            return new Panel($this->database, $data);
        }, $results);
    }

    /**
     * Get online panels
     */
    public function getOnline(): array
    {
        $results = $this->database->select(
            'panels',
            ['status' => Panel::STATUS_ONLINE, 'is_active' => true],
            ['*'],
            ['priority' => 'ASC', 'name' => 'ASC']
        );

        return array_map(function ($data) {
            return new Panel($this->database, $data);
        }, $results);
    }

    /**
     * Get panels by type
     */
    public function getByType(string $type): array
    {
        $results = $this->database->select(
            'panels',
            ['type' => $type],
            ['*'],
            ['priority' => 'ASC', 'name' => 'ASC']
        );

        return array_map(function ($data) {
            return new Panel($this->database, $data);
        }, $results);
    }

    /**
     * Get all panels with filtering and pagination
     */
    public function findAll(?string $status, ?string $type, int $limit, int $offset): array
    {
        $sql = "SELECT * FROM panels";
        $conditions = [];
        $params = [];

        if ($status !== null) {
            $conditions[] = "status = ?";
            $params[] = $status;
        }
        if ($type !== null) {
            $conditions[] = "type = ?";
            $params[] = $type;
        }

        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }

        $sql .= " ORDER BY priority ASC, name ASC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;

        $results = $this->database->query($sql, $params);

        return array_map(function ($data) {
            return new Panel($this->database, $data);
        }, $results);
    }

    /**
     * Get panels with capacity
     */
    public function getWithCapacity(): array
    {
        $sql = "
            SELECT * FROM panels 
            WHERE is_active = 1 
            AND status = ? 
            AND (max_users = 0 OR current_users < max_users)
            ORDER BY priority ASC, name ASC
        ";

        $results = $this->database->query($sql, [Panel::STATUS_ONLINE]);

        return array_map(function ($data) {
            return new Panel($this->database, $data);
        }, $results);
    }

    /**
     * Get best panel for new user
     */
    public function getBestPanelForUser(string $type = null): ?Panel
    {
        $conditions = [
            'is_active' => true,
            'status' => Panel::STATUS_ONLINE
        ];

        if ($type) {
            $conditions['type'] = $type;
        }

        // First try to get panels with capacity
        $sql = "
            SELECT * FROM panels 
            WHERE is_active = 1 
            AND status = ? 
            " . ($type ? "AND type = ? " : "") . "
            AND (max_users = 0 OR current_users < max_users)
            ORDER BY 
                priority ASC,
                (CASE WHEN max_users = 0 THEN 0 ELSE (current_users / max_users) END) ASC,
                name ASC
            LIMIT 1
        ";

        $params = $type ? [Panel::STATUS_ONLINE, $type] : [Panel::STATUS_ONLINE];
        $results = $this->database->query($sql, $params);
        $data = $results[0] ?? null;

        return $data ? new Panel($this->database, $data) : null;
    }

    /**
     * Create new panel
     */
    public function create(array $data): Panel
    {
        $panel = new Panel($this->database);
        $panel->fill($data);

        if (!$panel->save()) {
            throw new WeBotException('Failed to create panel');
        }

        return $panel;
    }

    /**
     * Update panel
     */
    public function update(int $id, array $data): bool
    {
        $panel = $this->findById($id);
        if (!$panel) {
            throw new WeBotException('Panel not found');
        }

        $panel->fill($data);

        return $panel->save();
    }

    /**
     * Delete panel
     */
    public function delete(int $id): bool
    {
        // Check if panel has active services
        $sql = "SELECT COUNT(*) as count FROM `services` WHERE `panel_id` = ? AND `status` = ?";
        $activeServices = $this->database->query($sql, [$id, 'active'])[0] ?? null;

        if ($activeServices && $activeServices['count'] > 0) {
            throw new WeBotException('Cannot delete panel with active services');
        }

        return $this->database->delete('panels', ['id' => $id]) > 0;
    }

    /**
     * Update panel status
     */
    public function updateStatus(int $id, string $status, ?string $healthStatus = null): bool
    {
        $updateData = [
            'status' => $status,
            'last_health_check' => date('Y-m-d H:i:s')
        ];

        if ($healthStatus !== null) {
            $updateData['health_status'] = $healthStatus;
        }

        return $this->database->update('panels', $updateData, ['id' => $id]) > 0;
    }

    /**
     * Update panel statistics
     */
    public function updateStatistics(int $id, array $stats): bool
    {
        $panel = $this->findById($id);
        if (!$panel) {
            return false;
        }

        return $panel->updateStatistics($stats);
    }

    /**
     * Update panel token
     */
    public function updateToken(int $id, string $token, ?string $refreshToken = null, ?int $expiresIn = null): bool
    {
        $panel = $this->findById($id);
        if (!$panel) {
            return false;
        }

        return $panel->updateToken($token, $refreshToken, $expiresIn);
    }

    /**
     * Get statistics for a specific panel
     */
    public function getPanelStats(int $panelId): array
    {
        $sql = "
            SELECT 
                p.current_users,
                p.max_users,
                (SELECT COUNT(*) FROM services s WHERE s.panel_id = p.id) as total_services,
                (SELECT COUNT(*) FROM services s WHERE s.panel_id = p.id AND s.status = 'active') as active_services
            FROM panels p
            WHERE p.id = ?
        ";
        $results = $this->database->query($sql, [$panelId]);
        $stats = $results[0] ?? [];

        return [
            'current_users' => (int)($stats['current_users'] ?? 0),
            'max_users' => (int)($stats['max_users'] ?? 0),
            'total_services' => (int)($stats['total_services'] ?? 0),
            'active_services' => (int)($stats['active_services'] ?? 0),
        ];
    }

    /**
     * Get panel statistics
     */
    public function getStatistics(): array
    {
        $sql = "
            SELECT 
                COUNT(*) as total_panels,
                SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_panels,
                SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as online_panels,
                SUM(CASE WHEN health_status = ? THEN 1 ELSE 0 END) as healthy_panels,
                SUM(current_users) as total_users,
                SUM(CASE WHEN max_users = 0 THEN 999999 ELSE max_users END) as total_capacity,
                AVG(CASE WHEN max_users > 0 THEN (current_users / max_users) * 100 ELSE 0 END) as avg_usage_percentage
            FROM panels
        ";

        $results = $this->database->query($sql, [Panel::STATUS_ONLINE, Panel::HEALTH_HEALTHY]);
        $stats = $results[0] ?? [];

        return [
            'total_panels' => (int) $stats['total_panels'],
            'active_panels' => (int) $stats['active_panels'],
            'online_panels' => (int) $stats['online_panels'],
            'healthy_panels' => (int) $stats['healthy_panels'],
            'total_users' => (int) $stats['total_users'],
            'total_capacity' => (int) $stats['total_capacity'],
            'avg_usage_percentage' => round((float) $stats['avg_usage_percentage'], 2)
        ];
    }

    /**
     * Get panels by health status
     */
    public function getByHealthStatus(string $healthStatus): array
    {
        $results = $this->database->select(
            'panels',
            ['health_status' => $healthStatus],
            ['*'],
            ['priority' => 'ASC', 'name' => 'ASC']
        );

        return array_map(function ($data) {
            return new Panel($this->database, $data);
        }, $results);
    }

    /**
     * Get panels needing health check
     */
    public function getNeedingHealthCheck(int $intervalMinutes = 5): array
    {
        $sql = "
            SELECT * FROM panels 
            WHERE is_active = 1 
            AND (
                last_health_check IS NULL 
                OR last_health_check < DATE_SUB(NOW(), INTERVAL ? MINUTE)
            )
            ORDER BY priority ASC, name ASC
        ";

        $results = $this->database->query($sql, [$intervalMinutes]);

        return array_map(function ($data) {
            return new Panel($this->database, $data);
        }, $results);
    }

    /**
     * Get panels with expired tokens
     */
    public function getWithExpiredTokens(): array
    {
        $sql = "
            SELECT * FROM panels 
            WHERE is_active = 1 
            AND token_expires_at IS NOT NULL 
            AND token_expires_at <= NOW()
            ORDER BY priority ASC, name ASC
        ";

        $results = $this->database->query($sql);

        return array_map(function ($data) {
            return new Panel($this->database, $data);
        }, $results);
    }

    /**
     * Search panels
     */
    public function search(string $query, array $filters = []): array
    {
        $conditions = [];
        $params = [];
        $types = '';

        // Add search query
        if (!empty($query)) {
            $conditions[] = "(name LIKE ? OR url LIKE ? OR type LIKE ?)";
            $searchTerm = "%{$query}%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $types .= 'sss';
        }

        // Add filters
        if (isset($filters['type'])) {
            $conditions[] = "type = ?";
            $params[] = $filters['type'];
            $types .= 's';
        }

        if (isset($filters['status'])) {
            $conditions[] = "status = ?";
            $params[] = $filters['status'];
            $types .= 's';
        }

        if (isset($filters['is_active'])) {
            $conditions[] = "is_active = ?";
            $params[] = $filters['is_active'] ? 1 : 0;
            $types .= 'i';
        }

        $sql = "SELECT * FROM panels";
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }
        $sql .= " ORDER BY priority ASC, name ASC";

        $results = $this->database->query($sql, $params);

        return array_map(function ($data) {
            return new Panel($this->database, $data);
        }, $results);
    }

    /**
     * Get panel usage report
     */
    public function getUsageReport(): array
    {
        $sql = "
            SELECT 
                p.id,
                p.name,
                p.type,
                p.status,
                p.current_users,
                p.max_users,
                CASE 
                    WHEN p.max_users = 0 THEN 0 
                    ELSE (p.current_users / p.max_users) * 100 
                END as usage_percentage,
                COUNT(s.id) as total_services,
                SUM(CASE WHEN s.status = 'active' THEN 1 ELSE 0 END) as active_services
            FROM panels p
            LEFT JOIN services s ON p.id = s.panel_id
            WHERE p.is_active = 1
            GROUP BY p.id
            ORDER BY usage_percentage DESC, p.priority ASC
        ";

        $report = $this->database->query($sql);

        return array_map(function ($row) {
            return [
                'id' => (int) $row['id'],
                'name' => $row['name'],
                'type' => $row['type'],
                'status' => $row['status'],
                'current_users' => (int) $row['current_users'],
                'max_users' => (int) $row['max_users'],
                'usage_percentage' => round((float) $row['usage_percentage'], 2),
                'total_services' => (int) $row['total_services'],
                'active_services' => (int) $row['active_services']
            ];
        }, $report);
    }

    /**
     * Bulk update panel statuses
     */
    public function bulkUpdateStatus(array $panelIds, string $status): int
    {
        if (empty($panelIds)) {
            return 0;
        }

        $placeholders = str_repeat('?,', count($panelIds) - 1) . '?';
        $sql = "
            UPDATE panels 
            SET status = ?, last_health_check = NOW() 
            WHERE id IN ({$placeholders})
        ";

        $params = array_merge([$status], $panelIds);
        return $this->database->execute($sql, $params);
    }

    /**
     * Get panel health summary
     */
    public function getHealthSummary(): array
    {
        $sql = "
            SELECT 
                health_status,
                COUNT(*) as count
            FROM panels 
            WHERE is_active = 1
            GROUP BY health_status
        ";

        $results = $this->database->query($sql);
        $summary = [];

        foreach ($results as $row) {
            $summary[$row['health_status']] = (int) $row['count'];
        }

        return $summary;
    }
}
