<?php

declare(strict_types=1);

namespace WeBot\Microservices\Core;

use WeBot\Core\CacheManager;
use WeBot\Utils\Logger;

/**
 * Rate Limiter
 *
 * Implements rate limiting using sliding window algorithm
 * to control request rates across microservices.
 *
 * @package WeBot\Microservices\Core
 * @version 2.0
 */
class RateLimiter
{
    private CacheManager $cache;
    private Logger $logger;
    private array $config;

    public function __construct(CacheManager $cache, array $config = [])
    {
        $this->cache = $cache;
        $this->logger = Logger::getInstance();
        $this->config = array_merge($this->getDefaultConfig(), $config);
    }

    /**
     * Check if request is allowed
     */
    public function isAllowed(string $identifier, string $resource = 'default', array $limits = null): bool
    {
        $limits = $limits ?? $this->config['default_limits'];

        foreach ($limits as $window => $maxRequests) {
            if (!$this->checkWindow($identifier, $resource, $window, $maxRequests)) {
                $this->logger->warning("Rate limit exceeded", [
                    'identifier' => $identifier,
                    'resource' => $resource,
                    'window' => $window,
                    'limit' => $maxRequests
                ]);

                return false;
            }
        }

        // Record the request
        $this->recordRequest($identifier, $resource);

        return true;
    }

    /**
     * Get current usage for identifier
     */
    public function getCurrentUsage(string $identifier, string $resource = 'default'): array
    {
        $usage = [];

        foreach ($this->config['default_limits'] as $window => $maxRequests) {
            $count = $this->getRequestCount($identifier, $resource, $window);
            $usage[$window] = [
                'current' => $count,
                'limit' => $maxRequests,
                'remaining' => max(0, $maxRequests - $count),
                'reset_time' => time() + $window
            ];
        }

        return $usage;
    }

    /**
     * Reset rate limit for identifier
     */
    public function reset(string $identifier, string $resource = 'default'): void
    {
        foreach (array_keys($this->config['default_limits']) as $window) {
            $key = $this->buildKey($identifier, $resource, $window);
            $this->cache->delete($key);
        }

        $this->logger->info("Rate limit reset", [
            'identifier' => $identifier,
            'resource' => $resource
        ]);
    }

    /**
     * Get rate limit statistics
     */
    public function getStatistics(string $identifier, string $resource = 'default'): array
    {
        $stats = [
            'identifier' => $identifier,
            'resource' => $resource,
            'windows' => [],
            'total_requests' => 0,
            'blocked_requests' => 0
        ];

        foreach ($this->config['default_limits'] as $window => $maxRequests) {
            $key = $this->buildKey($identifier, $resource, $window);
            $data = $this->cache->get($key, ['count' => 0, 'blocked' => 0]);

            $stats['windows'][$window] = [
                'requests' => $data['count'],
                'blocked' => $data['blocked'],
                'limit' => $maxRequests,
                'window_size' => $window
            ];

            $stats['total_requests'] += $data['count'];
            $stats['blocked_requests'] += $data['blocked'];
        }

        return $stats;
    }

    /**
     * Check if request fits within window
     */
    private function checkWindow(string $identifier, string $resource, int $window, int $maxRequests): bool
    {
        $count = $this->getRequestCount($identifier, $resource, $window);
        return $count < $maxRequests;
    }

    /**
     * Get request count for window
     */
    private function getRequestCount(string $identifier, string $resource, int $window): int
    {
        $key = $this->buildKey($identifier, $resource, $window);
        $data = $this->cache->get($key);

        if ($data === null) {
            return 0;
        }

        // Clean old entries
        $cutoff = time() - $window;
        $data['requests'] = array_filter($data['requests'], function ($timestamp) use ($cutoff) {
            return $timestamp > $cutoff;
        });

        // Update cache with cleaned data
        $this->cache->set($key, $data, $window);

        return count($data['requests']);
    }

    /**
     * Record a request
     */
    private function recordRequest(string $identifier, string $resource): void
    {
        $timestamp = time();

        foreach (array_keys($this->config['default_limits']) as $window) {
            $key = $this->buildKey($identifier, $resource, $window);
            $data = $this->cache->get($key, ['requests' => [], 'count' => 0]);

            // Add current request
            $data['requests'][] = $timestamp;
            $data['count']++;

            // Clean old entries
            $cutoff = $timestamp - $window;
            $data['requests'] = array_filter($data['requests'], function ($ts) use ($cutoff) {
                return $ts > $cutoff;
            });

            // Update cache
            $this->cache->set($key, $data, $window);
        }
    }

    /**
     * Record a blocked request
     */
    private function recordBlocked(string $identifier, string $resource): void
    {
        foreach (array_keys($this->config['default_limits']) as $window) {
            $key = $this->buildKey($identifier, $resource, $window);
            $data = $this->cache->get($key, ['requests' => [], 'count' => 0, 'blocked' => 0]);

            $data['blocked']++;

            $this->cache->set($key, $data, $window);
        }
    }

    /**
     * Build cache key
     */
    private function buildKey(string $identifier, string $resource, int $window): string
    {
        return "rate_limit:{$identifier}:{$resource}:{$window}";
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'default_limits' => [
                60 => 100,      // 100 requests per minute
                3600 => 1000,   // 1000 requests per hour
                86400 => 10000  // 10000 requests per day
            ],
            'burst_limits' => [
                1 => 10,        // 10 requests per second (burst)
                60 => 200       // 200 requests per minute (burst)
            ]
        ];
    }
}
