<?php

declare(strict_types=1);

namespace WeBot\Core\Cache\Contracts;

/**
 * Cache Interface
 *
 * Defines the contract for cache implementations.
 *
 * @package WeBot\Core\Cache\Contracts
 * @version 2.0
 */
interface CacheInterface
{
    /**
     * Get cached value
     */
    public function get(string $key, $default = null);

    /**
     * Set cached value
     */
    public function set(string $key, $value, ?int $ttl = null): bool;

    /**
     * Delete cached value
     */
    public function delete(string $key): bool;

    /**
     * Check if key exists
     */
    public function exists(string $key): bool;

    /**
     * Increment value
     */
    public function increment(string $key, int $value = 1): int;

    /**
     * Decrement value
     */
    public function decrement(string $key, int $value = 1): int;

    /**
     * Set multiple values
     */
    public function setMultiple(array $values, ?int $ttl = null): bool;

    /**
     * Get multiple values
     */
    public function getMultiple(array $keys, $default = null): array;

    /**
     * Flush all cache
     */
    public function flush(): bool;
}
