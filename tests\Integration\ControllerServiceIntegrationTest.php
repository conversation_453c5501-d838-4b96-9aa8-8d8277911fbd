<?php

declare(strict_types=1);

namespace WeBot\Tests\Integration;

use WeBot\Core\Application;
use WeBot\Core\Config;
use WeBot\Controllers\UserController;
use WeBot\Controllers\AdminController;
use WeBot\Controllers\PaymentController;
use WeBot\Controllers\ServiceController;
use WeBot\Services\TelegramService;
use WeBot\Services\DatabaseService;
use WeBot\Services\PaymentService;
use WeBot\Services\PanelService;

/**
 * Controller-Service Integration Test
 * 
 * Tests the integration between Controllers and Services,
 * verifying proper dependency injection and data flow.
 * 
 * @package WeBot\Tests\Integration
 * @version 2.0
 */
class ControllerServiceIntegrationTest
{
    private $container;
    private Config $config;
    private array $testResults = [];
    private int $totalTests = 0;
    private int $passedTests = 0;
    private int $failedTests = 0;

    public function __construct()
    {
        $this->setupTestEnvironment();
        $this->initializeContainer();
    }

    /**
     * Setup test environment
     */
    private function setupTestEnvironment(): void
    {
        // Set test mode
        putenv('WEBOT_TEST_MODE=true');
        putenv('DISABLE_EXTERNAL_APIS=true');
        
        // Mock configuration
        $this->config = new Config();
    }

    /**
     * Initialize dependency injection container
     */
    private function initializeContainer(): void
    {
        // Create a simple mock container
        $this->container = new class {
            private array $services = [];

            public function add(string $name, $service): void {
                $this->services[$name] = $service;
            }

            public function get(string $name) {
                if (isset($this->services[$name])) {
                    $service = $this->services[$name];
                    return is_callable($service) ? $service() : $service;
                }
                return null;
            }
        };
        
        // Register config
        $this->container->add('config', $this->config);
        
        // Register services with mocks for testing
        $this->container->add('telegram', function() {
            return new TelegramService($this->config);
        });
        
        $this->container->add('database', function() {
            return $this->createMockDatabaseService();
        });
        
        $this->container->add('payment', function() {
            return new PaymentService($this->config, $this->container->get('database'));
        });
        
        $this->container->add('panel', function() {
            return new PanelService($this->config, $this->container->get('database'));
        });
        
        $this->container->add('logger', function() {
            return $this->createMockLogger();
        });
    }

    /**
     * Create mock database service
     */
    private function createMockDatabaseService(): object
    {
        return new class {
            public function prepare(string $sql): object {
                return new class {
                    public function bind_param(string $types, ...$params): bool { return true; }
                    public function execute(): bool { return true; }
                    public function get_result(): object {
                        return new class {
                            public function fetch_assoc(): ?array {
                                return ['userid' => 123, 'first_name' => 'Test', 'wallet' => 50000];
                            }
                            public function fetch_all(int $mode): array {
                                return [['id' => 1, 'name' => 'Test']];
                            }
                        };
                    }
                    public function close(): void {}
                };
            }
            
            public function fetchRow(string $sql, array $params = [], string $types = ''): ?array {
                return ['userid' => 123, 'first_name' => 'Test', 'wallet' => 50000];
            }
            
            public function fetchAll(string $sql, array $params = [], string $types = ''): array {
                return [['id' => 1, 'name' => 'Test']];
            }
            
            public function insert(string $table, array $data): int { return 1; }
            public function update(string $table, array $data, array $where): int { return 1; }
            public function delete(string $table, array $where): int { return 1; }
            public function getAffectedRows(): int { return 1; }
            public function getLastInsertId(): int { return 1; }
        };
    }

    /**
     * Create mock logger
     */
    private function createMockLogger(): object
    {
        return new class {
            public function info(string $message, array $context = []): void {}
            public function error(string $message, array $context = []): void {}
            public function debug(string $message, array $context = []): void {}
            public function warning(string $message, array $context = []): void {}
        };
    }

    /**
     * Run all integration tests
     */
    public function runAllTests(): array
    {
        echo "🧪 Controller-Service Integration Tests\n";
        echo "=====================================\n\n";

        $this->testUserControllerIntegration();
        $this->testAdminControllerIntegration();
        $this->testPaymentControllerIntegration();
        $this->testServiceControllerIntegration();
        $this->testDependencyInjection();
        $this->testDataFlow();

        return $this->getTestResults();
    }

    /**
     * Test UserController integration
     */
    private function testUserControllerIntegration(): void
    {
        $this->runTest('UserController Integration', function() {
            $controller = new UserController($this->container);
            
            // Test handleStart method
            $mockMessage = [
                'from' => ['id' => 123, 'first_name' => 'Test'],
                'chat' => ['id' => 123],
                'message_id' => 1,
                'text' => '/start'
            ];
            
            $result = $controller->handleStart($mockMessage);
            
            // Verify result structure
            if (!is_array($result)) {
                throw new \Exception('handleStart should return array');
            }
            
            return true;
        });
    }

    /**
     * Test AdminController integration
     */
    private function testAdminControllerIntegration(): void
    {
        $this->runTest('AdminController Integration', function() {
            $controller = new AdminController($this->container);
            
            // Test handleAdmin method
            $mockMessage = [
                'from' => ['id' => 123, 'first_name' => 'Admin'],
                'chat' => ['id' => 123],
                'message_id' => 1,
                'text' => '/admin'
            ];
            
            $result = $controller->handleAdmin($mockMessage);
            
            if (!is_array($result)) {
                throw new \Exception('handleAdmin should return array');
            }
            
            return true;
        });
    }

    /**
     * Test PaymentController integration
     */
    private function testPaymentControllerIntegration(): void
    {
        $this->runTest('PaymentController Integration', function() {
            $controller = new PaymentController($this->container);
            
            // Test handleCallback method
            $mockCallback = [
                'from' => ['id' => 123],
                'message' => ['chat' => ['id' => 123], 'message_id' => 1],
                'data' => 'my_wallet'
            ];
            
            $result = $controller->handleCallback($mockCallback);
            
            if (!is_array($result)) {
                throw new \Exception('handleCallback should return array');
            }
            
            return true;
        });
    }

    /**
     * Test ServiceController integration
     */
    private function testServiceControllerIntegration(): void
    {
        $this->runTest('ServiceController Integration', function() {
            $controller = new ServiceController($this->container);
            
            // Test handleCallback method
            $mockCallback = [
                'from' => ['id' => 123],
                'message' => ['chat' => ['id' => 123], 'message_id' => 1],
                'data' => 'my_services'
            ];
            
            $result = $controller->handleCallback($mockCallback);
            
            if (!is_array($result)) {
                throw new \Exception('handleCallback should return array');
            }
            
            return true;
        });
    }

    /**
     * Test dependency injection
     */
    private function testDependencyInjection(): void
    {
        $this->runTest('Dependency Injection', function() {
            // Test that all services are properly injected
            $userController = new UserController($this->container);
            $adminController = new AdminController($this->container);
            $paymentController = new PaymentController($this->container);
            $serviceController = new ServiceController($this->container);
            
            // Verify controllers are instantiated without errors
            if (!($userController instanceof UserController)) {
                throw new \Exception('UserController not properly instantiated');
            }
            
            if (!($adminController instanceof AdminController)) {
                throw new \Exception('AdminController not properly instantiated');
            }
            
            if (!($paymentController instanceof PaymentController)) {
                throw new \Exception('PaymentController not properly instantiated');
            }
            
            if (!($serviceController instanceof ServiceController)) {
                throw new \Exception('ServiceController not properly instantiated');
            }
            
            return true;
        });
    }

    /**
     * Test data flow between controllers and services
     */
    private function testDataFlow(): void
    {
        $this->runTest('Data Flow', function() {
            // Test that data flows correctly from controller to service
            $telegramService = $this->container->get('telegram');
            $databaseService = $this->container->get('database');
            $paymentService = $this->container->get('payment');
            $panelService = $this->container->get('panel');
            
            // Verify services are accessible
            if (!$telegramService) {
                throw new \Exception('TelegramService not accessible');
            }
            
            if (!$databaseService) {
                throw new \Exception('DatabaseService not accessible');
            }
            
            if (!$paymentService) {
                throw new \Exception('PaymentService not accessible');
            }
            
            if (!$panelService) {
                throw new \Exception('PanelService not accessible');
            }
            
            return true;
        });
    }

    /**
     * Run individual test
     */
    private function runTest(string $testName, callable $test): void
    {
        $this->totalTests++;
        
        try {
            $result = $test();
            
            if ($result === true) {
                $this->passedTests++;
                $this->testResults[] = ['name' => $testName, 'status' => 'PASS', 'error' => null];
                echo "✅ {$testName}\n";
            } else {
                $this->failedTests++;
                $this->testResults[] = ['name' => $testName, 'status' => 'FAIL', 'error' => 'Test returned false'];
                echo "❌ {$testName}: Test returned false\n";
            }
        } catch (\Throwable $e) {
            $this->failedTests++;
            $this->testResults[] = ['name' => $testName, 'status' => 'FAIL', 'error' => $e->getMessage()];
            echo "❌ {$testName}: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Get test results
     */
    private function getTestResults(): array
    {
        $successRate = $this->totalTests > 0 ? round(($this->passedTests / $this->totalTests) * 100, 2) : 0;
        
        return [
            'total_tests' => $this->totalTests,
            'passed_tests' => $this->passedTests,
            'failed_tests' => $this->failedTests,
            'success_rate' => $successRate,
            'results' => $this->testResults
        ];
    }

    /**
     * Print test summary
     */
    public function printSummary(): void
    {
        $results = $this->getTestResults();
        
        echo "\n📊 Test Summary:\n";
        echo "================\n";
        echo "Total Tests: {$results['total_tests']}\n";
        echo "Passed: {$results['passed_tests']}\n";
        echo "Failed: {$results['failed_tests']}\n";
        echo "Success Rate: {$results['success_rate']}%\n";
        
        if ($results['failed_tests'] > 0) {
            echo "\n❌ Failed Tests:\n";
            foreach ($results['results'] as $result) {
                if ($result['status'] === 'FAIL') {
                    echo "  - {$result['name']}: {$result['error']}\n";
                }
            }
            echo "\n🔴 Integration Test: FAILED\n";
        } else {
            echo "\n🟢 Integration Test: PASSED\n";
        }
    }
}

// Run tests if executed directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'] ?? '')) {
    $tester = new ControllerServiceIntegrationTest();
    $tester->runAllTests();
    $tester->printSummary();
}
