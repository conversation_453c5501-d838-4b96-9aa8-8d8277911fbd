<?php

declare(strict_types=1);

namespace WeBot\Services;

use WeBot\Core\CacheManager;
use WeBot\Services\MultiLevelCacheService;
use WeBot\Utils\Logger;
use WeBot\Events\EventDispatcher;

/**
 * Cache Invalidation Service
 *
 * Manages intelligent cache invalidation strategies including
 * tag-based invalidation, dependency tracking, and event-driven clearing.
 *
 * @package WeBot\Services
 * @version 2.0
 */
class CacheInvalidationService
{
    private MultiLevelCacheService $cache;
    private CacheManager $redisCache;
    private Logger $logger;
    private EventDispatcher $eventDispatcher;
    private array $config;
    private array $tagMappings = [];
    private array $dependencyGraph = [];
    private array $invalidationRules = [];

    public function __construct(
        MultiLevelCacheService $cache,
        CacheManager $redisCache,
        EventDispatcher $eventDispatcher,
        array $config = []
    ) {
        $this->cache = $cache;
        $this->redisCache = $redisCache;
        $this->logger = Logger::getInstance();
        $this->eventDispatcher = $eventDispatcher;
        $this->config = array_merge($this->getDefaultConfig(), $config);

        $this->initializeInvalidationRules();
        $this->setupEventListeners();
    }

    /**
     * Invalidate cache by tags
     */
    public function invalidateByTags(array $tags): array
    {
        $invalidated = [];

        foreach ($tags as $tag) {
            $keys = $this->getKeysByTag($tag);

            foreach ($keys as $key) {
                if ($this->cache->delete($key)) {
                    $invalidated[] = $key;
                }
            }

            // Remove tag mapping
            $this->removeTagMapping($tag);
        }

        $this->logger->info('Cache invalidated by tags', [
            'tags' => $tags,
            'invalidated_keys' => count($invalidated)
        ]);

        return $invalidated;
    }

    /**
     * Invalidate cache by pattern
     */
    public function invalidateByPattern(string $pattern): array
    {
        $invalidated = [];

        // Get keys matching pattern
        $keys = $this->redisCache->getKeysByPattern($pattern);

        foreach ($keys as $key) {
            if ($this->cache->delete($key)) {
                $invalidated[] = $key;
            }
        }

        $this->logger->info('Cache invalidated by pattern', [
            'pattern' => $pattern,
            'invalidated_keys' => count($invalidated)
        ]);

        return $invalidated;
    }

    /**
     * Invalidate cache by dependency
     */
    public function invalidateByDependency(string $dependency): array
    {
        $invalidated = [];

        // Get all keys that depend on this dependency
        $dependentKeys = $this->getDependentKeys($dependency);

        foreach ($dependentKeys as $key) {
            if ($this->cache->delete($key)) {
                $invalidated[] = $key;
            }
        }

        // Cascade to dependent dependencies
        $cascadeInvalidated = $this->cascadeInvalidation($dependency);
        $invalidated = array_merge($invalidated, $cascadeInvalidated);

        $this->logger->info('Cache invalidated by dependency', [
            'dependency' => $dependency,
            'invalidated_keys' => count($invalidated)
        ]);

        return $invalidated;
    }

    /**
     * Smart invalidation based on data changes
     */
    public function smartInvalidate(string $entity, string $action, array $data = []): array
    {
        $invalidated = [];

        // Apply invalidation rules
        $rules = $this->getInvalidationRules($entity, $action);

        foreach ($rules as $rule) {
            $ruleInvalidated = $this->applyInvalidationRule($rule, $data);
            $invalidated = array_merge($invalidated, $ruleInvalidated);
        }

        // Dispatch invalidation event
        $this->eventDispatcher->dispatchByName('cache.invalidated', [
            'entity' => $entity,
            'action' => $action,
            'data' => $data,
            'invalidated_keys' => $invalidated
        ]);

        $this->logger->info('Smart cache invalidation', [
            'entity' => $entity,
            'action' => $action,
            'invalidated_keys' => count($invalidated)
        ]);

        return $invalidated;
    }

    /**
     * Register cache key with tags
     */
    public function registerKeyWithTags(string $key, array $tags): void
    {
        foreach ($tags as $tag) {
            if (!isset($this->tagMappings[$tag])) {
                $this->tagMappings[$tag] = [];
            }

            $this->tagMappings[$tag][] = $key;
        }

        // Store tag mappings in Redis for persistence
        $this->storeTagMappings();
    }

    /**
     * Register cache dependency
     */
    public function registerDependency(string $key, array $dependencies): void
    {
        foreach ($dependencies as $dependency) {
            if (!isset($this->dependencyGraph[$dependency])) {
                $this->dependencyGraph[$dependency] = [];
            }

            $this->dependencyGraph[$dependency][] = $key;
        }

        // Store dependency graph in Redis
        $this->storeDependencyGraph();
    }

    /**
     * Add invalidation rule
     */
    public function addInvalidationRule(string $entity, string $action, array $rule): void
    {
        $ruleKey = "{$entity}.{$action}";

        if (!isset($this->invalidationRules[$ruleKey])) {
            $this->invalidationRules[$ruleKey] = [];
        }

        $this->invalidationRules[$ruleKey][] = $rule;

        // Store rules in Redis
        $this->storeInvalidationRules();

        $this->logger->debug('Invalidation rule added', [
            'entity' => $entity,
            'action' => $action,
            'rule' => $rule
        ]);
    }

    /**
     * Time-based invalidation
     */
    public function scheduleInvalidation(string $key, int $timestamp): void
    {
        $scheduleKey = "invalidation:schedule:{$timestamp}";

        $scheduled = $this->redisCache->get($scheduleKey, []);
        $scheduled[] = $key;

        $this->redisCache->set($scheduleKey, $scheduled, $timestamp - time());

        $this->logger->debug('Cache invalidation scheduled', [
            'key' => $key,
            'timestamp' => $timestamp
        ]);
    }

    /**
     * Process scheduled invalidations
     */
    public function processScheduledInvalidations(): array
    {
        $processed = [];
        $now = time();

        // Get all scheduled invalidation keys
        $scheduleKeys = $this->redisCache->getKeysByPattern('invalidation:schedule:*');

        foreach ($scheduleKeys as $scheduleKey) {
            $timestamp = (int)str_replace('invalidation:schedule:', '', $scheduleKey);

            if ($timestamp <= $now) {
                $keysToInvalidate = $this->redisCache->get($scheduleKey, []);

                foreach ($keysToInvalidate as $key) {
                    if ($this->cache->delete($key)) {
                        $processed[] = $key;
                    }
                }

                // Remove the schedule entry
                $this->redisCache->delete($scheduleKey);
            }
        }

        if (!empty($processed)) {
            $this->logger->info('Processed scheduled invalidations', [
                'invalidated_keys' => count($processed)
            ]);
        }

        return $processed;
    }

    /**
     * Conditional invalidation
     */
    public function conditionalInvalidate(string $key, callable $condition): bool
    {
        if ($condition()) {
            $success = $this->cache->delete($key);

            $this->logger->debug('Conditional invalidation', [
                'key' => $key,
                'success' => $success
            ]);

            return $success;
        }

        return false;
    }

    /**
     * Bulk invalidation with batching
     */
    public function bulkInvalidate(array $keys, int $batchSize = 100): array
    {
        $invalidated = [];
        $batches = array_chunk($keys, $batchSize);

        foreach ($batches as $batch) {
            foreach ($batch as $key) {
                if ($this->cache->delete($key)) {
                    $invalidated[] = $key;
                }
            }

            // Small delay between batches to prevent overwhelming the system
            if (count($batches) > 1) {
                usleep(10000); // 10ms
            }
        }

        $this->logger->info('Bulk cache invalidation', [
            'total_keys' => count($keys),
            'invalidated_keys' => count($invalidated),
            'batches' => count($batches)
        ]);

        return $invalidated;
    }

    /**
     * Get invalidation statistics
     */
    public function getInvalidationStats(): array
    {
        return [
            'tag_mappings' => count($this->tagMappings),
            'dependency_graph_nodes' => count($this->dependencyGraph),
            'invalidation_rules' => count($this->invalidationRules),
            'scheduled_invalidations' => $this->getScheduledInvalidationsCount(),
            'recent_invalidations' => $this->getRecentInvalidationsCount()
        ];
    }

    /**
     * Initialize invalidation rules
     */
    private function initializeInvalidationRules(): void
    {
        // Load rules from Redis
        $storedRules = $this->redisCache->get('invalidation:rules', []);
        $this->invalidationRules = $storedRules;

        // Load tag mappings
        $storedMappings = $this->redisCache->get('invalidation:tag_mappings', []);
        $this->tagMappings = $storedMappings;

        // Load dependency graph
        $storedGraph = $this->redisCache->get('invalidation:dependency_graph', []);
        $this->dependencyGraph = $storedGraph;

        // Add default rules
        $this->addDefaultInvalidationRules();
    }

    /**
     * Add default invalidation rules
     */
    private function addDefaultInvalidationRules(): void
    {
        // User-related invalidations
        $this->addInvalidationRule('user', 'update', [
            'type' => 'tags',
            'tags' => ['users', 'user:{user_id}']
        ]);

        $this->addInvalidationRule('user', 'delete', [
            'type' => 'pattern',
            'pattern' => 'user:{user_id}:*'
        ]);

        // Service-related invalidations
        $this->addInvalidationRule('service', 'create', [
            'type' => 'tags',
            'tags' => ['services', 'user:{user_id}:services']
        ]);

        $this->addInvalidationRule('service', 'update', [
            'type' => 'tags',
            'tags' => ['services', 'service:{service_id}']
        ]);

        // Payment-related invalidations
        $this->addInvalidationRule('payment', 'create', [
            'type' => 'tags',
            'tags' => ['payments', 'user:{user_id}:payments', 'user:{user_id}:balance']
        ]);
    }

    /**
     * Setup event listeners
     */
    private function setupEventListeners(): void
    {
        // Listen for data change events
        $this->eventDispatcher->addListener('user.updated', function ($event) {
            $this->smartInvalidate('user', 'update', $event);
        });

        $this->eventDispatcher->addListener('service.created', function ($event) {
            $this->smartInvalidate('service', 'create', $event);
        });

        $this->eventDispatcher->addListener('payment.completed', function ($event) {
            $this->smartInvalidate('payment', 'create', $event);
        });
    }

    /**
     * Get keys by tag
     */
    private function getKeysByTag(string $tag): array
    {
        return $this->tagMappings[$tag] ?? [];
    }

    /**
     * Remove tag mapping
     */
    private function removeTagMapping(string $tag): void
    {
        unset($this->tagMappings[$tag]);
        $this->storeTagMappings();
    }

    /**
     * Get dependent keys
     */
    private function getDependentKeys(string $dependency): array
    {
        return $this->dependencyGraph[$dependency] ?? [];
    }

    /**
     * Cascade invalidation
     */
    private function cascadeInvalidation(string $dependency): array
    {
        $invalidated = [];

        // Find dependencies that depend on this dependency
        foreach ($this->dependencyGraph as $dep => $keys) {
            if (in_array($dependency, $keys)) {
                $cascadeInvalidated = $this->invalidateByDependency($dep);
                $invalidated = array_merge($invalidated, $cascadeInvalidated);
            }
        }

        return $invalidated;
    }

    /**
     * Get invalidation rules
     */
    private function getInvalidationRules(string $entity, string $action): array
    {
        $ruleKey = "{$entity}.{$action}";
        return $this->invalidationRules[$ruleKey] ?? [];
    }

    /**
     * Apply invalidation rule
     */
    private function applyInvalidationRule(array $rule, array $data): array
    {
        $invalidated = [];

        switch ($rule['type']) {
            case 'tags':
                $tags = $this->interpolateTags($rule['tags'], $data);
                $invalidated = $this->invalidateByTags($tags);
                break;

            case 'pattern':
                $pattern = $this->interpolatePattern($rule['pattern'], $data);
                $invalidated = $this->invalidateByPattern($pattern);
                break;

            case 'dependency':
                $dependency = $this->interpolatePattern($rule['dependency'], $data);
                $invalidated = $this->invalidateByDependency($dependency);
                break;
        }

        return $invalidated;
    }

    /**
     * Interpolate tags with data
     */
    private function interpolateTags(array $tags, array $data): array
    {
        return array_map(function ($tag) use ($data) {
            return $this->interpolateString($tag, $data);
        }, $tags);
    }

    /**
     * Interpolate pattern with data
     */
    private function interpolatePattern(string $pattern, array $data): string
    {
        return $this->interpolateString($pattern, $data);
    }

    /**
     * Interpolate string with data
     */
    private function interpolateString(string $string, array $data): string
    {
        foreach ($data as $key => $value) {
            $string = str_replace("{{$key}}", (string)$value, $string);
        }
        return $string;
    }

    /**
     * Store tag mappings
     */
    private function storeTagMappings(): void
    {
        $this->redisCache->set('invalidation:tag_mappings', $this->tagMappings, 86400);
    }

    /**
     * Store dependency graph
     */
    private function storeDependencyGraph(): void
    {
        $this->redisCache->set('invalidation:dependency_graph', $this->dependencyGraph, 86400);
    }

    /**
     * Store invalidation rules
     */
    private function storeInvalidationRules(): void
    {
        $this->redisCache->set('invalidation:rules', $this->invalidationRules, 86400);
    }

    /**
     * Get scheduled invalidations count
     */
    private function getScheduledInvalidationsCount(): int
    {
        $scheduleKeys = $this->redisCache->getKeysByPattern('invalidation:schedule:*');
        return count($scheduleKeys);
    }

    /**
     * Get recent invalidations count
     */
    private function getRecentInvalidationsCount(): int
    {
        // This would be implemented based on your logging/metrics system
        return 0;
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'batch_size' => 100,
            'cascade_enabled' => true,
            'event_driven' => true,
            'scheduled_processing_interval' => 60, // seconds
            'max_dependency_depth' => 5
        ];
    }
}
