<?php

declare(strict_types=1);

/**
 * Cache Configuration
 * 
 * @package WeBot\Config
 * @version 2.0
 */

return [
    /*
    |--------------------------------------------------------------------------
    | Default Cache Store
    |--------------------------------------------------------------------------
    */
    'default' => $_ENV['CACHE_DRIVER'] ?? 'file',

    /*
    |--------------------------------------------------------------------------
    | Cache Stores
    |--------------------------------------------------------------------------
    */
    'stores' => [
        'array' => [
            'driver' => 'array',
            'serialize' => false,
        ],

        'file' => [
            'driver' => 'file',
            'path' => storage_path('cache'),
        ],

        'redis' => [
            'driver' => 'redis',
            'connection' => 'cache',
            'lock_connection' => 'default',
        ],

        'memcached' => [
            'driver' => 'memcached',
            'persistent_id' => $_ENV['MEMCACHED_PERSISTENT_ID'] ?? null,
            'sasl' => [
                $_ENV['MEMCACHED_USERNAME'] ?? null,
                $_ENV['MEMCACHED_PASSWORD'] ?? null,
            ],
            'options' => [
                // Memcached::OPT_CONNECT_TIMEOUT => 2000,
            ],
            'servers' => [
                [
                    'host' => $_ENV['MEMCACHED_HOST'] ?? '127.0.0.1',
                    'port' => $_ENV['MEMCACHED_PORT'] ?? 11211,
                    'weight' => 100,
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Key Prefix
    |--------------------------------------------------------------------------
    */
    'prefix' => $_ENV['CACHE_PREFIX'] ?? 'webot_cache',

    /*
    |--------------------------------------------------------------------------
    | Cache TTL (Time To Live)
    |--------------------------------------------------------------------------
    */
    'ttl' => [
        'default' => (int) ($_ENV['CACHE_TTL'] ?? 3600),
        'short' => 300,    // 5 minutes
        'medium' => 1800,  // 30 minutes
        'long' => 86400,   // 24 hours
        'permanent' => 0,  // Never expire
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Tags
    |--------------------------------------------------------------------------
    */
    'tags' => [
        'users' => 'users',
        'services' => 'services',
        'payments' => 'payments',
        'panels' => 'panels',
        'settings' => 'settings',
    ],
];
