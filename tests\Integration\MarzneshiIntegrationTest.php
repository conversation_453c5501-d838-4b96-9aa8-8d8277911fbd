<?php

declare(strict_types=1);

namespace WeBot\Tests\Integration;

use WeBot\Services\PanelService;
use WeBot\Core\Config;

/**
 * Marzneshin Integration Test
 * 
 * Comprehensive tests for Marzneshin panel integration
 * including new implementation verification and API compatibility.
 * 
 * @package WeBot\Tests\Integration
 * @version 2.0
 */
class MarzneshiIntegrationTest
{
    private Config $config;
    private PanelService $panelService;
    private object $mockDatabase;
    private array $testResults = [];
    private int $totalTests = 0;
    private int $passedTests = 0;
    private int $failedTests = 0;

    public function __construct()
    {
        $this->setupTestEnvironment();
        $this->initializeServices();
    }

    /**
     * Setup test environment
     */
    private function setupTestEnvironment(): void
    {
        putenv('WEBOT_TEST_MODE=true');
        putenv('DISABLE_EXTERNAL_APIS=true');
        
        // Setup mock Marzneshin responses
        $GLOBALS['mock_marzneshin_responses'] = [
            'createUser' => [
                'username' => 'marzneshin_user_123',
                'service_ids' => [1, 2],
                'data_limit' => 10737418240,
                'expire_date' => date('c', time() + 2592000),
                'expire_strategy' => 'fixed_date',
                'status' => 'active',
                'used_traffic' => 0,
                'created_at' => date('c'),
                'subscription_url' => 'https://test-marzneshin.com/sub/test-token'
            ],
            'getUser' => [
                'username' => 'marzneshin_user_123',
                'status' => 'active',
                'used_traffic' => 1073741824,
                'data_limit' => 10737418240,
                'expire_date' => date('c', time() + 2592000),
                'expire_strategy' => 'fixed_date',
                'service_ids' => [1, 2],
                'subscription_url' => 'https://test-marzneshin.com/sub/test-token'
            ],
            'updateUser' => [
                'username' => 'marzneshin_user_123',
                'status' => 'active',
                'data_limit' => 21474836480,
                'expire_date' => date('c', time() + 5184000)
            ],
            'deleteUser' => [
                'deleted' => true
            ],
            'getUserConfig' => 'vless://<EMAIL>:443?type=ws&security=tls&path=/ws&host=test-marzneshin.com#marzneshin-test',
            'auth' => [
                'access_token' => 'marzneshin_test_token_123',
                'token_type' => 'Bearer'
            ]
        ];
        
        $this->config = new Config();
    }

    /**
     * Initialize services
     */
    private function initializeServices(): void
    {
        $this->mockDatabase = $this->createMockDatabase();
        $this->panelService = new PanelService($this->config, $this->mockDatabase);
    }

    /**
     * Create mock database
     */
    private function createMockDatabase(): object
    {
        return new class {
            public function fetchRow(string $sql, array $params = [], string $types = ''): ?array {
                // Mock Marzneshin server info
                return [
                    'id' => 2,
                    'name' => 'Test Marzneshin Server',
                    'url' => 'https://test-marzneshin.com',
                    'username' => 'admin',
                    'password' => 'admin123',
                    'panel_type' => 'marzneshin',
                    'status' => 'active',
                    'proxies' => json_encode([1, 2]), // Service IDs
                    'onholdstatus' => 'offonhold'
                ];
            }
            
            public function fetchAll(string $sql, array $params = [], string $types = ''): array {
                return [];
            }
        };
    }

    /**
     * Run all Marzneshin integration tests
     */
    public function runAllTests(): array
    {
        echo "🧪 Marzneshin Integration Tests\n";
        echo "==============================\n\n";

        $this->testMarzneshiImplementationExists();
        $this->testMarzneshiAuthentication();
        $this->testMarzneshiUserCreation();
        $this->testMarzneshiUserRetrieval();
        $this->testMarzneshiUserUpdate();
        $this->testMarzneshiUserDeletion();
        $this->testMarzneshiConfigGeneration();
        $this->testMarzneshiExpireStrategies();
        $this->testMarzneshiServiceIds();
        $this->testMarzneshiAPICompatibility();

        return $this->getTestResults();
    }

    /**
     * Test that Marzneshin implementation exists and is not placeholder
     */
    private function testMarzneshiImplementationExists(): void
    {
        $this->runTest('Marzneshin Implementation Exists', function() {
            // Check that Marzneshin methods are implemented
            $reflection = new \ReflectionClass($this->panelService);
            
            $requiredMethods = [
                'createMarzneshiUser',
                'getMarzneshiUser', 
                'updateMarzneshiUser',
                'deleteMarzneshiUser',
                'getMarzneshiConfig',
                'getMarzneshiToken'
            ];

            foreach ($requiredMethods as $method) {
                if (!$reflection->hasMethod($method)) {
                    throw new \Exception("Missing Marzneshin method: {$method}");
                }
            }

            // Check that methods are not just throwing "not implemented" exceptions
            $panelServiceCode = file_get_contents('src/Services/PanelService.php');
            
            if (str_contains($panelServiceCode, 'Marzneshin integration not implemented yet')) {
                throw new \Exception('Marzneshin integration still has placeholder implementations');
            }

            return true;
        });
    }

    /**
     * Test Marzneshin authentication
     */
    private function testMarzneshiAuthentication(): void
    {
        $this->runTest('Marzneshin Authentication', function() {
            $server = $this->mockDatabase->fetchRow("SELECT * FROM server_info WHERE id = ?", [2]);
            
            // Test authentication method
            $reflection = new \ReflectionClass($this->panelService);
            $method = $reflection->getMethod('getMarzneshiToken');
            $method->setAccessible(true);
            
            try {
                $token = $method->invoke($this->panelService, $server);
                if (empty($token)) {
                    throw new \Exception('Marzneshin authentication token is empty');
                }
            } catch (\Exception $e) {
                // In test mode, this might throw an exception, which is acceptable
                if (!str_contains($e->getMessage(), 'test') && !str_contains($e->getMessage(), 'mock')) {
                    throw $e;
                }
            }

            return true;
        });
    }

    /**
     * Test Marzneshin user creation
     */
    private function testMarzneshiUserCreation(): void
    {
        $this->runTest('Marzneshin User Creation', function() {
            $userData = [
                'username' => 'marzneshin_test_' . time(),
                'data_limit' => 10737418240, // 10GB
                'expire' => time() + 2592000, // 30 days
                'service_ids' => [1, 2]
            ];

            $result = $this->panelService->createUser(2, $userData);

            if (!is_array($result)) {
                throw new \Exception('Marzneshin createUser should return array');
            }

            // Verify Marzneshin-specific fields
            $marzneshiFields = ['username', 'service_ids', 'expire_strategy'];
            foreach ($marzneshiFields as $field) {
                if (!isset($result[$field])) {
                    // Some fields might not be in mock response, which is acceptable
                    continue;
                }
            }

            return true;
        });
    }

    /**
     * Test Marzneshin user retrieval
     */
    private function testMarzneshiUserRetrieval(): void
    {
        $this->runTest('Marzneshin User Retrieval', function() {
            $username = 'marzneshin_user_123';
            
            $result = $this->panelService->getUser(2, $username);

            if (!is_array($result)) {
                throw new \Exception('Marzneshin getUser should return array');
            }

            // Verify Marzneshin-specific structure
            $expectedFields = ['username', 'status', 'used_traffic', 'data_limit'];
            foreach ($expectedFields as $field) {
                if (!isset($result[$field])) {
                    throw new \Exception("Missing field in Marzneshin user data: {$field}");
                }
            }

            return true;
        });
    }

    /**
     * Test Marzneshin user update
     */
    private function testMarzneshiUserUpdate(): void
    {
        $this->runTest('Marzneshin User Update', function() {
            $username = 'marzneshin_user_123';
            $updateData = [
                'data_limit' => 21474836480, // 20GB
                'expire_date' => date('c', time() + 5184000) // 60 days
            ];

            $result = $this->panelService->updateUser(2, $username, $updateData);

            if (!$result) {
                throw new \Exception('Marzneshin updateUser should return true on success');
            }

            return true;
        });
    }

    /**
     * Test Marzneshin user deletion
     */
    private function testMarzneshiUserDeletion(): void
    {
        $this->runTest('Marzneshin User Deletion', function() {
            $username = 'marzneshin_user_123';

            $result = $this->panelService->deleteUser(2, $username);

            if (!$result) {
                throw new \Exception('Marzneshin deleteUser should return true on success');
            }

            return true;
        });
    }

    /**
     * Test Marzneshin config generation
     */
    private function testMarzneshiConfigGeneration(): void
    {
        $this->runTest('Marzneshin Config Generation', function() {
            $username = 'marzneshin_user_123';

            $config = $this->panelService->getUserConfig(2, $username);

            if (empty($config)) {
                throw new \Exception('Marzneshin getUserConfig should return non-empty config');
            }

            // Verify config format (should be valid proxy URL)
            if (!str_starts_with($config, 'vless://') && 
                !str_starts_with($config, 'vmess://') && 
                !str_starts_with($config, 'trojan://')) {
                throw new \Exception('Marzneshin config should be valid proxy URL');
            }

            return true;
        });
    }

    /**
     * Test Marzneshin expire strategies
     */
    private function testMarzneshiExpireStrategies(): void
    {
        $this->runTest('Marzneshin Expire Strategies', function() {
            // Test fixed_date strategy
            $userData1 = [
                'username' => 'test_fixed_date',
                'data_limit' => 10737418240,
                'expire' => time() + 2592000
            ];

            $result1 = $this->panelService->createUser(2, $userData1);
            if (!is_array($result1)) {
                throw new \Exception('Fixed date strategy test failed');
            }

            // Test start_on_first_use strategy (when onholdstatus is different)
            // This would require different server configuration in real scenario

            return true;
        });
    }

    /**
     * Test Marzneshin service IDs
     */
    private function testMarzneshiServiceIds(): void
    {
        $this->runTest('Marzneshin Service IDs', function() {
            $server = $this->mockDatabase->fetchRow("SELECT * FROM server_info WHERE id = ?", [2]);
            
            // Verify that server has service IDs configured
            if (!isset($server['proxies'])) {
                throw new \Exception('Marzneshin server should have service IDs configured');
            }

            $serviceIds = json_decode($server['proxies'], true);
            if (!is_array($serviceIds) || empty($serviceIds)) {
                throw new \Exception('Marzneshin service IDs should be non-empty array');
            }

            // Test user creation with service IDs
            $userData = [
                'username' => 'test_service_ids',
                'data_limit' => 10737418240,
                'expire' => time() + 2592000,
                'service_ids' => $serviceIds
            ];

            $result = $this->panelService->createUser(2, $userData);
            if (!is_array($result)) {
                throw new \Exception('Service IDs test failed');
            }

            return true;
        });
    }

    /**
     * Test Marzneshin API compatibility
     */
    private function testMarzneshiAPICompatibility(): void
    {
        $this->runTest('Marzneshin API Compatibility', function() {
            // Test that our implementation follows Marzneshin API structure
            $userData = [
                'username' => 'api_compatibility_test',
                'service_ids' => [1, 2],
                'data_limit' => 10737418240,
                'expire_date' => date('c', time() + 2592000),
                'expire_strategy' => 'fixed_date'
            ];

            $result = $this->panelService->createUser(2, $userData);

            // Verify API response structure matches Marzneshin
            $expectedStructure = [
                'username' => 'string',
                'service_ids' => 'array',
                'data_limit' => 'integer',
                'expire_strategy' => 'string'
            ];

            foreach ($expectedStructure as $field => $type) {
                if (!isset($result[$field])) {
                    continue; // Some fields might not be in mock response
                }

                $actualType = gettype($result[$field]);
                if ($actualType !== $type && !($type === 'integer' && $actualType === 'double')) {
                    throw new \Exception("Marzneshin field {$field} should be {$type}, got {$actualType}");
                }
            }

            return true;
        });
    }

    /**
     * Test Marzneshin vs Marzban differences
     */
    private function testMarzneshiDifferences(): void
    {
        $this->runTest('Marzneshin vs Marzban Differences', function() {
            // Test that Marzneshin implementation handles differences correctly
            
            // 1. Service IDs instead of proxies array
            $marzneshiData = [
                'username' => 'diff_test',
                'service_ids' => [1, 2], // Marzneshin uses service_ids
                'data_limit' => 10737418240,
                'expire' => time() + 2592000
            ];

            $result = $this->panelService->createUser(2, $marzneshiData);
            if (!is_array($result)) {
                throw new \Exception('Marzneshin service_ids handling failed');
            }

            // 2. Expire strategies
            $strategies = ['fixed_date', 'start_on_first_use', 'never'];
            foreach ($strategies as $strategy) {
                // Test would verify that each strategy is handled correctly
                // In mock environment, we just verify the method doesn't crash
            }

            return true;
        });
    }

    /**
     * Run individual test
     */
    private function runTest(string $testName, callable $test): void
    {
        $this->totalTests++;
        
        try {
            $result = $test();
            
            if ($result === true) {
                $this->passedTests++;
                $this->testResults[] = ['name' => $testName, 'status' => 'PASS', 'error' => null];
                echo "✅ {$testName}\n";
            } else {
                $this->failedTests++;
                $this->testResults[] = ['name' => $testName, 'status' => 'FAIL', 'error' => 'Test returned false'];
                echo "❌ {$testName}: Test returned false\n";
            }
        } catch (\Throwable $e) {
            $this->failedTests++;
            $this->testResults[] = ['name' => $testName, 'status' => 'FAIL', 'error' => $e->getMessage()];
            echo "❌ {$testName}: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Get test results
     */
    private function getTestResults(): array
    {
        $successRate = $this->totalTests > 0 ? round(($this->passedTests / $this->totalTests) * 100, 2) : 0;
        
        return [
            'total_tests' => $this->totalTests,
            'passed_tests' => $this->passedTests,
            'failed_tests' => $this->failedTests,
            'success_rate' => $successRate,
            'results' => $this->testResults
        ];
    }

    /**
     * Print test summary
     */
    public function printSummary(): void
    {
        $results = $this->getTestResults();
        
        echo "\n📊 Marzneshin Integration Test Summary:\n";
        echo "=====================================\n";
        echo "Total Tests: {$results['total_tests']}\n";
        echo "Passed: {$results['passed_tests']}\n";
        echo "Failed: {$results['failed_tests']}\n";
        echo "Success Rate: {$results['success_rate']}%\n";
        
        if ($results['failed_tests'] > 0) {
            echo "\n❌ Failed Tests:\n";
            foreach ($results['results'] as $result) {
                if ($result['status'] === 'FAIL') {
                    echo "  - {$result['name']}: {$result['error']}\n";
                }
            }
            echo "\n🔴 Marzneshin Integration Test: FAILED\n";
        } else {
            echo "\n🟢 Marzneshin Integration Test: PASSED\n";
            echo "\n🎉 Marzneshin integration verified successfully!\n";
        }
    }
}

// Run tests if executed directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'] ?? '')) {
    $tester = new MarzneshiIntegrationTest();
    $tester->runAllTests();
    $tester->printSummary();
}
