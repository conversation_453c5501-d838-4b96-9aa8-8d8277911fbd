<?php
/**
 * Marzban Panel Integration Test for WeBot
 * 
 * This script tests the Marzban panel integration and verifies
 * that the adapter can communicate with Marzban API.
 */

declare(strict_types=1);

echo "=== WeBot Marzban Panel Integration Test ===\n\n";

// Load the application
try {
    require_once __DIR__ . '/autoload.php';
    echo "✅ Application loaded successfully\n\n";
} catch (Exception $e) {
    echo "❌ Failed to load application: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 1: Marzban adapter class availability
echo "1. Marzban Adapter Class Availability Test:\n";
$adapterClassOk = false;

if (class_exists('WeBot\Adapters\MarzbanAdapter')) {
    echo "   ✅ MarzbanAdapter class exists\n";
    $adapterClassOk = true;
    
    $methods = ['authenticate', 'createUser', 'getUser', 'updateUser', 'deleteUser'];
    foreach ($methods as $method) {
        if (method_exists('WeBot\Adapters\MarzbanAdapter', $method)) {
            echo "   ✅ MarzbanAdapter has {$method}() method\n";
        } else {
            echo "   ❌ MarzbanAdapter missing {$method}() method\n";
            $adapterClassOk = false;
        }
    }
} else {
    echo "   ❌ MarzbanAdapter class does not exist\n";
}

// Test 2: Marzban configuration
echo "\n2. Marzban Configuration Test:\n";
$configOk = false;

$marzbanUrl = env('MARZBAN_MAIN_URL');
$marzbanUsername = env('MARZBAN_MAIN_USERNAME');
$marzbanPassword = env('MARZBAN_MAIN_PASSWORD');
$marzbanEnabled = env('MARZBAN_MAIN_ENABLED', 'false');

if (!empty($marzbanUrl)) {
    if (filter_var($marzbanUrl, FILTER_VALIDATE_URL)) {
        echo "   ✅ Marzban URL format is valid: {$marzbanUrl}\n";
        $configOk = true;
    } else {
        echo "   ❌ Marzban URL format is invalid\n";
    }
} else {
    echo "   ⚠️  Marzban URL not configured\n";
}

if (!empty($marzbanUsername)) {
    echo "   ✅ Marzban username configured: {$marzbanUsername}\n";
} else {
    echo "   ⚠️  Marzban username not configured\n";
}

if (!empty($marzbanPassword)) {
    echo "   ✅ Marzban password configured: " . str_repeat('*', min(strlen($marzbanPassword), 8)) . "\n";
} else {
    echo "   ⚠️  Marzban password not configured\n";
}

echo "   ℹ️  Marzban enabled: {$marzbanEnabled}\n";

// Test 3: Marzban config file
echo "\n3. Marzban Config File Test:\n";
$configFileOk = true;

$marzbanConfigFile = 'config/panels/marzban.php';
if (file_exists($marzbanConfigFile)) {
    echo "   ✅ Marzban config file exists\n";
    
    try {
        $marzbanConfig = include $marzbanConfigFile;
        if (is_array($marzbanConfig)) {
            echo "   ✅ Marzban config file loads properly\n";
            
            $requiredKeys = ['api_endpoints', 'default_settings', 'authentication'];
            foreach ($requiredKeys as $key) {
                if (isset($marzbanConfig[$key])) {
                    echo "   ✅ Config has '{$key}' section\n";
                } else {
                    echo "   ❌ Config missing '{$key}' section\n";
                    $configFileOk = false;
                }
            }
        } else {
            echo "   ❌ Marzban config file format is invalid\n";
            $configFileOk = false;
        }
    } catch (Exception $e) {
        echo "   ❌ Failed to load Marzban config: " . $e->getMessage() . "\n";
        $configFileOk = false;
    }
} else {
    echo "   ❌ Marzban config file does not exist\n";
    $configFileOk = false;
}

// Test 4: HTTP client for API calls
echo "\n4. HTTP Client for API Calls Test:\n";
$httpClientOk = false;

if (class_exists('GuzzleHttp\Client')) {
    echo "   ✅ GuzzleHttp client is available\n";
    $httpClientOk = true;
    
    try {
        $client = new GuzzleHttp\Client([
            'timeout' => 10,
            'connect_timeout' => 5,
            'verify' => false // For testing with self-signed certificates
        ]);
        echo "   ✅ HTTP client configured for API calls\n";
    } catch (Exception $e) {
        echo "   ❌ Failed to configure HTTP client: " . $e->getMessage() . "\n";
        $httpClientOk = false;
    }
} else {
    echo "   ❌ GuzzleHttp client not available\n";
}

// Test 5: Panel adapter interface
echo "\n5. Panel Adapter Interface Test:\n";
$interfaceOk = false;

if (interface_exists('WeBot\Contracts\PanelAdapterInterface')) {
    echo "   ✅ PanelAdapterInterface exists\n";
    $interfaceOk = true;
    
    if ($adapterClassOk) {
        $reflection = new ReflectionClass('WeBot\Adapters\MarzbanAdapter');
        if ($reflection->implementsInterface('WeBot\Contracts\PanelAdapterInterface')) {
            echo "   ✅ MarzbanAdapter implements PanelAdapterInterface\n";
        } else {
            echo "   ❌ MarzbanAdapter does not implement PanelAdapterInterface\n";
            $interfaceOk = false;
        }
    }
} else {
    echo "   ❌ PanelAdapterInterface does not exist\n";
}

// Test 6: API endpoint connectivity (if configured)
echo "\n6. API Endpoint Connectivity Test:\n";
$connectivityOk = false;

if ($configOk && $httpClientOk && !empty($marzbanUrl)) {
    try {
        $client = new GuzzleHttp\Client([
            'timeout' => 10,
            'connect_timeout' => 5,
            'verify' => false
        ]);
        
        // Test basic connectivity to the panel
        $healthCheckUrl = rtrim($marzbanUrl, '/') . '/api/core/system';
        
        $response = $client->get($healthCheckUrl, [
            'headers' => [
                'Accept' => 'application/json',
                'User-Agent' => 'WeBot/2.0'
            ]
        ]);
        
        if ($response->getStatusCode() === 200) {
            echo "   ✅ Marzban panel is reachable\n";
            $connectivityOk = true;
        } else {
            echo "   ⚠️  Marzban panel returned status: " . $response->getStatusCode() . "\n";
        }
    } catch (GuzzleHttp\Exception\ConnectException $e) {
        echo "   ❌ Cannot connect to Marzban panel: Connection failed\n";
    } catch (GuzzleHttp\Exception\RequestException $e) {
        echo "   ⚠️  Marzban panel connection issue: " . $e->getMessage() . "\n";
    } catch (Exception $e) {
        echo "   ❌ Marzban connectivity test failed: " . $e->getMessage() . "\n";
    }
} else {
    echo "   ⚠️  Skipped connectivity test (configuration incomplete)\n";
}

// Test 7: Authentication flow simulation
echo "\n7. Authentication Flow Simulation Test:\n";
$authFlowOk = true;

if ($configOk && $httpClientOk) {
    try {
        // Simulate authentication request structure
        $authData = [
            'username' => $marzbanUsername ?: 'test_user',
            'password' => $marzbanPassword ?: 'test_password'
        ];
        
        echo "   ✅ Authentication data structure prepared\n";
        
        // Test JSON encoding
        $jsonData = json_encode($authData);
        if ($jsonData !== false) {
            echo "   ✅ Authentication data can be JSON encoded\n";
        } else {
            echo "   ❌ Failed to JSON encode authentication data\n";
            $authFlowOk = false;
        }
        
        // Test headers preparation
        $headers = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'User-Agent' => 'WeBot/2.0'
        ];
        echo "   ✅ Authentication headers prepared\n";
        
    } catch (Exception $e) {
        echo "   ❌ Authentication flow simulation failed: " . $e->getMessage() . "\n";
        $authFlowOk = false;
    }
} else {
    echo "   ⚠️  Skipped authentication flow test (configuration incomplete)\n";
}

echo "\n=== Overall Status ===\n";

if ($adapterClassOk && $configFileOk && $httpClientOk && $interfaceOk && $authFlowOk) {
    if ($configOk && $connectivityOk) {
        echo "✅ Marzban panel integration is fully functional!\n";
        echo "ℹ️  Adapter is ready and panel is reachable\n";
    } else {
        echo "⚠️  Marzban panel integration infrastructure is ready but needs configuration\n";
        echo "ℹ️  All components are available but panel connection needs setup\n";
        echo "🔧 To complete Marzban integration:\n";
        echo "   1. Configure MARZBAN_MAIN_URL in .env file\n";
        echo "   2. Set MARZBAN_MAIN_USERNAME and MARZBAN_MAIN_PASSWORD\n";
        echo "   3. Ensure Marzban panel is running and accessible\n";
        echo "   4. Test authentication with panel\n";
    }
    exit(0);
} else {
    echo "❌ Marzban panel integration has issues.\n";
    echo "\n🔧 To fix Marzban integration issues:\n";
    echo "   1. Ensure MarzbanAdapter class exists and implements interface\n";
    echo "   2. Check Marzban configuration file\n";
    echo "   3. Install GuzzleHttp for API requests\n";
    echo "   4. Configure panel connection settings\n";
    exit(1);
}
