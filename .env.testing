# WeBot Testing Configuration
# تنظیمات محیط تست WeBot

# ==============================================
# Application Configuration
# ==============================================
APP_ENV=testing
APP_DEBUG=true
APP_TIMEZONE=Asia/Tehran
MEMORY_LIMIT=512M

# ==============================================
# Telegram Bot Configuration (Test)
# ==============================================
TELEGRAM_BOT_TOKEN=test_token_123456789
TELEGRAM_BOT_USERNAME=test_webot
WEBHOOK_URL=https://test.example.com/webhook
ADMIN_ID=123456789
CHANNEL_ID=-1001234567890
DISABLE_EXTERNAL_APIS=true

# ==============================================
# Test Database Configuration  
# ==============================================
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=webot_test
DB_USERNAME=root
DB_PASSWORD=

# Alternative test database settings
TEST_DB_HOST=localhost
TEST_DB_PORT=3306
TEST_DB_DATABASE=webot_test
TEST_DB_USERNAME=root
TEST_DB_PASSWORD=

# ==============================================
# Panel Configuration (Test)
# ==============================================
MARZBAN_URL=https://test-panel.example.com
MARZBAN_USERNAME=test_admin
MARZBAN_PASSWORD=test_password

MARZNESHIN_URL=https://test-marzneshin.example.com
MARZNESHIN_USERNAME=test_admin
MARZNESHIN_PASSWORD=test_password

# ==============================================
# Payment Gateway Configuration (Test)
# ==============================================
ZARINPAL_MERCHANT_ID=test_merchant_id
ZARINPAL_SANDBOX=true
ZARINPAL_CALLBACK_URL=https://test.example.com/payment/callback

NOWPAYMENTS_API_KEY=test_api_key
NOWPAYMENTS_SANDBOX=true
NOWPAYMENTS_CALLBACK_URL=https://test.example.com/payment/nowpayments/callback

# ==============================================
# Logging Configuration
# ==============================================
LOG_LEVEL=debug
LOG_CHANNEL=file
LOG_PATH=storage/logs/test.log

# ==============================================
# Cache Configuration
# ==============================================
CACHE_DRIVER=array
CACHE_TTL=3600

# ==============================================
# Session Configuration
# ==============================================
SESSION_DRIVER=array
SESSION_LIFETIME=7200

# ==============================================
# Queue Configuration
# ==============================================
QUEUE_DRIVER=sync

# ==============================================
# Mail Configuration (Test)
# ==============================================
MAIL_DRIVER=log
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="WeBot Test"

# ==============================================
# Security Configuration
# ==============================================
ENCRYPTION_KEY=test_encryption_key_32_characters
JWT_SECRET=test_jwt_secret_key_for_testing_only

# ==============================================
# Rate Limiting (Disabled for testing)
# ==============================================
RATE_LIMIT_ENABLED=false
RATE_LIMIT_MAX_ATTEMPTS=1000
RATE_LIMIT_DECAY_MINUTES=1

# ==============================================
# Feature Flags (Testing)
# ==============================================
FEATURE_PAYMENTS=true
FEATURE_PANELS=true
FEATURE_TICKETS=true
FEATURE_REFERRALS=true

# ==============================================
# External Services (Mocked)
# ==============================================
MOCK_EXTERNAL_SERVICES=true
MOCK_TELEGRAM_API=true
MOCK_PANEL_API=true
MOCK_PAYMENT_GATEWAYS=true

# ==============================================
# Test Specific Settings
# ==============================================
TEST_USER_ID=123456789
TEST_ADMIN_ID=987654321
TEST_CHANNEL_ID=-1001234567890
TEST_PAYMENT_AMOUNT=50000
TEST_SERVICE_VOLUME=10737418240
TEST_SERVICE_DAYS=30
