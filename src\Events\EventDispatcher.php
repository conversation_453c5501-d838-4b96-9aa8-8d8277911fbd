<?php

declare(strict_types=1);

namespace WeBot\Events;

use WeBot\Utils\Logger;

/**
 * Event Dispatcher
 *
 * Manages event dispatching and listener registration.
 * Provides a centralized event system for the WeBot application.
 *
 * @package WeBot\Events
 * @version 2.0
 */
class EventDispatcher
{
    private array $listeners = [];
    private Logger $logger;
    private array $eventHistory = [];
    private int $maxHistorySize = 100;

    public function __construct()
    {
        $this->logger = Logger::getInstance();
    }

    /**
     * Add event listener
     */
    public function addListener(string $eventName, callable $listener, int $priority = 0): void
    {
        if (!isset($this->listeners[$eventName])) {
            $this->listeners[$eventName] = [];
        }

        $this->listeners[$eventName][] = [
            'listener' => $listener,
            'priority' => $priority
        ];

        // Sort by priority (higher priority first)
        usort($this->listeners[$eventName], function ($a, $b) {
            return $b['priority'] <=> $a['priority'];
        });

        $this->logger->debug("Event listener added", [
            'event' => $eventName,
            'priority' => $priority
        ]);
    }

    /**
     * Remove event listener
     */
    public function removeListener(string $eventName, callable $listener): void
    {
        if (!isset($this->listeners[$eventName])) {
            return;
        }

        $this->listeners[$eventName] = array_filter(
            $this->listeners[$eventName],
            function ($item) use ($listener) {
                return $item['listener'] !== $listener;
            }
        );

        $this->logger->debug("Event listener removed", ['event' => $eventName]);
    }

    /**
     * Check if event has listeners
     */
    public function hasListeners(string $eventName): bool
    {
        return !empty($this->listeners[$eventName]);
    }

    /**
     * Get listeners for event
     */
    public function getListeners(string $eventName): array
    {
        return $this->listeners[$eventName] ?? [];
    }

    /**
     * Get all listeners
     */
    public function getAllListeners(): array
    {
        return $this->listeners;
    }

    /**
     * Dispatch event
     */
    public function dispatch(EventInterface $event): EventInterface
    {
        $eventName = $event->getName();

        $this->logger->info("Dispatching event", [
            'event' => $eventName,
            'data' => $event->getData()
        ]);

        // Add to history
        $this->addToHistory($event);

        // Get listeners for this event
        $listeners = $this->getListeners($eventName);

        if (empty($listeners)) {
            $this->logger->debug("No listeners found for event", ['event' => $eventName]);
            return $event;
        }

        // Execute listeners
        foreach ($listeners as $listenerData) {
            if ($event->isStoppable() && $event->isPropagationStopped()) {
                $this->logger->debug("Event propagation stopped", ['event' => $eventName]);
                break;
            }

            try {
                $listener = $listenerData['listener'];
                $priority = $listenerData['priority'];

                $this->logger->debug("Executing event listener", [
                    'event' => $eventName,
                    'priority' => $priority
                ]);

                $listener($event);
            } catch (\Throwable $e) {
                $this->logger->error("Event listener failed", [
                    'event' => $eventName,
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]);

                // Continue with other listeners
                continue;
            }
        }

        $this->logger->info("Event dispatched successfully", [
            'event' => $eventName,
            'listeners_executed' => count($listeners)
        ]);

        return $event;
    }

    /**
     * Dispatch event by name and data
     */
    public function dispatchByName(string $eventName, array $data = []): void
    {
        // Create a generic event
        $event = new class ($data) extends BaseEvent {
            private string $eventName;

            public function __construct(array $data, string $eventName = '')
            {
                parent::__construct($data);
                $this->eventName = $eventName;
            }

            public function getName(): string
            {
                return $this->eventName ?: parent::getName();
            }
        };

        // Set the event name
        $reflection = new \ReflectionProperty($event, 'eventName');
        $reflection->setAccessible(true);
        $reflection->setValue($event, $eventName);

        $this->dispatch($event);
    }

    /**
     * Add event to history
     */
    private function addToHistory(EventInterface $event): void
    {
        $this->eventHistory[] = [
            'name' => $event->getName(),
            'timestamp' => $event->getTimestamp(),
            'data_size' => count($event->getData())
        ];

        // Limit history size
        if (count($this->eventHistory) > $this->maxHistorySize) {
            array_shift($this->eventHistory);
        }
    }

    /**
     * Get event history
     */
    public function getEventHistory(): array
    {
        return $this->eventHistory;
    }

    /**
     * Clear event history
     */
    public function clearEventHistory(): void
    {
        $this->eventHistory = [];
        $this->logger->debug("Event history cleared");
    }

    /**
     * Get event statistics
     */
    public function getEventStatistics(): array
    {
        $stats = [
            'total_events' => count($this->eventHistory),
            'total_listeners' => array_sum(array_map('count', $this->listeners)),
            'events_by_name' => [],
            'recent_events' => array_slice($this->eventHistory, -10)
        ];

        // Count events by name
        foreach ($this->eventHistory as $event) {
            $name = $event['name'];
            if (!isset($stats['events_by_name'][$name])) {
                $stats['events_by_name'][$name] = 0;
            }
            $stats['events_by_name'][$name]++;
        }

        return $stats;
    }

    /**
     * Clear all listeners
     */
    public function clearListeners(): void
    {
        $this->listeners = [];
        $this->logger->debug("All event listeners cleared");
    }

    /**
     * Clear listeners for specific event
     */
    public function clearListenersForEvent(string $eventName): void
    {
        unset($this->listeners[$eventName]);
        $this->logger->debug("Event listeners cleared", ['event' => $eventName]);
    }
}
