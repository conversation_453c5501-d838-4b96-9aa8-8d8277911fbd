<?php
/**
 * Session Management Test for WeBot
 * 
 * This script tests the session management functionality and verifies
 * that sessions can be handled properly.
 */

declare(strict_types=1);

echo "=== WeBot Session Management Test ===\n\n";

// Load the application
try {
    require_once __DIR__ . '/autoload.php';
    echo "✅ Application loaded successfully\n\n";
} catch (Exception $e) {
    echo "❌ Failed to load application: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 1: Session manager class availability
echo "1. Session Manager Class Availability Test:\n";
$sessionManagerOk = false;

if (class_exists('WeBot\Core\SessionManager')) {
    echo "   ✅ SessionManager class exists\n";
    $sessionManagerOk = true;
    
    try {
        $sessionManager = new WeBot\Core\SessionManager();
        echo "   ✅ SessionManager can be instantiated\n";
        
        $methods = ['start', 'get', 'set', 'destroy'];
        foreach ($methods as $method) {
            if (method_exists($sessionManager, $method)) {
                echo "   ✅ SessionManager has {$method}() method\n";
            } else {
                echo "   ❌ SessionManager missing {$method}() method\n";
                $sessionManagerOk = false;
            }
        }
    } catch (Exception $e) {
        echo "   ❌ Failed to instantiate SessionManager: " . $e->getMessage() . "\n";
        $sessionManagerOk = false;
    }
} else {
    echo "   ❌ SessionManager class does not exist\n";
}

// Test 2: Session directory permissions
echo "\n2. Session Directory Permissions Test:\n";
$sessionDirOk = true;

$sessionDir = 'storage/sessions';
if (is_dir($sessionDir)) {
    if (is_writable($sessionDir)) {
        echo "   ✅ Session directory is writable\n";
    } else {
        echo "   ❌ Session directory is not writable\n";
        $sessionDirOk = false;
    }
} else {
    echo "   ⚠️  Session directory does not exist, creating...\n";
    if (mkdir($sessionDir, 0755, true)) {
        echo "   ✅ Session directory created successfully\n";
    } else {
        echo "   ❌ Failed to create session directory\n";
        $sessionDirOk = false;
    }
}

// Test 3: PHP session configuration
echo "\n3. PHP Session Configuration Test:\n";
$phpSessionOk = true;

// Check session configuration
$sessionSavePath = session_save_path();
echo "   ℹ️  Current session save path: " . ($sessionSavePath ?: 'default') . "\n";

$sessionName = session_name();
echo "   ℹ️  Session name: {$sessionName}\n";

$sessionCookieLifetime = session_get_cookie_params()['lifetime'];
echo "   ℹ️  Session cookie lifetime: {$sessionCookieLifetime} seconds\n";

// Test if we can start a session
if (session_status() === PHP_SESSION_NONE) {
    try {
        session_start();
        echo "   ✅ PHP session can be started\n";
        
        // Test session write
        $_SESSION['test_key'] = 'test_value_' . time();
        echo "   ✅ Can write to session\n";
        
        // Test session read
        if (isset($_SESSION['test_key'])) {
            echo "   ✅ Can read from session\n";
        } else {
            echo "   ❌ Cannot read from session\n";
            $phpSessionOk = false;
        }
        
        // Clean up
        unset($_SESSION['test_key']);
        
    } catch (Exception $e) {
        echo "   ❌ Failed to start PHP session: " . $e->getMessage() . "\n";
        $phpSessionOk = false;
    }
} else {
    echo "   ✅ Session already active\n";
}

// Test 4: File-based session storage
echo "\n4. File-based Session Storage Test:\n";
$fileSessionOk = true;

try {
    $sessionId = 'test_session_' . time();
    $sessionData = [
        'user_id' => 12345,
        'username' => 'test_user',
        'login_time' => time(),
        'data' => ['key1' => 'value1', 'key2' => 'value2']
    ];
    
    $sessionFile = $sessionDir . '/sess_' . $sessionId;
    $serializedData = serialize($sessionData);
    
    if (file_put_contents($sessionFile, $serializedData)) {
        echo "   ✅ Can write session file\n";
        
        // Test reading session file
        $readData = file_get_contents($sessionFile);
        if ($readData !== false) {
            $unserializedData = unserialize($readData);
            if ($unserializedData === $sessionData) {
                echo "   ✅ Can read session file correctly\n";
            } else {
                echo "   ❌ Session data mismatch\n";
                $fileSessionOk = false;
            }
        } else {
            echo "   ❌ Cannot read session file\n";
            $fileSessionOk = false;
        }
        
        // Clean up
        unlink($sessionFile);
        echo "   ✅ Session file cleanup successful\n";
    } else {
        echo "   ❌ Cannot write session file\n";
        $fileSessionOk = false;
    }
} catch (Exception $e) {
    echo "   ❌ File-based session test failed: " . $e->getMessage() . "\n";
    $fileSessionOk = false;
}

// Test 5: Session security features
echo "\n5. Session Security Features Test:\n";
$securityOk = true;

try {
    // Test session ID regeneration
    $oldSessionId = session_id();
    session_regenerate_id(true);
    $newSessionId = session_id();
    
    if ($oldSessionId !== $newSessionId) {
        echo "   ✅ Session ID regeneration works\n";
    } else {
        echo "   ❌ Session ID regeneration failed\n";
        $securityOk = false;
    }
    
    // Test session cookie parameters
    $cookieParams = session_get_cookie_params();
    echo "   ℹ️  Cookie secure: " . ($cookieParams['secure'] ? 'Yes' : 'No') . "\n";
    echo "   ℹ️  Cookie httponly: " . ($cookieParams['httponly'] ? 'Yes' : 'No') . "\n";
    echo "   ℹ️  Cookie samesite: " . ($cookieParams['samesite'] ?? 'Not set') . "\n";
    
} catch (Exception $e) {
    echo "   ❌ Session security test failed: " . $e->getMessage() . "\n";
    $securityOk = false;
}

// Test 6: Session cleanup simulation
echo "\n6. Session Cleanup Simulation Test:\n";
$cleanupOk = true;

try {
    // Create expired session files
    $expiredSessions = [];
    for ($i = 0; $i < 3; $i++) {
        $sessionId = 'expired_session_' . $i;
        $sessionFile = $sessionDir . '/sess_' . $sessionId;
        $expiredData = ['expired' => true, 'created' => time() - 3600]; // 1 hour ago
        
        file_put_contents($sessionFile, serialize($expiredData));
        $expiredSessions[] = $sessionFile;
    }
    
    echo "   ✅ Created expired session files\n";
    
    // Simulate cleanup by checking file modification time
    $cleanedCount = 0;
    foreach ($expiredSessions as $sessionFile) {
        if (file_exists($sessionFile)) {
            $modTime = filemtime($sessionFile);
            if (time() - $modTime > 1800) { // 30 minutes old
                unlink($sessionFile);
                $cleanedCount++;
            }
        }
    }
    
    echo "   ✅ Cleaned up {$cleanedCount} expired sessions\n";
    
    // Clean up any remaining files
    foreach ($expiredSessions as $sessionFile) {
        if (file_exists($sessionFile)) {
            unlink($sessionFile);
        }
    }
    
} catch (Exception $e) {
    echo "   ❌ Session cleanup test failed: " . $e->getMessage() . "\n";
    $cleanupOk = false;
}

echo "\n=== Overall Status ===\n";
if ($sessionDirOk && $phpSessionOk && $fileSessionOk && $securityOk && $cleanupOk) {
    echo "✅ Session management is working properly!\n";
    echo "ℹ️  File-based session storage is operational\n";
    
    if (!$sessionManagerOk) {
        echo "⚠️  SessionManager class needs attention but basic sessions work\n";
    }
    
    exit(0);
} else {
    echo "❌ Session management has issues.\n";
    echo "\n🔧 To fix session issues:\n";
    echo "   1. Ensure session directory is writable\n";
    echo "   2. Check SessionManager class implementation\n";
    echo "   3. Verify PHP session configuration\n";
    echo "   4. Test session operations manually\n";
    exit(1);
}
