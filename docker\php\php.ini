; WeBot PHP Configuration for Production

[PHP]
; Engine Settings
engine = On
short_open_tag = Off
precision = 14
output_buffering = 4096
zlib.output_compression = Off
implicit_flush = Off
unserialize_callback_func =
serialize_precision = -1
disable_functions = exec,passthru,shell_exec,system,proc_open,popen,curl_exec,curl_multi_exec,parse_ini_file,show_source
disable_classes =
zend.enable_gc = On
zend.exception_ignore_args = On
zend.exception_string_param_max_len = 0

; Miscellaneous
expose_php = Off
max_execution_time = 30
max_input_time = 60
memory_limit = 256M
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT
display_errors = Off
display_startup_errors = Off
log_errors = On
ignore_repeated_errors = Off
ignore_repeated_source = Off
report_memleaks = On
variables_order = "GPCS"
request_order = "GP"
register_argc_argv = Off
auto_globals_jit = On
post_max_size = 50M
auto_prepend_file =
auto_append_file =
default_mimetype = "text/html"
default_charset = "UTF-8"

; File Uploads
file_uploads = On
upload_max_filesize = 50M
max_file_uploads = 20

; Fopen wrappers
allow_url_fopen = On
allow_url_include = Off
default_socket_timeout = 60

; Dynamic Extensions
extension=bcmath
extension=ctype
extension=curl
extension=dom
extension=fileinfo
extension=filter
extension=gd
extension=hash
extension=iconv
extension=intl
extension=json
extension=libxml
extension=mbstring
extension=mysqli
extension=mysqlnd
extension=opcache
extension=openssl
extension=pcre
extension=pdo
extension=pdo_mysql
extension=phar
extension=posix
extension=redis
extension=session
extension=simplexml
extension=sockets
extension=sodium
extension=tokenizer
extension=xml
extension=xmlreader
extension=xmlwriter
extension=zip
extension=zlib

; Module Settings

[CLI Server]
cli_server.color = On

[Date]
date.timezone = Asia/Tehran

[filter]
filter.default = unsafe_raw
filter.default_flags =

[iconv]
iconv.input_encoding =
iconv.internal_encoding =
iconv.output_encoding =

[imap]
imap.enable_insecure_rsh = 0

[intl]
intl.default_locale =
intl.error_level = 0
intl.use_exceptions = 0

[sqlite3]
sqlite3.extension_dir =

[Pcre]
pcre.backtrack_limit = 100000
pcre.recursion_limit = 100000
pcre.jit = 1

[Pdo]
pdo_mysql.default_socket =

[Pdo_mysql]
pdo_mysql.default_socket =

[Phar]
phar.readonly = On
phar.require_hash = On
phar.cache_list =

[mail function]
SMTP = localhost
smtp_port = 25
mail.add_x_header = Off

[ODBC]
odbc.allow_persistent = On
odbc.check_persistent = On
odbc.max_persistent = -1
odbc.max_links = -1
odbc.defaultlrl = 4096
odbc.defaultbinmode = 1

[MySQLi]
mysqli.max_persistent = -1
mysqli.allow_persistent = On
mysqli.max_links = -1
mysqli.default_port = 3306
mysqli.default_socket =
mysqli.default_host =
mysqli.default_user =
mysqli.default_pw =
mysqli.reconnect = Off

[mysqlnd]
mysqlnd.collect_statistics = On
mysqlnd.collect_memory_statistics = On

[OPcache]
opcache.enable = 1
opcache.enable_cli = 1
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1
opcache.save_comments = 1
opcache.load_comments = 1
opcache.enable_file_override = 0
opcache.optimization_level = 0x7FFFBFFF
opcache.inherited_hack = 1
opcache.dups_fix = 0
opcache.blacklist_filename =

[curl]
curl.cainfo =

[openssl]
openssl.cafile =
openssl.capath =

[ffi]
ffi.enable = preload

[Session]
session.save_handler = redis
session.save_path = "tcp://redis:6379"
session.use_strict_mode = 1
session.use_cookies = 1
session.use_only_cookies = 1
session.name = WEBOTSSID
session.auto_start = 0
session.cookie_lifetime = 0
session.cookie_path = /
session.cookie_domain =
session.cookie_httponly = 1
session.cookie_samesite = Strict
session.serialize_handler = php
session.gc_probability = 1
session.gc_divisor = 1000
session.gc_maxlifetime = 1440
session.referer_check =
session.cache_limiter = nocache
session.cache_expire = 180
session.use_trans_sid = 0
session.sid_length = 26
session.trans_sid_tags = "a=href,area=href,frame=src,form="
session.sid_bits_per_character = 5
