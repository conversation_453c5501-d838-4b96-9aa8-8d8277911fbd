<?php

declare(strict_types=1);

namespace WeBot\Contracts;

/**
 * Panel Adapter Interface
 *
 * Defines the contract that all panel adapters must implement
 * to ensure consistent behavior across different panel types.
 *
 * @package WeBot\Contracts
 * @version 2.0
 */
interface PanelAdapterInterface
{
    /**
     * Authenticate with the panel
     *
     * @return array ['success' => bool, 'token' => string|null, 'error' => string|null]
     */
    public function authenticate(): array;

    /**
     * Validate authentication token
     *
     * @param string|null $token
     * @return bool
     */
    public function validateToken(?string $token = null): bool;

    /**
     * Refresh authentication token
     *
     * @return array ['success' => bool, 'token' => string|null, 'error' => string|null]
     */
    public function refreshToken(): array;

    /**
     * Create new user
     *
     * @param array $userData
     * @return array ['success' => bool, 'username' => string|null, 'error' => string|null, ...]
     */
    public function createUser(array $userData): array;

    /**
     * Get user information
     *
     * @param string $username
     * @return array ['success' => bool, 'username' => string|null, 'error' => string|null, ...]
     */
    public function getUser(string $username): array;

    /**
     * Update user
     *
     * @param string $username
     * @param array $updateData
     * @return array ['success' => bool, 'error' => string|null]
     */
    public function updateUser(string $username, array $updateData): array;

    /**
     * Delete user
     *
     * @param string $username
     * @return array ['success' => bool, 'error' => string|null]
     */
    public function deleteUser(string $username): array;

    /**
     * Suspend user
     *
     * @param string $username
     * @return array ['success' => bool, 'error' => string|null]
     */
    public function suspendUser(string $username): array;

    /**
     * Reactivate user
     *
     * @param string $username
     * @return array ['success' => bool, 'error' => string|null]
     */
    public function reactivateUser(string $username): array;

    /**
     * Get user statistics
     *
     * @param string $username
     * @return array ['success' => bool, 'used_traffic' => int, 'total_traffic' => int, ...]
     */
    public function getUserStats(string $username): array;

    /**
     * Generate configuration for client
     *
     * @param string $username
     * @param string $clientType
     * @return array ['success' => bool, 'config' => string, 'error' => string|null]
     */
    public function generateConfig(string $username, string $clientType = 'v2ray'): array;

    /**
     * Get system information
     *
     * @return array ['success' => bool, 'version' => string, 'memory_usage' => int, ...]
     */
    public function getSystemInfo(): array;

    /**
     * Health check
     *
     * @return array ['success' => bool, 'error' => string|null]
     */
    public function healthCheck(): array;

    /**
     * Bulk update users
     *
     * @param array $usernames
     * @param array $updateData
     * @return array ['success' => bool, 'total' => int, 'successful' => int, 'failed' => int]
     */
    public function bulkUpdateUsers(array $usernames, array $updateData): array;

    /**
     * Get bulk user statistics
     *
     * @param array $usernames
     * @return array ['success' => bool, 'users' => array]
     */
    public function getBulkUserStats(array $usernames): array;
}
