<?php

declare(strict_types=1);

namespace WeBot\Tests\E2E;

use WeBot\Core\Application;
use WeBot\Services\TelegramService;
use WeBot\Services\PaymentService;
use WeBot\Services\PanelService;
use WeBot\Services\DatabaseService;
use WeBot\Models\User;

/**
 * User Journey End-to-End Test
 * 
 * Complete end-to-end testing of user journeys
 * from registration to service activation.
 * 
 * @package WeBot\Tests\E2E
 * @version 2.0
 */
class UserJourneyTest
{
    private Application $app;
    private DatabaseService $database;
    private TelegramService $telegramService;
    private PaymentService $paymentService;
    private PanelService $panelService;
    private array $testResults = [];
    private int $totalTests = 0;
    private int $passedTests = 0;
    private int $failedTests = 0;

    public function __construct()
    {
        $this->setupTestEnvironment();
        $this->initializeServices();
    }

    /**
     * Run all E2E tests
     */
    public function runAllTests(): array
    {
        echo "🧪 Starting End-to-End User Journey Tests...\n\n";

        $this->testNewUserRegistration();
        $this->testUserProfileManagement();
        $this->testServicePurchaseFlow();
        $this->testPaymentProcessing();
        $this->testServiceActivation();
        $this->testServiceManagement();
        $this->testUserSupport();
        $this->testAdminOperations();

        return $this->generateTestReport();
    }

    /**
     * Test new user registration journey
     */
    private function testNewUserRegistration(): void
    {
        echo "📝 Testing New User Registration Journey...\n";

        try {
            // Simulate /start command from new user
            $telegramUpdate = [
                'update_id' => 123456,
                'message' => [
                    'message_id' => 1,
                    'from' => [
                        'id' => 987654321,
                        'is_bot' => false,
                        'first_name' => 'Test',
                        'last_name' => 'User',
                        'username' => 'testuser'
                    ],
                    'chat' => [
                        'id' => 987654321,
                        'first_name' => 'Test',
                        'last_name' => 'User',
                        'username' => 'testuser',
                        'type' => 'private'
                    ],
                    'date' => time(),
                    'text' => '/start'
                ]
            ];

            // Process the update
            $response = $this->app->processWebhook($telegramUpdate);
            
            $this->assertTrue($response['success'], 'Start command should be processed successfully');
            
            // Verify user was created
            $user = User::findByTelegramId($this->database, 987654321);
            $this->assertNotNull($user, 'User should be created after /start command');
            $this->assertEquals('testuser', $user->getUsername(), 'Username should be saved correctly');

            // Test welcome message was sent
            $this->assertContains('welcome', strtolower($response['message']), 'Welcome message should be sent');

            $this->recordTestResult('New User Registration', true, 'User successfully registered via /start command');

        } catch (\Exception $e) {
            $this->recordTestResult('New User Registration', false, $e->getMessage());
        }
    }

    /**
     * Test user profile management
     */
    private function testUserProfileManagement(): void
    {
        echo "👤 Testing User Profile Management...\n";

        try {
            $userId = 987654321;

            // Test profile viewing
            $profileUpdate = $this->createTelegramUpdate($userId, '/profile');
            $response = $this->app->processWebhook($profileUpdate);
            
            $this->assertTrue($response['success'], 'Profile command should work');
            $this->assertContains('profile', strtolower($response['message']), 'Profile info should be displayed');

            // Test phone number update
            $phoneUpdate = $this->createTelegramUpdate($userId, '/phone');
            $response = $this->app->processWebhook($phoneUpdate);
            
            $this->assertTrue($response['success'], 'Phone update command should work');

            // Simulate phone number input
            $phoneInputUpdate = $this->createTelegramUpdate($userId, '+1234567890');
            $response = $this->app->processWebhook($phoneInputUpdate);
            
            $this->assertTrue($response['success'], 'Phone number should be updated');

            // Verify phone was saved
            $user = User::findByTelegramId($this->database, $userId);
            $this->assertEquals('+1234567890', $user->getPhone(), 'Phone number should be saved');

            $this->recordTestResult('User Profile Management', true, 'Profile management working correctly');

        } catch (\Exception $e) {
            $this->recordTestResult('User Profile Management', false, $e->getMessage());
        }
    }

    /**
     * Test service purchase flow
     */
    private function testServicePurchaseFlow(): void
    {
        echo "🛒 Testing Service Purchase Flow...\n";

        try {
            $userId = 987654321;

            // Test services menu
            $servicesUpdate = $this->createTelegramUpdate($userId, '/services');
            $response = $this->app->processWebhook($servicesUpdate);
            
            $this->assertTrue($response['success'], 'Services command should work');
            $this->assertNotEmpty($response['keyboard'], 'Services keyboard should be displayed');

            // Test service selection (VPN service)
            $serviceSelectUpdate = $this->createCallbackUpdate($userId, 'select_service_vpn');
            $response = $this->app->processWebhook($serviceSelectUpdate);
            
            $this->assertTrue($response['success'], 'Service selection should work');
            $this->assertContains('vpn', strtolower($response['message']), 'VPN service info should be shown');

            // Test plan selection
            $planSelectUpdate = $this->createCallbackUpdate($userId, 'select_plan_monthly');
            $response = $this->app->processWebhook($planSelectUpdate);
            
            $this->assertTrue($response['success'], 'Plan selection should work');
            $this->assertContains('payment', strtolower($response['message']), 'Payment options should be shown');

            $this->recordTestResult('Service Purchase Flow', true, 'Service selection flow working correctly');

        } catch (\Exception $e) {
            $this->recordTestResult('Service Purchase Flow', false, $e->getMessage());
        }
    }

    /**
     * Test payment processing
     */
    private function testPaymentProcessing(): void
    {
        echo "💳 Testing Payment Processing...\n";

        try {
            $userId = 987654321;

            // Test payment method selection
            $paymentUpdate = $this->createCallbackUpdate($userId, 'payment_stripe');
            $response = $this->app->processWebhook($paymentUpdate);
            
            $this->assertTrue($response['success'], 'Payment method selection should work');

            // Simulate successful payment webhook
            $paymentData = [
                'user_id' => $userId,
                'amount' => 10.00,
                'currency' => 'USD',
                'service_type' => 'vpn',
                'plan' => 'monthly'
            ];

            $paymentResult = $this->paymentService->processPayment($paymentData);
            $this->assertTrue($paymentResult['success'], 'Payment should be processed successfully');

            // Verify payment record was created
            $payment = $this->paymentService->getLatestPayment($userId);
            $this->assertNotNull($payment, 'Payment record should be created');
            $this->assertEquals('completed', $payment['status'], 'Payment status should be completed');

            $this->recordTestResult('Payment Processing', true, 'Payment processed successfully');

        } catch (\Exception $e) {
            $this->recordTestResult('Payment Processing', false, $e->getMessage());
        }
    }

    /**
     * Test service activation
     */
    private function testServiceActivation(): void
    {
        echo "🚀 Testing Service Activation...\n";

        try {
            $userId = 987654321;

            // Simulate service activation after payment
            $activationResult = $this->panelService->activateService($userId, 'vpn', [
                'plan' => 'monthly',
                'server' => 'us-east-1'
            ]);

            $this->assertTrue($activationResult['success'], 'Service should be activated successfully');
            $this->assertNotEmpty($activationResult['config'], 'Service config should be provided');

            // Test service status check
            $statusUpdate = $this->createTelegramUpdate($userId, '/mystatus');
            $response = $this->app->processWebhook($statusUpdate);
            
            $this->assertTrue($response['success'], 'Status command should work');
            $this->assertContains('active', strtolower($response['message']), 'Active service should be shown');

            // Test config download
            $configUpdate = $this->createCallbackUpdate($userId, 'download_config');
            $response = $this->app->processWebhook($configUpdate);
            
            $this->assertTrue($response['success'], 'Config download should work');

            $this->recordTestResult('Service Activation', true, 'Service activated and accessible');

        } catch (\Exception $e) {
            $this->recordTestResult('Service Activation', false, $e->getMessage());
        }
    }

    /**
     * Test service management
     */
    private function testServiceManagement(): void
    {
        echo "⚙️ Testing Service Management...\n";

        try {
            $userId = 987654321;

            // Test service renewal
            $renewUpdate = $this->createCallbackUpdate($userId, 'renew_service');
            $response = $this->app->processWebhook($renewUpdate);
            
            $this->assertTrue($response['success'], 'Service renewal should work');

            // Test service suspension
            $suspendUpdate = $this->createCallbackUpdate($userId, 'suspend_service');
            $response = $this->app->processWebhook($suspendUpdate);
            
            $this->assertTrue($response['success'], 'Service suspension should work');

            // Test service reactivation
            $reactivateUpdate = $this->createCallbackUpdate($userId, 'reactivate_service');
            $response = $this->app->processWebhook($reactivateUpdate);
            
            $this->assertTrue($response['success'], 'Service reactivation should work');

            $this->recordTestResult('Service Management', true, 'Service management operations working');

        } catch (\Exception $e) {
            $this->recordTestResult('Service Management', false, $e->getMessage());
        }
    }

    /**
     * Test user support
     */
    private function testUserSupport(): void
    {
        echo "🆘 Testing User Support...\n";

        try {
            $userId = 987654321;

            // Test support command
            $supportUpdate = $this->createTelegramUpdate($userId, '/support');
            $response = $this->app->processWebhook($supportUpdate);
            
            $this->assertTrue($response['success'], 'Support command should work');
            $this->assertNotEmpty($response['keyboard'], 'Support options should be displayed');

            // Test ticket creation
            $ticketUpdate = $this->createCallbackUpdate($userId, 'create_ticket');
            $response = $this->app->processWebhook($ticketUpdate);
            
            $this->assertTrue($response['success'], 'Ticket creation should work');

            // Test FAQ access
            $faqUpdate = $this->createCallbackUpdate($userId, 'view_faq');
            $response = $this->app->processWebhook($faqUpdate);
            
            $this->assertTrue($response['success'], 'FAQ should be accessible');

            $this->recordTestResult('User Support', true, 'Support system working correctly');

        } catch (\Exception $e) {
            $this->recordTestResult('User Support', false, $e->getMessage());
        }
    }

    /**
     * Test admin operations
     */
    private function testAdminOperations(): void
    {
        echo "👨‍💼 Testing Admin Operations...\n";

        try {
            $adminId = 123456789; // Admin user ID

            // Test admin panel access
            $adminUpdate = $this->createTelegramUpdate($adminId, '/admin');
            $response = $this->app->processWebhook($adminUpdate);
            
            $this->assertTrue($response['success'], 'Admin command should work');
            $this->assertContains('admin', strtolower($response['message']), 'Admin panel should be accessible');

            // Test user management
            $userMgmtUpdate = $this->createCallbackUpdate($adminId, 'admin_users');
            $response = $this->app->processWebhook($userMgmtUpdate);
            
            $this->assertTrue($response['success'], 'User management should work');

            // Test system stats
            $statsUpdate = $this->createCallbackUpdate($adminId, 'admin_stats');
            $response = $this->app->processWebhook($statsUpdate);
            
            $this->assertTrue($response['success'], 'System stats should be accessible');

            $this->recordTestResult('Admin Operations', true, 'Admin operations working correctly');

        } catch (\Exception $e) {
            $this->recordTestResult('Admin Operations', false, $e->getMessage());
        }
    }

    /**
     * Setup test environment
     */
    private function setupTestEnvironment(): void
    {
        putenv('WEBOT_TEST_MODE=true');
        putenv('DISABLE_EXTERNAL_APIS=true');
        
        // Setup mock responses
        $GLOBALS['mock_responses'] = [
            'telegram_api' => [
                'sendMessage' => ['ok' => true, 'result' => ['message_id' => 123]],
                'editMessageText' => ['ok' => true, 'result' => ['message_id' => 123]],
                'answerCallbackQuery' => ['ok' => true, 'result' => true]
            ],
            'payment_gateway' => [
                'create_payment' => ['success' => true, 'payment_id' => 'pay_123'],
                'confirm_payment' => ['success' => true, 'status' => 'completed']
            ],
            'panel_api' => [
                'create_user' => ['success' => true, 'user_id' => 'panel_user_123'],
                'activate_service' => ['success' => true, 'config' => 'vpn_config_data']
            ]
        ];
    }

    /**
     * Initialize services
     */
    private function initializeServices(): void
    {
        $this->app = new Application();
        $this->database = $this->app->getContainer()->get(DatabaseService::class);
        $this->telegramService = $this->app->getContainer()->get(TelegramService::class);
        $this->paymentService = $this->app->getContainer()->get(PaymentService::class);
        $this->panelService = $this->app->getContainer()->get(PanelService::class);
    }

    /**
     * Create telegram update for testing
     */
    private function createTelegramUpdate(int $userId, string $text): array
    {
        return [
            'update_id' => rand(100000, 999999),
            'message' => [
                'message_id' => rand(1, 1000),
                'from' => [
                    'id' => $userId,
                    'is_bot' => false,
                    'first_name' => 'Test',
                    'username' => 'testuser'
                ],
                'chat' => [
                    'id' => $userId,
                    'type' => 'private'
                ],
                'date' => time(),
                'text' => $text
            ]
        ];
    }

    /**
     * Create callback query update for testing
     */
    private function createCallbackUpdate(int $userId, string $data): array
    {
        return [
            'update_id' => rand(100000, 999999),
            'callback_query' => [
                'id' => 'callback_' . rand(1000, 9999),
                'from' => [
                    'id' => $userId,
                    'is_bot' => false,
                    'first_name' => 'Test',
                    'username' => 'testuser'
                ],
                'message' => [
                    'message_id' => rand(1, 1000),
                    'chat' => [
                        'id' => $userId,
                        'type' => 'private'
                    ],
                    'date' => time()
                ],
                'data' => $data
            ]
        ];
    }

    /**
     * Assert helper methods
     */
    private function assertTrue(bool $condition, string $message): void
    {
        if (!$condition) {
            throw new \Exception("Assertion failed: {$message}");
        }
    }

    private function assertNotNull($value, string $message): void
    {
        if ($value === null) {
            throw new \Exception("Assertion failed: {$message}");
        }
    }

    private function assertEquals($expected, $actual, string $message): void
    {
        if ($expected !== $actual) {
            throw new \Exception("Assertion failed: {$message}. Expected: {$expected}, Actual: {$actual}");
        }
    }

    private function assertContains(string $needle, string $haystack, string $message): void
    {
        if (strpos($haystack, $needle) === false) {
            throw new \Exception("Assertion failed: {$message}");
        }
    }

    private function assertNotEmpty($value, string $message): void
    {
        if (empty($value)) {
            throw new \Exception("Assertion failed: {$message}");
        }
    }

    /**
     * Record test result
     */
    private function recordTestResult(string $testName, bool $passed, string $message): void
    {
        $this->totalTests++;
        
        if ($passed) {
            $this->passedTests++;
            echo "  ✅ {$testName}: PASSED\n";
        } else {
            $this->failedTests++;
            echo "  ❌ {$testName}: FAILED - {$message}\n";
        }

        $this->testResults[] = [
            'test' => $testName,
            'status' => $passed ? 'PASSED' : 'FAILED',
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Generate test report
     */
    private function generateTestReport(): array
    {
        $successRate = $this->totalTests > 0 ? ($this->passedTests / $this->totalTests) * 100 : 0;

        echo "\n📊 E2E Test Results Summary:\n";
        echo "Total Tests: {$this->totalTests}\n";
        echo "Passed: {$this->passedTests}\n";
        echo "Failed: {$this->failedTests}\n";
        echo "Success Rate: " . round($successRate, 2) . "%\n\n";

        return [
            'total_tests' => $this->totalTests,
            'passed_tests' => $this->passedTests,
            'failed_tests' => $this->failedTests,
            'success_rate' => round($successRate, 2),
            'results' => $this->testResults,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
}
