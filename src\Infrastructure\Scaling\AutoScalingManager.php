<?php

declare(strict_types=1);

namespace WeBot\Infrastructure\Scaling;

use WeBot\Core\CacheManager;
use WeBot\Services\DatabaseService;
use WeBot\Utils\Logger;
use WeBot\Exceptions\WeBotException;
use WeBot\Infrastructure\LoadBalancer\LoadBalancerManager;

/**
 * Auto Scaling Manager
 *
 * Manages automatic scaling of application instances based on
 * metrics, load patterns, and predefined scaling policies.
 *
 * @package WeBot\Infrastructure\Scaling
 * @version 2.0
 */
class AutoScalingManager
{
    private CacheManager $cache;
    private DatabaseService $database;
    private Logger $logger;
    private LoadBalancerManager $loadBalancer;
    private array $config;
    private array $scalingPolicies = [];
    private array $instances = [];
    private array $metrics = [];
    private array $metricsHistory = [];
    private array $scalingHistory = [];

    public function __construct(
        CacheManager $cache,
        DatabaseService $database,
        LoadBalancerManager $loadBalancer,
        array $config = []
    ) {
        $this->cache = $cache;
        $this->database = $database;
        $this->logger = Logger::getInstance();
        $this->loadBalancer = $loadBalancer;
        $this->config = array_merge($this->getDefaultConfig(), $config);

        $this->initializeScalingPolicies();
        $this->loadInstances();
    }

    /**
     * Evaluate scaling decisions
     */
    public function evaluateScaling(): array
    {
        try {
            $this->logger->info("Starting auto-scaling evaluation");

            // Collect current metrics
            $currentMetrics = $this->collectMetrics();

            // Evaluate each scaling policy
            $scalingDecisions = [];

            foreach ($this->scalingPolicies as $policyName => $policy) {
                $decision = $this->evaluatePolicy($policy, $currentMetrics);

                if ($decision['action'] !== 'none') {
                    $scalingDecisions[] = $decision;

                    $this->logger->info("Scaling decision made", [
                        'policy' => $policyName,
                        'action' => $decision['action'],
                        'reason' => $decision['reason']
                    ]);
                }
            }

            // Execute scaling decisions
            $results = [];
            foreach ($scalingDecisions as $decision) {
                $result = $this->executeScalingDecision($decision);
                $results[] = $result;
            }

            // Update metrics history
            $this->updateMetricsHistory($currentMetrics);

            return [
                'evaluation_time' => time(),
                'current_metrics' => $currentMetrics,
                'scaling_decisions' => $scalingDecisions,
                'execution_results' => $results,
                'current_instances' => count($this->instances)
            ];
        } catch (\Exception $e) {
            $this->logger->error("Auto-scaling evaluation failed", [
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Scale out (add instances)
     */
    public function scaleOut(int $instanceCount = 1, array $options = []): array
    {
        try {
            $this->logger->info("Scaling out", [
                'instance_count' => $instanceCount,
                'current_instances' => count($this->instances)
            ]);

            $newInstances = [];

            for ($i = 0; $i < $instanceCount; $i++) {
                $instance = $this->createInstance($options);

                if ($instance) {
                    $newInstances[] = $instance;
                    $this->instances[$instance['id']] = $instance;

                    // Add to load balancer
                    $this->loadBalancer->addServer([
                        'id' => $instance['id'],
                        'host' => $instance['host'],
                        'port' => $instance['port'],
                        'weight' => $instance['weight'] ?? 1
                    ]);
                }
            }

            // Update cache
            $this->cache->set('auto_scaling:instances', $this->instances, 3600);

            $this->logger->info("Scale out completed", [
                'new_instances' => count($newInstances),
                'total_instances' => count($this->instances)
            ]);

            return [
                'success' => true,
                'action' => 'scale_out',
                'new_instances' => $newInstances,
                'total_instances' => count($this->instances)
            ];
        } catch (\Exception $e) {
            $this->logger->error("Scale out failed", [
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Scale in (remove instances)
     */
    public function scaleIn(int $instanceCount = 1): array
    {
        try {
            $this->logger->info("Scaling in", [
                'instance_count' => $instanceCount,
                'current_instances' => count($this->instances)
            ]);

            // Ensure minimum instances
            $minInstances = $this->config['min_instances'];
            $currentInstances = count($this->instances);
            $maxRemovable = max(0, $currentInstances - $minInstances);
            $actualRemoveCount = min($instanceCount, $maxRemovable);

            if ($actualRemoveCount === 0) {
                return [
                    'success' => false,
                    'action' => 'scale_in',
                    'reason' => 'Cannot scale below minimum instances',
                    'min_instances' => $minInstances
                ];
            }

            $removedInstances = [];
            $instancesToRemove = $this->selectInstancesForRemoval($actualRemoveCount);

            foreach ($instancesToRemove as $instance) {
                // Remove from load balancer first
                $this->loadBalancer->removeServer($instance['id']);

                // Gracefully shutdown instance
                $this->shutdownInstance($instance);

                $removedInstances[] = $instance;
                unset($this->instances[$instance['id']]);
            }

            // Update cache
            $this->cache->set('auto_scaling:instances', $this->instances, 3600);

            $this->logger->info("Scale in completed", [
                'removed_instances' => count($removedInstances),
                'total_instances' => count($this->instances)
            ]);

            return [
                'success' => true,
                'action' => 'scale_in',
                'removed_instances' => $removedInstances,
                'total_instances' => count($this->instances)
            ];
        } catch (\Exception $e) {
            $this->logger->error("Scale in failed", [
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Get scaling status
     */
    public function getScalingStatus(): array
    {
        $metrics = $this->collectMetrics();

        return [
            'current_instances' => count($this->instances),
            'min_instances' => $this->config['min_instances'],
            'max_instances' => $this->config['max_instances'],
            'instances' => $this->instances,
            'current_metrics' => $metrics,
            'scaling_policies' => $this->scalingPolicies,
            'last_scaling_action' => $this->getLastScalingAction(),
            'scaling_history' => $this->getScalingHistory(),
            'recommendations' => $this->getScalingRecommendations($metrics)
        ];
    }

    /**
     * Add scaling policy
     */
    public function addScalingPolicy(string $name, array $policy): bool
    {
        try {
            $this->validateScalingPolicy($policy);

            $this->scalingPolicies[$name] = array_merge([
                'name' => $name,
                'enabled' => true,
                'created_at' => time()
            ], $policy);

            // Store in cache
            $this->cache->set('auto_scaling:policies', $this->scalingPolicies, 3600);

            $this->logger->info("Scaling policy added", [
                'policy_name' => $name,
                'policy' => $policy
            ]);

            return true;
        } catch (\Exception $e) {
            $this->logger->error("Failed to add scaling policy", [
                'policy_name' => $name,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Collect current metrics
     */
    private function collectMetrics(): array
    {
        $loadBalancerStats = $this->loadBalancer->getStatistics();

        return [
            'timestamp' => time(),
            'cpu_usage' => $this->getCPUUsage(),
            'memory_usage' => $this->getMemoryUsage(),
            'request_rate' => $this->getRequestRate(),
            'response_time' => $this->getAverageResponseTime(),
            'error_rate' => $this->getErrorRate(),
            'active_connections' => $loadBalancerStats['total_connections'],
            'healthy_instances' => $loadBalancerStats['healthy_servers'],
            'total_instances' => count($this->instances),
            'queue_length' => $this->getQueueLength(),
            'disk_usage' => $this->getDiskUsage(),
            'network_io' => $this->getNetworkIO()
        ];
    }

    /**
     * Evaluate scaling policy
     */
    private function evaluatePolicy(array $policy, array $metrics): array
    {
        if (!$policy['enabled']) {
            return ['action' => 'none', 'reason' => 'Policy disabled'];
        }

        $triggers = $policy['triggers'];
        $triggeredConditions = [];

        foreach ($triggers as $trigger) {
            if ($this->evaluateTrigger($trigger, $metrics)) {
                $triggeredConditions[] = $trigger;
            }
        }

        if (empty($triggeredConditions)) {
            return ['action' => 'none', 'reason' => 'No triggers activated'];
        }

        // Determine scaling action
        $scaleOutTriggers = array_filter($triggeredConditions, fn($t) => $t['action'] === 'scale_out');
        $scaleInTriggers = array_filter($triggeredConditions, fn($t) => $t['action'] === 'scale_in');

        if (!empty($scaleOutTriggers)) {
            return [
                'action' => 'scale_out',
                'policy' => $policy['name'],
                'triggers' => $scaleOutTriggers,
                'instance_count' => $policy['scale_out_count'] ?? 1,
                'reason' => 'Scale out triggers activated'
            ];
        }

        if (!empty($scaleInTriggers)) {
            return [
                'action' => 'scale_in',
                'policy' => $policy['name'],
                'triggers' => $scaleInTriggers,
                'instance_count' => $policy['scale_in_count'] ?? 1,
                'reason' => 'Scale in triggers activated'
            ];
        }

        return ['action' => 'none', 'reason' => 'No valid scaling action'];
    }

    /**
     * Evaluate trigger condition
     */
    private function evaluateTrigger(array $trigger, array $metrics): bool
    {
        $metric = $trigger['metric'];
        $operator = $trigger['operator'];
        $threshold = $trigger['threshold'];
        $duration = $trigger['duration'] ?? 0;

        if (!isset($metrics[$metric])) {
            return false;
        }

        $currentValue = $metrics[$metric];

        // Evaluate condition
        $conditionMet = match ($operator) {
            '>' => $currentValue > $threshold,
            '<' => $currentValue < $threshold,
            '>=' => $currentValue >= $threshold,
            '<=' => $currentValue <= $threshold,
            '==' => $currentValue == $threshold,
            '!=' => $currentValue != $threshold,
            default => false
        };

        if (!$conditionMet) {
            return false;
        }

        // Check duration if specified
        if ($duration > 0) {
            return $this->checkTriggerDuration($trigger, $metrics, $duration);
        }

        return true;
    }

    /**
     * Execute scaling decision
     */
    private function executeScalingDecision(array $decision): array
    {
        try {
            // Check cooldown period
            if (!$this->checkCooldownPeriod($decision['action'])) {
                return [
                    'success' => false,
                    'action' => $decision['action'],
                    'reason' => 'Cooldown period active'
                ];
            }

            $result = match ($decision['action']) {
                'scale_out' => $this->scaleOut($decision['instance_count']),
                'scale_in' => $this->scaleIn($decision['instance_count']),
                default => ['success' => false, 'reason' => 'Unknown action']
            };

            // Record scaling action
            $this->recordScalingAction($decision, $result);

            return $result;
        } catch (\Exception $e) {
            $this->logger->error("Failed to execute scaling decision", [
                'decision' => $decision,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'action' => $decision['action'],
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Create new instance
     */
    private function createInstance(array $options = []): ?array
    {
        try {
            $instanceId = uniqid('instance_');

            // In a real implementation, this would create actual infrastructure
            $instance = [
                'id' => $instanceId,
                'host' => $options['host'] ?? $this->generateInstanceHost(),
                'port' => $options['port'] ?? $this->generateInstancePort(),
                'weight' => $options['weight'] ?? 1,
                'status' => 'starting',
                'created_at' => time(),
                'cpu_usage' => 0,
                'memory_usage' => 0,
                'connections' => 0
            ];

            // Simulate instance startup time
            sleep(1);

            $instance['status'] = 'running';

            $this->logger->info("Instance created", [
                'instance_id' => $instanceId,
                'host' => $instance['host'],
                'port' => $instance['port']
            ]);

            return $instance;
        } catch (\Exception $e) {
            $this->logger->error("Failed to create instance", [
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Shutdown instance
     */
    private function shutdownInstance(array $instance): bool
    {
        try {
            $this->logger->info("Shutting down instance", [
                'instance_id' => $instance['id']
            ]);

            // In a real implementation, this would terminate actual infrastructure
            // Simulate graceful shutdown
            sleep(1);

            $this->logger->info("Instance shutdown completed", [
                'instance_id' => $instance['id']
            ]);

            return true;
        } catch (\Exception $e) {
            $this->logger->error("Failed to shutdown instance", [
                'instance_id' => $instance['id'],
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Select instances for removal
     */
    private function selectInstancesForRemoval(int $count): array
    {
        // Sort instances by criteria (oldest first, least connections, etc.)
        $sortedInstances = $this->instances;

        uasort($sortedInstances, function ($a, $b) {
            // Prioritize oldest instances with least connections
            if ($a['connections'] === $b['connections']) {
                return $a['created_at'] <=> $b['created_at'];
            }
            return $a['connections'] <=> $b['connections'];
        });

        return array_slice($sortedInstances, 0, $count, true);
    }

    /**
     * Mock metric collection methods
     */
    private function getCPUUsage(): float
    {
        return rand(10, 90) / 100;
    }

    private function getMemoryUsage(): float
    {
        return rand(20, 80) / 100;
    }

    private function getRequestRate(): float
    {
        return rand(50, 500) / 10; // requests per second
    }

    private function getAverageResponseTime(): float
    {
        return rand(100, 1000) / 10; // milliseconds
    }

    private function getErrorRate(): float
    {
        return rand(0, 5) / 100;
    }

    private function getQueueLength(): int
    {
        return rand(0, 100);
    }

    private function getDiskUsage(): float
    {
        return rand(30, 70) / 100;
    }

    private function getNetworkIO(): array
    {
        return [
            'bytes_in' => rand(1000000, 10000000),
            'bytes_out' => rand(1000000, 10000000)
        ];
    }

    private function generateInstanceHost(): string
    {
        return '10.0.0.' . rand(10, 250);
    }

    private function generateInstancePort(): int
    {
        return rand(8080, 8090);
    }

    private function checkCooldownPeriod(string $action): bool
    {
        $lastAction = $this->cache->get("auto_scaling:last_{$action}");
        $cooldownPeriod = $this->config['cooldown_period'];

        return !$lastAction || (time() - $lastAction) >= $cooldownPeriod;
    }

    private function recordScalingAction(array $decision, array $result): void
    {
        $action = [
            'timestamp' => time(),
            'action' => $decision['action'],
            'policy' => $decision['policy'] ?? 'manual',
            'reason' => $decision['reason'],
            'result' => $result,
            'instances_before' => count($this->instances),
            'instances_after' => $result['total_instances'] ?? count($this->instances)
        ];

        // Store in cache and database
        $this->cache->set("auto_scaling:last_{$decision['action']}", time(), 3600);

        $history = $this->cache->get('auto_scaling:history', []);
        $history[] = $action;

        // Keep only last 100 actions
        if (count($history) > 100) {
            $history = array_slice($history, -100);
        }

        $this->cache->set('auto_scaling:history', $history, 86400);
    }

    private function initializeScalingPolicies(): void
    {
        // Load policies from cache or set defaults
        $cachedPolicies = $this->cache->get('auto_scaling:policies');

        if ($cachedPolicies) {
            $this->scalingPolicies = $cachedPolicies;
        } else {
            $this->scalingPolicies = $this->getDefaultScalingPolicies();
            $this->cache->set('auto_scaling:policies', $this->scalingPolicies, 3600);
        }
    }

    private function loadInstances(): void
    {
        $cachedInstances = $this->cache->get('auto_scaling:instances', []);
        $this->instances = $cachedInstances;
    }

    private function getDefaultScalingPolicies(): array
    {
        return [
            'cpu_based_scaling' => [
                'name' => 'cpu_based_scaling',
                'enabled' => true,
                'triggers' => [
                    [
                        'metric' => 'cpu_usage',
                        'operator' => '>',
                        'threshold' => 0.8,
                        'duration' => 300,
                        'action' => 'scale_out'
                    ],
                    [
                        'metric' => 'cpu_usage',
                        'operator' => '<',
                        'threshold' => 0.3,
                        'duration' => 600,
                        'action' => 'scale_in'
                    ]
                ],
                'scale_out_count' => 1,
                'scale_in_count' => 1
            ],
            'request_rate_scaling' => [
                'name' => 'request_rate_scaling',
                'enabled' => true,
                'triggers' => [
                    [
                        'metric' => 'request_rate',
                        'operator' => '>',
                        'threshold' => 100,
                        'duration' => 180,
                        'action' => 'scale_out'
                    ]
                ],
                'scale_out_count' => 2,
                'scale_in_count' => 1
            ]
        ];
    }

    private function getDefaultConfig(): array
    {
        return [
            'min_instances' => 1,
            'max_instances' => 10,
            'cooldown_period' => 300,        // 5 minutes
            'evaluation_interval' => 60,     // 1 minute
            'metrics_retention' => 86400,    // 24 hours
            'enable_predictive_scaling' => false,
            'enable_scheduled_scaling' => false
        ];
    }

    /**
     * Update metrics history
     */
    private function updateMetricsHistory(array $metrics): void
    {
        $timestamp = time();
        $this->metricsHistory[] = array_merge($metrics, ['timestamp' => $timestamp]);

        // Keep only last 100 entries
        if (count($this->metricsHistory) > 100) {
            $this->metricsHistory = array_slice($this->metricsHistory, -100);
        }
    }

    /**
     * Get last scaling action
     */
    private function getLastScalingAction(): ?array
    {
        if (empty($this->scalingHistory)) {
            return null;
        }

        return end($this->scalingHistory);
    }

    /**
     * Get scaling history
     */
    private function getScalingHistory(): array
    {
        return $this->scalingHistory;
    }

    /**
     * Get scaling recommendations
     */
    private function getScalingRecommendations(array $metrics): array
    {
        $recommendations = [];

        if ($metrics['cpu_usage'] > 80) {
            $recommendations[] = [
                'type' => 'scale_up',
                'reason' => 'High CPU usage detected',
                'priority' => 'high'
            ];
        }

        if ($metrics['memory_usage'] > 85) {
            $recommendations[] = [
                'type' => 'scale_up',
                'reason' => 'High memory usage detected',
                'priority' => 'high'
            ];
        }

        if ($metrics['cpu_usage'] < 30 && $metrics['memory_usage'] < 40) {
            $recommendations[] = [
                'type' => 'scale_down',
                'reason' => 'Low resource utilization',
                'priority' => 'low'
            ];
        }

        return $recommendations;
    }

    /**
     * Validate scaling policy
     */
    private function validateScalingPolicy(array $policy): void
    {
        $requiredFields = ['min_instances', 'max_instances', 'triggers'];

        foreach ($requiredFields as $field) {
            if (!isset($policy[$field])) {
                throw new \InvalidArgumentException("Required policy field missing: " . $field);
            }
        }

        if ($policy['min_instances'] < 1) {
            throw new \InvalidArgumentException("Minimum instances must be at least 1");
        }

        if ($policy['max_instances'] < $policy['min_instances']) {
            throw new \InvalidArgumentException("Maximum instances must be greater than minimum instances");
        }
    }

    /**
     * Check trigger duration
     */
    private function checkTriggerDuration(array $trigger, array $metrics, int $duration): bool
    {
        // Check if condition has been met for the specified duration
        $currentTime = time();
        $requiredTime = $currentTime - $duration;

        // Count how many recent metrics meet the trigger condition
        $matchingCount = 0;
        foreach ($this->metricsHistory as $historicalMetric) {
            if ($historicalMetric['timestamp'] >= $requiredTime) {
                if ($this->evaluateTriggerCondition($trigger, $historicalMetric)) {
                    $matchingCount++;
                }
            }
        }

        // Require at least 3 consecutive matching metrics
        return $matchingCount >= 3;
    }

    /**
     * Evaluate trigger condition
     */
    private function evaluateTriggerCondition(array $trigger, array $metrics): bool
    {
        $metric = $trigger['metric'];
        $operator = $trigger['operator'];
        $threshold = $trigger['threshold'];

        if (!isset($metrics[$metric])) {
            return false;
        }

        $value = $metrics[$metric];

        switch ($operator) {
            case '>':
                return $value > $threshold;
            case '>=':
                return $value >= $threshold;
            case '<':
                return $value < $threshold;
            case '<=':
                return $value <= $threshold;
            case '==':
                return $value == $threshold;
            case '!=':
                return $value != $threshold;
            default:
                return false;
        }
    }
}
