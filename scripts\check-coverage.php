<?php

declare(strict_types=1);

/**
 * Coverage Threshold Checker
 * 
 * Checks if code coverage meets the minimum threshold.
 * 
 * @package WeBot\Scripts
 * @version 2.0
 */

// Configuration
const MINIMUM_COVERAGE = 80.0;
const COVERAGE_FILE = 'coverage/clover.xml';
const REPORTS_DIR = 'reports';

// Colors for output
const COLOR_RED = "\033[31m";
const COLOR_GREEN = "\033[32m";
const COLOR_YELLOW = "\033[33m";
const COLOR_BLUE = "\033[34m";
const COLOR_RESET = "\033[0m";

/**
 * Print colored message
 */
function printMessage(string $message, string $color = COLOR_RESET): void
{
    echo $color . $message . COLOR_RESET . PHP_EOL;
}

/**
 * Print header
 */
function printHeader(): void
{
    printMessage("", COLOR_BLUE);
    printMessage("🔍 WeBot Coverage Threshold Checker", COLOR_BLUE);
    printMessage("=====================================", COLOR_BLUE);
    printMessage("");
}

/**
 * Check if coverage file exists
 */
function checkCoverageFile(): bool
{
    if (!file_exists(COVERAGE_FILE)) {
        printMessage("❌ Coverage file not found: " . COVERAGE_FILE, COLOR_RED);
        printMessage("   Please run tests with coverage first:", COLOR_YELLOW);
        printMessage("   vendor/bin/phpunit --coverage-clover=" . COVERAGE_FILE, COLOR_YELLOW);
        return false;
    }
    
    printMessage("✅ Coverage file found: " . COVERAGE_FILE, COLOR_GREEN);
    return true;
}

/**
 * Parse coverage from clover XML
 */
function parseCoverage(): ?array
{
    try {
        $xml = simplexml_load_file(COVERAGE_FILE);
        
        if ($xml === false) {
            printMessage("❌ Failed to parse coverage XML file", COLOR_RED);
            return null;
        }

        $metrics = $xml->project->metrics;
        
        if (!$metrics) {
            printMessage("❌ No metrics found in coverage file", COLOR_RED);
            return null;
        }

        $statements = (int) $metrics['statements'];
        $coveredStatements = (int) $metrics['coveredstatements'];
        $methods = (int) $metrics['methods'];
        $coveredMethods = (int) $metrics['coveredmethods'];
        $elements = (int) $metrics['elements'];
        $coveredElements = (int) $metrics['coveredelements'];

        $statementCoverage = $statements > 0 ? ($coveredStatements / $statements) * 100 : 0;
        $methodCoverage = $methods > 0 ? ($coveredMethods / $methods) * 100 : 0;
        $elementCoverage = $elements > 0 ? ($coveredElements / $elements) * 100 : 0;

        return [
            'statements' => [
                'total' => $statements,
                'covered' => $coveredStatements,
                'percentage' => $statementCoverage
            ],
            'methods' => [
                'total' => $methods,
                'covered' => $coveredMethods,
                'percentage' => $methodCoverage
            ],
            'elements' => [
                'total' => $elements,
                'covered' => $coveredElements,
                'percentage' => $elementCoverage
            ]
        ];
        
    } catch (Exception $e) {
        printMessage("❌ Error parsing coverage file: " . $e->getMessage(), COLOR_RED);
        return null;
    }
}

/**
 * Display coverage report
 */
function displayCoverageReport(array $coverage): void
{
    printMessage("📊 Coverage Report", COLOR_BLUE);
    printMessage("------------------", COLOR_BLUE);
    
    foreach ($coverage as $type => $data) {
        $percentage = round($data['percentage'], 2);
        $color = $percentage >= MINIMUM_COVERAGE ? COLOR_GREEN : COLOR_RED;
        
        printMessage(sprintf(
            "%s: %d/%d (%.2f%%)",
            ucfirst($type),
            $data['covered'],
            $data['total'],
            $percentage
        ), $color);
    }
    
    printMessage("");
}

/**
 * Check if coverage meets threshold
 */
function checkThreshold(array $coverage): bool
{
    $statementCoverage = $coverage['statements']['percentage'];
    
    printMessage("🎯 Coverage Threshold Check", COLOR_BLUE);
    printMessage("---------------------------", COLOR_BLUE);
    printMessage("Minimum required: " . MINIMUM_COVERAGE . "%", COLOR_YELLOW);
    printMessage("Current coverage: " . round($statementCoverage, 2) . "%", COLOR_YELLOW);
    
    if ($statementCoverage >= MINIMUM_COVERAGE) {
        printMessage("✅ Coverage threshold met!", COLOR_GREEN);
        return true;
    } else {
        $needed = MINIMUM_COVERAGE - $statementCoverage;
        printMessage("❌ Coverage threshold not met!", COLOR_RED);
        printMessage("   Need " . round($needed, 2) . "% more coverage", COLOR_RED);
        return false;
    }
}

/**
 * Generate coverage badge data
 */
function generateBadgeData(array $coverage): void
{
    $percentage = round($coverage['statements']['percentage'], 1);
    $color = $percentage >= 80 ? 'brightgreen' : ($percentage >= 60 ? 'yellow' : 'red');
    
    $badgeData = [
        'schemaVersion' => 1,
        'label' => 'coverage',
        'message' => $percentage . '%',
        'color' => $color
    ];
    
    // Create reports directory if it doesn't exist
    if (!is_dir(REPORTS_DIR)) {
        mkdir(REPORTS_DIR, 0755, true);
    }
    
    file_put_contents(REPORTS_DIR . '/coverage-badge.json', json_encode($badgeData, JSON_PRETTY_PRINT));
    printMessage("📊 Coverage badge data generated: " . REPORTS_DIR . '/coverage-badge.json', COLOR_GREEN);
}

/**
 * Generate coverage summary
 */
function generateSummary(array $coverage): void
{
    $summary = [
        'timestamp' => date('Y-m-d H:i:s'),
        'threshold' => MINIMUM_COVERAGE,
        'coverage' => $coverage,
        'passed' => $coverage['statements']['percentage'] >= MINIMUM_COVERAGE
    ];
    
    // Create reports directory if it doesn't exist
    if (!is_dir(REPORTS_DIR)) {
        mkdir(REPORTS_DIR, 0755, true);
    }
    
    file_put_contents(REPORTS_DIR . '/coverage-summary.json', json_encode($summary, JSON_PRETTY_PRINT));
    printMessage("📋 Coverage summary generated: " . REPORTS_DIR . '/coverage-summary.json', COLOR_GREEN);
}

/**
 * Main execution
 */
function main(): int
{
    printHeader();
    
    // Check if coverage file exists
    if (!checkCoverageFile()) {
        return 1;
    }
    
    // Parse coverage data
    $coverage = parseCoverage();
    if ($coverage === null) {
        return 1;
    }
    
    // Display coverage report
    displayCoverageReport($coverage);
    
    // Generate additional reports
    generateBadgeData($coverage);
    generateSummary($coverage);
    
    // Check threshold
    $passed = checkThreshold($coverage);
    
    printMessage("");
    if ($passed) {
        printMessage("🎉 All coverage checks passed!", COLOR_GREEN);
        return 0;
    } else {
        printMessage("💥 Coverage checks failed!", COLOR_RED);
        printMessage("   Please add more tests to improve coverage.", COLOR_YELLOW);
        return 1;
    }
}

// Run the script
exit(main());
