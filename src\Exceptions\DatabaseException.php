<?php

declare(strict_types=1);

namespace WeBot\Exceptions;

use Exception;

/**
 * Database Exception
 *
 * Exception thrown for database-related errors.
 *
 * @package WeBot\Exceptions
 * @version 2.0
 */
class DatabaseException extends Exception
{
    private ?string $sqlQuery = null;
    private array $sqlParams = [];

    public function __construct(
        string $message = "",
        int $code = 0,
        ?Exception $previous = null,
        ?string $sqlQuery = null,
        array $sqlParams = []
    ) {
        parent::__construct($message, $code, $previous);
        $this->sqlQuery = $sqlQuery;
        $this->sqlParams = $sqlParams;
    }

    /**
     * Get SQL query that caused the exception
     */
    public function getSqlQuery(): ?string
    {
        return $this->sqlQuery;
    }

    /**
     * Get SQL parameters
     */
    public function getSqlParams(): array
    {
        return $this->sqlParams;
    }

    /**
     * Get detailed error information
     */
    public function getDetailedInfo(): array
    {
        return [
            'message' => $this->getMessage(),
            'code' => $this->getCode(),
            'file' => $this->getFile(),
            'line' => $this->getLine(),
            'sql_query' => $this->sqlQuery,
            'sql_params' => $this->sqlParams,
            'trace' => $this->getTraceAsString()
        ];
    }
}
