<?php

declare(strict_types=1);

namespace WeBot\Core;

/**
 * Configuration Interface
 *
 * Defines the contract for configuration management in WeBot.
 *
 * @package WeBot\Core
 * @version 2.0
 */
interface ConfigInterface
{
    /**
     * Get configuration value by key
     *
     * @param string $key Configuration key
     * @param mixed $default Default value if key not found
     * @return mixed Configuration value
     */
    public function get(string $key, $default = null);

    /**
     * Set configuration value
     *
     * @param string $key Configuration key
     * @param mixed $value Configuration value
     * @return void
     */
    public function set(string $key, $value): void;

    /**
     * Check if configuration key exists
     *
     * @param string $key Configuration key
     * @return bool True if key exists
     */
    public function has(string $key): bool;

    /**
     * Get all configuration values
     *
     * @return array All configuration values
     */
    public function all(): array;
}
