<?php

/**
 * PHPUnit Stubs for IDE Support
 * 
 * This file provides basic PHPUnit class definitions for IDE support
 * when vendor directory is not available.
 */

namespace PHPUnit\Framework;

/**
 * TestCase stub
 */
abstract class TestCase
{
    protected function setUp(): void {}
    protected function tearDown(): void {}

    public static function assertTrue($condition, string $message = ''): void {}
    public static function assertFalse($condition, string $message = ''): void {}
    public static function assertEquals($expected, $actual, string $message = ''): void {}
    public static function assertNull($actual, string $message = ''): void {}
    public static function assertNotNull($actual, string $message = ''): void {}
    public static function assertSame($expected, $actual, string $message = ''): void {}
    public static function assertInstanceOf(string $expected, $actual, string $message = ''): void {}
    public static function assertArrayHasKey($key, $array, string $message = ''): void {}
    public static function assertStringContains(string $needle, string $haystack, string $message = ''): void {}
    public static function assertGreaterThan($expected, $actual, string $message = ''): void {}
    public static function assertLessThanOrEqual($expected, $actual, string $message = ''): void {}
    public static function assertIsArray($actual, string $message = ''): void {}
    public static function assertMatchesRegularExpression(string $pattern, string $string, string $message = ''): void {}

    public function createMock(string $originalClassName): MockObject\MockObject
    {
        return new MockObject\MockObject();
    }

    public function expectException(string $exception): void {}
    public function expectExceptionMessage(string $message): void {}

    // Mock expectation methods
    public function once(): MockObject\Invocation { return new MockObject\Invocation(); }
    public function never(): MockObject\Invocation { return new MockObject\Invocation(); }
    public function exactly(int $count): MockObject\Invocation { return new MockObject\Invocation(); }
    public function atLeastOnce(): MockObject\Invocation { return new MockObject\Invocation(); }
    public function stringContains(string $needle): MockObject\Constraint { return new MockObject\Constraint(); }
}

namespace PHPUnit\Framework\MockObject;

/**
 * MockObject stub
 */
class MockObject
{
    public function expects($matcher): self { return $this; }
    public function method(string $methodName): self { return $this; }
    public function with(...$arguments): self { return $this; }
    public function willReturn($value): self { return $this; }
    public function willReturnOnConsecutiveCalls(...$values): self { return $this; }
    public function willThrowException(\Throwable $exception): self { return $this; }
    public function once(): self { return $this; }
    public function never(): self { return $this; }
    public function exactly(int $count): self { return $this; }
    public function atLeastOnce(): self { return $this; }
}

/**
 * Invocation stub
 */
class Invocation
{
    public function __construct() {}
}

/**
 * Constraint stub
 */
class Constraint
{
    public function __construct() {}
}
