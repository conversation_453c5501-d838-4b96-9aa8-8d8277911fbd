# WeBot Production Configuration
# Use with: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

version: '3.8'

services:
  # WeBot Application - Production
  webot:
    build:
      target: production
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - LOG_LEVEL=warning
    volumes:
      # Only mount necessary directories
      - webot_storage:/var/www/html/storage
      - webot_uploads:/var/www/html/public/uploads
      - webot_logs:/var/www/html/logs
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # MySQL - Production
  mysql:
    environment:
      MYSQL_ROOT_PASSWORD_FILE: /run/secrets/mysql_root_password
      MYSQL_PASSWORD_FILE: /run/secrets/mysql_password
    volumes:
      - mysql_prod_data:/var/lib/mysql
      - ./docker/mysql/prod.cnf:/etc/mysql/conf.d/prod.cnf
      - ./backups:/backups
    secrets:
      - mysql_root_password
      - mysql_password
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Redis - Production
  redis:
    volumes:
      - redis_prod_data:/data
      - ./docker/redis/prod.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf --requirepass ${REDIS_PASSWORD}
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Load Balancer
  nginx:
    image: nginx:alpine
    container_name: webot_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/prod.conf:/etc/nginx/conf.d/default.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
      - webot_logs:/var/log/nginx
    depends_on:
      - webot
    networks:
      - webot_network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: webot_prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - webot_network

  # Monitoring - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: webot_grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - webot_network

  # Log Management - Loki
  loki:
    image: grafana/loki:latest
    container_name: webot_loki
    restart: unless-stopped
    ports:
      - "3100:3100"
    volumes:
      - ./docker/loki/loki.yml:/etc/loki/local-config.yaml
      - loki_data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - webot_network

  # Log Collection - Promtail
  promtail:
    image: grafana/promtail:latest
    container_name: webot_promtail
    restart: unless-stopped
    volumes:
      - ./docker/promtail/promtail.yml:/etc/promtail/config.yml
      - webot_logs:/var/log/webot
      - /var/log:/var/log:ro
    command: -config.file=/etc/promtail/config.yml
    depends_on:
      - loki
    networks:
      - webot_network

  # Backup Service
  backup:
    image: alpine:latest
    container_name: webot_backup
    restart: "no"
    volumes:
      - mysql_prod_data:/data/mysql:ro
      - redis_prod_data:/data/redis:ro
      - webot_storage:/data/storage:ro
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh
    command: /backup.sh
    depends_on:
      - mysql
      - redis
    networks:
      - webot_network

# Production Volumes
volumes:
  mysql_prod_data:
    driver: local
  redis_prod_data:
    driver: local
  webot_storage:
    driver: local
  webot_uploads:
    driver: local
  webot_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  loki_data:
    driver: local

# Secrets for Production
secrets:
  mysql_root_password:
    file: ./secrets/mysql_root_password.txt
  mysql_password:
    file: ./secrets/mysql_password.txt
