<?php

declare(strict_types=1);

namespace WeBot\Core;

/**
 * Secrets Manager
 *
 * Secure management of sensitive configuration data
 * with encryption and access control.
 *
 * @package WeBot\Core
 * @version 2.0
 */
class SecretsManager
{
    private string $secretsPath;
    private string $encryptionKey;
    private array $cache = [];

    public function __construct(string $secretsPath = 'storage/secrets')
    {
        $this->secretsPath = $secretsPath;
        $this->encryptionKey = $this->getEncryptionKey();
        $this->ensureSecretsDirectory();
    }

    /**
     * Store encrypted secret
     */
    public function store(string $key, string $value): bool
    {
        try {
            $encrypted = $this->encrypt($value);
            $filePath = $this->getSecretPath($key);

            $result = file_put_contents($filePath, $encrypted, LOCK_EX);

            if ($result !== false) {
                // Set restrictive permissions
                chmod($filePath, 0600);

                // Cache the decrypted value
                $this->cache[$key] = $value;

                return true;
            }

            return false;
        } catch (\Exception $e) {
            error_log("Failed to store secret '{$key}': " . $e->getMessage());
            return false;
        }
    }

    /**
     * Retrieve and decrypt secret
     */
    public function get(string $key): ?string
    {
        // Check cache first
        if (isset($this->cache[$key])) {
            return $this->cache[$key];
        }

        try {
            $filePath = $this->getSecretPath($key);

            if (!file_exists($filePath)) {
                return null;
            }

            $encrypted = file_get_contents($filePath);
            if ($encrypted === false) {
                return null;
            }

            $decrypted = $this->decrypt($encrypted);

            // Cache the result
            $this->cache[$key] = $decrypted;

            return $decrypted;
        } catch (\Exception $e) {
            error_log("Failed to retrieve secret '{$key}': " . $e->getMessage());
            return null;
        }
    }

    /**
     * Check if secret exists
     */
    public function exists(string $key): bool
    {
        return file_exists($this->getSecretPath($key));
    }

    /**
     * Delete secret
     */
    public function delete(string $key): bool
    {
        try {
            $filePath = $this->getSecretPath($key);

            if (file_exists($filePath)) {
                // Clear from cache
                unset($this->cache[$key]);

                // Securely delete file
                return $this->secureDelete($filePath);
            }

            return true;
        } catch (\Exception $e) {
            error_log("Failed to delete secret '{$key}': " . $e->getMessage());
            return false;
        }
    }

    /**
     * List all secret keys
     */
    public function listKeys(): array
    {
        $keys = [];
        $files = glob($this->secretsPath . '/*.secret');

        foreach ($files as $file) {
            $key = basename($file, '.secret');
            $keys[] = $key;
        }

        return $keys;
    }

    /**
     * Rotate encryption key
     */
    public function rotateEncryptionKey(): bool
    {
        try {
            // Get all current secrets
            $secrets = [];
            foreach ($this->listKeys() as $key) {
                $secrets[$key] = $this->get($key);
            }

            // Generate new encryption key
            $newKey = $this->generateEncryptionKey();
            $this->storeEncryptionKey($newKey);
            $this->encryptionKey = $newKey;

            // Re-encrypt all secrets with new key
            foreach ($secrets as $key => $value) {
                $this->store($key, $value);
            }

            // Clear cache
            $this->cache = [];

            return true;
        } catch (\Exception $e) {
            error_log("Failed to rotate encryption key: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Backup secrets (encrypted)
     */
    public function backup(string $backupPath): bool
    {
        try {
            $backup = [
                'timestamp' => time(),
                'secrets' => []
            ];

            foreach ($this->listKeys() as $key) {
                $filePath = $this->getSecretPath($key);
                $encrypted = file_get_contents($filePath);
                $backup['secrets'][$key] = base64_encode($encrypted);
            }

            $backupData = json_encode($backup, JSON_PRETTY_PRINT);
            $result = file_put_contents($backupPath, $backupData, LOCK_EX);

            if ($result !== false) {
                chmod($backupPath, 0600);
                return true;
            }

            return false;
        } catch (\Exception $e) {
            error_log("Failed to backup secrets: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Restore secrets from backup
     */
    public function restore(string $backupPath): bool
    {
        try {
            if (!file_exists($backupPath)) {
                return false;
            }

            $backupData = file_get_contents($backupPath);
            $backup = json_decode($backupData, true);

            if (!$backup || !isset($backup['secrets'])) {
                return false;
            }

            foreach ($backup['secrets'] as $key => $encryptedData) {
                $encrypted = base64_decode($encryptedData);
                $filePath = $this->getSecretPath($key);

                file_put_contents($filePath, $encrypted, LOCK_EX);
                chmod($filePath, 0600);
            }

            // Clear cache to force reload
            $this->cache = [];

            return true;
        } catch (\Exception $e) {
            error_log("Failed to restore secrets: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Encrypt data
     */
    private function encrypt(string $data): string
    {
        $iv = random_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $this->encryptionKey, 0, $iv);

        if ($encrypted === false) {
            throw new \RuntimeException('Encryption failed');
        }

        return base64_encode($iv . $encrypted);
    }

    /**
     * Decrypt data
     */
    private function decrypt(string $encryptedData): string
    {
        $data = base64_decode($encryptedData);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);

        $decrypted = openssl_decrypt($encrypted, 'AES-256-CBC', $this->encryptionKey, 0, $iv);

        if ($decrypted === false) {
            throw new \RuntimeException('Decryption failed');
        }

        return $decrypted;
    }

    /**
     * Get encryption key
     */
    private function getEncryptionKey(): string
    {
        $keyFile = $this->secretsPath . '/.encryption_key';

        if (file_exists($keyFile)) {
            return file_get_contents($keyFile);
        }

        // Generate new key
        $key = $this->generateEncryptionKey();
        $this->storeEncryptionKey($key);

        return $key;
    }

    /**
     * Generate encryption key
     */
    private function generateEncryptionKey(): string
    {
        return random_bytes(32);
    }

    /**
     * Store encryption key
     */
    private function storeEncryptionKey(string $key): void
    {
        $keyFile = $this->secretsPath . '/.encryption_key';
        file_put_contents($keyFile, $key, LOCK_EX);
        chmod($keyFile, 0600);
    }

    /**
     * Get secret file path
     */
    private function getSecretPath(string $key): string
    {
        // Sanitize key name
        $key = preg_replace('/[^a-zA-Z0-9_-]/', '_', $key);
        return $this->secretsPath . '/' . $key . '.secret';
    }

    /**
     * Ensure secrets directory exists
     */
    private function ensureSecretsDirectory(): void
    {
        if (!is_dir($this->secretsPath)) {
            mkdir($this->secretsPath, 0700, true);
        }
    }

    /**
     * Securely delete file
     */
    private function secureDelete(string $filePath): bool
    {
        if (!file_exists($filePath)) {
            return true;
        }

        // Overwrite file with random data multiple times
        $fileSize = filesize($filePath);
        $handle = fopen($filePath, 'r+b');

        if ($handle) {
            for ($i = 0; $i < 3; $i++) {
                fseek($handle, 0);
                fwrite($handle, random_bytes($fileSize));
                fflush($handle);
            }
            fclose($handle);
        }

        return unlink($filePath);
    }

    /**
     * Clear cache
     */
    public function clearCache(): void
    {
        $this->cache = [];
    }

    /**
     * Get cache statistics
     */
    public function getCacheStats(): array
    {
        return [
            'cached_secrets' => count($this->cache),
            'total_secrets' => count($this->listKeys()),
            'cache_hit_ratio' => count($this->cache) > 0 ?
                count($this->cache) / count($this->listKeys()) : 0
        ];
    }
}
