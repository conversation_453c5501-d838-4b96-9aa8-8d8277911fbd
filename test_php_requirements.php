<?php
/**
 * PHP Requirements Checker for WeBot
 * 
 * This script checks if all required PHP extensions and configurations
 * are properly set up for WeBot to function correctly.
 */

declare(strict_types=1);

echo "=== WeBot PHP Requirements Check ===\n\n";

// Check PHP version
echo "1. PHP Version Check:\n";
$phpVersion = PHP_VERSION;
$requiredVersion = '8.1.0';
$versionOk = version_compare($phpVersion, $requiredVersion, '>=');

echo "   Current PHP Version: {$phpVersion}\n";
echo "   Required PHP Version: {$requiredVersion}+\n";
echo "   Status: " . ($versionOk ? "✅ OK" : "❌ FAIL") . "\n\n";

// Check required extensions
echo "2. Required Extensions Check:\n";
$requiredExtensions = [
    'curl' => 'HTTP client functionality',
    'json' => 'JSON encoding/decoding',
    'pdo' => 'Database connectivity',
    'mbstring' => 'Multi-byte string handling',
    'gd' => 'Image processing (QR codes)',
    'redis' => 'Cache and session storage (optional but recommended)'
];

$allExtensionsOk = true;
foreach ($requiredExtensions as $extension => $description) {
    $loaded = extension_loaded($extension);
    $status = $loaded ? "✅ OK" : "❌ MISSING";
    
    if (!$loaded && $extension !== 'redis') {
        $allExtensionsOk = false;
    }
    
    echo "   {$extension}: {$status} - {$description}\n";
}

echo "\n";

// Check important PHP settings
echo "3. PHP Configuration Check:\n";
$settings = [
    'memory_limit' => ['current' => ini_get('memory_limit'), 'recommended' => '256M'],
    'max_execution_time' => ['current' => ini_get('max_execution_time'), 'recommended' => '30'],
    'upload_max_filesize' => ['current' => ini_get('upload_max_filesize'), 'recommended' => '10M'],
    'post_max_size' => ['current' => ini_get('post_max_size'), 'recommended' => '10M']
];

foreach ($settings as $setting => $info) {
    echo "   {$setting}: {$info['current']} (recommended: {$info['recommended']})\n";
}

echo "\n";

// Overall status
echo "=== Overall Status ===\n";
if ($versionOk && $allExtensionsOk) {
    echo "✅ All requirements met! WeBot should work properly.\n";
    exit(0);
} else {
    echo "❌ Some requirements are missing. Please install missing components.\n";
    
    if (!$versionOk) {
        echo "\n🔧 To fix PHP version:\n";
        echo "   - Update PHP to version 8.1 or higher\n";
    }
    
    if (!$allExtensionsOk) {
        echo "\n🔧 To fix missing extensions:\n";
        echo "   - Install missing PHP extensions\n";
        echo "   - On Windows with XAMPP: Enable extensions in php.ini\n";
        echo "   - On Ubuntu/Debian: sudo apt-get install php-[extension-name]\n";
        echo "   - On CentOS/RHEL: sudo yum install php-[extension-name]\n";
    }
    
    exit(1);
}
