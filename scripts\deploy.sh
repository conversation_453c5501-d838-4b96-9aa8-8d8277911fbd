#!/bin/bash
# WeBot Deployment Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-production}
VERSION=${2:-latest}
BACKUP_DIR="/var/backups/webot"
APP_DIR="/var/www/webot"

echo -e "${BLUE}🚀 WeBot Deployment Script v2.0${NC}"
echo -e "${BLUE}Environment: ${ENVIRONMENT}${NC}"
echo -e "${BLUE}Version: ${VERSION}${NC}"
echo "=================================="

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_permissions() {
    if [[ $EUID -eq 0 ]]; then
        log_error "This script should not be run as root"
        exit 1
    fi
}

# Backup current deployment
backup_current() {
    log_info "Creating backup..."
    
    BACKUP_NAME="webot_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    if [ -d "$APP_DIR" ]; then
        tar -czf "$BACKUP_DIR/$BACKUP_NAME.tar.gz" -C "$APP_DIR" .
        log_info "Backup created: $BACKUP_DIR/$BACKUP_NAME.tar.gz"
    else
        log_warn "No existing deployment found to backup"
    fi
}

# Health check function
health_check() {
    local url=$1
    local max_attempts=30
    local attempt=1
    
    log_info "Performing health check on $url"
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$url/health" > /dev/null; then
            log_info "Health check passed!"
            return 0
        fi
        
        log_warn "Health check attempt $attempt/$max_attempts failed, retrying in 10s..."
        sleep 10
        ((attempt++))
    done
    
    log_error "Health check failed after $max_attempts attempts"
    return 1
}

# Deploy function
deploy() {
    log_info "Starting deployment..."
    
    # Navigate to app directory
    cd "$APP_DIR"
    
    # Pull latest changes
    log_info "Pulling latest code..."
    git fetch origin
    git checkout main
    git pull origin main
    
    # Update dependencies
    log_info "Updating dependencies..."
    composer install --no-dev --optimize-autoloader --no-interaction
    
    # Clear cache
    log_info "Clearing cache..."
    rm -rf storage/cache/*
    
    # Set permissions
    log_info "Setting permissions..."
    chmod -R 755 storage public
    chmod -R 644 storage/logs
    
    # Update Docker containers
    log_info "Updating Docker containers..."
    docker-compose pull
    docker-compose up -d --force-recreate
    
    # Wait for services to start
    log_info "Waiting for services to start..."
    sleep 30
    
    # Run migrations
    log_info "Running database migrations..."
    docker-compose exec -T webot php migrations/migrate.php
    
    # Clean up Docker
    log_info "Cleaning up Docker..."
    docker system prune -f
    
    log_info "Deployment completed successfully!"
}

# Rollback function
rollback() {
    log_warn "Rolling back deployment..."
    
    # Find latest backup
    LATEST_BACKUP=$(ls -t "$BACKUP_DIR"/*.tar.gz 2>/dev/null | head -n1)
    
    if [ -z "$LATEST_BACKUP" ]; then
        log_error "No backup found for rollback"
        exit 1
    fi
    
    log_info "Rolling back to: $LATEST_BACKUP"
    
    # Stop services
    cd "$APP_DIR"
    docker-compose down
    
    # Restore backup
    rm -rf "$APP_DIR"/*
    tar -xzf "$LATEST_BACKUP" -C "$APP_DIR"
    
    # Restart services
    docker-compose up -d
    
    log_info "Rollback completed"
}

# Main deployment process
main() {
    check_permissions
    
    case "$ENVIRONMENT" in
        "production")
            APP_URL="https://your-domain.com"
            ;;
        "staging")
            APP_URL="https://staging.your-domain.com"
            ;;
        *)
            log_error "Invalid environment: $ENVIRONMENT"
            exit 1
            ;;
    esac
    
    # Create backup
    backup_current
    
    # Deploy
    if deploy; then
        # Health check
        if health_check "$APP_URL"; then
            log_info "✅ Deployment successful!"
            
            # Send notification
            if [ -n "$SLACK_WEBHOOK" ]; then
                curl -X POST -H 'Content-type: application/json' \
                    --data "{\"text\":\"🚀 WeBot deployed successfully to $ENVIRONMENT\"}" \
                    "$SLACK_WEBHOOK"
            fi
        else
            log_error "❌ Health check failed, rolling back..."
            rollback
            exit 1
        fi
    else
        log_error "❌ Deployment failed, rolling back..."
        rollback
        exit 1
    fi
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "rollback")
        rollback
        ;;
    "health")
        health_check "${2:-http://localhost}"
        ;;
    *)
        echo "Usage: $0 {deploy|rollback|health} [environment] [version]"
        echo "  deploy    - Deploy WeBot"
        echo "  rollback  - Rollback to previous version"
        echo "  health    - Check application health"
        exit 1
        ;;
esac
