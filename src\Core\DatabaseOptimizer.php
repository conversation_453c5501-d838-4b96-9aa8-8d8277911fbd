<?php

declare(strict_types=1);

namespace WeBot\Core;

use WeBot\Services\DatabaseService;

/**
 * Database Optimizer
 *
 * Optimizes database performance through query optimization,
 * indexing, connection pooling, and performance monitoring.
 *
 * @package WeBot\Core
 * @version 2.0
 */
class DatabaseOptimizer
{
    private DatabaseService $database;
    private array $queryLog = [];
    private array $slowQueries = [];
    private float $slowQueryThreshold = 1.0; // seconds
    private bool $profilingEnabled = false;

    public function __construct(DatabaseService $database)
    {
        $this->database = $database;
        $this->profilingEnabled = !Environment::getInstance()->isProduction();
    }

    /**
     * Optimize database tables
     */
    public function optimizeTables(): array
    {
        $results = [];
        $tables = $this->getTables();

        foreach ($tables as $table) {
            $results[$table] = $this->optimizeTable($table);
        }

        return $results;
    }

    /**
     * Optimize specific table
     */
    public function optimizeTable(string $table): array
    {
        $result = [
            'table' => $table,
            'before' => $this->getTableStats($table),
            'optimizations' => [],
            'after' => null
        ];

        // Analyze table
        $this->database->query("ANALYZE TABLE `{$table}`");
        $result['optimizations'][] = 'analyzed';

        // Optimize table
        $this->database->query("OPTIMIZE TABLE `{$table}`");
        $result['optimizations'][] = 'optimized';

        // Check for missing indexes
        $missingIndexes = $this->findMissingIndexes($table);
        foreach ($missingIndexes as $index) {
            $this->createIndex($table, $index);
            $result['optimizations'][] = "created_index_{$index['name']}";
        }

        $result['after'] = $this->getTableStats($table);

        return $result;
    }

    /**
     * Create recommended indexes
     */
    public function createRecommendedIndexes(): array
    {
        $indexes = [
            'users' => [
                ['columns' => ['userid'], 'type' => 'UNIQUE', 'name' => 'idx_users_userid'],
                ['columns' => ['step'], 'type' => 'INDEX', 'name' => 'idx_users_step'],
                ['columns' => ['isAdmin'], 'type' => 'INDEX', 'name' => 'idx_users_admin'],
                ['columns' => ['banned'], 'type' => 'INDEX', 'name' => 'idx_users_banned'],
                ['columns' => ['created_at'], 'type' => 'INDEX', 'name' => 'idx_users_created']
            ],
            'payments' => [
                ['columns' => ['user_id'], 'type' => 'INDEX', 'name' => 'idx_payments_user'],
                ['columns' => ['status'], 'type' => 'INDEX', 'name' => 'idx_payments_status'],
                ['columns' => ['gateway'], 'type' => 'INDEX', 'name' => 'idx_payments_gateway'],
                ['columns' => ['created_at'], 'type' => 'INDEX', 'name' => 'idx_payments_created'],
                ['columns' => ['user_id', 'status'], 'type' => 'INDEX', 'name' => 'idx_payments_user_status']
            ],
            'services' => [
                ['columns' => ['user_id'], 'type' => 'INDEX', 'name' => 'idx_services_user'],
                ['columns' => ['server_id'], 'type' => 'INDEX', 'name' => 'idx_services_server'],
                ['columns' => ['status'], 'type' => 'INDEX', 'name' => 'idx_services_status'],
                ['columns' => ['expires_at'], 'type' => 'INDEX', 'name' => 'idx_services_expires'],
                ['columns' => ['user_id', 'status'], 'type' => 'INDEX', 'name' => 'idx_services_user_status']
            ],
            'server_info' => [
                ['columns' => ['status'], 'type' => 'INDEX', 'name' => 'idx_server_status'],
                ['columns' => ['panel_type'], 'type' => 'INDEX', 'name' => 'idx_server_panel_type']
            ]
        ];

        $results = [];
        foreach ($indexes as $table => $tableIndexes) {
            if ($this->tableExists($table)) {
                foreach ($tableIndexes as $index) {
                    if (!$this->indexExists($table, $index['name'])) {
                        $results[] = $this->createIndex($table, $index);
                    }
                }
            }
        }

        return $results;
    }

    /**
     * Analyze slow queries
     */
    public function analyzeSlowQueries(): array
    {
        // Enable slow query log
        $this->database->query("SET GLOBAL slow_query_log = 'ON'");
        $this->database->query("SET GLOBAL long_query_time = {$this->slowQueryThreshold}");

        // Get slow queries from log
        $slowQueries = $this->getSlowQueriesFromLog();

        $analysis = [];
        foreach ($slowQueries as $query) {
            $analysis[] = [
                'query' => $query['sql'],
                'execution_time' => $query['query_time'],
                'rows_examined' => $query['rows_examined'],
                'rows_sent' => $query['rows_sent'],
                'suggestions' => $this->suggestOptimizations($query)
            ];
        }

        return $analysis;
    }

    /**
     * Optimize query performance
     */
    public function optimizeQuery(string $sql): array
    {
        $startTime = microtime(true);

        // Explain query
        $explain = $this->explainQuery($sql);

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        $optimization = [
            'original_sql' => $sql,
            'execution_time' => $executionTime,
            'explain' => $explain,
            'suggestions' => $this->analyzeExplain($explain),
            'optimized_sql' => $this->optimizeSql($sql, $explain)
        ];

        if ($this->profilingEnabled && $executionTime > $this->slowQueryThreshold) {
            $this->slowQueries[] = $optimization;
        }

        return $optimization;
    }

    /**
     * Setup connection pooling
     */
    public function setupConnectionPooling(): array
    {
        $config = [
            'max_connections' => 200,
            'max_user_connections' => 50,
            'thread_cache_size' => 16,
            'table_open_cache' => 4000,
            'query_cache_type' => 1,
            'query_cache_size' => '64M',
            'innodb_buffer_pool_size' => '1G',
            'innodb_log_file_size' => '256M',
            'innodb_flush_log_at_trx_commit' => 2,
            'innodb_file_per_table' => 1
        ];

        $results = [];
        foreach ($config as $variable => $value) {
            try {
                $this->database->query("SET GLOBAL {$variable} = {$value}");
                $results[$variable] = ['status' => 'success', 'value' => $value];
            } catch (\Exception $e) {
                $results[$variable] = ['status' => 'error', 'error' => $e->getMessage()];
            }
        }

        return $results;
    }

    /**
     * Monitor database performance
     */
    public function getPerformanceMetrics(): array
    {
        $metrics = [];

        // Connection metrics
        $metrics['connections'] = $this->getConnectionMetrics();

        // Query metrics
        $metrics['queries'] = $this->getQueryMetrics();

        // InnoDB metrics
        $metrics['innodb'] = $this->getInnoDBMetrics();

        // Table metrics
        $metrics['tables'] = $this->getTableMetrics();

        // Slow query metrics
        $metrics['slow_queries'] = count($this->slowQueries);

        return $metrics;
    }

    /**
     * Get table statistics
     */
    private function getTableStats(string $table): array
    {
        $stats = $this->database->fetchRow(
            "SELECT 
                table_rows,
                data_length,
                index_length,
                data_free,
                auto_increment
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() AND table_name = ?",
            [$table]
        );

        return $stats ?: [];
    }

    /**
     * Find missing indexes
     */
    private function findMissingIndexes(string $table): array
    {
        // This is a simplified version - in production, you'd analyze query patterns
        $missingIndexes = [];

        // Check for foreign key columns without indexes
        $foreignKeys = $this->getForeignKeyColumns($table);
        $existingIndexes = $this->getExistingIndexes($table);

        foreach ($foreignKeys as $column) {
            if (!$this->columnHasIndex($column, $existingIndexes)) {
                $missingIndexes[] = [
                    'name' => "idx_{$table}_{$column}",
                    'columns' => [$column],
                    'type' => 'INDEX'
                ];
            }
        }

        return $missingIndexes;
    }

    /**
     * Create index
     */
    private function createIndex(string $table, array $index): array
    {
        try {
            $columns = implode(', ', array_map(fn($col) => "`{$col}`", $index['columns']));

            $sql = "CREATE {$index['type']} {$index['name']} ON `{$table}` ({$columns})";
            $this->database->query($sql);

            return [
                'status' => 'success',
                'table' => $table,
                'index' => $index['name'],
                'sql' => $sql
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'table' => $table,
                'index' => $index['name'],
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Explain query
     */
    private function explainQuery(string $sql): array
    {
        try {
            return $this->database->fetchAll("EXPLAIN {$sql}");
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Analyze explain output
     */
    private function analyzeExplain(array $explain): array
    {
        $suggestions = [];

        foreach ($explain as $row) {
            // Check for full table scans
            if ($row['type'] === 'ALL') {
                $suggestions[] = "Full table scan detected on {$row['table']}. Consider adding an index.";
            }

            // Check for filesort
            if (str_contains($row['Extra'] ?? '', 'Using filesort')) {
                $suggestions[] = "Filesort detected on {$row['table']}. Consider optimizing ORDER BY clause.";
            }

            // Check for temporary tables
            if (str_contains($row['Extra'] ?? '', 'Using temporary')) {
                $suggestions[] = "Temporary table created for {$row['table']}. Consider query optimization.";
            }

            // Check for high row examination
            if (($row['rows'] ?? 0) > 1000) {
                $suggestions[] = "High number of rows examined ({$row['rows']}) on {$row['table']}.";
            }
        }

        return $suggestions;
    }

    /**
     * Get connection metrics
     */
    private function getConnectionMetrics(): array
    {
        $metrics = $this->database->fetchAll("SHOW STATUS LIKE 'Connections'");
        $threads = $this->database->fetchAll("SHOW STATUS LIKE 'Threads_%'");

        return array_merge($metrics, $threads);
    }

    /**
     * Get query metrics
     */
    private function getQueryMetrics(): array
    {
        return $this->database->fetchAll("SHOW STATUS LIKE 'Com_%'");
    }

    /**
     * Get InnoDB metrics
     */
    private function getInnoDBMetrics(): array
    {
        return $this->database->fetchAll("SHOW STATUS LIKE 'Innodb_%'");
    }

    /**
     * Get table metrics
     */
    private function getTableMetrics(): array
    {
        return $this->database->fetchAll(
            "SELECT 
                table_name,
                table_rows,
                data_length,
                index_length,
                data_free
            FROM information_schema.tables 
            WHERE table_schema = DATABASE()"
        );
    }

    /**
     * Helper methods
     */
    private function getTables(): array
    {
        $tables = $this->database->fetchAll("SHOW TABLES");
        return array_column($tables, array_keys($tables[0])[0]);
    }

    private function tableExists(string $table): bool
    {
        $result = $this->database->fetchValue(
            "SELECT COUNT(*) FROM information_schema.tables 
             WHERE table_schema = DATABASE() AND table_name = ?",
            [$table]
        );
        return $result > 0;
    }

    private function indexExists(string $table, string $index): bool
    {
        $result = $this->database->fetchValue(
            "SELECT COUNT(*) FROM information_schema.statistics 
             WHERE table_schema = DATABASE() AND table_name = ? AND index_name = ?",
            [$table, $index]
        );
        return $result > 0;
    }

    private function getForeignKeyColumns(string $table): array
    {
        // Simplified - return common foreign key patterns
        $commonForeignKeys = ['user_id', 'server_id', 'plan_id', 'payment_id'];
        $existingColumns = $this->getTableColumns($table);

        return array_intersect($commonForeignKeys, $existingColumns);
    }

    private function getTableColumns(string $table): array
    {
        $columns = $this->database->fetchAll("SHOW COLUMNS FROM `{$table}`");
        return array_column($columns, 'Field');
    }

    private function getExistingIndexes(string $table): array
    {
        return $this->database->fetchAll("SHOW INDEX FROM `{$table}`");
    }

    private function columnHasIndex(string $column, array $indexes): bool
    {
        foreach ($indexes as $index) {
            if ($index['Column_name'] === $column) {
                return true;
            }
        }
        return false;
    }

    private function getSlowQueriesFromLog(): array
    {
        // This would parse the slow query log file
        // For now, return the in-memory slow queries
        return $this->slowQueries;
    }

    private function suggestOptimizations(array $query): array
    {
        $suggestions = [];

        if ($query['query_time'] > 2.0) {
            $suggestions[] = 'Consider adding appropriate indexes';
            $suggestions[] = 'Review query structure for optimization opportunities';
        }

        if ($query['rows_examined'] > $query['rows_sent'] * 10) {
            $suggestions[] = 'Query examines too many rows - optimize WHERE clause';
            $suggestions[] = 'Consider adding more selective indexes';
        }

        if ($query['rows_examined'] > 100000) {
            $suggestions[] = 'Large table scan detected - consider partitioning';
        }

        if (stripos($query['sql'], 'ORDER BY') !== false && stripos($query['sql'], 'LIMIT') === false) {
            $suggestions[] = 'ORDER BY without LIMIT can be expensive - consider adding LIMIT';
        }

        if (stripos($query['sql'], 'SELECT *') !== false) {
            $suggestions[] = 'Avoid SELECT * - specify only needed columns';
        }

        return $suggestions;
    }

    private function optimizeSql(string $sql, array $explain): string
    {
        $optimized = $sql;

        // Replace SELECT * with specific columns if possible
        if (stripos($sql, 'SELECT *') !== false) {
            // This would need table schema analysis
            // For now, just add a comment
            $optimized = "/* TODO: Replace SELECT * with specific columns */ " . $sql;
        }

        // Add LIMIT if ORDER BY without LIMIT
        if (stripos($sql, 'ORDER BY') !== false && stripos($sql, 'LIMIT') === false) {
            // Add suggested LIMIT
            $optimized .= " /* Consider adding LIMIT clause */";
        }

        // Suggest index hints based on explain
        foreach ($explain as $row) {
            if (isset($row['key']) && $row['key'] === null && $row['rows'] > 1000) {
                $table = $row['table'];
                $optimized = "/* Consider adding index on {$table} */ " . $optimized;
            }
        }

        return $optimized;
    }

    /**
     * Suggest database indexes for optimization
     */
    public function suggestIndexes(): array
    {
        $suggestions = [];

        try {
            // Get slow queries that might benefit from indexes
            $slowQueries = $this->analyzeSlowQueries();

            foreach ($slowQueries as $query) {
                if (isset($query['sql'])) {
                    $indexSuggestions = $this->analyzeQueryForIndexes($query['sql']);
                    $suggestions = array_merge($suggestions, $indexSuggestions);
                }
            }

            // Get tables without primary keys
            $tablesWithoutPK = $this->findTablesWithoutPrimaryKey();
            foreach ($tablesWithoutPK as $table) {
                $suggestions[] = [
                    'type' => 'primary_key',
                    'table' => $table,
                    'recommendation' => "Add primary key to table {$table}",
                    'priority' => 'high',
                    'sql' => "ALTER TABLE `{$table}` ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST;"
                ];
            }

            // Get tables with missing foreign key indexes
            $missingFKIndexes = $this->findMissingForeignKeyIndexes();
            foreach ($missingFKIndexes as $fkIndex) {
                $suggestions[] = [
                    'type' => 'foreign_key_index',
                    'table' => $fkIndex['table'],
                    'column' => $fkIndex['column'],
                    'recommendation' => "Add index for foreign key {$fkIndex['column']} in table {$fkIndex['table']}",
                    'priority' => 'medium',
                    'sql' => "CREATE INDEX idx_{$fkIndex['table']}_{$fkIndex['column']} ON `{$fkIndex['table']}` (`{$fkIndex['column']}`);"
                ];
            }
        } catch (\Exception $e) {
            $suggestions[] = [
                'type' => 'error',
                'message' => 'Failed to analyze indexes: ' . $e->getMessage()
            ];
        }

        return $suggestions;
    }

    /**
     * Analyze query for potential index improvements
     */
    private function analyzeQueryForIndexes(string $sql): array
    {
        $suggestions = [];

        // Simple analysis - look for WHERE clauses without indexes
        if (preg_match_all('/WHERE\s+(\w+)\.?(\w+)\s*[=<>]/i', $sql, $matches)) {
            for ($i = 0; $i < count($matches[0]); $i++) {
                $table = $matches[1][$i];
                $column = $matches[2][$i];

                $suggestions[] = [
                    'type' => 'where_clause_index',
                    'table' => $table,
                    'column' => $column,
                    'recommendation' => "Consider adding index on {$table}.{$column} for WHERE clause optimization",
                    'priority' => 'medium',
                    'sql' => "CREATE INDEX idx_{$table}_{$column} ON `{$table}` (`{$column}`);"
                ];
            }
        }

        return $suggestions;
    }

    /**
     * Find tables without primary key
     */
    private function findTablesWithoutPrimaryKey(): array
    {
        $tables = [];

        try {
            $result = $this->database->query("
                SELECT TABLE_NAME
                FROM information_schema.TABLES
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME NOT IN (
                    SELECT TABLE_NAME
                    FROM information_schema.KEY_COLUMN_USAGE
                    WHERE CONSTRAINT_NAME = 'PRIMARY'
                    AND TABLE_SCHEMA = DATABASE()
                )
            ");

            while ($row = $result->fetch_assoc()) {
                $tables[] = $row['TABLE_NAME'];
            }
        } catch (\Exception $e) {
            // Ignore errors for now
        }

        return $tables;
    }

    /**
     * Find missing foreign key indexes
     */
    private function findMissingForeignKeyIndexes(): array
    {
        $missing = [];

        try {
            $result = $this->database->query("
                SELECT
                    kcu.TABLE_NAME,
                    kcu.COLUMN_NAME
                FROM information_schema.KEY_COLUMN_USAGE kcu
                LEFT JOIN information_schema.STATISTICS s
                    ON kcu.TABLE_NAME = s.TABLE_NAME
                    AND kcu.COLUMN_NAME = s.COLUMN_NAME
                WHERE kcu.TABLE_SCHEMA = DATABASE()
                AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
                AND s.COLUMN_NAME IS NULL
            ");

            while ($row = $result->fetch_assoc()) {
                $missing[] = [
                    'table' => $row['TABLE_NAME'],
                    'column' => $row['COLUMN_NAME']
                ];
            }
        } catch (\Exception $e) {
            // Ignore errors for now
        }

        return $missing;
    }
}
