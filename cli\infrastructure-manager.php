#!/usr/bin/env php
<?php

declare(strict_types=1);

require_once __DIR__ . '/../autoload.php';

use WeBot\Core\Application;
use WeBot\Core\Config;
use WeBot\Core\CacheManager;
use WeBot\Services\DatabaseService;
use WeBot\Infrastructure\InfrastructureManager;
use WeBot\Utils\Logger;

/**
 * Infrastructure Manager CLI
 * 
 * Command-line interface for managing infrastructure services,
 * load balancing, auto-scaling, health checks, and circuit breakers.
 * 
 * @package WeBot\CLI
 * @version 2.0
 */
class InfrastructureManagerCLI
{
    private Application $app;
    private InfrastructureManager $infrastructureManager;
    private Logger $logger;
    
    public function __construct()
    {
        $this->app = new Application();
        $this->logger = Logger::getInstance();

        // Initialize infrastructure manager
        $config = new Config();
        $configArray = [
            'prefix' => 'webot:',
            'default_ttl' => 3600,
            'enabled' => true,
            'redis_host' => '127.0.0.1',
            'redis_port' => 6379
        ];
        $cache = new CacheManager($configArray);
        $database = new DatabaseService($config);

        $this->infrastructureManager = new InfrastructureManager($cache, $database);
    }
    
    /**
     * Main CLI entry point
     */
    public function run(array $argv): void
    {
        $command = $argv[1] ?? 'help';
        $args = array_slice($argv, 2);
        
        try {
            switch ($command) {
                case 'status':
                    $this->statusCommand($args);
                    break;
                    
                case 'dashboard':
                    $this->dashboardCommand($args);
                    break;
                    
                case 'health':
                    $this->healthCommand($args);
                    break;
                    
                case 'loadbalancer':
                case 'lb':
                    $this->loadBalancerCommand($args);
                    break;
                    
                case 'scaling':
                    $this->scalingCommand($args);
                    break;
                    
                case 'circuit-breaker':
                case 'cb':
                    $this->circuitBreakerCommand($args);
                    break;
                    
                case 'optimize':
                    $this->optimizeCommand($args);
                    break;
                    
                case 'alerts':
                    $this->alertsCommand($args);
                    break;
                    
                case 'metrics':
                    $this->metricsCommand($args);
                    break;
                    
                case 'monitor':
                    $this->monitorCommand($args);
                    break;
                    
                case 'help':
                default:
                    $this->helpCommand();
                    break;
            }
        } catch (\Exception $e) {
            $this->error("Command failed: " . $e->getMessage());
            exit(1);
        }
    }
    
    /**
     * Status command
     */
    private function statusCommand(array $args): void
    {
        $component = $args[0] ?? 'all';
        
        $this->info("Getting infrastructure status...");
        
        try {
            if ($component === 'all') {
                $result = $this->infrastructureManager->handleRequest([
                    'action' => 'get_infrastructure_status'
                ]);
                $this->displayInfrastructureStatus($result);
            } else {
                $this->displayComponentStatus($component);
            }
        } catch (\Exception $e) {
            $this->error("Status check failed: " . $e->getMessage());
        }
    }
    
    /**
     * Dashboard command
     */
    private function dashboardCommand(array $args): void
    {
        $this->info("Generating infrastructure dashboard...");
        
        try {
            $result = $this->infrastructureManager->handleRequest([
                'action' => 'get_infrastructure_dashboard'
            ]);
            
            $this->displayDashboard($result);
        } catch (\Exception $e) {
            $this->error("Dashboard generation failed: " . $e->getMessage());
        }
    }
    
    /**
     * Health command
     */
    private function healthCommand(array $args): void
    {
        $action = $args[0] ?? 'check';
        
        try {
            switch ($action) {
                case 'check':
                    $result = $this->infrastructureManager->handleRequest([
                        'action' => 'perform_infrastructure_health_check'
                    ]);
                    $this->displayHealthCheck($result);
                    break;
                    
                case 'dashboard':
                    $result = $this->infrastructureManager->handleRequest([
                        'action' => 'get_health_dashboard'
                    ]);
                    $this->displayHealthDashboard($result);
                    break;
                    
                case 'component':
                    $component = $args[1] ?? null;
                    if (!$component) {
                        $this->error("Component name is required");
                        return;
                    }
                    $result = $this->infrastructureManager->handleRequest([
                        'action' => 'get_component_health',
                        'data' => ['component' => $component]
                    ]);
                    $this->displayComponentHealth($result);
                    break;
                    
                default:
                    $this->error("Unknown health action: {$action}");
            }
        } catch (\Exception $e) {
            $this->error("Health command failed: " . $e->getMessage());
        }
    }
    
    /**
     * Load balancer command
     */
    private function loadBalancerCommand(array $args): void
    {
        $action = $args[0] ?? 'status';
        
        try {
            switch ($action) {
                case 'status':
                    $result = $this->infrastructureManager->handleRequest([
                        'action' => 'get_lb_statistics'
                    ]);
                    $this->displayLoadBalancerStatus($result);
                    break;
                    
                case 'add-server':
                    $host = $args[1] ?? null;
                    $port = isset($args[2]) ? (int)$args[2] : 80;
                    if (!$host) {
                        $this->error("Server host is required");
                        return;
                    }
                    $result = $this->infrastructureManager->handleRequest([
                        'action' => 'add_server',
                        'data' => [
                            'server_config' => [
                                'host' => $host,
                                'port' => $port,
                                'weight' => 1
                            ]
                        ]
                    ]);
                    if ($result['success']) {
                        $this->success("Server added successfully: {$host}:{$port}");
                    } else {
                        $this->error("Failed to add server");
                    }
                    break;
                    
                case 'remove-server':
                    $serverId = $args[1] ?? null;
                    if (!$serverId) {
                        $this->error("Server ID is required");
                        return;
                    }
                    $result = $this->infrastructureManager->handleRequest([
                        'action' => 'remove_server',
                        'data' => ['server_id' => $serverId]
                    ]);
                    if ($result['success']) {
                        $this->success("Server removed successfully: {$serverId}");
                    } else {
                        $this->error("Failed to remove server");
                    }
                    break;
                    
                case 'algorithm':
                    $algorithm = $args[1] ?? null;
                    if (!$algorithm) {
                        $this->error("Algorithm is required");
                        $this->info("Available algorithms: round_robin, least_connections, weighted_round_robin, ip_hash");
                        return;
                    }
                    $result = $this->infrastructureManager->handleRequest([
                        'action' => 'set_lb_algorithm',
                        'data' => ['algorithm' => $algorithm]
                    ]);
                    if ($result['success']) {
                        $this->success("Load balancing algorithm set to: {$algorithm}");
                    } else {
                        $this->error("Failed to set algorithm");
                    }
                    break;
                    
                default:
                    $this->error("Unknown load balancer action: {$action}");
            }
        } catch (\Exception $e) {
            $this->error("Load balancer command failed: " . $e->getMessage());
        }
    }
    
    /**
     * Scaling command
     */
    private function scalingCommand(array $args): void
    {
        $action = $args[0] ?? 'status';
        
        try {
            switch ($action) {
                case 'status':
                    $result = $this->infrastructureManager->handleRequest([
                        'action' => 'get_scaling_status'
                    ]);
                    $this->displayScalingStatus($result);
                    break;
                    
                case 'out':
                    $count = isset($args[1]) ? (int)$args[1] : 1;
                    $result = $this->infrastructureManager->handleRequest([
                        'action' => 'scale_out',
                        'data' => ['instance_count' => $count]
                    ]);
                    if ($result['success']) {
                        $this->success("Scaled out {$count} instance(s). Total: {$result['total_instances']}");
                    } else {
                        $this->error("Scale out failed: " . ($result['reason'] ?? 'Unknown error'));
                    }
                    break;
                    
                case 'in':
                    $count = isset($args[1]) ? (int)$args[1] : 1;
                    $result = $this->infrastructureManager->handleRequest([
                        'action' => 'scale_in',
                        'data' => ['instance_count' => $count]
                    ]);
                    if ($result['success']) {
                        $this->success("Scaled in {$count} instance(s). Total: {$result['total_instances']}");
                    } else {
                        $this->error("Scale in failed: " . ($result['reason'] ?? 'Unknown error'));
                    }
                    break;
                    
                case 'evaluate':
                    $result = $this->infrastructureManager->handleRequest([
                        'action' => 'evaluate_scaling'
                    ]);
                    $this->displayScalingEvaluation($result);
                    break;
                    
                default:
                    $this->error("Unknown scaling action: {$action}");
            }
        } catch (\Exception $e) {
            $this->error("Scaling command failed: " . $e->getMessage());
        }
    }
    
    /**
     * Circuit breaker command
     */
    private function circuitBreakerCommand(array $args): void
    {
        $action = $args[0] ?? 'status';
        
        try {
            switch ($action) {
                case 'status':
                    $service = $args[1] ?? null;
                    if ($service) {
                        $result = $this->infrastructureManager->handleRequest([
                            'action' => 'get_circuit_breaker_status',
                            'data' => ['service' => $service]
                        ]);
                        $this->displayCircuitBreakerStatus($result, $service);
                    } else {
                        $result = $this->infrastructureManager->handleRequest([
                            'action' => 'get_all_circuit_breaker_status'
                        ]);
                        $this->displayAllCircuitBreakerStatus($result);
                    }
                    break;
                    
                case 'reset':
                    $service = $args[1] ?? null;
                    if (!$service) {
                        $this->error("Service name is required");
                        return;
                    }
                    $result = $this->infrastructureManager->handleRequest([
                        'action' => 'reset_circuit_breaker',
                        'data' => ['service' => $service]
                    ]);
                    if ($result['success']) {
                        $this->success("Circuit breaker reset for service: {$service}");
                    } else {
                        $this->error("Failed to reset circuit breaker");
                    }
                    break;
                    
                default:
                    $this->error("Unknown circuit breaker action: {$action}");
            }
        } catch (\Exception $e) {
            $this->error("Circuit breaker command failed: " . $e->getMessage());
        }
    }
    
    /**
     * Optimize command
     */
    private function optimizeCommand(array $args): void
    {
        $apply = in_array('--apply', $args);
        
        $this->info("Analyzing infrastructure for optimization opportunities...");
        
        try {
            $result = $this->infrastructureManager->handleRequest([
                'action' => 'optimize_infrastructure',
                'data' => ['apply' => $apply]
            ]);
            
            $this->displayOptimizationResults($result, $apply);
        } catch (\Exception $e) {
            $this->error("Optimization failed: " . $e->getMessage());
        }
    }
    
    /**
     * Alerts command
     */
    private function alertsCommand(array $args): void
    {
        $action = $args[0] ?? 'list';
        
        try {
            switch ($action) {
                case 'list':
                    $result = $this->infrastructureManager->handleRequest([
                        'action' => 'get_infrastructure_alerts'
                    ]);
                    $this->displayAlerts($result);
                    break;
                    
                case 'ack':
                case 'acknowledge':
                    $alertId = $args[1] ?? null;
                    if (!$alertId) {
                        $this->error("Alert ID is required");
                        return;
                    }
                    $result = $this->infrastructureManager->handleRequest([
                        'action' => 'acknowledge_alert',
                        'data' => ['alert_id' => $alertId]
                    ]);
                    if ($result['success']) {
                        $this->success("Alert acknowledged: {$alertId}");
                    } else {
                        $this->error("Failed to acknowledge alert");
                    }
                    break;
                    
                default:
                    $this->error("Unknown alerts action: {$action}");
            }
        } catch (\Exception $e) {
            $this->error("Alerts command failed: " . $e->getMessage());
        }
    }
    
    /**
     * Metrics command
     */
    private function metricsCommand(array $args): void
    {
        $this->info("Collecting infrastructure metrics...");
        
        try {
            $result = $this->infrastructureManager->handleRequest([
                'action' => 'get_infrastructure_metrics'
            ]);
            
            $this->displayMetrics($result);
        } catch (\Exception $e) {
            $this->error("Metrics collection failed: " . $e->getMessage());
        }
    }
    
    /**
     * Monitor command (real-time monitoring)
     */
    private function monitorCommand(array $args): void
    {
        $interval = isset($args[0]) ? (int)$args[0] : 5;
        
        $this->info("Starting real-time monitoring (interval: {$interval}s)");
        $this->info("Press Ctrl+C to stop");
        
        while (true) {
            try {
                system('clear'); // Clear screen
                
                $result = $this->infrastructureManager->handleRequest([
                    'action' => 'get_infrastructure_status'
                ]);
                
                $this->displayMonitoringView($result);
                
                sleep($interval);
            } catch (\Exception $e) {
                $this->error("Monitoring failed: " . $e->getMessage());
                break;
            }
        }
    }
    
    /**
     * Display methods
     */
    private function displayInfrastructureStatus(array $status): void
    {
        $this->info("\n=== Infrastructure Status ===");
        $this->info("Overall Status: " . strtoupper($status['overall_status']));
        
        $this->info("\n=== Load Balancer ===");
        $lb = $status['load_balancer'];
        $this->info("Total Servers: " . $lb['total_servers']);
        $this->info("Healthy Servers: " . $lb['healthy_servers']);
        $this->info("Algorithm: " . $lb['algorithm']);
        
        $this->info("\n=== Auto Scaling ===");
        $scaling = $status['auto_scaling'];
        $this->info("Current Instances: " . $scaling['current_instances']);
        $this->info("Min Instances: " . $scaling['min_instances']);
        $this->info("Max Instances: " . $scaling['max_instances']);
        
        $this->info("\n=== Health Checks ===");
        $health = $status['health_checks'];
        $this->info("Current Status: " . strtoupper($health['current_status']));
        $this->info("Total Components: " . $health['summary']['total_components']);
        $this->info("Healthy Components: " . $health['summary']['healthy']);
        
        $this->info("\n=== Circuit Breakers ===");
        $cb = $status['circuit_breakers'];
        $this->info("Total Services: " . $cb['total_services']);
        $this->info("Healthy Services: " . $cb['healthy_services']);
        $this->info("Failed Services: " . $cb['failed_services']);
    }
    
    private function displayDashboard(array $dashboard): void
    {
        $this->info("\n=== Infrastructure Dashboard ===");
        
        $overview = $dashboard['overview'];
        $this->info("Total Servers: " . $overview['total_servers']);
        $this->info("Healthy Servers: " . $overview['healthy_servers']);
        $this->info("CPU Utilization: " . round($overview['cpu_utilization'] * 100, 1) . "%");
        $this->info("Memory Utilization: " . round($overview['memory_utilization'] * 100, 1) . "%");
        
        if (!empty($dashboard['alerts'])) {
            $this->warning("\n=== Active Alerts ===");
            foreach ($dashboard['alerts'] as $alert) {
                $this->warning("- [{$alert['severity']}] {$alert['component']}: {$alert['message']}");
            }
        }
        
        if (!empty($dashboard['recommendations'])) {
            $this->info("\n=== Recommendations ===");
            foreach ($dashboard['recommendations'] as $rec) {
                $this->info("- [{$rec['priority']}] {$rec['description']}");
            }
        }
    }
    
    private function displayHealthCheck(array $result): void
    {
        $this->info("\n=== Infrastructure Health Check ===");
        $this->info("Overall Health: " . strtoupper($result['overall_health']));
        $this->info("Check Time: " . date('Y-m-d H:i:s', $result['timestamp']));
        
        foreach ($result['components'] as $component => $health) {
            $status = $health['overall_status'] ?? $health['status'] ?? 'unknown';
            $statusColor = $status === 'healthy' ? 'green' : ($status === 'degraded' ? 'yellow' : 'red');
            $this->coloredOutput("  {$component}: {$status}", $statusColor);
        }
    }
    
    /**
     * Show help information
     */
    private function helpCommand(): void
    {
        $this->info("WeBot Infrastructure Manager CLI");
        $this->info("Usage: php infrastructure-manager.php <command> [options]");
        $this->info("");
        $this->info("Commands:");
        $this->info("  status [component]              Show infrastructure status");
        $this->info("  dashboard                       Show infrastructure dashboard");
        $this->info("  health <action> [params]        Health check operations");
        $this->info("  loadbalancer <action> [params]  Load balancer operations");
        $this->info("  scaling <action> [params]       Auto-scaling operations");
        $this->info("  circuit-breaker <action> [params] Circuit breaker operations");
        $this->info("  optimize [--apply]              Optimize infrastructure");
        $this->info("  alerts <action> [params]        Alert management");
        $this->info("  metrics                         Show infrastructure metrics");
        $this->info("  monitor [interval]              Real-time monitoring");
        $this->info("  help                            Show this help message");
        $this->info("");
        $this->info("Examples:");
        $this->info("  php infrastructure-manager.php status");
        $this->info("  php infrastructure-manager.php lb add-server 192.168.1.10 8080");
        $this->info("  php infrastructure-manager.php scaling out 2");
        $this->info("  php infrastructure-manager.php health check");
        $this->info("  php infrastructure-manager.php monitor 10");
    }
    
    /**
     * Output helper methods
     */
    private function coloredOutput(string $text, string $color): void
    {
        $colors = [
            'red' => "\033[31m",
            'green' => "\033[32m",
            'yellow' => "\033[33m",
            'blue' => "\033[34m",
            'reset' => "\033[0m"
        ];
        
        $colorCode = $colors[$color] ?? $colors['reset'];
        echo $colorCode . $text . $colors['reset'] . "\n";
    }
    
    private function info(string $message): void
    {
        echo $message . "\n";
    }
    
    private function success(string $message): void
    {
        $this->coloredOutput($message, 'green');
    }
    
    private function warning(string $message): void
    {
        $this->coloredOutput($message, 'yellow');
    }
    
    private function error(string $message): void
    {
        $this->coloredOutput($message, 'red');
    }

    /**
     * Additional display methods
     */
    private function displayComponentStatus(string $component): void
    {
        $this->info("Getting status for component: {$component}");
        // Mock implementation
        $this->info("Component: {$component}");
        $this->info("Status: Healthy");
        $this->info("Last Check: " . date('Y-m-d H:i:s'));
    }

    private function displayHealthDashboard(array $result): void
    {
        $this->info("\n=== Health Dashboard ===");
        $this->info("Current Status: " . strtoupper($result['current_status']));
        $this->info("Total Components: " . $result['summary']['total_components']);
        $this->info("Healthy: " . $result['summary']['healthy']);
        $this->info("Degraded: " . $result['summary']['degraded']);
        $this->info("Unhealthy: " . $result['summary']['unhealthy']);
    }

    private function displayComponentHealth(array $result): void
    {
        $this->info("\n=== Component Health ===");
        $this->info("Component: " . $result['component']);
        $this->info("Status: " . strtoupper($result['status']));
        $this->info("Details: " . json_encode($result['details'], JSON_PRETTY_PRINT));
    }

    private function displayLoadBalancerStatus(array $result): void
    {
        $this->info("\n=== Load Balancer Status ===");
        $this->info("Total Servers: " . $result['total_servers']);
        $this->info("Healthy Servers: " . $result['healthy_servers']);
        $this->info("Algorithm: " . $result['algorithm']);
        $this->info("Total Connections: " . $result['total_connections']);
    }

    private function displayScalingStatus(array $result): void
    {
        $this->info("\n=== Auto Scaling Status ===");
        $this->info("Current Instances: " . $result['current_instances']);
        $this->info("Min Instances: " . $result['min_instances']);
        $this->info("Max Instances: " . $result['max_instances']);

        if (!empty($result['recommendations'])) {
            $this->info("\nRecommendations:");
            foreach ($result['recommendations'] as $rec) {
                $this->info("- " . $rec['description']);
            }
        }
    }

    private function displayScalingEvaluation(array $result): void
    {
        $this->info("\n=== Scaling Evaluation ===");
        $this->info("Evaluation Time: " . date('Y-m-d H:i:s', $result['evaluation_time']));
        $this->info("Current Instances: " . $result['current_instances']);

        if (!empty($result['scaling_decisions'])) {
            $this->info("\nScaling Decisions:");
            foreach ($result['scaling_decisions'] as $decision) {
                $this->info("- Action: " . $decision['action']);
                $this->info("  Reason: " . $decision['reason']);
            }
        } else {
            $this->info("No scaling actions needed");
        }
    }

    private function displayCircuitBreakerStatus(array $result, string $service): void
    {
        $this->info("\n=== Circuit Breaker Status: {$service} ===");
        $this->info("State: " . strtoupper($result['state']));
        $this->info("Failure Count: " . $result['failure_count']);
        $this->info("Success Count: " . $result['success_count']);
        $this->info("Failure Rate: " . round($result['failure_rate'], 2) . "%");
        $this->info("Available: " . ($result['is_available'] ? 'Yes' : 'No'));
    }

    private function displayAllCircuitBreakerStatus(array $result): void
    {
        $this->info("\n=== All Circuit Breakers Status ===");
        $this->info("Total Services: " . $result['total_services']);
        $this->info("Healthy Services: " . $result['healthy_services']);
        $this->info("Failed Services: " . $result['failed_services']);

        foreach ($result['circuit_breakers'] as $service => $status) {
            $stateColor = $status['state'] === 'closed' ? 'green' : 'red';
            $this->coloredOutput("  {$service}: {$status['state']}", $stateColor);
        }
    }

    private function displayOptimizationResults(array $result, bool $applied): void
    {
        $this->info("\n=== Infrastructure Optimization ===");

        if ($applied) {
            $this->success("Optimizations applied successfully!");
        } else {
            $this->info("Optimization analysis completed (use --apply to apply changes)");
        }

        foreach ($result['optimizations'] as $component => $optimization) {
            $this->info("\n{$component}:");
            if (isset($optimization['recommended_algorithm'])) {
                $this->info("  Recommended: " . $optimization['recommended_algorithm']);
                $this->info("  Reason: " . $optimization['reason']);
            } elseif (isset($optimization['recommended_instances'])) {
                $this->info("  Recommended Instances: " . $optimization['recommended_instances']);
                $this->info("  Reason: " . $optimization['reason']);
            } else {
                $this->info("  " . json_encode($optimization, JSON_PRETTY_PRINT));
            }
        }
    }

    private function displayAlerts(array $result): void
    {
        $this->info("\n=== Infrastructure Alerts ===");

        if (!empty($result['active_alerts'])) {
            $this->warning("Active Alerts:");
            foreach ($result['active_alerts'] as $alert) {
                $this->warning("  [{$alert['severity']}] {$alert['component']}: {$alert['message']}");
            }
        } else {
            $this->success("No active alerts");
        }

        if (isset($result['alert_summary'])) {
            $summary = $result['alert_summary'];
            $this->info("\nAlert Summary (24h):");
            $this->info("  Total: " . $summary['total_alerts_24h']);
            $this->info("  Critical: " . $summary['critical_alerts_24h']);
            $this->info("  Warning: " . $summary['warning_alerts_24h']);
        }
    }

    private function displayMetrics(array $result): void
    {
        $this->info("\n=== Infrastructure Metrics ===");

        if (isset($result['system_metrics'])) {
            $system = $result['system_metrics'];
            $this->info("System Metrics:");
            $this->info("  CPU Usage: " . round($system['cpu_usage'] * 100, 1) . "%");
            $this->info("  Memory Usage: " . round($system['memory_usage'] * 100, 1) . "%");
            $this->info("  Disk Usage: " . round($system['disk_usage'] * 100, 1) . "%");
        }

        if (isset($result['load_balancer'])) {
            $lb = $result['load_balancer'];
            $this->info("\nLoad Balancer:");
            $this->info("  Total Servers: " . $lb['total_servers']);
            $this->info("  Healthy Servers: " . $lb['healthy_servers']);
        }

        if (isset($result['auto_scaling'])) {
            $scaling = $result['auto_scaling'];
            $this->info("\nAuto Scaling:");
            $this->info("  Current Instances: " . $scaling['current_instances']);
        }
    }

    private function displayMonitoringView(array $status): void
    {
        $this->info("=== Real-time Infrastructure Monitoring ===");
        $this->info("Time: " . date('Y-m-d H:i:s'));
        $this->info("Overall Status: " . strtoupper($status['overall_status']));

        $this->info("\nLoad Balancer: " . $status['load_balancer']['healthy_servers'] . "/" . $status['load_balancer']['total_servers'] . " servers healthy");
        $this->info("Auto Scaling: " . $status['auto_scaling']['current_instances'] . " instances running");
        $this->info("Health Checks: " . strtoupper($status['health_checks']['current_status']));
        $this->info("Circuit Breakers: " . $status['circuit_breakers']['healthy_services'] . "/" . $status['circuit_breakers']['total_services'] . " services healthy");

        if (isset($status['system_metrics'])) {
            $system = $status['system_metrics'];
            $this->info("\nSystem Resources:");
            $this->info("  CPU: " . round($system['cpu_usage'] * 100, 1) . "%");
            $this->info("  Memory: " . round($system['memory_usage'] * 100, 1) . "%");
            $this->info("  Disk: " . round($system['disk_usage'] * 100, 1) . "%");
        }
    }
}

// Run CLI if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $cli = new InfrastructureManagerCLI();
    $cli->run($argv);
}
