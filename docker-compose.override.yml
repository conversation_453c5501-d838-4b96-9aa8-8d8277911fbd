# WeBot Development Override Configuration
# This file is automatically loaded by docker-compose for development

version: '3.8'

services:
  # WeBot Application - Development Overrides
  webot:
    build:
      target: development
    environment:
      - APP_ENV=development
      - APP_DEBUG=true
      - LOG_LEVEL=debug
      - DB_HOST=mysql
      - DB_DATABASE=webot_dev
      - DB_USERNAME=webot
      - DB_PASSWORD=webot
      - XDEBUG_MODE=debug
      - XDEBUG_CONFIG=client_host=host.docker.internal
    volumes:
      # Mount source code for live editing
      - .:/var/www/html
      - ./storage:/var/www/html/storage
      - ./public/uploads:/var/www/html/public/uploads
      # Exclude vendor and node_modules for performance
      - /var/www/html/vendor
      - /var/www/html/node_modules
    ports:
      - "8000:80"
      - "9003:9003"  # Xdebug port
    command: ["php", "-S", "0.0.0.0:80", "-t", "public"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s

  # MySQL - Development Overrides
  mysql:
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: webot_dev
      MYSQL_USER: webot
      MYSQL_PASSWORD: webot
    ports:
      - "3307:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ./docker/mysql/dev.cnf:/etc/mysql/conf.d/dev.cnf
    command: --default-authentication-plugin=mysql_native_password

  # Redis - Development Overrides
  redis:
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
      - ./docker/redis/dev.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf

  # Development Tools
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: webot_phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: root
      MYSQL_ROOT_PASSWORD: root
    ports:
      - "8082:80"
    depends_on:
      - mysql
    networks:
      - webot_network

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: webot_redis_commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - webot_network

  # Mailhog for email testing
  mailhog:
    image: mailhog/mailhog:latest
    container_name: webot_mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - webot_network

  # Nginx for development (optional)
  nginx-dev:
    image: nginx:alpine
    container_name: webot_nginx_dev
    restart: unless-stopped
    ports:
      - "8090:80"
    volumes:
      - ./docker/nginx/dev.conf:/etc/nginx/conf.d/webot.conf
      - ./public:/var/www/html/public
      - ./storage/logs/nginx:/var/log/nginx
    depends_on:
      - webot
    networks:
      - webot_network

volumes:
  mysql_dev_data:
    driver: local
  redis_dev_data:
    driver: local
