<?php

declare(strict_types=1);

namespace WeBot\Middleware;

/**
 * Security Headers Middleware
 *
 * Adds security headers to HTTP responses to protect against:
 * - XSS attacks
 * - Clickjacking
 * - MIME type sniffing
 * - Content type confusion
 * - Information disclosure
 *
 * @package WeBot\Middleware
 * @version 2.0
 */
class SecurityHeadersMiddleware
{
    private array $config;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'csp' => [
                'default-src' => "'self'",
                'script-src' => "'self' 'unsafe-inline'",
                'style-src' => "'self' 'unsafe-inline'",
                'img-src' => "'self' data: https:",
                'font-src' => "'self'",
                'connect-src' => "'self'",
                'frame-ancestors' => "'none'",
                'base-uri' => "'self'",
                'form-action' => "'self'"
            ],
            'hsts' => [
                'max-age' => 31536000, // 1 year
                'include-subdomains' => true,
                'preload' => true
            ],
            'referrer_policy' => 'strict-origin-when-cross-origin',
            'permissions_policy' => [
                'geolocation' => '()',
                'microphone' => '()',
                'camera' => '()',
                'payment' => '()',
                'usb' => '()',
                'magnetometer' => '()',
                'gyroscope' => '()',
                'speaker' => '()'
            ],
            'custom_headers' => []
        ], $config);
    }

    /**
     * Handle security headers
     */
    public function handle(array $request, callable $next): array
    {
        $response = $next($request);

        // Add security headers
        $response['headers'] = $response['headers'] ?? [];
        $response['headers'] = array_merge($response['headers'], $this->getSecurityHeaders());

        return $response;
    }

    /**
     * Get all security headers
     */
    private function getSecurityHeaders(): array
    {
        $headers = [];

        // Content Security Policy
        $headers['Content-Security-Policy'] = $this->buildCspHeader();

        // HTTP Strict Transport Security
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            $headers['Strict-Transport-Security'] = $this->buildHstsHeader();
        }

        // X-Frame-Options (legacy support)
        $headers['X-Frame-Options'] = 'DENY';

        // X-Content-Type-Options
        $headers['X-Content-Type-Options'] = 'nosniff';

        // X-XSS-Protection (legacy support)
        $headers['X-XSS-Protection'] = '1; mode=block';

        // Referrer Policy
        $headers['Referrer-Policy'] = $this->config['referrer_policy'];

        // Permissions Policy
        $headers['Permissions-Policy'] = $this->buildPermissionsPolicyHeader();

        // Cross-Origin Embedder Policy
        $headers['Cross-Origin-Embedder-Policy'] = 'require-corp';

        // Cross-Origin Opener Policy
        $headers['Cross-Origin-Opener-Policy'] = 'same-origin';

        // Cross-Origin Resource Policy
        $headers['Cross-Origin-Resource-Policy'] = 'same-origin';

        // Server header removal/modification
        $headers['Server'] = 'WeBot/2.0';

        // X-Powered-By header removal
        if (function_exists('header_remove')) {
            header_remove('X-Powered-By');
        }

        // Custom headers
        $headers = array_merge($headers, $this->config['custom_headers']);

        return $headers;
    }

    /**
     * Build Content Security Policy header
     */
    private function buildCspHeader(): string
    {
        $cspParts = [];

        foreach ($this->config['csp'] as $directive => $value) {
            $cspParts[] = "{$directive} {$value}";
        }

        return implode('; ', $cspParts);
    }

    /**
     * Build HTTP Strict Transport Security header
     */
    private function buildHstsHeader(): string
    {
        $hsts = "max-age={$this->config['hsts']['max-age']}";

        if ($this->config['hsts']['include-subdomains']) {
            $hsts .= '; includeSubDomains';
        }

        if ($this->config['hsts']['preload']) {
            $hsts .= '; preload';
        }

        return $hsts;
    }

    /**
     * Build Permissions Policy header
     */
    private function buildPermissionsPolicyHeader(): string
    {
        $policies = [];

        foreach ($this->config['permissions_policy'] as $feature => $allowlist) {
            $policies[] = "{$feature}={$allowlist}";
        }

        return implode(', ', $policies);
    }

    /**
     * Set custom CSP for specific routes
     */
    public function setCustomCsp(array $csp): void
    {
        $this->config['csp'] = array_merge($this->config['csp'], $csp);
    }

    /**
     * Add custom header
     */
    public function addCustomHeader(string $name, string $value): void
    {
        $this->config['custom_headers'][$name] = $value;
    }

    /**
     * Get CSP nonce for inline scripts/styles
     */
    public function getCspNonce(): string
    {
        static $nonce = null;

        if ($nonce === null) {
            $nonce = base64_encode(random_bytes(16));
        }

        return $nonce;
    }

    /**
     * Update CSP to include nonce
     */
    public function enableNonce(): void
    {
        $nonce = $this->getCspNonce();

        $this->config['csp']['script-src'] = "'self' 'nonce-{$nonce}'";
        $this->config['csp']['style-src'] = "'self' 'nonce-{$nonce}'";
    }

    /**
     * Set development mode (relaxed CSP)
     */
    public function setDevelopmentMode(bool $enabled = true): void
    {
        if ($enabled) {
            $this->config['csp']['script-src'] = "'self' 'unsafe-inline' 'unsafe-eval'";
            $this->config['csp']['style-src'] = "'self' 'unsafe-inline'";
            $this->config['csp']['connect-src'] = "'self' ws: wss:";
        }
    }

    /**
     * Configure for API responses
     */
    public function configureForApi(): void
    {
        // Stricter CSP for API
        $this->config['csp'] = [
            'default-src' => "'none'",
            'frame-ancestors' => "'none'",
            'base-uri' => "'none'"
        ];

        // API-specific headers
        $this->config['custom_headers']['X-Content-Type-Options'] = 'nosniff';
        $this->config['custom_headers']['Cache-Control'] = 'no-store, no-cache, must-revalidate';
        $this->config['custom_headers']['Pragma'] = 'no-cache';
    }

    /**
     * Configure for file uploads
     */
    public function configureForUploads(): void
    {
        // Allow form data for uploads
        $this->config['csp']['form-action'] = "'self'";

        // Prevent execution of uploaded files
        $this->config['custom_headers']['X-Content-Type-Options'] = 'nosniff';
        $this->config['custom_headers']['Content-Disposition'] = 'attachment';
    }
}
