<?php

declare(strict_types=1);

namespace WeBot\Documentation;

/**
 * OpenAPI Specification Generator
 *
 * Generates comprehensive OpenAPI 3.0 specification for WeBot API
 * including all endpoints, schemas, authentication, and examples.
 *
 * @package WeBot\Documentation
 * @version 2.0
 */
class OpenApiGenerator
{
    private array $spec;
    private string $version;
    private string $baseUrl;

    public function __construct(string $version = '2.0.0', string $baseUrl = 'https://api.webot.com')
    {
        $this->version = $version;
        $this->baseUrl = $baseUrl;
        $this->initializeSpec();
    }

    /**
     * Initialize base OpenAPI specification
     */
    private function initializeSpec(): void
    {
        $this->spec = [
            'openapi' => '3.0.3',
            'info' => [
                'title' => 'WeBot API',
                'description' => 'Professional Telegram VPN Bot API - Complete documentation for all endpoints, authentication, and data models.',
                'version' => $this->version,
                'contact' => [
                    'name' => 'WeBot Support',
                    'url' => 'https://webot.com/support',
                    'email' => '<EMAIL>'
                ],
                'license' => [
                    'name' => 'MIT',
                    'url' => 'https://opensource.org/licenses/MIT'
                ]
            ],
            'servers' => [
                [
                    'url' => $this->baseUrl,
                    'description' => 'Production server'
                ],
                [
                    'url' => 'https://staging-api.webot.com',
                    'description' => 'Staging server'
                ],
                [
                    'url' => 'http://localhost:8000',
                    'description' => 'Development server'
                ]
            ],
            'paths' => [],
            'components' => [
                'schemas' => [],
                'securitySchemes' => [],
                'responses' => [],
                'parameters' => [],
                'examples' => []
            ],
            'security' => [],
            'tags' => []
        ];
    }

    /**
     * Generate complete OpenAPI specification
     */
    public function generate(): array
    {
        $this->addSecuritySchemes();
        $this->addTags();
        $this->addCommonSchemas();
        $this->addCommonResponses();
        $this->addCommonParameters();
        $this->addPaths();

        return $this->spec;
    }

    /**
     * Add security schemes
     */
    private function addSecuritySchemes(): void
    {
        $this->spec['components']['securitySchemes'] = [
            'BearerAuth' => [
                'type' => 'http',
                'scheme' => 'bearer',
                'bearerFormat' => 'JWT',
                'description' => 'JWT token for API authentication'
            ],
            'TelegramAuth' => [
                'type' => 'apiKey',
                'in' => 'header',
                'name' => 'X-Telegram-Bot-Api-Secret-Token',
                'description' => 'Telegram Bot API secret token for webhook validation'
            ],
            'SessionAuth' => [
                'type' => 'apiKey',
                'in' => 'cookie',
                'name' => 'WEBOT_SESSION',
                'description' => 'Session-based authentication'
            ]
        ];

        $this->spec['security'] = [
            ['BearerAuth' => []],
            ['TelegramAuth' => []],
            ['SessionAuth' => []]
        ];
    }

    /**
     * Add API tags for organization
     */
    private function addTags(): void
    {
        $this->spec['tags'] = [
            [
                'name' => 'Authentication',
                'description' => 'User authentication and authorization endpoints'
            ],
            [
                'name' => 'Users',
                'description' => 'User management and profile operations'
            ],
            [
                'name' => 'Services',
                'description' => 'VPN service management and configuration'
            ],
            [
                'name' => 'Payments',
                'description' => 'Payment processing and wallet management'
            ],
            [
                'name' => 'Admin',
                'description' => 'Administrative functions and management'
            ],
            [
                'name' => 'Tickets',
                'description' => 'Support ticket system'
            ],
            [
                'name' => 'Panels',
                'description' => 'VPN panel integration and monitoring'
            ],
            [
                'name' => 'Webhooks',
                'description' => 'Telegram webhook and callback handling'
            ],
            [
                'name' => 'Health',
                'description' => 'System health and monitoring endpoints'
            ]
        ];
    }

    /**
     * Add common data schemas
     */
    private function addCommonSchemas(): void
    {
        $this->spec['components']['schemas'] = [
            'User' => [
                'type' => 'object',
                'required' => ['id', 'telegram_id', 'status'],
                'properties' => [
                    'id' => [
                        'type' => 'integer',
                        'format' => 'int64',
                        'description' => 'Unique user identifier'
                    ],
                    'telegram_id' => [
                        'type' => 'integer',
                        'format' => 'int64',
                        'description' => 'Telegram user ID'
                    ],
                    'username' => [
                        'type' => 'string',
                        'nullable' => true,
                        'description' => 'Telegram username'
                    ],
                    'first_name' => [
                        'type' => 'string',
                        'description' => 'User first name'
                    ],
                    'last_name' => [
                        'type' => 'string',
                        'nullable' => true,
                        'description' => 'User last name'
                    ],
                    'phone' => [
                        'type' => 'string',
                        'nullable' => true,
                        'pattern' => '^\+[1-9]\d{1,14}$',
                        'description' => 'Phone number in E.164 format'
                    ],
                    'email' => [
                        'type' => 'string',
                        'format' => 'email',
                        'nullable' => true,
                        'description' => 'User email address'
                    ],
                    'status' => [
                        'type' => 'string',
                        'enum' => ['active', 'banned', 'pending', 'suspended'],
                        'description' => 'User account status'
                    ],
                    'balance' => [
                        'type' => 'number',
                        'format' => 'decimal',
                        'minimum' => 0,
                        'description' => 'User wallet balance'
                    ],
                    'created_at' => [
                        'type' => 'string',
                        'format' => 'date-time',
                        'description' => 'Account creation timestamp'
                    ],
                    'updated_at' => [
                        'type' => 'string',
                        'format' => 'date-time',
                        'description' => 'Last update timestamp'
                    ]
                ]
            ],
            'Service' => [
                'type' => 'object',
                'required' => ['id', 'user_id', 'plan_id', 'status'],
                'properties' => [
                    'id' => [
                        'type' => 'integer',
                        'format' => 'int64',
                        'description' => 'Service unique identifier'
                    ],
                    'user_id' => [
                        'type' => 'integer',
                        'format' => 'int64',
                        'description' => 'Owner user ID'
                    ],
                    'plan_id' => [
                        'type' => 'integer',
                        'format' => 'int64',
                        'description' => 'Service plan ID'
                    ],
                    'server_id' => [
                        'type' => 'integer',
                        'format' => 'int64',
                        'description' => 'Assigned server ID'
                    ],
                    'uuid' => [
                        'type' => 'string',
                        'format' => 'uuid',
                        'description' => 'Service UUID for panel integration'
                    ],
                    'username' => [
                        'type' => 'string',
                        'description' => 'Service username'
                    ],
                    'status' => [
                        'type' => 'string',
                        'enum' => ['active', 'expired', 'suspended', 'limited'],
                        'description' => 'Service status'
                    ],
                    'traffic_used' => [
                        'type' => 'integer',
                        'format' => 'int64',
                        'minimum' => 0,
                        'description' => 'Used traffic in bytes'
                    ],
                    'traffic_limit' => [
                        'type' => 'integer',
                        'format' => 'int64',
                        'minimum' => 0,
                        'description' => 'Traffic limit in bytes'
                    ],
                    'expires_at' => [
                        'type' => 'string',
                        'format' => 'date-time',
                        'nullable' => true,
                        'description' => 'Service expiration date'
                    ],
                    'created_at' => [
                        'type' => 'string',
                        'format' => 'date-time',
                        'description' => 'Service creation timestamp'
                    ]
                ]
            ],
            'Payment' => [
                'type' => 'object',
                'required' => ['id', 'user_id', 'amount', 'status'],
                'properties' => [
                    'id' => [
                        'type' => 'integer',
                        'format' => 'int64',
                        'description' => 'Payment unique identifier'
                    ],
                    'user_id' => [
                        'type' => 'integer',
                        'format' => 'int64',
                        'description' => 'User ID who made the payment'
                    ],
                    'amount' => [
                        'type' => 'integer',
                        'minimum' => 1,
                        'description' => 'Payment amount in smallest currency unit (cents)'
                    ],
                    'currency' => [
                        'type' => 'string',
                        'enum' => ['USD', 'EUR', 'IRR', 'TRX', 'USDT'],
                        'description' => 'Payment currency'
                    ],
                    'gateway' => [
                        'type' => 'string',
                        'enum' => ['zarinpal', 'idpay', 'nextpay', 'crypto', 'manual'],
                        'description' => 'Payment gateway used'
                    ],
                    'status' => [
                        'type' => 'string',
                        'enum' => ['pending', 'completed', 'failed', 'cancelled', 'refunded'],
                        'description' => 'Payment status'
                    ],
                    'transaction_id' => [
                        'type' => 'string',
                        'nullable' => true,
                        'description' => 'Gateway transaction ID'
                    ],
                    'description' => [
                        'type' => 'string',
                        'nullable' => true,
                        'description' => 'Payment description'
                    ],
                    'created_at' => [
                        'type' => 'string',
                        'format' => 'date-time',
                        'description' => 'Payment creation timestamp'
                    ],
                    'completed_at' => [
                        'type' => 'string',
                        'format' => 'date-time',
                        'nullable' => true,
                        'description' => 'Payment completion timestamp'
                    ]
                ]
            ]
        ];
    }

    /**
     * Add common response schemas
     */
    private function addCommonResponses(): void
    {
        $this->spec['components']['responses'] = [
            'Success' => [
                'description' => 'Successful operation',
                'content' => [
                    'application/json' => [
                        'schema' => [
                            'type' => 'object',
                            'properties' => [
                                'success' => [
                                    'type' => 'boolean',
                                    'example' => true
                                ],
                                'message' => [
                                    'type' => 'string',
                                    'example' => 'Operation completed successfully'
                                ],
                                'data' => [
                                    'type' => 'object',
                                    'description' => 'Response data'
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            'Error' => [
                'description' => 'Error response',
                'content' => [
                    'application/json' => [
                        'schema' => [
                            'type' => 'object',
                            'properties' => [
                                'success' => [
                                    'type' => 'boolean',
                                    'example' => false
                                ],
                                'error' => [
                                    'type' => 'string',
                                    'example' => 'VALIDATION_ERROR'
                                ],
                                'message' => [
                                    'type' => 'string',
                                    'example' => 'Invalid input data'
                                ],
                                'details' => [
                                    'type' => 'object',
                                    'description' => 'Additional error details'
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            'Unauthorized' => [
                'description' => 'Authentication required',
                'content' => [
                    'application/json' => [
                        'schema' => [
                            'type' => 'object',
                            'properties' => [
                                'success' => [
                                    'type' => 'boolean',
                                    'example' => false
                                ],
                                'error' => [
                                    'type' => 'string',
                                    'example' => 'UNAUTHORIZED'
                                ],
                                'message' => [
                                    'type' => 'string',
                                    'example' => 'Authentication required'
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            'Forbidden' => [
                'description' => 'Insufficient permissions',
                'content' => [
                    'application/json' => [
                        'schema' => [
                            'type' => 'object',
                            'properties' => [
                                'success' => [
                                    'type' => 'boolean',
                                    'example' => false
                                ],
                                'error' => [
                                    'type' => 'string',
                                    'example' => 'FORBIDDEN'
                                ],
                                'message' => [
                                    'type' => 'string',
                                    'example' => 'Insufficient permissions'
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            'NotFound' => [
                'description' => 'Resource not found',
                'content' => [
                    'application/json' => [
                        'schema' => [
                            'type' => 'object',
                            'properties' => [
                                'success' => [
                                    'type' => 'boolean',
                                    'example' => false
                                ],
                                'error' => [
                                    'type' => 'string',
                                    'example' => 'NOT_FOUND'
                                ],
                                'message' => [
                                    'type' => 'string',
                                    'example' => 'Resource not found'
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            'RateLimited' => [
                'description' => 'Rate limit exceeded',
                'content' => [
                    'application/json' => [
                        'schema' => [
                            'type' => 'object',
                            'properties' => [
                                'success' => [
                                    'type' => 'boolean',
                                    'example' => false
                                ],
                                'error' => [
                                    'type' => 'string',
                                    'example' => 'RATE_LIMITED'
                                ],
                                'message' => [
                                    'type' => 'string',
                                    'example' => 'Rate limit exceeded'
                                ],
                                'retry_after' => [
                                    'type' => 'integer',
                                    'example' => 60
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * Add common parameters
     */
    private function addCommonParameters(): void
    {
        $this->spec['components']['parameters'] = [
            'UserId' => [
                'name' => 'user_id',
                'in' => 'path',
                'required' => true,
                'schema' => [
                    'type' => 'integer',
                    'format' => 'int64'
                ],
                'description' => 'User unique identifier'
            ],
            'ServiceId' => [
                'name' => 'service_id',
                'in' => 'path',
                'required' => true,
                'schema' => [
                    'type' => 'integer',
                    'format' => 'int64'
                ],
                'description' => 'Service unique identifier'
            ],
            'PaymentId' => [
                'name' => 'payment_id',
                'in' => 'path',
                'required' => true,
                'schema' => [
                    'type' => 'integer',
                    'format' => 'int64'
                ],
                'description' => 'Payment unique identifier'
            ],
            'Page' => [
                'name' => 'page',
                'in' => 'query',
                'required' => false,
                'schema' => [
                    'type' => 'integer',
                    'minimum' => 1,
                    'default' => 1
                ],
                'description' => 'Page number for pagination'
            ],
            'Limit' => [
                'name' => 'limit',
                'in' => 'query',
                'required' => false,
                'schema' => [
                    'type' => 'integer',
                    'minimum' => 1,
                    'maximum' => 100,
                    'default' => 20
                ],
                'description' => 'Number of items per page'
            ]
        ];
    }

    /**
     * Add API paths
     */
    private function addPaths(): void
    {
        $this->spec['paths'] = array_merge(
            $this->getWebhookPaths(),
            $this->getAuthPaths(),
            $this->getUserPaths(),
            $this->getServicePaths(),
            $this->getPaymentPaths(),
            $this->getAdminPaths(),
            $this->getHealthPaths()
        );
    }

    /**
     * Get webhook endpoints
     */
    private function getWebhookPaths(): array
    {
        return [
            '/webhook/telegram' => [
                'post' => [
                    'tags' => ['Webhooks'],
                    'summary' => 'Telegram webhook handler',
                    'description' => 'Handles incoming Telegram updates including messages, callbacks, and inline queries',
                    'security' => [['TelegramAuth' => []]],
                    'requestBody' => [
                        'required' => true,
                        'content' => [
                            'application/json' => [
                                'schema' => [
                                    'type' => 'object',
                                    'properties' => [
                                        'update_id' => ['type' => 'integer'],
                                        'message' => ['type' => 'object'],
                                        'callback_query' => ['type' => 'object'],
                                        'inline_query' => ['type' => 'object']
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'responses' => [
                        '200' => ['$ref' => '#/components/responses/Success'],
                        '401' => ['$ref' => '#/components/responses/Unauthorized'],
                        '429' => ['$ref' => '#/components/responses/RateLimited']
                    ]
                ]
            ]
        ];
    }

    /**
     * Get authentication endpoints
     */
    private function getAuthPaths(): array
    {
        return [
            '/api/auth/login' => [
                'post' => [
                    'tags' => ['Authentication'],
                    'summary' => 'User login',
                    'description' => 'Authenticate user and return JWT token',
                    'security' => [],
                    'requestBody' => [
                        'required' => true,
                        'content' => [
                            'application/json' => [
                                'schema' => [
                                    'type' => 'object',
                                    'required' => ['telegram_id'],
                                    'properties' => [
                                        'telegram_id' => ['type' => 'integer', 'format' => 'int64'],
                                        'username' => ['type' => 'string'],
                                        'first_name' => ['type' => 'string'],
                                        'auth_data' => ['type' => 'string']
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'responses' => [
                        '200' => [
                            'description' => 'Login successful',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        'type' => 'object',
                                        'properties' => [
                                            'success' => ['type' => 'boolean'],
                                            'token' => ['type' => 'string'],
                                            'user' => ['$ref' => '#/components/schemas/User']
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        '401' => ['$ref' => '#/components/responses/Unauthorized'],
                        '429' => ['$ref' => '#/components/responses/RateLimited']
                    ]
                ]
            ],
            '/api/auth/refresh' => [
                'post' => [
                    'tags' => ['Authentication'],
                    'summary' => 'Refresh JWT token',
                    'description' => 'Refresh expired JWT token',
                    'security' => [['BearerAuth' => []]],
                    'responses' => [
                        '200' => [
                            'description' => 'Token refreshed',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        'type' => 'object',
                                        'properties' => [
                                            'success' => ['type' => 'boolean'],
                                            'token' => ['type' => 'string']
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        '401' => ['$ref' => '#/components/responses/Unauthorized']
                    ]
                ]
            ]
        ];
    }

    /**
     * Get user management endpoints
     */
    private function getUserPaths(): array
    {
        return [
            '/api/users/profile' => [
                'get' => [
                    'tags' => ['Users'],
                    'summary' => 'Get user profile',
                    'description' => 'Retrieve current user profile information',
                    'security' => [['BearerAuth' => []]],
                    'responses' => [
                        '200' => [
                            'description' => 'User profile',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        'type' => 'object',
                                        'properties' => [
                                            'success' => ['type' => 'boolean'],
                                            'data' => ['$ref' => '#/components/schemas/User']
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        '401' => ['$ref' => '#/components/responses/Unauthorized']
                    ]
                ],
                'put' => [
                    'tags' => ['Users'],
                    'summary' => 'Update user profile',
                    'description' => 'Update current user profile information',
                    'security' => [['BearerAuth' => []]],
                    'requestBody' => [
                        'required' => true,
                        'content' => [
                            'application/json' => [
                                'schema' => [
                                    'type' => 'object',
                                    'properties' => [
                                        'first_name' => ['type' => 'string'],
                                        'last_name' => ['type' => 'string'],
                                        'phone' => ['type' => 'string'],
                                        'email' => ['type' => 'string', 'format' => 'email']
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'responses' => [
                        '200' => ['$ref' => '#/components/responses/Success'],
                        '400' => ['$ref' => '#/components/responses/Error'],
                        '401' => ['$ref' => '#/components/responses/Unauthorized']
                    ]
                ]
            ]
        ];
    }

    /**
     * Get service management endpoints
     */
    private function getServicePaths(): array
    {
        return [
            '/api/services' => [
                'get' => [
                    'tags' => ['Services'],
                    'summary' => 'List user services',
                    'description' => 'Get list of user VPN services',
                    'security' => [['BearerAuth' => []]],
                    'parameters' => [
                        ['$ref' => '#/components/parameters/Page'],
                        ['$ref' => '#/components/parameters/Limit']
                    ],
                    'responses' => [
                        '200' => [
                            'description' => 'Services list',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        'type' => 'object',
                                        'properties' => [
                                            'success' => ['type' => 'boolean'],
                                            'data' => [
                                                'type' => 'array',
                                                'items' => ['$ref' => '#/components/schemas/Service']
                                            ],
                                            'pagination' => [
                                                'type' => 'object',
                                                'properties' => [
                                                    'page' => ['type' => 'integer'],
                                                    'limit' => ['type' => 'integer'],
                                                    'total' => ['type' => 'integer']
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        '401' => ['$ref' => '#/components/responses/Unauthorized']
                    ]
                ],
                'post' => [
                    'tags' => ['Services'],
                    'summary' => 'Create new service',
                    'description' => 'Purchase and create new VPN service',
                    'security' => [['BearerAuth' => []]],
                    'requestBody' => [
                        'required' => true,
                        'content' => [
                            'application/json' => [
                                'schema' => [
                                    'type' => 'object',
                                    'required' => ['plan_id', 'server_id'],
                                    'properties' => [
                                        'plan_id' => ['type' => 'integer'],
                                        'server_id' => ['type' => 'integer'],
                                        'username' => ['type' => 'string'],
                                        'duration_days' => ['type' => 'integer', 'minimum' => 1]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'responses' => [
                        '201' => [
                            'description' => 'Service created',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        'type' => 'object',
                                        'properties' => [
                                            'success' => ['type' => 'boolean'],
                                            'data' => ['$ref' => '#/components/schemas/Service']
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        '400' => ['$ref' => '#/components/responses/Error'],
                        '401' => ['$ref' => '#/components/responses/Unauthorized']
                    ]
                ]
            ],
            '/api/services/{service_id}' => [
                'get' => [
                    'tags' => ['Services'],
                    'summary' => 'Get service details',
                    'description' => 'Get detailed information about a specific service',
                    'security' => [['BearerAuth' => []]],
                    'parameters' => [
                        ['$ref' => '#/components/parameters/ServiceId']
                    ],
                    'responses' => [
                        '200' => [
                            'description' => 'Service details',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        'type' => 'object',
                                        'properties' => [
                                            'success' => ['type' => 'boolean'],
                                            'data' => ['$ref' => '#/components/schemas/Service']
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        '404' => ['$ref' => '#/components/responses/NotFound'],
                        '401' => ['$ref' => '#/components/responses/Unauthorized']
                    ]
                ]
            ],
            '/api/services/{service_id}/config' => [
                'get' => [
                    'tags' => ['Services'],
                    'summary' => 'Get service configuration',
                    'description' => 'Get VPN configuration for the service',
                    'security' => [['BearerAuth' => []]],
                    'parameters' => [
                        ['$ref' => '#/components/parameters/ServiceId']
                    ],
                    'responses' => [
                        '200' => [
                            'description' => 'Service configuration',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        'type' => 'object',
                                        'properties' => [
                                            'success' => ['type' => 'boolean'],
                                            'data' => [
                                                'type' => 'object',
                                                'properties' => [
                                                    'config_url' => ['type' => 'string'],
                                                    'qr_code' => ['type' => 'string'],
                                                    'subscription_url' => ['type' => 'string']
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        '404' => ['$ref' => '#/components/responses/NotFound'],
                        '401' => ['$ref' => '#/components/responses/Unauthorized']
                    ]
                ]
            ]
        ];
    }

    /**
     * Get payment endpoints
     */
    private function getPaymentPaths(): array
    {
        return [
            '/api/payments' => [
                'post' => [
                    'tags' => ['Payments'],
                    'summary' => 'Create payment',
                    'description' => 'Create new payment for wallet top-up or service purchase',
                    'security' => [['BearerAuth' => []]],
                    'requestBody' => [
                        'required' => true,
                        'content' => [
                            'application/json' => [
                                'schema' => [
                                    'type' => 'object',
                                    'required' => ['amount', 'gateway'],
                                    'properties' => [
                                        'amount' => ['type' => 'integer', 'minimum' => 1000],
                                        'gateway' => ['type' => 'string', 'enum' => ['zarinpal', 'idpay', 'nextpay']],
                                        'currency' => ['type' => 'string', 'enum' => ['USD', 'EUR', 'IRR']],
                                        'description' => ['type' => 'string']
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'responses' => [
                        '201' => [
                            'description' => 'Payment created',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        'type' => 'object',
                                        'properties' => [
                                            'success' => ['type' => 'boolean'],
                                            'data' => [
                                                'type' => 'object',
                                                'properties' => [
                                                    'payment_id' => ['type' => 'integer'],
                                                    'payment_url' => ['type' => 'string'],
                                                    'amount' => ['type' => 'integer']
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        '400' => ['$ref' => '#/components/responses/Error'],
                        '401' => ['$ref' => '#/components/responses/Unauthorized']
                    ]
                ]
            ],
            '/api/payments/{payment_id}' => [
                'get' => [
                    'tags' => ['Payments'],
                    'summary' => 'Get payment status',
                    'description' => 'Check payment status and details',
                    'security' => [['BearerAuth' => []]],
                    'parameters' => [
                        ['$ref' => '#/components/parameters/PaymentId']
                    ],
                    'responses' => [
                        '200' => [
                            'description' => 'Payment details',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        'type' => 'object',
                                        'properties' => [
                                            'success' => ['type' => 'boolean'],
                                            'data' => ['$ref' => '#/components/schemas/Payment']
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        '404' => ['$ref' => '#/components/responses/NotFound'],
                        '401' => ['$ref' => '#/components/responses/Unauthorized']
                    ]
                ]
            ]
        ];
    }

    /**
     * Get admin endpoints
     */
    private function getAdminPaths(): array
    {
        return [
            '/api/admin/users' => [
                'get' => [
                    'tags' => ['Admin'],
                    'summary' => 'List all users',
                    'description' => 'Get paginated list of all users (admin only)',
                    'security' => [['BearerAuth' => []]],
                    'parameters' => [
                        ['$ref' => '#/components/parameters/Page'],
                        ['$ref' => '#/components/parameters/Limit'],
                        [
                            'name' => 'status',
                            'in' => 'query',
                            'schema' => [
                                'type' => 'string',
                                'enum' => ['active', 'banned', 'pending', 'suspended']
                            ]
                        ]
                    ],
                    'responses' => [
                        '200' => [
                            'description' => 'Users list',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        'type' => 'object',
                                        'properties' => [
                                            'success' => ['type' => 'boolean'],
                                            'data' => [
                                                'type' => 'array',
                                                'items' => ['$ref' => '#/components/schemas/User']
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ],
                        '401' => ['$ref' => '#/components/responses/Unauthorized'],
                        '403' => ['$ref' => '#/components/responses/Forbidden']
                    ]
                ]
            ]
        ];
    }

    /**
     * Get health check endpoints
     */
    private function getHealthPaths(): array
    {
        return [
            '/api/health' => [
                'get' => [
                    'tags' => ['Health'],
                    'summary' => 'Health check',
                    'description' => 'Check API health status',
                    'security' => [],
                    'responses' => [
                        '200' => [
                            'description' => 'Health status',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        'type' => 'object',
                                        'properties' => [
                                            'status' => ['type' => 'string', 'example' => 'healthy'],
                                            'timestamp' => ['type' => 'string', 'format' => 'date-time'],
                                            'version' => ['type' => 'string'],
                                            'uptime' => ['type' => 'integer']
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * Export specification to JSON file
     */
    public function exportToFile(string $filename = 'openapi.json'): bool
    {
        $spec = $this->generate();
        $json = json_encode($spec, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

        return file_put_contents($filename, $json) !== false;
    }

    /**
     * Export specification to YAML file
     */
    public function exportToYaml(string $filename = 'openapi.yaml'): bool
    {
        $spec = $this->generate();

        // Simple YAML conversion (for production, use symfony/yaml)
        $yaml = $this->arrayToYaml($spec);

        return file_put_contents($filename, $yaml) !== false;
    }

    /**
     * Simple array to YAML converter
     */
    private function arrayToYaml(array $array, int $indent = 0): string
    {
        $yaml = '';
        $spaces = str_repeat('  ', $indent);

        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $yaml .= $spaces . $key . ":\n";
                $yaml .= $this->arrayToYaml($value, $indent + 1);
            } else {
                $yaml .= $spaces . $key . ': ' . $this->formatYamlValue($value) . "\n";
            }
        }

        return $yaml;
    }

    /**
     * Format value for YAML
     */
    private function formatYamlValue($value): string
    {
        if (is_bool($value)) {
            return $value ? 'true' : 'false';
        }

        if (is_null($value)) {
            return 'null';
        }

        if (is_string($value) && (strpos($value, ' ') !== false || strpos($value, ':') !== false)) {
            return '"' . addslashes($value) . '"';
        }

        return (string) $value;
    }
}
