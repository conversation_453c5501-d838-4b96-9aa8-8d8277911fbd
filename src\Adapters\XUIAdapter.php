<?php

declare(strict_types=1);

namespace WeBot\Adapters;

use <PERSON>Bot\Contracts\PanelAdapterInterface;
use WeBot\Exceptions\PanelException;
use WeBot\Utils\HttpClientWrapper;

/**
 * X-UI Panel Adapter
 *
 * Handles all interactions with X-UI panel including
 * inbound management, client operations, and system monitoring.
 *
 * @package WeBot\Adapters
 * @version 2.0
 */
class XUIAdapter implements PanelAdapterInterface
{
    private $httpClient;
    private array $config;
    private ?string $sessionCookie = null;
    private ?int $sessionExpiry = null;

    public function __construct(array $config)
    {
        $this->config = $config;

        // Use HttpClientWrapper for compatibility
        $this->httpClient = new HttpClientWrapper([
            'base_uri' => rtrim($config['url'], '/') . '/',
            'timeout' => $config['timeout'] ?? 30,
            'verify' => $config['verify_ssl'] ?? true,
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'User-Agent' => 'WeBot/2.0 X-UI-Adapter'
            ],
            'cookies' => true
        ]);
    }

    /**
     * Authenticate with X-UI panel
     */
    public function authenticate(): array
    {
        try {
            $response = $this->httpClient->post('login', [
                'form_params' => [
                    'username' => $this->config['username'],
                    'password' => $this->config['password']
                ]
            ]);

            if ($response->getStatusCode() === 200) {
                $this->sessionExpiry = time() + 3600; // 1 hour session

                return [
                    'success' => true,
                    'session' => 'authenticated',
                    'expires_in' => 3600
                ];
            }

            return [
                'success' => false,
                'error' => 'Authentication failed'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Authentication failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Validate session
     */
    public function validateToken(?string $token = null): bool
    {
        if ($this->sessionExpiry && time() >= $this->sessionExpiry) {
            return false;
        }

        try {
            $response = $this->makeAuthenticatedRequest('POST', 'panel/api/inbounds/list');
            return $response['success'];
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Refresh session
     */
    public function refreshToken(): array
    {
        return $this->authenticate();
    }

    /**
     * Create user (client in X-UI)
     */
    public function createUser(array $userData): array
    {
        $this->ensureAuthenticated();

        // First, get or create inbound
        $inboundId = $this->getOrCreateInbound($userData);

        if (!$inboundId) {
            return [
                'success' => false,
                'error' => 'Failed to get or create inbound'
            ];
        }

        $clientData = $this->mapToXUIClient($userData);

        try {
            $response = $this->makeAuthenticatedRequest('POST', "panel/api/inbounds/{$inboundId}/addClient", $clientData);

            if ($response['success']) {
                return [
                    'success' => true,
                    'username' => $clientData['email'],
                    'client_id' => $clientData['id'],
                    'inbound_id' => $inboundId,
                    'subscription_url' => $this->generateSubscriptionUrl($inboundId, $clientData['id']),
                    'data_limit' => $clientData['totalGB'],
                    'expire' => $clientData['expiryTime'],
                    'status' => $clientData['enable'] ? 'active' : 'disabled'
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to create client: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get user (client) information
     */
    public function getUser(string $username): array
    {
        $this->ensureAuthenticated();

        try {
            // Find client across all inbounds
            $client = $this->findClientByEmail($username);

            if (!$client) {
                return [
                    'success' => false,
                    'error' => 'Client not found'
                ];
            }

            return [
                'success' => true,
                'username' => $client['email'],
                'client_id' => $client['id'],
                'inbound_id' => $client['inbound_id'],
                'data_limit' => $client['totalGB'] ?? 0,
                'data_used' => $client['up'] + $client['down'],
                'expire' => $client['expiryTime'],
                'status' => $client['enable'] ? 'active' : 'disabled',
                'subscription_url' => $this->generateSubscriptionUrl($client['inbound_id'], $client['id']),
                'upload_traffic' => $client['up'] ?? 0,
                'download_traffic' => $client['down'] ?? 0,
                'total_traffic' => ($client['up'] ?? 0) + ($client['down'] ?? 0)
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to get client: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Update user (client)
     */
    public function updateUser(string $username, array $updateData): array
    {
        $this->ensureAuthenticated();

        try {
            $client = $this->findClientByEmail($username);

            if (!$client) {
                return [
                    'success' => false,
                    'error' => 'Client not found'
                ];
            }

            $updatedClient = array_merge($client, $this->mapToXUIClient($updateData));

            $response = $this->makeAuthenticatedRequest(
                'POST',
                "panel/api/inbounds/{$client['inbound_id']}/updateClient/{$client['id']}",
                $updatedClient
            );

            if ($response['success']) {
                return [
                    'success' => true,
                    'username' => $username,
                    'updated_fields' => array_keys($updateData)
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to update client: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Delete user (client)
     */
    public function deleteUser(string $username): array
    {
        $this->ensureAuthenticated();

        try {
            $client = $this->findClientByEmail($username);

            if (!$client) {
                return [
                    'success' => false,
                    'error' => 'Client not found'
                ];
            }

            $response = $this->makeAuthenticatedRequest(
                'POST',
                "panel/api/inbounds/{$client['inbound_id']}/delClient/{$client['id']}"
            );

            if ($response['success']) {
                return [
                    'success' => true,
                    'username' => $username,
                    'message' => 'Client deleted successfully'
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to delete client: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Suspend user (disable client)
     */
    public function suspendUser(string $username): array
    {
        return $this->updateUser($username, ['enable' => false]);
    }

    /**
     * Reactivate user (enable client)
     */
    public function reactivateUser(string $username): array
    {
        return $this->updateUser($username, ['enable' => true]);
    }

    /**
     * Get user statistics
     */
    public function getUserStats(string $username): array
    {
        $userInfo = $this->getUser($username);

        if (!$userInfo['success']) {
            return $userInfo;
        }

        $totalTraffic = $userInfo['data_limit'] ?? 0;
        $usedTraffic = $userInfo['data_used'] ?? 0;
        $remainingTraffic = max(0, $totalTraffic - $usedTraffic);

        return [
            'success' => true,
            'username' => $username,
            'used_traffic' => $usedTraffic,
            'total_traffic' => $totalTraffic,
            'remaining_traffic' => $remainingTraffic,
            'usage_percentage' => $totalTraffic > 0 ? ($usedTraffic / $totalTraffic) * 100 : 0,
            'upload_traffic' => $userInfo['upload_traffic'],
            'download_traffic' => $userInfo['download_traffic'],
            'expire_date' => $userInfo['expire'],
            'status' => $userInfo['status']
        ];
    }

    /**
     * Generate configuration
     */
    public function generateConfig(string $username, string $clientType = 'v2ray'): array
    {
        $userInfo = $this->getUser($username);

        if (!$userInfo['success']) {
            return $userInfo;
        }

        $subscriptionUrl = $userInfo['subscription_url'];

        try {
            // Get subscription content
            $response = $this->httpClient->get($subscriptionUrl);
            $configContent = $response->getBody()->getContents();

            switch (strtolower($clientType)) {
                case 'v2ray':
                case 'v2rayn':
                    $config = $configContent; // Already in correct format
                    break;
                case 'clash':
                    $config = $this->formatClashConfig($configContent);
                    break;
                case 'sing-box':
                    $config = $this->formatSingBoxConfig($configContent);
                    break;
                case 'json':
                    $config = $this->formatJSONConfig($configContent);
                    break;
                default:
                    $config = $configContent;
            }

            return [
                'success' => true,
                'config' => $config,
                'client_type' => $clientType,
                'subscription_url' => $subscriptionUrl
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to generate config: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get system information
     */
    public function getSystemInfo(): array
    {
        $this->ensureAuthenticated();

        try {
            $response = $this->makeAuthenticatedRequest('POST', 'panel/api/server/status');

            if ($response['success']) {
                $data = $response['data'];
                return [
                    'success' => true,
                    'version' => $data['version'] ?? 'unknown',
                    'uptime' => $data['uptime'] ?? 0,
                    'cpu_usage' => $data['cpu'] ?? 0,
                    'memory_usage' => $data['mem']['used'] ?? 0,
                    'memory_total' => $data['mem']['total'] ?? 0,
                    'disk_usage' => $data['disk']['used'] ?? 0,
                    'disk_total' => $data['disk']['total'] ?? 0,
                    'network_up' => $data['netIO']['up'] ?? 0,
                    'network_down' => $data['netIO']['down'] ?? 0,
                    'xray_status' => $data['xray']['state'] ?? 'unknown'
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to get system info: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Health check
     */
    public function healthCheck(): array
    {
        try {
            $response = $this->httpClient->get('');

            return [
                'success' => $response->getStatusCode() === 200,
                'status_code' => $response->getStatusCode()
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Bulk operations
     */
    public function bulkUpdateUsers(array $usernames, array $updateData): array
    {
        $results = [];
        $successCount = 0;
        $failureCount = 0;

        foreach ($usernames as $username) {
            $result = $this->updateUser($username, $updateData);
            $results[$username] = $result;

            if ($result['success']) {
                $successCount++;
            } else {
                $failureCount++;
            }
        }

        return [
            'success' => $failureCount === 0,
            'total' => count($usernames),
            'successful' => $successCount,
            'failed' => $failureCount,
            'results' => $results
        ];
    }

    public function getBulkUserStats(array $usernames): array
    {
        $results = [];
        $successCount = 0;

        foreach ($usernames as $username) {
            $stats = $this->getUserStats($username);
            if ($stats['success']) {
                $results[$username] = $stats;
                $successCount++;
            }
        }

        return [
            'success' => true,
            'total' => count($usernames),
            'successful' => $successCount,
            'users' => $results
        ];
    }

    /**
     * X-UI specific methods
     */
    public function getInbounds(): array
    {
        $this->ensureAuthenticated();

        try {
            $response = $this->makeAuthenticatedRequest('POST', 'panel/api/inbounds/list');

            if ($response['success']) {
                return [
                    'success' => true,
                    'inbounds' => $response['data']['obj'] ?? []
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to get inbounds: ' . $e->getMessage()
            ];
        }
    }

    public function createInbound(array $inboundData): array
    {
        $this->ensureAuthenticated();

        try {
            $response = $this->makeAuthenticatedRequest('POST', 'panel/api/inbounds/add', $inboundData);

            if ($response['success']) {
                return [
                    'success' => true,
                    'inbound_id' => $response['data']['id'] ?? null,
                    'message' => 'Inbound created successfully'
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to create inbound: ' . $e->getMessage()
            ];
        }
    }

    public function resetClientTraffic(string $username): array
    {
        $this->ensureAuthenticated();

        try {
            $client = $this->findClientByEmail($username);

            if (!$client) {
                return [
                    'success' => false,
                    'error' => 'Client not found'
                ];
            }

            $response = $this->makeAuthenticatedRequest(
                'POST',
                "panel/api/inbounds/{$client['inbound_id']}/resetClientTraffic/{$client['id']}"
            );

            if ($response['success']) {
                return [
                    'success' => true,
                    'username' => $username,
                    'message' => 'Client traffic reset successfully'
                ];
            }

            return $response;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to reset client traffic: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Private helper methods
     */
    private function ensureAuthenticated(): void
    {
        if (!$this->validateToken()) {
            $authResult = $this->authenticate();
            if (!$authResult['success']) {
                throw new PanelException('Authentication failed: ' . $authResult['error']);
            }
        }
    }

    private function makeAuthenticatedRequest(string $method, string $endpoint, array $data = []): array
    {
        try {
            $options = [];

            if (!empty($data)) {
                if ($method === 'GET') {
                    $options['query'] = $data;
                } else {
                    $options['json'] = $data;
                }
            }

            $response = $this->httpClient->request($method, $endpoint, $options);
            $responseData = json_decode($response->getBody()->getContents(), true);

            return [
                'success' => $response->getStatusCode() === 200 && ($responseData['success'] ?? true),
                'data' => $responseData,
                'status_code' => $response->getStatusCode()
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'status_code' => 0,
                'response' => ''
            ];
        }
    }

    private function findClientByEmail(string $email): ?array
    {
        $inbounds = $this->getInbounds();

        if (!$inbounds['success']) {
            return null;
        }

        foreach ($inbounds['inbounds'] as $inbound) {
            if (isset($inbound['clientStats'])) {
                foreach ($inbound['clientStats'] as $client) {
                    if ($client['email'] === $email) {
                        $client['inbound_id'] = $inbound['id'];
                        return $client;
                    }
                }
            }
        }

        return null;
    }

    private function getOrCreateInbound(array $userData): ?int
    {
        $inbounds = $this->getInbounds();

        if (!$inbounds['success']) {
            return null;
        }

        // Try to find existing inbound
        foreach ($inbounds['inbounds'] as $inbound) {
            if ($inbound['protocol'] === 'vmess' || $inbound['protocol'] === 'vless') {
                return $inbound['id'];
            }
        }

        // Create new inbound if none found
        $inboundData = [
            'up' => 0,
            'down' => 0,
            'total' => 0,
            'remark' => 'WeBot Auto-Created',
            'enable' => true,
            'expiryTime' => 0,
            'listen' => '',
            'port' => 443,
            'protocol' => 'vmess',
            'settings' => json_encode([
                'clients' => [],
                'decryption' => 'none',
                'fallbacks' => []
            ]),
            'streamSettings' => json_encode([
                'network' => 'ws',
                'security' => 'tls',
                'wsSettings' => [
                    'path' => '/ws',
                    'headers' => []
                ],
                'tlsSettings' => [
                    'serverName' => $this->config['domain'] ?? 'example.com',
                    'certificates' => []
                ]
            ]),
            'sniffing' => json_encode([
                'enabled' => true,
                'destOverride' => ['http', 'tls']
            ])
        ];

        $result = $this->createInbound($inboundData);
        return $result['success'] ? $result['inbound_id'] : null;
    }

    private function mapToXUIClient(array $userData): array
    {
        $client = [
            'id' => $userData['client_id'] ?? $this->generateUUID(),
            'email' => $userData['username'] ?? $userData['email'],
            'limitIp' => $userData['connection_limit'] ?? 1,
            'totalGB' => isset($userData['data_limit']) ? (int) $userData['data_limit'] : 0,
            'expiryTime' => 0,
            'enable' => $userData['status'] !== 'disabled',
            'tgId' => '',
            'subId' => $userData['sub_id'] ?? ''
        ];

        if (isset($userData['expire'])) {
            $client['expiryTime'] = is_numeric($userData['expire']) ? (int) $userData['expire'] * 1000 : strtotime($userData['expire']) * 1000;
        }

        if (isset($userData['expire_days'])) {
            $client['expiryTime'] = strtotime("+{$userData['expire_days']} days") * 1000;
        }

        return $client;
    }

    private function generateSubscriptionUrl(int $inboundId, string $clientId): string
    {
        return rtrim($this->config['url'], '/') . "/sub/{$clientId}";
    }

    private function generateUUID(): string
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0xffff)
        );
    }

    private function formatClashConfig(string $content): string
    {
        $configs = explode("\n", base64_decode($content));
        $clashConfig = "proxies:\n";

        foreach ($configs as $config) {
            if (!empty(trim($config))) {
                $clashConfig .= "  - " . $this->convertToClashProxy($config) . "\n";
            }
        }

        return $clashConfig;
    }

    private function formatSingBoxConfig(string $content): string
    {
        $configs = explode("\n", base64_decode($content));
        $outbounds = [];

        foreach ($configs as $config) {
            if (!empty(trim($config))) {
                $outbounds[] = $this->convertToSingBoxOutbound($config);
            }
        }

        return json_encode(['outbounds' => $outbounds], JSON_PRETTY_PRINT);
    }

    private function formatJSONConfig(string $content): string
    {
        $configs = explode("\n", base64_decode($content));
        $jsonConfigs = [];

        foreach ($configs as $config) {
            if (!empty(trim($config))) {
                $jsonConfigs[] = $this->parseConfigToJSON($config);
            }
        }

        return json_encode($jsonConfigs, JSON_PRETTY_PRINT);
    }

    private function convertToClashProxy(string $config): string
    {
        unset($config); // Suppress unused parameter warning
        // Simplified conversion - would need full implementation
        return "name: proxy, type: vmess, server: example.com, port: 443";
    }

    private function convertToSingBoxOutbound(string $config): array
    {
        unset($config); // Suppress unused parameter warning
        // Simplified conversion - would need full implementation
        return [
            'type' => 'vmess',
            'tag' => 'proxy',
            'server' => 'example.com',
            'server_port' => 443
        ];
    }

    private function parseConfigToJSON(string $config): array
    {
        unset($config); // Suppress unused parameter warning
        // Simplified parsing - would need full implementation
        return [
            'protocol' => 'vmess',
            'settings' => [
                'vnext' => [
                    [
                        'address' => 'example.com',
                        'port' => 443,
                        'users' => []
                    ]
                ]
            ]
        ];
    }
}
