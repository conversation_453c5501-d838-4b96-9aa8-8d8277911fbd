# WeBot v2.0 Production Dockerfile
# Multi-stage build for optimized production image

# Build stage
FROM php:8.2-fpm-alpine AS builder

# Install build dependencies
RUN apk add --no-cache \
    git \
    unzip \
    curl \
    libzip-dev \
    icu-dev \
    oniguruma-dev \
    freetype-dev \
    libjpeg-turbo-dev \
    libpng-dev \
    autoconf \
    g++ \
    make \
    linux-headers

# Install PHP extensions
RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) \
        pdo_mysql \
        mysqli \
        zip \
        intl \
        mbstring \
        gd \
        opcache \
        bcmath \
        sockets

# Install Redis extension
RUN pecl install redis && docker-php-ext-enable redis

# Install Composer
COPY --from=composer:2.6 /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www/html

# Copy composer files
COPY composer.json composer.lock ./

# Install dependencies (no dev dependencies for production)
RUN composer install --no-dev --optimize-autoloader --no-scripts --no-interaction

# Development stage
FROM php:8.2-fpm-alpine AS development

# Install development dependencies
RUN apk add --no-cache \
    git \
    unzip \
    curl \
    libzip-dev \
    icu-dev \
    oniguruma-dev \
    freetype-dev \
    libjpeg-turbo-dev \
    libpng-dev \
    autoconf \
    g++ \
    make \
    bash \
    vim \
    nano \
    linux-headers

# Install PHP extensions
RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) \
        pdo_mysql \
        mysqli \
        zip \
        intl \
        mbstring \
        gd \
        opcache \
        bcmath \
        sockets

# Install Redis extension
RUN pecl install redis && docker-php-ext-enable redis

# Install Xdebug for development
RUN pecl install xdebug && docker-php-ext-enable xdebug

# Install Composer
COPY --from=composer:2.6 /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www/html

# Copy application files first
COPY . .

# Copy composer files
COPY composer.json composer.lock ./

# Install all dependencies (including dev)
RUN composer install --optimize-autoloader --no-scripts --no-interaction

# Create necessary directories
RUN mkdir -p \
    storage/logs \
    storage/cache \
    storage/sessions \
    storage/uploads \
    public/assets \
    coverage \
    reports

# Set permissions
RUN chmod -R 777 storage public coverage reports

# Configure Xdebug
RUN echo "xdebug.mode=debug,coverage" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini \
    && echo "xdebug.client_host=host.docker.internal" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini \
    && echo "xdebug.client_port=9003" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini \
    && echo "xdebug.start_with_request=yes" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini

# Expose port
EXPOSE 80

# Default command for development
CMD ["php", "-S", "0.0.0.0:80", "-t", "public"]

# Production stage
FROM php:8.2-fpm-alpine AS production

# Install runtime dependencies only
RUN apk add --no-cache \
    nginx \
    supervisor \
    curl \
    libzip \
    icu \
    oniguruma \
    freetype \
    libjpeg-turbo \
    libpng \
    mysql-client \
    redis

# Install PHP extensions (copy from builder)
COPY --from=builder /usr/local/lib/php/extensions/ /usr/local/lib/php/extensions/
COPY --from=builder /usr/local/etc/php/conf.d/ /usr/local/etc/php/conf.d/

# Create non-root user
RUN addgroup -g 1000 webot && \
    adduser -D -s /bin/sh -u 1000 -G webot webot

# Set working directory
WORKDIR /var/www/html

# Copy application files
COPY --chown=webot:webot . .

# Copy vendor from builder
COPY --from=builder --chown=webot:webot /var/www/html/vendor ./vendor

# Create necessary directories
RUN mkdir -p \
    storage/logs \
    storage/cache \
    storage/sessions \
    storage/uploads \
    public/assets \
    && chown -R webot:webot storage public

# Copy configuration files
COPY docker/nginx/nginx.conf /etc/nginx/nginx.conf
COPY docker/nginx/default.conf /etc/nginx/http.d/default.conf
COPY docker/php/php.ini /usr/local/etc/php/php.ini
COPY docker/php/php-fpm.conf /usr/local/etc/php-fpm.d/www.conf
COPY docker/supervisor/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Set permissions
RUN chmod -R 755 storage public \
    && chmod -R 644 storage/logs \
    && chmod +x docker/entrypoint.sh

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# Expose port
EXPOSE 80

# Switch to non-root user
USER webot

# Entry point
ENTRYPOINT ["docker/entrypoint.sh"]

# Default command
CMD ["supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
