# WeBot Logo Design Guide
## راهنمای طراحی لوگوی WeBot

**تاریخ**: 2025-01-07  
**نسخه**: 1.0

---

## 🎨 مفهوم طراحی

### هویت برند WeBot:
- **نام**: WeBot (وی‌بات)
- **حوزه**: ربات فروش VPN
- **شخصیت**: مدرن، قابل اعتماد، فناورانه
- **مخاطب**: کاربران ایرانی

---

## 🎯 المان‌های کلیدی لوگو

### 1. نماد ربات:
- **شکل**: ربات ساده و دوستانه
- **ویژگی‌ها**: چشم‌های روشن، آنتن، بدنه مستطیلی
- **احساس**: فناوری + دوستانه

### 2. رنگ‌بندی:

#### رنگ‌های اصلی:
- **آبی اصلی**: `#4A90E2` (اعتماد، فناوری)
- **آبی تیره**: `#357ABD` (پایداری)
- **سبز**: `#50C878` (اتصال، موفقیت)
- **خاکستری**: `#2C5282` (حرفه‌ای)

#### رنگ‌های مکمل:
- **سفید**: `#FFFFFF` (تمیزی)
- **خاکستری روشن**: `#F7FAFC` (پس‌زمینه)

### 3. تایپوگرافی:
- **فونت اصلی**: IRANSans (فارسی)
- **فونت انگلیسی**: Arial Bold
- **سایز**: متناسب با کاربرد

---

## 📐 ابعاد و نسبت‌ها

### ابعاد استاندارد:
- **لوگوی کامل**: 400x400 پیکسل
- **آیکون**: 64x64, 128x128, 256x256 پیکسل
- **بنر**: 1200x400 پیکسل
- **فاویکون**: 32x32, 16x16 پیکسل

### نسبت‌ها:
- **نسبت طلایی**: 1.618:1
- **فاصله‌گذاری**: 1/4 ارتفاع لوگو
- **حداقل سایز**: 48 پیکسل

---

## 🔧 فایل‌های موجود

### 1. `webot-logo.svg`
- **فرمت**: SVG (وکتور)
- **کاربرد**: وب، چاپ با کیفیت بالا
- **مزایا**: قابل تغییر سایز، حجم کم

### 2. `logo-generator.html`
- **کاربرد**: تولید لوگو با رنگ‌های مختلف
- **ویژگی‌ها**: تعاملی، قابل تنظیم
- **خروجی**: PNG با کیفیت بالا

### 3. `logo.png` (فعلی)
- **وضعیت**: نیاز به جایگزینی
- **ابعاد**: 249x255 پیکسل
- **مشکل**: مربوط به برند قدیمی

---

## 🎨 نحوه استفاده

### برای طراح:
1. از فایل SVG به عنوان مبنا استفاده کنید
2. رنگ‌ها را طبق راهنما تنظیم کنید
3. ابعاد مختلف تولید کنید
4. فرمت‌های مختلف ایجاد کنید

### برای توسعه‌دهنده:
1. فایل `logo-generator.html` را باز کنید
2. رنگ‌ها را تنظیم کنید
3. لوگو را دانلود کنید
4. فایل `logo.png` را جایگزین کنید

---

## 📋 چک‌لیست تولید لوگو

### ✅ فایل‌های مورد نیاز:
- [ ] `logo.png` (400x400)
- [ ] `logo-small.png` (128x128)
- [ ] `logo-icon.png` (64x64)
- [ ] `favicon.ico` (32x32, 16x16)
- [ ] `logo.svg` (وکتور)

### ✅ کیفیت:
- [ ] وضوح بالا (300 DPI برای چاپ)
- [ ] پس‌زمینه شفاف
- [ ] رنگ‌های صحیح
- [ ] متن خوانا

### ✅ سازگاری:
- [ ] تست در پس‌زمینه‌های مختلف
- [ ] تست در سایزهای مختلف
- [ ] سازگاری با مرورگرها
- [ ] نمایش در موبایل

---

## 🔄 مراحل پیاده‌سازی

### فاز 1: تولید فایل‌ها
1. باز کردن `logo-generator.html`
2. تنظیم رنگ‌ها
3. دانلود PNG با سایز 400x400
4. تغییر نام به `logo.png`

### فاز 2: تولید سایزهای مختلف
1. تغییر سایز canvas در HTML
2. تولید 128x128 برای آیکون
3. تولید 64x64 برای فاویکون
4. تولید 32x32 و 16x16

### فاز 3: جایگزینی
1. پشتیبان‌گیری از `logo.png` فعلی
2. جایگزینی فایل جدید
3. تست در ربات
4. بررسی نمایش

### فاز 4: بهینه‌سازی
1. فشرده‌سازی فایل‌ها
2. تست سرعت بارگذاری
3. بررسی کیفیت
4. تأیید نهایی

---

## 🎨 ایده‌های طراحی

### حالت‌های مختلف:
1. **حالت روشن**: پس‌زمینه سفید
2. **حالت تیره**: پس‌زمینه تیره
3. **حالت شفاف**: بدون پس‌زمینه
4. **حالت تک‌رنگ**: برای چاپ

### انیمیشن‌ها:
1. **چشمک زدن**: برای loading
2. **چرخش آنتن**: برای اتصال
3. **تغییر رنگ چشم**: برای وضعیت
4. **حرکت دست**: برای تعامل

---

## 📞 پشتیبانی

### ابزارهای پیشنهادی:
- **Adobe Illustrator**: برای ویرایش SVG
- **Photoshop**: برای ویرایش PNG
- **GIMP**: جایگزین رایگان
- **Inkscape**: ویرایش SVG رایگان

### منابع:
- فونت IRANSans: موجود در پوشه assets
- راهنمای رنگ: این سند
- نمونه‌ها: فایل‌های موجود

---

## 🚀 نحوه استفاده سریع

### برای جایگزینی لوگوی فعلی:

1. **باز کردن Logo Generator:**
   ```bash
   # در مرورگر باز کنید:
   file:///path/to/assets/logo-generator.html
   ```

2. **تولید لوگوی جدید:**
   - رنگ‌ها را تنظیم کنید
   - دکمه "تولید لوگو" را بزنید
   - دکمه "دانلود PNG" را بزنید

3. **جایگزینی فایل‌ها:**
   ```bash
   # جایگزینی لوگوی اصلی
   mv webot-logo.png assets/logo.png

   # جایگزینی لوگوی install
   cp assets/logo.png install/image/logo.png
   ```

4. **تست:**
   - بررسی نمایش در ربات
   - تست در صفحه phpqrcode
   - بررسی کیفیت

---

*این راهنما برای تولید لوگوی حرفه‌ای WeBot طراحی شده است.*
