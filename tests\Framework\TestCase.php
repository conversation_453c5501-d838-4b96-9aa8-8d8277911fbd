<?php

declare(strict_types=1);

namespace WeBot\Tests\Framework;

use WeBot\Core\Database;
use WeBot\Utils\Logger;
use Monolog\Logger as MonologLogger;
use PHPUnit\Framework\TestCase as PHPUnitTestCase;

/**
 * Base Test Case
 * 
 * Provides foundation for all WeBot tests with proper setup,
 * teardown, assertions, and mock utilities.
 * 
 * @package WeBot\Tests\Framework
 * @version 2.0
 */
abstract class TestCase extends PHPUnitTestCase
{
    protected Database $database;
    protected MonologLogger $logger;
    protected object $config;
    protected array $mocks = [];
    protected array $testData = [];
    protected static int $testCount = 0;
    protected static int $passedCount = 0;
    protected static int $failedCount = 0;
    protected static array $failures = [];
    
    /**
     * Setup before each test
     */
    protected function setUp(): void
    {
        $this->setupTestEnvironment();
        $this->setupDatabase();
        $this->setupMocks();
        $this->setupTestData();
    }
    
    /**
     * Cleanup after each test
     */
    protected function tearDown(): void
    {
        $this->cleanupTestData();
        $this->cleanupMocks();
        $this->cleanupDatabase();
    }
    
    /**
     * Setup test environment
     */
    private function setupTestEnvironment(): void
    {
        // Set test mode
        putenv('WEBOT_TEST_MODE=true');
        putenv('WEBOT_ENV=testing');
        
        // Disable external APIs
        putenv('DISABLE_EXTERNAL_APIS=true');
        
        // Setup test configuration - create mock config object
        $this->config = new class {
            private array $data = [
                'database' => [
                    'driver' => 'sqlite',
                    'database' => ':memory:',
                    'prefix' => 'test_'
                ],
                'telegram' => [
                    'token' => 'test_token',
                    'webhook_url' => 'https://test.example.com/webhook'
                ],
                'app' => [
                    'debug' => true,
                    'env' => 'testing'
                ]
            ];

            public function get(string $key, $default = null) {
                return $this->data[$key] ?? $default;
            }

            public function set(string $key, $value): void {
                $this->data[$key] = $value;
            }
        };
        
        $this->logger = Logger::getInstance();
    }
    
    /**
     * Setup test database
     */
    private function setupDatabase(): void
    {
        $this->database = new Database($this->config->get('database'));
        
        // Create test tables
        $this->createTestTables();
        
        // Seed test data
        $this->seedTestData();
    }
    
    /**
     * Create test tables
     */
    private function createTestTables(): void
    {
        $tables = [
            'users' => "
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    telegram_id BIGINT UNIQUE NOT NULL,
                    username VARCHAR(255),
                    first_name VARCHAR(255),
                    phone VARCHAR(20),
                    status ENUM('active', 'banned', 'pending') DEFAULT 'pending',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ",
            'services' => "
                CREATE TABLE IF NOT EXISTS services (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    name VARCHAR(255) NOT NULL,
                    type VARCHAR(50) NOT NULL,
                    status ENUM('active', 'expired', 'suspended') DEFAULT 'active',
                    expires_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            ",
            'payments' => "
                CREATE TABLE IF NOT EXISTS payments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    amount DECIMAL(10,2) NOT NULL,
                    currency VARCHAR(10) DEFAULT 'USD',
                    status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
                    gateway VARCHAR(50),
                    transaction_id VARCHAR(255),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            ",
            'setting' => "
                CREATE TABLE IF NOT EXISTS setting (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    type VARCHAR(100) NOT NULL UNIQUE,
                    value TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            "
        ];
        
        foreach ($tables as $name => $sql) {
            $this->database->execute($sql);
        }
    }
    
    /**
     * Seed test data
     */
    private function seedTestData(): void
    {
        // Insert test users
        $this->database->execute(
            "INSERT INTO users (telegram_id, username, first_name, phone, status) VALUES 
             (123456789, 'testuser1', 'Test User 1', '+1234567890', 'active'),
             (987654321, 'testuser2', 'Test User 2', '+0987654321', 'pending'),
             (555666777, 'banneduser', 'Banned User', '+5556667777', 'banned')"
        );
        
        // Insert test settings
        $this->database->execute(
            "INSERT INTO setting (type, value) VALUES 
             ('BOT_STATES', '{\"USDRate\": 1.0, \"TRXRate\": 0.1}'),
             ('ADMIN_IDS', '[123456789]')"
        );
    }
    
    /**
     * Setup mocks
     */
    private function setupMocks(): void
    {
        $this->mocks = [
            'telegram_responses' => [
                'sendMessage' => ['ok' => true, 'result' => ['message_id' => 123]],
                'editMessageText' => ['ok' => true, 'result' => ['message_id' => 123]],
                'answerCallbackQuery' => ['ok' => true, 'result' => true],
                'getMe' => ['ok' => true, 'result' => ['id' => 123, 'username' => 'testbot']]
            ],
            'panel_responses' => [
                'createUser' => ['success' => true, 'user_id' => 'test_user_123'],
                'getUserInfo' => ['success' => true, 'data' => ['status' => 'active']],
                'deleteUser' => ['success' => true]
            ],
            'payment_responses' => [
                'createPayment' => ['success' => true, 'payment_id' => 'pay_123'],
                'verifyPayment' => ['success' => true, 'status' => 'completed']
            ]
        ];
        
        // Set global mocks
        $GLOBALS['test_mocks'] = $this->mocks;
    }
    
    /**
     * Setup test data
     */
    private function setupTestData(): void
    {
        $this->testData = [
            'valid_telegram_message' => [
                'message_id' => 123,
                'from' => [
                    'id' => 123456789,
                    'username' => 'testuser1',
                    'first_name' => 'Test User 1'
                ],
                'chat' => [
                    'id' => 123456789,
                    'type' => 'private'
                ],
                'text' => '/start',
                'date' => time()
            ],
            'valid_callback_query' => [
                'id' => 'callback_123',
                'from' => [
                    'id' => 123456789,
                    'username' => 'testuser1',
                    'first_name' => 'Test User 1'
                ],
                'message' => [
                    'message_id' => 123,
                    'chat' => ['id' => 123456789]
                ],
                'data' => 'test_callback'
            ]
        ];
    }
    
    /**
     * Cleanup test data
     */
    private function cleanupTestData(): void
    {
        $tables = ['payments', 'services', 'users', 'setting'];
        
        foreach ($tables as $table) {
            $this->database->execute("DELETE FROM {$table}");
        }
    }
    
    /**
     * Cleanup mocks
     */
    private function cleanupMocks(): void
    {
        unset($GLOBALS['test_mocks']);
        $this->mocks = [];
    }
    
    /**
     * Cleanup database
     */
    private function cleanupDatabase(): void
    {
        // SQLite in-memory database is automatically cleaned up
    }
    
    /**
     * Custom assert that condition is true
     */
    protected function weBotAssertTrue(bool $condition, string $message = ''): void
    {
        self::$testCount++;

        if ($condition) {
            self::$passedCount++;
            echo "✅ PASS: " . ($message ?: 'Assertion passed') . "\n";
        } else {
            self::$failedCount++;
            $failure = "❌ FAIL: " . ($message ?: 'Assertion failed');
            echo $failure . "\n";
            self::$failures[] = $failure;
        }
    }

    /**
     * Custom assert that condition is false
     */
    protected function weBotAssertFalse(bool $condition, string $message = ''): void
    {
        $this->weBotAssertTrue(!$condition, $message);
    }

    /**
     * Custom assert that two values are equal
     */
    protected function weBotAssertEquals($expected, $actual, string $message = ''): void
    {
        $this->weBotAssertTrue(
            $expected === $actual,
            $message ?: "Expected '{$expected}', got '{$actual}'"
        );
    }

    /**
     * Custom assert that two values are not equal
     */
    protected function weBotAssertNotEquals($expected, $actual, string $message = ''): void
    {
        $this->weBotAssertTrue(
            $expected !== $actual,
            $message ?: "Expected not '{$expected}', but got '{$actual}'"
        );
    }

    /**
     * Custom assert that array has key
     */
    protected function weBotAssertArrayHasKey(string $key, array $array, string $message = ''): void
    {
        $this->weBotAssertTrue(
            array_key_exists($key, $array),
            $message ?: "Array does not have key '{$key}'"
        );
    }

    /**
     * Custom assert that string contains substring
     */
    public function weBotAssertStringContains(string $needle, string $haystack, string $message = ''): void
    {
        $this->weBotAssertTrue(
            strpos($haystack, $needle) !== false,
            $message ?: "String '{$haystack}' does not contain '{$needle}'"
        );
    }

    /**
     * Custom assert that string does not contain substring
     */
    protected function weBotAssertStringNotContains(string $needle, string $haystack, string $message = ''): void
    {
        $this->weBotAssertTrue(
            strpos($haystack, $needle) === false,
            $message ?: "String '{$haystack}' should not contain '{$needle}'"
        );
    }

    /**
     * Custom assert that value is not empty
     */
    protected function weBotAssertNotEmpty($value, string $message = ''): void
    {
        $this->weBotAssertTrue(
            !empty($value),
            $message ?: "Value should not be empty"
        );
    }

    /**
     * Custom assert that object is instance of class
     */
    protected function weBotAssertInstanceOf(string $expected, $actual, string $message = ''): void
    {
        $this->weBotAssertTrue(
            $actual instanceof $expected,
            $message ?: "Object is not instance of {$expected}"
        );
    }

    /**
     * Custom assert that array contains value
     */
    protected function weBotAssertContains($needle, array $haystack, string $message = ''): void
    {
        $this->weBotAssertTrue(
            in_array($needle, $haystack, true),
            $message ?: "Array does not contain expected value"
        );
    }

    /**
     * Custom assert that file exists
     */
    protected function weBotAssertFileExists(string $filename, string $message = ''): void
    {
        $this->weBotAssertTrue(
            file_exists($filename),
            $message ?: "File does not exist: {$filename}"
        );
    }

    /**
     * Custom assert that first value is greater than second
     */
    protected function weBotAssertGreaterThan($expected, $actual, string $message = ''): void
    {
        $this->weBotAssertTrue(
            $actual > $expected,
            $message ?: "Value {$actual} is not greater than {$expected}"
        );
    }

    /**
     * Custom assert that first value is less than second
     */
    protected function weBotAssertLessThan($expected, $actual, string $message = ''): void
    {
        $this->weBotAssertTrue(
            $actual < $expected,
            $message ?: "Value {$actual} is not less than {$expected}"
        );
    }

    /**
     * Custom assert that first value is greater than or equal to second
     */
    protected function weBotAssertGreaterThanOrEqual($expected, $actual, string $message = ''): void
    {
        $this->weBotAssertTrue(
            $actual >= $expected,
            $message ?: "Value {$actual} is not greater than or equal to {$expected}"
        );
    }

    /**
     * Custom assert that first value is less than or equal to second
     */
    protected function weBotAssertLessThanOrEqual($expected, $actual, string $message = ''): void
    {
        $this->weBotAssertTrue(
            $actual <= $expected,
            $message ?: "Value {$actual} is not less than or equal to {$expected}"
        );
    }

    /**
     * Custom assert that values are equal with delta for floats
     */
    protected function weBotAssertEqualsWithDelta($expected, $actual, float $delta, string $message = ''): void
    {
        $this->weBotAssertTrue(
            abs($expected - $actual) <= $delta,
            $message ?: "Values are not equal within delta {$delta}"
        );
    }

    /**
     * Custom assert that string matches regular expression
     */
    protected function weBotAssertMatchesRegularExpression(string $pattern, string $string, string $message = ''): void
    {
        $this->weBotAssertTrue(
            preg_match($pattern, $string) === 1,
            $message ?: "String does not match pattern {$pattern}"
        );
    }

    /**
     * Custom assert that directory exists
     */
    protected function weBotAssertDirectoryExists(string $directory, string $message = ''): void
    {
        $this->weBotAssertTrue(
            is_dir($directory),
            $message ?: "Directory does not exist: {$directory}"
        );
    }

    /**
     * Custom assert that file is readable
     */
    protected function weBotAssertFileIsReadable(string $filename, string $message = ''): void
    {
        $this->weBotAssertFileExists($filename, $message);
        $this->weBotAssertTrue(
            is_readable($filename),
            $message ?: "File is not readable: {$filename}"
        );
    }

    /**
     * Custom assert that file is writable
     */
    protected function weBotAssertFileIsWritable(string $filename, string $message = ''): void
    {
        $this->weBotAssertTrue(
            is_writable($filename),
            $message ?: "File is not writable: {$filename}"
        );
    }

    /**
     * Custom assert that value is array
     */
    protected function weBotAssertIsArray($actual, string $message = ''): void
    {
        $this->weBotAssertTrue(
            is_array($actual),
            $message ?: "Value is not an array"
        );
    }

    /**
     * Custom assert that value is string
     */
    protected function weBotAssertIsString($actual, string $message = ''): void
    {
        $this->weBotAssertTrue(
            is_string($actual),
            $message ?: "Value is not a string"
        );
    }

    /**
     * Custom assert that value is integer
     */
    protected function weBotAssertIsInt($actual, string $message = ''): void
    {
        $this->weBotAssertTrue(
            is_int($actual),
            $message ?: "Value is not an integer"
        );
    }

    /**
     * Custom assert that value is float
     */
    protected function weBotAssertIsFloat($actual, string $message = ''): void
    {
        $this->weBotAssertTrue(
            is_float($actual),
            $message ?: "Value is not a float"
        );
    }

    /**
     * Custom assert that value is boolean
     */
    protected function weBotAssertIsBool($actual, string $message = ''): void
    {
        $this->weBotAssertTrue(
            is_bool($actual),
            $message ?: "Value is not a boolean"
        );
    }

    /**
     * Custom assert that value is null
     */
    protected function weBotAssertIsNull($actual, string $message = ''): void
    {
        $this->weBotAssertTrue(
            is_null($actual),
            $message ?: "Value is not null"
        );
    }

    /**
     * Custom assert that value is not null
     */
    protected function weBotAssertIsNotNull($actual, string $message = ''): void
    {
        $this->weBotAssertTrue(
            !is_null($actual),
            $message ?: "Value is null"
        );
    }

    /**
     * Custom assert that value is null
     */
    protected function weBotAssertNull($value, string $message = ''): void
    {
        $this->weBotAssertTrue(
            $value === null,
            $message ?: "Expected null, got " . gettype($value)
        );
    }

    /**
     * Custom assert that value is not null
     */
    protected function weBotAssertNotNull($value, string $message = ''): void
    {
        $this->weBotAssertTrue(
            $value !== null,
            $message ?: "Expected non-null value, got null"
        );
    }
    
    /**
     * Get test statistics
     */
    public static function getTestStats(): array
    {
        return [
            'total' => self::$testCount,
            'passed' => self::$passedCount,
            'failed' => self::$failedCount,
            'failures' => self::$failures,
            'success_rate' => self::$testCount > 0 ? (self::$passedCount / self::$testCount) * 100 : 0
        ];
    }
    
    /**
     * Reset test statistics
     */
    public static function resetStats(): void
    {
        self::$testCount = 0;
        self::$passedCount = 0;
        self::$failedCount = 0;
        self::$failures = [];
    }
}
