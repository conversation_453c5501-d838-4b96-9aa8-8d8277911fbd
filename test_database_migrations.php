<?php
/**
 * Database Migrations Test for WeBot
 * 
 * This script tests the database migration system and verifies
 * that migrations can be executed properly.
 */

declare(strict_types=1);

echo "=== WeBot Database Migrations Test ===\n\n";

// Load the application
try {
    require_once __DIR__ . '/autoload.php';
    echo "✅ Application loaded successfully\n\n";
} catch (Exception $e) {
    echo "❌ Failed to load application: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 1: Migration files existence
echo "1. Migration Files Existence Test:\n";
$migrationDir = 'migrations';
$migrationFilesOk = true;

if (is_dir($migrationDir)) {
    echo "   ✅ Migrations directory exists\n";
    
    $migrationFiles = glob($migrationDir . '/*.sql');
    if (!empty($migrationFiles)) {
        echo "   ✅ Found " . count($migrationFiles) . " migration files\n";
        
        foreach ($migrationFiles as $file) {
            $filename = basename($file);
            if (is_readable($file)) {
                echo "   ✅ {$filename}: Readable\n";
            } else {
                echo "   ❌ {$filename}: Not readable\n";
                $migrationFilesOk = false;
            }
        }
    } else {
        echo "   ❌ No migration files found\n";
        $migrationFilesOk = false;
    }
} else {
    echo "   ❌ Migrations directory does not exist\n";
    $migrationFilesOk = false;
}

// Test 2: Migration runner script
echo "\n2. Migration Runner Script Test:\n";
$runnerOk = true;

$runnerScript = 'scripts/run-migrations.php';
if (file_exists($runnerScript)) {
    echo "   ✅ Migration runner script exists\n";
    
    if (is_readable($runnerScript)) {
        echo "   ✅ Migration runner script is readable\n";
    } else {
        echo "   ❌ Migration runner script is not readable\n";
        $runnerOk = false;
    }
} else {
    echo "   ❌ Migration runner script does not exist\n";
    $runnerOk = false;
}

// Test 3: Migration runner class
echo "\n3. Migration Runner Class Test:\n";
$migrationClassOk = true;

try {
    if (class_exists('WeBot\Core\MigrationRunner')) {
        echo "   ✅ MigrationRunner class exists\n";
        
        $migrationRunner = new WeBot\Core\MigrationRunner();
        echo "   ✅ MigrationRunner can be instantiated\n";
        
        if (method_exists($migrationRunner, 'run')) {
            echo "   ✅ MigrationRunner has run() method\n";
        } else {
            echo "   ❌ MigrationRunner missing run() method\n";
            $migrationClassOk = false;
        }
    } else {
        echo "   ❌ MigrationRunner class does not exist\n";
        $migrationClassOk = false;
    }
} catch (Exception $e) {
    echo "   ❌ Failed to test MigrationRunner class: " . $e->getMessage() . "\n";
    $migrationClassOk = false;
}

// Test 4: Database connection for migrations
echo "\n4. Database Connection for Migrations Test:\n";
$dbConnectionOk = false;

try {
    $dbService = db();
    $connection = $dbService->getConnection();
    
    if ($connection !== null) {
        echo "   ✅ Database connection available for migrations\n";
        $dbConnectionOk = true;
    } else {
        echo "   ❌ Database connection not available\n";
    }
} catch (Exception $e) {
    echo "   ❌ Database connection test failed: " . $e->getMessage() . "\n";
}

// Test 5: Migration table check
if ($dbConnectionOk) {
    echo "\n5. Migration Table Check:\n";
    $migrationTableOk = true;
    
    try {
        $dbService = db();
        $connection = $dbService->getConnection();
        
        // Check if migrations table exists
        $result = $connection->query("SHOW TABLES LIKE 'migrations'");
        if ($result && $result->num_rows > 0) {
            echo "   ✅ Migrations table exists\n";
        } else {
            echo "   ⚠️  Migrations table does not exist (will be created on first run)\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Migration table check failed: " . $e->getMessage() . "\n";
        $migrationTableOk = false;
    }
} else {
    echo "\n5. Migration Table Check: Skipped (no database connection)\n";
    $migrationTableOk = true; // Don't fail the test for this
}

// Test 6: Migration file syntax check
echo "\n6. Migration File Syntax Check:\n";
$syntaxOk = true;

if ($migrationFilesOk) {
    $migrationFiles = glob($migrationDir . '/*.sql');
    $checkedFiles = 0;
    
    foreach (array_slice($migrationFiles, 0, 3) as $file) { // Check first 3 files
        $filename = basename($file);
        $content = file_get_contents($file);
        
        if ($content !== false) {
            // Basic SQL syntax checks
            if (stripos($content, 'CREATE TABLE') !== false || 
                stripos($content, 'ALTER TABLE') !== false ||
                stripos($content, 'INSERT INTO') !== false) {
                echo "   ✅ {$filename}: Contains valid SQL statements\n";
                $checkedFiles++;
            } else {
                echo "   ⚠️  {$filename}: No recognizable SQL statements\n";
            }
        } else {
            echo "   ❌ {$filename}: Cannot read file content\n";
            $syntaxOk = false;
        }
    }
    
    if ($checkedFiles > 0) {
        echo "   ℹ️  Checked {$checkedFiles} migration files for basic syntax\n";
    }
} else {
    echo "   ⚠️  Skipped syntax check (no migration files)\n";
}

// Test 7: Dry run capability
echo "\n7. Dry Run Capability Test:\n";
$dryRunOk = true;

if ($runnerOk) {
    try {
        // Test if we can run the migration script with --help or --dry-run
        $output = [];
        $returnCode = 0;
        
        exec('php scripts/run-migrations.php --help 2>&1', $output, $returnCode);
        
        if ($returnCode === 0 || !empty($output)) {
            echo "   ✅ Migration runner script can be executed\n";
        } else {
            echo "   ⚠️  Migration runner script execution test inconclusive\n";
        }
    } catch (Exception $e) {
        echo "   ❌ Dry run test failed: " . $e->getMessage() . "\n";
        $dryRunOk = false;
    }
} else {
    echo "   ⚠️  Skipped dry run test (no runner script)\n";
}

echo "\n=== Overall Status ===\n";
if ($migrationFilesOk && $runnerOk && $migrationClassOk && $syntaxOk) {
    if ($dbConnectionOk) {
        echo "✅ Migration system is fully functional!\n";
        echo "ℹ️  Ready to run database migrations\n";
        echo "🔧 To run migrations: php scripts/run-migrations.php\n";
    } else {
        echo "⚠️  Migration system is configured but database connection failed\n";
        echo "ℹ️  Migrations are ready but need database connection\n";
        echo "🔧 Fix database connection first, then run migrations\n";
    }
    exit(0);
} else {
    echo "❌ Migration system has issues.\n";
    echo "\n🔧 To fix migration issues:\n";
    echo "   1. Ensure migration files exist in migrations/ directory\n";
    echo "   2. Check migration runner script and class\n";
    echo "   3. Verify SQL syntax in migration files\n";
    echo "   4. Ensure database connection is working\n";
    exit(1);
}
