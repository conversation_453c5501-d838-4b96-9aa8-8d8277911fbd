<?php

declare(strict_types=1);

namespace WeBot\Core;

// Mock PSR Log classes if not available
if (!interface_exists('Psr\Log\LoggerInterface')) {
    class_alias('WeBot\Utils\MockLoggerInterface', 'Psr\Log\LoggerInterface');
}

use Psr\Log\LoggerInterface;

// Define LogLevel constants if not available
if (!class_exists('Psr\Log\LogLevel')) {
    class LogLevel
    {
        public const EMERGENCY = 'emergency';
        public const ALERT = 'alert';
        public const CRITICAL = 'critical';
        public const ERROR = 'error';
        public const WARNING = 'warning';
        public const NOTICE = 'notice';
        public const INFO = 'info';
        public const DEBUG = 'debug';
    }
}

/**
 * Advanced Logger
 *
 * Comprehensive logging system with structured logging,
 * multiple handlers, and advanced filtering capabilities.
 *
 * @package WeBot\Core
 * @version 2.0
 */
class AdvancedLogger implements LoggerInterface
{
    private array $handlers = [];
    private array $processors = [];
    private array $config;
    private string $channel;

    public function __construct(string $channel = 'webot', array $config = [])
    {
        $this->channel = $channel;
        $this->config = array_merge([
            'min_level' => LogLevel::DEBUG,
            'max_file_size' => 10 * 1024 * 1024, // 10MB
            'max_files' => 10,
            'date_format' => 'Y-m-d H:i:s',
            'include_context' => true,
            'include_extra' => true,
            'bubble' => true,
            'handlers' => [
                'file' => [
                    'enabled' => true,
                    'path' => 'storage/logs/webot.log',
                    'level' => LogLevel::DEBUG
                ],
                'error_file' => [
                    'enabled' => true,
                    'path' => 'storage/logs/error.log',
                    'level' => LogLevel::ERROR
                ],
                'database' => [
                    'enabled' => false,
                    'table' => 'logs',
                    'level' => LogLevel::WARNING
                ],
                'slack' => [
                    'enabled' => false,
                    'webhook_url' => '',
                    'level' => LogLevel::CRITICAL
                ]
            ]
        ], $config);

        $this->initializeHandlers();
        $this->initializeProcessors();
    }

    /**
     * Log emergency message
     */
    public function emergency($message, array $context = []): void
    {
        $this->log(LogLevel::EMERGENCY, $message, $context);
    }

    /**
     * Log alert message
     */
    public function alert($message, array $context = []): void
    {
        $this->log(LogLevel::ALERT, $message, $context);
    }

    /**
     * Log critical message
     */
    public function critical($message, array $context = []): void
    {
        $this->log(LogLevel::CRITICAL, $message, $context);
    }

    /**
     * Log error message
     */
    public function error($message, array $context = []): void
    {
        $this->log(LogLevel::ERROR, $message, $context);
    }

    /**
     * Log warning message
     */
    public function warning($message, array $context = []): void
    {
        $this->log(LogLevel::WARNING, $message, $context);
    }

    /**
     * Log notice message
     */
    public function notice($message, array $context = []): void
    {
        $this->log(LogLevel::NOTICE, $message, $context);
    }

    /**
     * Log info message
     */
    public function info($message, array $context = []): void
    {
        $this->log(LogLevel::INFO, $message, $context);
    }

    /**
     * Log debug message
     */
    public function debug($message, array $context = []): void
    {
        $this->log(LogLevel::DEBUG, $message, $context);
    }

    /**
     * Main log method
     */
    public function log($level, $message, array $context = []): void
    {
        if (!$this->isLevelEnabled($level)) {
            return;
        }

        $record = $this->createRecord($level, $message, $context);

        // Apply processors
        foreach ($this->processors as $processor) {
            $record = $processor($record);
        }

        // Send to handlers
        foreach ($this->handlers as $handler) {
            if ($handler->isHandling($record)) {
                $handler->handle($record);
            }
        }
    }

    /**
     * Log exception with full stack trace
     */
    public function logException(\Throwable $exception, string $level = LogLevel::ERROR, array $context = []): void
    {
        $context = array_merge($context, [
            'exception' => [
                'class' => get_class($exception),
                'message' => $exception->getMessage(),
                'code' => $exception->getCode(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString(),
                'previous' => $exception->getPrevious() ? [
                    'class' => get_class($exception->getPrevious()),
                    'message' => $exception->getPrevious()->getMessage()
                ] : null
            ]
        ]);

        $this->log($level, $exception->getMessage(), $context);
    }

    /**
     * Log user activity
     */
    public function logUserActivity(int $userId, string $action, array $details = []): void
    {
        $this->info('User activity', [
            'user_id' => $userId,
            'action' => $action,
            'details' => $details,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'timestamp' => time()
        ]);
    }

    /**
     * Log security event
     */
    public function logSecurityEvent(string $event, array $context = []): void
    {
        $context = array_merge($context, [
            'security_event' => true,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
        ]);

        $this->warning("Security event: {$event}", $context);
    }

    /**
     * Log performance metrics
     */
    public function logPerformance(string $operation, float $duration, array $metrics = []): void
    {
        $this->info('Performance metric', [
            'operation' => $operation,
            'duration_ms' => round($duration * 1000, 2),
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
            'metrics' => $metrics
        ]);
    }

    /**
     * Log API request/response
     */
    public function logApiCall(string $method, string $url, int $statusCode, float $duration, array $context = []): void
    {
        $level = $statusCode >= 500 ? LogLevel::ERROR :
                ($statusCode >= 400 ? LogLevel::WARNING : LogLevel::INFO);

        $this->log($level, "API call: {$method} {$url}", [
            'api_call' => true,
            'method' => $method,
            'url' => $url,
            'status_code' => $statusCode,
            'duration_ms' => round($duration * 1000, 2),
            'context' => $context
        ]);
    }

    /**
     * Get log statistics
     */
    public function getStatistics(): array
    {
        $stats = [
            'total_logs' => 0,
            'by_level' => [],
            'by_channel' => [],
            'recent_errors' => [],
            'performance_metrics' => []
        ];

        // This would be implemented to read from log files or database
        // For now, return basic structure
        return $stats;
    }

    /**
     * Search logs
     */
    public function searchLogs(array $criteria): array
    {
        unset($criteria); // Suppress unused parameter warning
        $results = [];

        // Implementation would search through log files or database
        // Based on criteria like level, date range, message content, etc.

        return $results;
    }

    /**
     * Create log record
     */
    private function createRecord(string $level, string $message, array $context): array
    {
        return [
            'channel' => $this->channel,
            'level' => $level,
            'level_name' => strtoupper($level),
            'message' => $message,
            'context' => $context,
            'datetime' => new \DateTime(),
            'extra' => []
        ];
    }

    /**
     * Check if level is enabled
     */
    private function isLevelEnabled(string $level): bool
    {
        $levels = [
            LogLevel::DEBUG => 100,
            LogLevel::INFO => 200,
            LogLevel::NOTICE => 250,
            LogLevel::WARNING => 300,
            LogLevel::ERROR => 400,
            LogLevel::CRITICAL => 500,
            LogLevel::ALERT => 550,
            LogLevel::EMERGENCY => 600
        ];

        $currentLevel = $levels[$this->config['min_level']] ?? 100;
        $messageLevel = $levels[$level] ?? 100;

        return $messageLevel >= $currentLevel;
    }

    /**
     * Initialize handlers
     */
    private function initializeHandlers(): void
    {
        // File handler
        if ($this->config['handlers']['file']['enabled']) {
            $this->handlers[] = new FileLogHandler(
                $this->config['handlers']['file']['path'],
                $this->config['handlers']['file']['level'],
                $this->config
            );
        }

        // Error file handler
        if ($this->config['handlers']['error_file']['enabled']) {
            $this->handlers[] = new FileLogHandler(
                $this->config['handlers']['error_file']['path'],
                $this->config['handlers']['error_file']['level'],
                $this->config
            );
        }

        // Database handler
        if ($this->config['handlers']['database']['enabled']) {
            $this->handlers[] = new DatabaseLogHandler(
                $this->config['handlers']['database']['table'],
                $this->config['handlers']['database']['level']
            );
        }

        // Slack handler
        if ($this->config['handlers']['slack']['enabled']) {
            $this->handlers[] = new SlackLogHandler(
                $this->config['handlers']['slack']['webhook_url'],
                $this->config['handlers']['slack']['level']
            );
        }
    }

    /**
     * Initialize processors
     */
    private function initializeProcessors(): void
    {
        // Add request ID processor
        $this->processors[] = function (array $record): array {
            $record['extra']['request_id'] = $_SERVER['HTTP_X_REQUEST_ID'] ??
                                           uniqid('req_', true);
            return $record;
        };

        // Add memory usage processor
        $this->processors[] = function (array $record): array {
            $record['extra']['memory_usage'] = memory_get_usage(true);
            $record['extra']['peak_memory'] = memory_get_peak_usage(true);
            return $record;
        };

        // Add process ID processor
        $this->processors[] = function (array $record): array {
            $record['extra']['process_id'] = getmypid();
            return $record;
        };

        // Add user context processor
        $this->processors[] = function (array $record): array {
            if (isset($_SESSION['user_id'])) {
                $record['extra']['user_id'] = $_SESSION['user_id'];
            }
            return $record;
        };
    }
}

/**
 * File Log Handler
 */
class FileLogHandler
{
    private string $filePath;
    private string $level;
    private array $config;

    public function __construct(string $filePath, string $level, array $config)
    {
        $this->filePath = $filePath;
        $this->level = $level;
        $this->config = $config;

        $this->ensureDirectoryExists();
    }

    public function isHandling(array $record): bool
    {
        $levels = [
            LogLevel::DEBUG => 100,
            LogLevel::INFO => 200,
            LogLevel::NOTICE => 250,
            LogLevel::WARNING => 300,
            LogLevel::ERROR => 400,
            LogLevel::CRITICAL => 500,
            LogLevel::ALERT => 550,
            LogLevel::EMERGENCY => 600
        ];

        $handlerLevel = $levels[$this->level] ?? 100;
        $recordLevel = $levels[$record['level']] ?? 100;

        return $recordLevel >= $handlerLevel;
    }

    public function handle(array $record): void
    {
        $this->rotateIfNeeded();

        $formatted = $this->format($record);
        file_put_contents($this->filePath, $formatted . PHP_EOL, FILE_APPEND | LOCK_EX);
    }

    private function format(array $record): string
    {
        $datetime = $record['datetime']->format($this->config['date_format']);
        $level = str_pad($record['level_name'], 9);
        $channel = str_pad($record['channel'], 10);

        $message = $record['message'];

        // Add context if enabled
        if ($this->config['include_context'] && !empty($record['context'])) {
            $message .= ' ' . json_encode($record['context'], JSON_UNESCAPED_UNICODE);
        }

        // Add extra if enabled
        if ($this->config['include_extra'] && !empty($record['extra'])) {
            $message .= ' ' . json_encode($record['extra'], JSON_UNESCAPED_UNICODE);
        }

        return "[{$datetime}] {$channel}.{$level}: {$message}";
    }

    private function rotateIfNeeded(): void
    {
        if (!file_exists($this->filePath)) {
            return;
        }

        if (filesize($this->filePath) > $this->config['max_file_size']) {
            $this->rotateFile();
        }
    }

    private function rotateFile(): void
    {
        $pathInfo = pathinfo($this->filePath);
        $baseName = $pathInfo['filename'];
        $extension = $pathInfo['extension'] ?? '';
        $directory = $pathInfo['dirname'];

        // Move existing rotated files
        for ($i = $this->config['max_files'] - 1; $i > 0; $i--) {
            $oldFile = "{$directory}/{$baseName}.{$i}.{$extension}";
            $newFile = "{$directory}/{$baseName}." . ($i + 1) . ".{$extension}";

            if (file_exists($oldFile)) {
                if ($i === $this->config['max_files'] - 1) {
                    unlink($oldFile); // Delete oldest file
                } else {
                    rename($oldFile, $newFile);
                }
            }
        }

        // Move current file to .1
        $rotatedFile = "{$directory}/{$baseName}.1.{$extension}";
        rename($this->filePath, $rotatedFile);
    }

    private function ensureDirectoryExists(): void
    {
        $directory = dirname($this->filePath);
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
    }
}

/**
 * Database Log Handler
 */
class DatabaseLogHandler
{
    private string $table;
    private string $level;

    public function __construct(string $table, string $level)
    {
        $this->table = $table;
        $this->level = $level;
    }

    public function isHandling(array $record): bool
    {
        unset($record); // Suppress unused parameter warning
        // Implementation similar to FileLogHandler
        return true;
    }

    public function handle(array $record): void
    {
        unset($record); // Suppress unused parameter warning
        // Implementation would insert log record into database
        // This is a placeholder for the actual database insertion
    }
}

/**
 * Slack Log Handler
 */
class SlackLogHandler
{
    private string $webhookUrl;
    private string $level;

    public function __construct(string $webhookUrl, string $level)
    {
        $this->webhookUrl = $webhookUrl;
        $this->level = $level;
    }

    public function isHandling(array $record): bool
    {
        unset($record); // Suppress unused parameter warning
        // Implementation similar to FileLogHandler
        return true;
    }

    public function handle(array $record): void
    {
        unset($record); // Suppress unused parameter warning
        // Implementation would send log to Slack webhook
        // This is a placeholder for the actual Slack integration
    }
}
