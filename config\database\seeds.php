<?php

declare(strict_types=1);

/**
 * Database Seeds Configuration
 * 
 * Configuration for database seeding, test data generation,
 * and initial application data setup.
 * 
 * @package WeBot\Config\Database
 * @version 2.0
 */

return [
    /*
    |--------------------------------------------------------------------------
    | Seeding Settings
    |--------------------------------------------------------------------------
    */
    'enabled' => (bool)($_ENV['SEEDING_ENABLED'] ?? true),
    'path' => $_ENV['SEEDERS_PATH'] ?? 'database/seeders',
    'namespace' => 'WeBot\\Database\\Seeders',

    /*
    |--------------------------------------------------------------------------
    | Execution Order
    |--------------------------------------------------------------------------
    */
    'execution_order' => [
        'AdminSeeder',
        'DefaultSettingsSeeder',
        'BotCommandsSeeder',
        'ServiceTemplatesSeeder',
        'PaymentMethodsSeeder',
        'TestDataSeeder', // Only in development/testing
    ],

    /*
    |--------------------------------------------------------------------------
    | Environment-Specific Seeders
    |--------------------------------------------------------------------------
    */
    'environments' => [
        'development' => [
            'AdminSeeder',
            'DefaultSettingsSeeder',
            'BotCommandsSeeder',
            'ServiceTemplatesSeeder',
            'PaymentMethodsSeeder',
            'TestDataSeeder',
            'DemoUsersSeeder',
            'SampleServicesSeeder',
        ],
        'testing' => [
            'AdminSeeder',
            'DefaultSettingsSeeder',
            'BotCommandsSeeder',
            'TestDataSeeder',
        ],
        'staging' => [
            'AdminSeeder',
            'DefaultSettingsSeeder',
            'BotCommandsSeeder',
            'ServiceTemplatesSeeder',
            'PaymentMethodsSeeder',
        ],
        'production' => [
            'AdminSeeder',
            'DefaultSettingsSeeder',
            'BotCommandsSeeder',
            'ServiceTemplatesSeeder',
            'PaymentMethodsSeeder',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Seeder Configuration
    |--------------------------------------------------------------------------
    */
    'seeders' => [
        'AdminSeeder' => [
            'class' => 'WeBot\\Database\\Seeders\\AdminSeeder',
            'description' => 'Create default admin user',
            'required' => true,
            'data' => [
                'telegram_id' => (int)($_ENV['ADMIN_TELEGRAM_ID'] ?? 123456789),
                'username' => $_ENV['ADMIN_USERNAME'] ?? 'admin',
                'first_name' => $_ENV['ADMIN_FIRST_NAME'] ?? 'WeBot Admin',
                'role' => 'admin',
                'status' => 'active',
                'is_verified' => true,
                'subscription_status' => 'vip',
            ],
        ],

        'DefaultSettingsSeeder' => [
            'class' => 'WeBot\\Database\\Seeders\\DefaultSettingsSeeder',
            'description' => 'Create default application settings',
            'required' => true,
            'data' => [
                'app_name' => $_ENV['APP_NAME'] ?? 'WeBot',
                'app_version' => '2.0.0',
                'default_language' => 'fa',
                'timezone' => 'Asia/Tehran',
                'currency' => 'USD',
                'maintenance_mode' => false,
                'registration_enabled' => true,
                'max_services_per_user' => 5,
                'default_service_duration' => 30, // days
                'support_telegram' => $_ENV['SUPPORT_TELEGRAM'] ?? '',
                'support_email' => $_ENV['SUPPORT_EMAIL'] ?? '',
            ],
        ],

        'BotCommandsSeeder' => [
            'class' => 'WeBot\\Database\\Seeders\\BotCommandsSeeder',
            'description' => 'Create default bot commands',
            'required' => true,
            'data' => [
                [
                    'command' => '/start',
                    'description' => 'شروع کار با ربات',
                    'category' => 'basic',
                    'required_role' => 'user',
                    'is_enabled' => true,
                    'response_type' => 'keyboard',
                ],
                [
                    'command' => '/help',
                    'description' => 'راهنمای استفاده از ربات',
                    'category' => 'basic',
                    'required_role' => 'user',
                    'is_enabled' => true,
                    'response_type' => 'text',
                ],
                [
                    'command' => '/menu',
                    'description' => 'منوی اصلی',
                    'category' => 'basic',
                    'required_role' => 'user',
                    'is_enabled' => true,
                    'response_type' => 'keyboard',
                ],
                [
                    'command' => '/profile',
                    'description' => 'مشاهده پروفایل کاربری',
                    'category' => 'user',
                    'required_role' => 'user',
                    'is_enabled' => true,
                    'response_type' => 'text',
                ],
                [
                    'command' => '/services',
                    'description' => 'مشاهده سرویس‌های من',
                    'category' => 'user',
                    'required_role' => 'user',
                    'is_enabled' => true,
                    'response_type' => 'keyboard',
                ],
                [
                    'command' => '/buy',
                    'description' => 'خرید سرویس جدید',
                    'category' => 'user',
                    'required_role' => 'user',
                    'is_enabled' => true,
                    'response_type' => 'keyboard',
                ],
                [
                    'command' => '/balance',
                    'description' => 'مشاهده موجودی',
                    'category' => 'user',
                    'required_role' => 'user',
                    'is_enabled' => true,
                    'response_type' => 'text',
                ],
                [
                    'command' => '/support',
                    'description' => 'پشتیبانی',
                    'category' => 'user',
                    'required_role' => 'user',
                    'is_enabled' => true,
                    'response_type' => 'text',
                ],
                [
                    'command' => '/admin',
                    'description' => 'پنل مدیریت',
                    'category' => 'admin',
                    'required_role' => 'admin',
                    'is_enabled' => true,
                    'response_type' => 'keyboard',
                ],
                [
                    'command' => '/stats',
                    'description' => 'آمار سیستم',
                    'category' => 'admin',
                    'required_role' => 'admin',
                    'is_enabled' => true,
                    'response_type' => 'text',
                ],
            ],
        ],

        'ServiceTemplatesSeeder' => [
            'class' => 'WeBot\\Database\\Seeders\\ServiceTemplatesSeeder',
            'description' => 'Create default service templates',
            'required' => false,
            'data' => [
                [
                    'name' => 'Basic VPN',
                    'service_type' => 'vpn',
                    'panel_type' => 'marzban',
                    'data_limit' => 50 * 1024 * 1024 * 1024, // 50GB
                    'duration_days' => 30,
                    'connection_limit' => 1,
                    'price' => 5.00,
                    'currency' => 'USD',
                    'is_active' => true,
                ],
                [
                    'name' => 'Premium VPN',
                    'service_type' => 'vpn',
                    'panel_type' => 'marzneshin',
                    'data_limit' => 100 * 1024 * 1024 * 1024, // 100GB
                    'duration_days' => 30,
                    'connection_limit' => 2,
                    'price' => 10.00,
                    'currency' => 'USD',
                    'is_active' => true,
                ],
                [
                    'name' => 'Unlimited VPN',
                    'service_type' => 'vpn',
                    'panel_type' => 'x-ui',
                    'data_limit' => null, // Unlimited
                    'duration_days' => 30,
                    'connection_limit' => 3,
                    'price' => 20.00,
                    'currency' => 'USD',
                    'is_active' => true,
                ],
            ],
        ],

        'PaymentMethodsSeeder' => [
            'class' => 'WeBot\\Database\\Seeders\\PaymentMethodsSeeder',
            'description' => 'Create default payment methods',
            'required' => false,
            'data' => [
                [
                    'name' => 'Stripe',
                    'type' => 'stripe',
                    'is_enabled' => (bool)($_ENV['STRIPE_ENABLED'] ?? false),
                    'config' => [
                        'public_key' => $_ENV['STRIPE_PUBLIC_KEY'] ?? '',
                        'currencies' => ['USD', 'EUR'],
                        'min_amount' => 1.00,
                        'max_amount' => 1000.00,
                    ],
                ],
                [
                    'name' => 'PayPal',
                    'type' => 'paypal',
                    'is_enabled' => (bool)($_ENV['PAYPAL_ENABLED'] ?? false),
                    'config' => [
                        'mode' => $_ENV['PAYPAL_MODE'] ?? 'sandbox',
                        'currencies' => ['USD', 'EUR'],
                        'min_amount' => 1.00,
                        'max_amount' => 1000.00,
                    ],
                ],
                [
                    'name' => 'Cryptocurrency',
                    'type' => 'crypto',
                    'is_enabled' => (bool)($_ENV['CRYPTO_ENABLED'] ?? false),
                    'config' => [
                        'supported_coins' => ['BTC', 'ETH', 'USDT'],
                        'min_confirmations' => 3,
                    ],
                ],
            ],
        ],

        'TestDataSeeder' => [
            'class' => 'WeBot\\Database\\Seeders\\TestDataSeeder',
            'description' => 'Create test data for development',
            'required' => false,
            'environments' => ['development', 'testing'],
            'data' => [
                'users_count' => 50,
                'services_count' => 100,
                'payments_count' => 200,
                'generate_realistic_data' => true,
            ],
        ],

        'DemoUsersSeeder' => [
            'class' => 'WeBot\\Database\\Seeders\\DemoUsersSeeder',
            'description' => 'Create demo users for development',
            'required' => false,
            'environments' => ['development'],
            'data' => [
                [
                    'telegram_id' => 111111111,
                    'username' => 'demo_user',
                    'first_name' => 'Demo User',
                    'role' => 'user',
                    'status' => 'active',
                    'balance' => 100.00,
                ],
                [
                    'telegram_id' => 222222222,
                    'username' => 'demo_moderator',
                    'first_name' => 'Demo Moderator',
                    'role' => 'moderator',
                    'status' => 'active',
                    'balance' => 50.00,
                ],
            ],
        ],

        'SampleServicesSeeder' => [
            'class' => 'WeBot\\Database\\Seeders\\SampleServicesSeeder',
            'description' => 'Create sample services for development',
            'required' => false,
            'environments' => ['development'],
            'data' => [
                'services_per_user' => 3,
                'include_expired' => true,
                'include_suspended' => true,
                'realistic_usage' => true,
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Execution Settings
    |--------------------------------------------------------------------------
    */
    'execution' => [
        'timeout' => (int)($_ENV['SEEDER_TIMEOUT'] ?? 300), // 5 minutes
        'memory_limit' => $_ENV['SEEDER_MEMORY_LIMIT'] ?? '256M',
        'batch_size' => (int)($_ENV['SEEDER_BATCH_SIZE'] ?? 100),
        'transaction_mode' => 'per_seeder', // 'per_seeder', 'per_batch', 'none'
        'continue_on_error' => (bool)($_ENV['SEEDER_CONTINUE_ON_ERROR'] ?? false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Data Generation
    |--------------------------------------------------------------------------
    */
    'data_generation' => [
        'faker_locale' => $_ENV['FAKER_LOCALE'] ?? 'fa_IR',
        'realistic_data' => true,
        'use_real_names' => false,
        'anonymize_data' => true,
        'seed_value' => (int)($_ENV['FAKER_SEED'] ?? 12345), // For consistent test data
    ],

    /*
    |--------------------------------------------------------------------------
    | Validation
    |--------------------------------------------------------------------------
    */
    'validation' => [
        'check_dependencies' => true,
        'validate_data' => true,
        'check_constraints' => true,
        'verify_relationships' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Cleanup
    |--------------------------------------------------------------------------
    */
    'cleanup' => [
        'truncate_before_seed' => (bool)($_ENV['TRUNCATE_BEFORE_SEED'] ?? false),
        'preserve_admin_data' => true,
        'preserve_settings' => true,
        'cleanup_test_data' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring
    |--------------------------------------------------------------------------
    */
    'monitoring' => [
        'log_execution' => true,
        'track_performance' => true,
        'report_statistics' => true,
        'notify_completion' => (bool)($_ENV['SEEDER_NOTIFY_COMPLETION'] ?? false),
    ],
];
