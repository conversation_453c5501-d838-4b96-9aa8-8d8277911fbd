<?php
/**
 * File Permissions Checker for WeBot
 * 
 * This script checks if all required directories have proper
 * read/write permissions for WeBot to function correctly.
 */

declare(strict_types=1);

echo "=== WeBot File Permissions Check ===\n\n";

// Directories that need write permissions
$writableDirectories = [
    'storage' => 'Main storage directory',
    'storage/logs' => 'Application logs',
    'storage/cache' => 'Cache files',
    'storage/sessions' => 'Session files',
    'storage/uploads' => 'Uploaded files',
    'storage/backups' => 'Backup files',
    'public/uploads' => 'Public uploaded files',
    'public/temp' => 'Temporary files',
    'logs' => 'Legacy logs directory'
];

// Directories that need read permissions
$readableDirectories = [
    'src' => 'Source code',
    'config' => 'Configuration files',
    'resources' => 'Resources and templates',
    'vendor' => 'Composer dependencies',
    'tests' => 'Test files'
];

// Files that need read permissions
$readableFiles = [
    'composer.json' => 'Composer configuration',
    'autoload.php' => 'Application autoloader',
    'index.php' => 'Main entry point',
    '.env' => 'Environment configuration (optional)'
];

echo "1. Writable Directories Check:\n";
$allWritableOk = true;
foreach ($writableDirectories as $dir => $description) {
    if (!file_exists($dir)) {
        echo "   📁 Creating directory: {$dir}\n";
        if (!mkdir($dir, 0755, true)) {
            echo "   ❌ {$dir}: Cannot create - {$description}\n";
            $allWritableOk = false;
            continue;
        }
    }
    
    if (is_writable($dir)) {
        echo "   ✅ {$dir}: Writable - {$description}\n";
    } else {
        echo "   ❌ {$dir}: Not writable - {$description}\n";
        $allWritableOk = false;
    }
}

echo "\n2. Readable Directories Check:\n";
$allReadableOk = true;
foreach ($readableDirectories as $dir => $description) {
    if (is_readable($dir)) {
        echo "   ✅ {$dir}: Readable - {$description}\n";
    } else {
        echo "   ❌ {$dir}: Not readable - {$description}\n";
        $allReadableOk = false;
    }
}

echo "\n3. Required Files Check:\n";
$allFilesOk = true;
foreach ($readableFiles as $file => $description) {
    if (file_exists($file)) {
        if (is_readable($file)) {
            echo "   ✅ {$file}: Readable - {$description}\n";
        } else {
            echo "   ❌ {$file}: Not readable - {$description}\n";
            $allFilesOk = false;
        }
    } else {
        if ($file === '.env') {
            echo "   ⚠️  {$file}: Not found - {$description} (optional)\n";
        } else {
            echo "   ❌ {$file}: Not found - {$description}\n";
            $allFilesOk = false;
        }
    }
}

echo "\n4. Test Write Operations:\n";
$writeTestOk = true;

// Test writing to storage/logs
$testLogFile = 'storage/logs/permission_test.log';
if (file_put_contents($testLogFile, "Permission test: " . date('Y-m-d H:i:s') . "\n")) {
    echo "   ✅ Can write to storage/logs\n";
    unlink($testLogFile); // Clean up
} else {
    echo "   ❌ Cannot write to storage/logs\n";
    $writeTestOk = false;
}

// Test writing to storage/cache
$testCacheFile = 'storage/cache/permission_test.cache';
if (file_put_contents($testCacheFile, "cache test")) {
    echo "   ✅ Can write to storage/cache\n";
    unlink($testCacheFile); // Clean up
} else {
    echo "   ❌ Cannot write to storage/cache\n";
    $writeTestOk = false;
}

echo "\n=== Overall Status ===\n";
if ($allWritableOk && $allReadableOk && $allFilesOk && $writeTestOk) {
    echo "✅ All file permissions are properly configured!\n";
    exit(0);
} else {
    echo "❌ Some file permission issues found.\n";
    echo "\n🔧 To fix permission issues:\n";
    echo "   On Linux/Mac:\n";
    echo "   - chmod 755 for directories\n";
    echo "   - chmod 644 for files\n";
    echo "   - chmod 777 for writable directories (storage, logs, cache)\n";
    echo "   \n";
    echo "   On Windows:\n";
    echo "   - Ensure the web server has read/write access\n";
    echo "   - Check folder properties and security settings\n";
    exit(1);
}
