<?php

declare(strict_types=1);

namespace WeBot\Microservices\Migration;

use WeBot\Core\Application;
use WeBot\Utils\Logger;
use WeBot\Exceptions\WeBotException;
use WeBot\Microservices\Bootstrap\MicroservicesBootstrap;
use <PERSON>Bot\Controllers\UserController;
use WeBot\Controllers\PaymentController;
use WeBot\Controllers\PanelController;

/**
 * Monolith to Microservices Bridge
 *
 * Provides a bridge between the existing monolithic architecture
 * and the new microservices architecture for gradual migration.
 *
 * @package WeBot\Microservices\Migration
 * @version 2.0
 */
class MonolithToMicroservicesBridge
{
    private Application $app;
    private MicroservicesBootstrap $microservices;
    private Logger $logger;
    private array $config;
    private array $migrationStatus = [];
    private mixed $container;

    public function __construct(
        Application $app,
        MicroservicesBootstrap $microservices,
        array $config = []
    ) {
        $this->app = $app;
        $this->microservices = $microservices;
        $this->logger = Logger::getInstance();
        $this->config = array_merge($this->getDefaultConfig(), $config);
        $this->container = $app; // Use app as container for now

        $this->loadMigrationStatus();
    }

    /**
     * Handle request with migration logic
     */
    public function handleRequest(array $request): array
    {
        $path = $request['path'] ?? '';
        $method = $request['method'] ?? 'POST';

        try {
            // Determine if request should go to microservices or monolith
            if ($this->shouldUseMicroservices($path, $method)) {
                return $this->handleMicroserviceRequest($request);
            } else {
                return $this->handleMonolithRequest($request);
            }
        } catch (\Exception $e) {
            $this->logger->error("Bridge request failed", [
                'path' => $path,
                'method' => $method,
                'error' => $e->getMessage()
            ]);

            // Fallback to monolith on microservice failure
            if ($this->shouldUseMicroservices($path, $method)) {
                $this->logger->info("Falling back to monolith", ['path' => $path]);
                return $this->handleMonolithRequest($request);
            }

            throw $e;
        }
    }

    /**
     * Migrate specific feature to microservices
     */
    public function migrateFeature(string $feature): array
    {
        try {
            $this->logger->info("Starting feature migration", ['feature' => $feature]);

            switch ($feature) {
                case 'user_management':
                    return $this->migrateUserManagement();

                case 'payment_processing':
                    return $this->migratePaymentProcessing();

                case 'panel_management':
                    return $this->migratePanelManagement();

                case 'all':
                    return $this->migrateAllFeatures();

                default:
                    throw new WeBotException("Unknown feature: {$feature}");
            }
        } catch (\Exception $e) {
            $this->logger->error("Feature migration failed", [
                'feature' => $feature,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Rollback feature to monolith
     */
    public function rollbackFeature(string $feature): array
    {
        try {
            $this->logger->info("Rolling back feature", ['feature' => $feature]);

            $this->migrationStatus[$feature] = [
                'status' => 'monolith',
                'migrated_at' => null,
                'rolled_back_at' => time()
            ];

            $this->saveMigrationStatus();

            return [
                'success' => true,
                'feature' => $feature,
                'status' => 'rolled_back',
                'message' => "Feature {$feature} rolled back to monolith"
            ];
        } catch (\Exception $e) {
            $this->logger->error("Feature rollback failed", [
                'feature' => $feature,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Get migration status
     */
    public function getMigrationStatus(): array
    {
        return [
            'overall_progress' => $this->calculateOverallProgress(),
            'features' => $this->migrationStatus,
            'microservices_health' => $this->microservices->getSystemStatus(),
            'last_updated' => time()
        ];
    }

    /**
     * Sync data between monolith and microservices
     */
    public function syncData(string $dataType): array
    {
        try {
            $this->logger->info("Starting data sync", ['data_type' => $dataType]);

            switch ($dataType) {
                case 'users':
                    return $this->syncUsers();

                case 'payments':
                    return $this->syncPayments();

                case 'services':
                    return $this->syncServices();

                case 'all':
                    return $this->syncAllData();

                default:
                    throw new WeBotException("Unknown data type: {$dataType}");
            }
        } catch (\Exception $e) {
            $this->logger->error("Data sync failed", [
                'data_type' => $dataType,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Handle microservice request
     */
    private function handleMicroserviceRequest(array $request): array
    {
        return $this->microservices->handleRequest($request);
    }

    /**
     * Handle monolith request
     */
    private function handleMonolithRequest(array $request): array
    {
        // Route to appropriate monolith controller
        $path = $request['path'] ?? '';

        if (str_contains($path, '/users') || str_contains($path, '/auth')) {
            $controller = new UserController($this->container);
            return $this->callMonolithController($controller, $request);
        }

        if (str_contains($path, '/payments') || str_contains($path, '/wallet')) {
            $controller = new PaymentController($this->container);
            return $this->callMonolithController($controller, $request);
        }

        if (str_contains($path, '/panels') || str_contains($path, '/services')) {
            $controller = new PanelController($this->container);
            return $this->callMonolithController($controller, $request);
        }

        throw new WeBotException("No suitable controller found for path: {$path}");
    }

    /**
     * Call monolith controller
     */
    private function callMonolithController($controller, array $request): array
    {
        // Convert microservice request format to monolith format
        $monolithRequest = $this->convertToMonolithFormat($request);

        // Call appropriate controller method based on request
        $action = $request['data']['action'] ?? 'handle';

        if (method_exists($controller, $action)) {
            return $controller->$action($monolithRequest);
        }

        // Fallback to generic handle method
        if (method_exists($controller, 'handle')) {
            return $controller->handle($monolithRequest);
        }

        throw new WeBotException("Controller method not found");
    }

    /**
     * Convert microservice request to monolith format
     */
    private function convertToMonolithFormat(array $request): array
    {
        // Convert microservice request format to legacy format
        return [
            'message' => $request['data'] ?? [],
            'callback_query' => $request['callback_query'] ?? null,
            'user_id' => $request['data']['user_id'] ?? null
        ];
    }

    /**
     * Determine if request should use microservices
     */
    private function shouldUseMicroservices(string $path, string $method): bool
    {
        // Check feature migration status
        if (str_contains($path, '/users') || str_contains($path, '/auth')) {
            return $this->isFeatureMigrated('user_management');
        }

        if (str_contains($path, '/payments') || str_contains($path, '/wallet')) {
            return $this->isFeatureMigrated('payment_processing');
        }

        if (str_contains($path, '/panels') || str_contains($path, '/services')) {
            return $this->isFeatureMigrated('panel_management');
        }

        // Check if path is explicitly configured for microservices
        foreach ($this->config['microservice_paths'] as $pattern) {
            if (fnmatch($pattern, $path)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if feature is migrated
     */
    private function isFeatureMigrated(string $feature): bool
    {
        return ($this->migrationStatus[$feature]['status'] ?? 'monolith') === 'microservice';
    }

    /**
     * Migrate user management
     */
    private function migrateUserManagement(): array
    {
        // Sync existing users to microservice
        $syncResult = $this->syncUsers();

        if ($syncResult['success']) {
            $this->migrationStatus['user_management'] = [
                'status' => 'microservice',
                'migrated_at' => time(),
                'sync_result' => $syncResult
            ];

            $this->saveMigrationStatus();

            return [
                'success' => true,
                'feature' => 'user_management',
                'status' => 'migrated',
                'sync_result' => $syncResult
            ];
        }

        throw new WeBotException("User management migration failed");
    }

    /**
     * Migrate payment processing
     */
    private function migratePaymentProcessing(): array
    {
        // Sync existing payments to microservice
        $syncResult = $this->syncPayments();

        if ($syncResult['success']) {
            $this->migrationStatus['payment_processing'] = [
                'status' => 'microservice',
                'migrated_at' => time(),
                'sync_result' => $syncResult
            ];

            $this->saveMigrationStatus();

            return [
                'success' => true,
                'feature' => 'payment_processing',
                'status' => 'migrated',
                'sync_result' => $syncResult
            ];
        }

        throw new WeBotException("Payment processing migration failed");
    }

    /**
     * Migrate panel management
     */
    private function migratePanelManagement(): array
    {
        // Sync existing panels and services to microservice
        $syncResult = $this->syncServices();

        if ($syncResult['success']) {
            $this->migrationStatus['panel_management'] = [
                'status' => 'microservice',
                'migrated_at' => time(),
                'sync_result' => $syncResult
            ];

            $this->saveMigrationStatus();

            return [
                'success' => true,
                'feature' => 'panel_management',
                'status' => 'migrated',
                'sync_result' => $syncResult
            ];
        }

        throw new WeBotException("Panel management migration failed");
    }

    /**
     * Migrate all features
     */
    private function migrateAllFeatures(): array
    {
        $results = [];

        $features = ['user_management', 'payment_processing', 'panel_management'];

        foreach ($features as $feature) {
            try {
                $results[$feature] = $this->migrateFeature($feature);
            } catch (\Exception $e) {
                $results[$feature] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }

        return [
            'success' => true,
            'results' => $results,
            'overall_progress' => $this->calculateOverallProgress()
        ];
    }

    /**
     * Sync users data
     */
    private function syncUsers(): array
    {
        // Mock implementation - replace with actual data sync
        return [
            'success' => true,
            'synced_count' => 100,
            'message' => 'Users synced successfully'
        ];
    }

    /**
     * Sync payments data
     */
    private function syncPayments(): array
    {
        // Mock implementation - replace with actual data sync
        return [
            'success' => true,
            'synced_count' => 50,
            'message' => 'Payments synced successfully'
        ];
    }

    /**
     * Sync services data
     */
    private function syncServices(): array
    {
        // Mock implementation - replace with actual data sync
        return [
            'success' => true,
            'synced_count' => 75,
            'message' => 'Services synced successfully'
        ];
    }

    /**
     * Sync all data
     */
    private function syncAllData(): array
    {
        $results = [
            'users' => $this->syncUsers(),
            'payments' => $this->syncPayments(),
            'services' => $this->syncServices()
        ];

        return [
            'success' => true,
            'results' => $results,
            'total_synced' => array_sum(array_column($results, 'synced_count'))
        ];
    }

    /**
     * Calculate overall migration progress
     */
    private function calculateOverallProgress(): float
    {
        $totalFeatures = 3; // user_management, payment_processing, panel_management
        $migratedFeatures = 0;

        foreach ($this->migrationStatus as $status) {
            if (($status['status'] ?? 'monolith') === 'microservice') {
                $migratedFeatures++;
            }
        }

        return ($migratedFeatures / $totalFeatures) * 100;
    }

    /**
     * Load migration status from cache
     */
    private function loadMigrationStatus(): void
    {
        // Try to get cache from container, fallback to default values
        try {
            $cache = $this->app->getContainer()->get('cache');
            $this->migrationStatus = $cache->get('migration_status', [
                'user_management' => ['status' => 'monolith'],
                'payment_processing' => ['status' => 'monolith'],
                'panel_management' => ['status' => 'monolith']
            ]);
        } catch (\Exception $e) {
            // Fallback to default values if cache is not available
            $this->migrationStatus = [
                'user_management' => ['status' => 'monolith'],
                'payment_processing' => ['status' => 'monolith'],
                'panel_management' => ['status' => 'monolith']
            ];
        }
    }

    /**
     * Save migration status to cache
     */
    private function saveMigrationStatus(): void
    {
        try {
            $cache = $this->app->getContainer()->get('cache');
            $cache->set('migration_status', $this->migrationStatus, 0);
        } catch (\Exception $e) {
            // Log error if cache is not available
            $this->logger->warning("Could not save migration status to cache", ['error' => $e->getMessage()]);
        }
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'microservice_paths' => [
                '/api/microservices/*',
                '/api/orchestrator/*'
            ],
            'fallback_to_monolith' => true,
            'sync_batch_size' => 100,
            'migration_timeout' => 300
        ];
    }
}
