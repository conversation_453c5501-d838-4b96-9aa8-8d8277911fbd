<?php

declare(strict_types=1);

namespace WeBot\Models;

use WeBot\Services\DatabaseService;
use WeBot\Utils\Helper;

/**
 * Payment Model
 *
 * Represents a payment transaction in the WeBot system
 * with gateway integration and status tracking.
 *
 * @package WeBot\Models
 * @version 2.0
 */
class Payment extends BaseModel
{
    protected string $table = 'payments';
    protected string $primaryKey = 'id';

    protected array $fillable = [
        'user_id',
        'amount',
        'gateway',
        'hash_id',
        'status',
        'gateway_transaction_id',
        'gateway_response',
        'metadata',
        'verified_at'
    ];

    protected array $casts = [
        'user_id' => 'int',
        'amount' => 'int',
        'gateway_response' => 'json',
        'metadata' => 'json',
        'verified_at' => 'datetime'
    ];

    // Payment statuses
    public const STATUS_PENDING = 'pending';
    public const STATUS_COMPLETED = 'completed';
    public const STATUS_FAILED = 'failed';
    public const STATUS_CANCELLED = 'cancelled';
    public const STATUS_REFUNDED = 'refunded';

    // Payment gateways
    public const GATEWAY_ZARINPAL = 'zarinpal';
    public const GATEWAY_NOWPAYMENTS = 'nowpayments';
    public const GATEWAY_WALLET = 'wallet';

    /**
     * Get validation rules
     */
    protected function getValidationRules(): array
    {
        return [
            'user_id' => 'required|integer|min:1',
            'amount' => 'required|integer|min:1000|max:10000000',
            'gateway' => 'required|string|in:zarinpal,nowpayments,wallet',
            'hash_id' => 'required|string|max:255',
            'status' => 'required|string|in:pending,completed,failed,cancelled,refunded'
        ];
    }

    /**
     * Check if payment is pending
     */
    public function isPending(): bool
    {
        return $this->getAttribute('status') === self::STATUS_PENDING;
    }

    /**
     * Check if payment is completed
     */
    public function isCompleted(): bool
    {
        return $this->getAttribute('status') === self::STATUS_COMPLETED;
    }

    /**
     * Check if payment is failed
     */
    public function isFailed(): bool
    {
        return $this->getAttribute('status') === self::STATUS_FAILED;
    }

    /**
     * Check if payment is cancelled
     */
    public function isCancelled(): bool
    {
        return $this->getAttribute('status') === self::STATUS_CANCELLED;
    }

    /**
     * Check if payment is refunded
     */
    public function isRefunded(): bool
    {
        return $this->getAttribute('status') === self::STATUS_REFUNDED;
    }

    /**
     * Mark payment as completed
     */
    public function markAsCompleted(string $gatewayTransactionId = null, array $gatewayResponse = []): bool
    {
        $this->setAttribute('status', self::STATUS_COMPLETED);
        $this->setAttribute('verified_at', date('Y-m-d H:i:s'));

        if ($gatewayTransactionId) {
            $this->setAttribute('gateway_transaction_id', $gatewayTransactionId);
        }

        if (!empty($gatewayResponse)) {
            $this->setAttribute('gateway_response', $gatewayResponse);
        }

        return $this->save();
    }

    /**
     * Mark payment as failed
     */
    public function markAsFailed(string $reason = '', array $gatewayResponse = []): bool
    {
        $this->setAttribute('status', self::STATUS_FAILED);

        $metadata = $this->getAttribute('metadata') ?? [];
        $metadata['failure_reason'] = $reason;
        $this->setAttribute('metadata', $metadata);

        if (!empty($gatewayResponse)) {
            $this->setAttribute('gateway_response', $gatewayResponse);
        }

        return $this->save();
    }

    /**
     * Mark payment as cancelled
     */
    public function markAsCancelled(string $reason = ''): bool
    {
        $this->setAttribute('status', self::STATUS_CANCELLED);

        $metadata = $this->getAttribute('metadata') ?? [];
        $metadata['cancellation_reason'] = $reason;
        $this->setAttribute('metadata', $metadata);

        return $this->save();
    }

    /**
     * Mark payment as refunded
     */
    public function markAsRefunded(int $refundAmount = null, string $reason = ''): bool
    {
        $this->setAttribute('status', self::STATUS_REFUNDED);

        $metadata = $this->getAttribute('metadata') ?? [];
        $metadata['refund_amount'] = $refundAmount ?? $this->getAttribute('amount');
        $metadata['refund_reason'] = $reason;
        $metadata['refunded_at'] = date('Y-m-d H:i:s');
        $this->setAttribute('metadata', $metadata);

        return $this->save();
    }

    /**
     * Get payment ID
     */
    public function getId(): int
    {
        return (int) $this->getKey();
    }

    /**
     * Get payment status
     */
    public function getStatus(): string
    {
        return $this->getAttribute('status') ?? self::STATUS_PENDING;
    }

    /**
     * Get transaction ID
     */
    public function getTransactionId(): ?string
    {
        return $this->getAttribute('gateway_transaction_id');
    }

    /**
     * Get formatted amount
     */
    public function getFormattedAmount(): string
    {
        return Helper::formatPrice($this->getAttribute('amount'));
    }

    /**
     * Get gateway title
     */
    public function getGatewayTitle(): string
    {
        return match ($this->getAttribute('gateway')) {
            self::GATEWAY_ZARINPAL => 'زرین‌پال',
            self::GATEWAY_NOWPAYMENTS => 'ارز دیجیتال',
            self::GATEWAY_WALLET => 'کیف پول',
            default => 'نامشخص'
        };
    }

    /**
     * Get status title
     */
    public function getStatusTitle(): string
    {
        return match ($this->getAttribute('status')) {
            self::STATUS_PENDING => 'در انتظار',
            self::STATUS_COMPLETED => 'موفق',
            self::STATUS_FAILED => 'ناموفق',
            self::STATUS_CANCELLED => 'لغو شده',
            self::STATUS_REFUNDED => 'بازگردانده شده',
            default => 'نامشخص'
        };
    }

    /**
     * Get status icon
     */
    public function getStatusIcon(): string
    {
        return match ($this->getAttribute('status')) {
            self::STATUS_PENDING => '⏳',
            self::STATUS_COMPLETED => '✅',
            self::STATUS_FAILED => '❌',
            self::STATUS_CANCELLED => '🚫',
            self::STATUS_REFUNDED => '🔄',
            default => '❓'
        };
    }

    /**
     * Get user
     */
    public function getUser(): ?User
    {
        $userId = $this->getAttribute('user_id');

        if (!$userId) {
            return null;
        }

        return User::find($this->database, $userId);
    }

    /**
     * Get payment URL
     */
    public function getPaymentUrl(): ?string
    {
        if (!$this->isPending()) {
            return null;
        }

        $gateway = $this->getAttribute('gateway');
        $hashId = $this->getAttribute('hash_id');

        return match ($gateway) {
            self::GATEWAY_ZARINPAL => "https://www.zarinpal.com/pg/StartPay/{$hashId}",
            self::GATEWAY_NOWPAYMENTS => "https://nowpayments.io/payment/{$hashId}",
            default => null
        };
    }

    /**
     * Check if payment is expired
     */
    public function isExpired(int $expiryMinutes = 30): bool
    {
        if (!$this->isPending()) {
            return false;
        }

        $createdAt = strtotime($this->getAttribute('created_at'));
        $expiryTime = $createdAt + ($expiryMinutes * 60);

        return time() > $expiryTime;
    }

    /**
     * Get time until expiry
     */
    public function getTimeUntilExpiry(int $expiryMinutes = 30): int
    {
        if (!$this->isPending()) {
            return 0;
        }

        $createdAt = strtotime($this->getAttribute('created_at'));
        $expiryTime = $createdAt + ($expiryMinutes * 60);

        return max(0, $expiryTime - time());
    }

    /**
     * Add metadata
     */
    public function addMetadata(string $key, $value): bool
    {
        $metadata = $this->getAttribute('metadata') ?? [];
        $metadata[$key] = $value;
        $this->setAttribute('metadata', $metadata);

        return $this->save();
    }

    /**
     * Get metadata value
     */
    public function getMetadata(string $key = null)
    {
        $metadata = $this->getAttribute('metadata') ?? [];

        if ($key === null) {
            return $metadata;
        }

        return $metadata[$key] ?? null;
    }

    /**
     * Generate unique hash ID
     */
    public static function generateHashId(): string
    {
        return Helper::randomString(32);
    }

    /**
     * Find payment by hash ID
     */
    public static function findByHashId(DatabaseService $database, string $hashId): ?Payment
    {
        $sql = "SELECT * FROM `payments` WHERE `hash_id` = ?";
        $attributes = $database->fetchRow($sql, [$hashId], 's');

        if ($attributes) {
            return new self($database, $attributes);
        }

        return null;
    }

    /**
     * Find payments by user
     */
    public static function findByUser(DatabaseService $database, int $userId, int $limit = 20): array
    {
        $sql = "SELECT * FROM `payments` WHERE `user_id` = ? ORDER BY `created_at` DESC LIMIT ?";
        $results = $database->fetchAll($sql, [$userId, $limit], 'ii');

        $payments = [];
        foreach ($results as $attributes) {
            $payments[] = new self($database, $attributes);
        }

        return $payments;
    }

    /**
     * Find payments by status
     */
    public static function findByStatus(DatabaseService $database, string $status, int $limit = 100): array
    {
        $sql = "SELECT * FROM `payments` WHERE `status` = ? ORDER BY `created_at` DESC LIMIT ?";
        $results = $database->fetchAll($sql, [$status, $limit], 'si');

        $payments = [];
        foreach ($results as $attributes) {
            $payments[] = new self($database, $attributes);
        }

        return $payments;
    }

    /**
     * Find payments by gateway
     */
    public static function findByGateway(DatabaseService $database, string $gateway, int $limit = 100): array
    {
        $sql = "SELECT * FROM `payments` WHERE `gateway` = ? ORDER BY `created_at` DESC LIMIT ?";
        $results = $database->fetchAll($sql, [$gateway, $limit], 'si');

        $payments = [];
        foreach ($results as $attributes) {
            $payments[] = new self($database, $attributes);
        }

        return $payments;
    }

    /**
     * Get pending payments
     */
    public static function getPendingPayments(DatabaseService $database): array
    {
        return self::findByStatus($database, self::STATUS_PENDING);
    }

    /**
     * Get expired payments
     */
    public static function getExpiredPayments(DatabaseService $database, int $expiryMinutes = 30): array
    {
        $expiryTime = date('Y-m-d H:i:s', time() - ($expiryMinutes * 60));

        $sql = "SELECT * FROM `payments` WHERE `status` = ? AND `created_at` < ?";
        $results = $database->fetchAll($sql, [self::STATUS_PENDING, $expiryTime], 'ss');

        $payments = [];
        foreach ($results as $attributes) {
            $payments[] = new self($database, $attributes);
        }

        return $payments;
    }

    /**
     * Get payment statistics
     */
    public static function getStatistics(DatabaseService $database, int $userId = null): array
    {
        $whereClause = $userId ? "WHERE user_id = ?" : "";
        $params = $userId ? [$userId] : [];
        $types = $userId ? 'i' : '';

        $stats = [
            'total_payments' => 0,
            'completed_payments' => 0,
            'failed_payments' => 0,
            'pending_payments' => 0,
            'total_amount' => 0,
            'completed_amount' => 0
        ];

        // Total payments
        $sql = "SELECT COUNT(*) FROM `payments` {$whereClause}";
        $stats['total_payments'] = (int) $database->fetchValue($sql, $params, $types);

        // Completed payments
        $completedParams = $params;
        $completedTypes = $types;
        if ($userId) {
            $completedParams[] = $userId;
            $completedTypes .= 'i';
        }

        $sql = "SELECT COUNT(*), COALESCE(SUM(amount), 0) FROM `payments` {$whereClause} AND status = 'completed'";
        $result = $database->fetchRow($sql, $completedParams, $completedTypes);
        $stats['completed_payments'] = (int) $result['COUNT(*)'];
        $stats['completed_amount'] = (int) $result['COALESCE(SUM(amount), 0)'];

        // Failed payments
        $sql = "SELECT COUNT(*) FROM `payments` {$whereClause} AND status = 'failed'";
        $stats['failed_payments'] = (int) $database->fetchValue($sql, $params, $types);

        // Pending payments
        $sql = "SELECT COUNT(*) FROM `payments` {$whereClause} AND status = 'pending'";
        $stats['pending_payments'] = (int) $database->fetchValue($sql, $params, $types);

        // Total amount
        $sql = "SELECT COALESCE(SUM(amount), 0) FROM `payments` {$whereClause}";
        $stats['total_amount'] = (int) $database->fetchValue($sql, $params, $types);

        return $stats;
    }

    /**
     * Get daily statistics
     */
    public static function getDailyStatistics(DatabaseService $database, string $date = null): array
    {
        $date = $date ?? date('Y-m-d');

        $sql = "SELECT 
                    COUNT(*) as total_payments,
                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_payments,
                    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_payments,
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_payments,
                    COALESCE(SUM(amount), 0) as total_amount,
                    COALESCE(SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END), 0) as completed_amount
                FROM `payments` 
                WHERE DATE(created_at) = ?";

        return $database->fetchRow($sql, [$date], 's') ?? [];
    }
}
