<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    
    <!-- SEO Meta Tags -->
    <title><?= $title ?? 'WeBot - ربات تلگرام VPN حرفه‌ای' ?></title>
    <meta name="description" content="<?= $description ?? 'WeBot - ربات تلگرام VPN حرفه‌ای با پشتیبانی از پنل‌های مختلف و امکانات پیشرفته' ?>">
    <meta name="keywords" content="تلگرام, ربات, VPN, وی‌پی‌ان, مرزبان, مرزنشین, X-UI">
    <meta name="author" content="WeBot Team">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?= $title ?? 'WeBot - ربات تلگرام VPN حرفه‌ای' ?>">
    <meta property="og:description" content="<?= $description ?? 'ربات تلگرام VPN حرفه‌ای با پشتیبانی کامل از زبان فارسی' ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?= $canonical_url ?? '' ?>">
    <meta property="og:image" content="<?= $og_image ?? '/assets/webot-logo.svg' ?>">
    <meta property="og:locale" content="fa_IR">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?= $title ?? 'WeBot - ربات تلگرام VPN حرفه‌ای' ?>">
    <meta name="twitter:description" content="<?= $description ?? 'ربات تلگرام VPN حرفه‌ای' ?>">
    <meta name="twitter:image" content="<?= $twitter_image ?? '/assets/webot-logo.svg' ?>">
    
    <!-- Canonical URL -->
    <?php if (isset($canonical_url)): ?>
    <link rel="canonical" href="<?= $canonical_url ?>">
    <?php endif; ?>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/assets/webot-logo.svg">
    <link rel="icon" type="image/png" href="/assets/logo.png">
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="/assets/IRANSans.ttf" as="font" type="font/ttf" crossorigin>
    
    <!-- CSS Styles -->
    <link rel="stylesheet" href="/assets/style.css">
    <link rel="stylesheet" href="/assets/webconf.css">
    
    <!-- TailwindCSS with RTL Support -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            content: ['./resources/**/*.{html,js,php}', './public/**/*.{html,js}'],
            theme: {
                extend: {
                    fontFamily: {
                        'vazir': ['Vazirmatn', 'IRANSans', 'Tahoma', 'Arial', 'sans-serif'],
                        'iran': ['IRANSans', 'Vazirmatn', 'Tahoma', 'Arial', 'sans-serif']
                    },
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a'
                        },
                        success: {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d'
                        },
                        warning: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f'
                        },
                        danger: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            200: '#fecaca',
                            300: '#fca5a5',
                            400: '#f87171',
                            500: '#ef4444',
                            600: '#dc2626',
                            700: '#b91c1c',
                            800: '#991b1b',
                            900: '#7f1d1d'
                        }
                    },
                    spacing: {
                        '18': '4.5rem',
                        '88': '22rem',
                        '128': '32rem'
                    }
                }
            },
            plugins: [],
            corePlugins: {
                // Enable RTL support
                textAlign: true,
                float: true,
                clear: true,
                inset: true,
                margin: true,
                padding: true
            }
        }
    </script>
    
    <!-- Custom RTL Styles -->
    <style>
        /* Persian/Farsi Font Loading */
        @font-face {
            font-family: 'Vazirmatn';
            src: url('/assets/fonts/Vazirmatn-Regular.woff2') format('woff2'),
                 url('/assets/fonts/Vazirmatn-Regular.woff') format('woff');
            font-weight: 400;
            font-style: normal;
            font-display: swap;
        }
        
        @font-face {
            font-family: 'IRANSans';
            src: url('/assets/IRANSans.ttf') format('truetype');
            font-weight: 400;
            font-style: normal;
            font-display: swap;
        }
        
        /* RTL Base Styles */
        body {
            font-family: 'Vazirmatn', 'IRANSans', 'Tahoma', 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
        }
        
        /* RTL Utilities */
        .rtl-flip {
            transform: scaleX(-1);
        }
        
        .ltr {
            direction: ltr;
            text-align: left;
        }
        
        /* Custom RTL Components */
        .btn-rtl {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-weight: 500;
            transition: colors 200ms;
        }

        .btn-primary {
            background-color: #2563eb;
            color: white;
            border: none;
            cursor: pointer;
        }

        .btn-primary:hover {
            background-color: #1d4ed8;
        }

        .btn-primary:focus {
            outline: none;
            box-shadow: 0 0 0 2px #3b82f6, 0 0 0 4px rgba(59, 130, 246, 0.5);
        }

        .btn-secondary {
            background-color: #e5e7eb;
            color: #111827;
            border: none;
            cursor: pointer;
        }

        .btn-secondary:hover {
            background-color: #d1d5db;
        }

        .btn-secondary:focus {
            outline: none;
            box-shadow: 0 0 0 2px #6b7280, 0 0 0 4px rgba(107, 114, 128, 0.5);
        }

        .card-rtl {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            border: 1px solid #e5e7eb;
            padding: 1.5rem;
        }

        .input-rtl {
            width: 100%;
            padding: 0.5rem 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }

        .input-rtl:focus {
            outline: none;
            box-shadow: 0 0 0 2px #3b82f6;
            border-color: #3b82f6;
        }
        
        /* Persian Number Styles */
        .persian-numbers {
            font-feature-settings: 'ss01' on;
        }
        
        /* Loading Animation */
        .loading-spinner {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid currentColor;
            border-top-color: transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        /* Dark Mode Support */
        @media (prefers-color-scheme: dark) {
            .card-rtl {
                background-color: #1f2937;
                border-color: #374151;
                color: white;
            }

            .input-rtl {
                background-color: #374151;
                border-color: #4b5563;
                color: white;
            }
        }
    </style>
    
    <!-- Additional Head Content -->
    <?= $head_content ?? '' ?>
</head>
<body class="bg-gray-50 text-gray-900 antialiased">
    <!-- Skip to Content Link for Accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:right-4 bg-primary-600 text-white px-4 py-2 rounded-md">
        پرش به محتوای اصلی
    </a>
    
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200" role="banner">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <img src="/assets/webot-logo.svg" alt="WeBot Logo" class="h-8 w-8 ml-2">
                    <h1 class="text-xl font-bold text-gray-900">WeBot</h1>
                </div>
                
                <!-- Navigation -->
                <nav class="hidden md:flex space-x-reverse space-x-8" role="navigation" aria-label="اصلی">
                    <a href="/" class="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium">
                        خانه
                    </a>
                    <a href="/docs" class="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium">
                        مستندات
                    </a>
                    <a href="/support" class="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium">
                        پشتیبانی
                    </a>
                </nav>
                
                <!-- Mobile Menu Button -->
                <button type="button" class="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500" aria-expanded="false" aria-controls="mobile-menu">
                    <span class="sr-only">باز کردن منوی اصلی</span>
                    <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <div class="md:hidden hidden" id="mobile-menu">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-50">
                <a href="/" class="text-gray-700 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium">خانه</a>
                <a href="/docs" class="text-gray-700 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium">مستندات</a>
                <a href="/support" class="text-gray-700 hover:text-primary-600 block px-3 py-2 rounded-md text-base font-medium">پشتیبانی</a>
            </div>
        </div>
    </header>
    
    <!-- Main Content -->
    <main id="main-content" class="flex-1" role="main">
        <?= $content ?? '' ?>
    </main>
    
    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-auto" role="contentinfo">
        <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">WeBot</h3>
                    <p class="text-gray-300 text-sm">
                        ربات تلگرام VPN حرفه‌ای با پشتیبانی کامل از زبان فارسی و امکانات پیشرفته
                    </p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">لینک‌های مفید</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="/docs" class="text-gray-300 hover:text-white">مستندات</a></li>
                        <li><a href="/api" class="text-gray-300 hover:text-white">API</a></li>
                        <li><a href="/support" class="text-gray-300 hover:text-white">پشتیبانی</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">تماس با ما</h3>
                    <ul class="space-y-2 text-sm">
                        <li class="text-gray-300">تلگرام: @WeBot_Support</li>
                        <li class="text-gray-300">ایمیل: <EMAIL></li>
                    </ul>
                </div>
            </div>
            <div class="mt-8 pt-8 border-t border-gray-700 text-center text-sm text-gray-300">
                <p>&copy; <?= date('Y') ?> WeBot. تمامی حقوق محفوظ است.</p>
            </div>
        </div>
    </footer>
    
    <!-- JavaScript -->
    <script>
        // Mobile menu toggle
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuButton = document.querySelector('[aria-controls="mobile-menu"]');
            const mobileMenu = document.getElementById('mobile-menu');
            
            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', function() {
                    const isExpanded = mobileMenuButton.getAttribute('aria-expanded') === 'true';
                    mobileMenuButton.setAttribute('aria-expanded', !isExpanded);
                    mobileMenu.classList.toggle('hidden');
                });
            }
        });
        
        // RTL-aware animations and interactions
        function initRTLSupport() {
            // Add RTL-specific JavaScript functionality here
            console.log('RTL support initialized');
        }
        
        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', initRTLSupport);
    </script>
    
    <!-- Additional Scripts -->
    <?= $scripts ?? '' ?>
</body>
</html>
