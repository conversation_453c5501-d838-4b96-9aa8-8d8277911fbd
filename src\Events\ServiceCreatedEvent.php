<?php

declare(strict_types=1);

namespace WeBot\Events;

class ServiceCreatedEvent extends BaseEvent
{
    public function __construct(int $serviceId, int $userId, array $serviceData)
    {
        parent::__construct([
            'service_id' => $serviceId,
            'user_id' => $userId,
            'service_data' => $serviceData,
            'server_id' => $serviceData['server_id'] ?? null,
            'plan_id' => $serviceData['plan_id'] ?? null,
            'volume' => $serviceData['volume'] ?? null,
            'expires_at' => $serviceData['expires_at'] ?? null
        ]);
    }

    public function getServiceId(): int
    {
        return $this->get('service_id');
    }

    public function getUserId(): int
    {
        return $this->get('user_id');
    }

    public function getServiceData(): array
    {
        return $this->get('service_data', []);
    }

    public function getServerId(): ?int
    {
        return $this->get('server_id');
    }

    public function getPlanId(): ?int
    {
        return $this->get('plan_id');
    }

    public function getVolume(): ?int
    {
        return $this->get('volume');
    }

    public function getExpiresAt(): ?string
    {
        return $this->get('expires_at');
    }
} 