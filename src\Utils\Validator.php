<?php

declare(strict_types=1);

namespace WeBot\Utils;

use WeBot\Exceptions\ValidationException;

/**
 * WeBot Validator
 *
 * Simple validation class for input validation.
 *
 * @package WeBot\Utils
 * @version 2.0
 */
class Validator
{
    private array $data = [];
    private array $rules = [];
    private array $errors = [];
    private array $messages = [];

    public function __construct(array $data = [])
    {
        $this->data = $data;
        $this->setupDefaultMessages();
    }

    /**
     * Set validation rules
     */
    public function rules(array $rules): self
    {
        $this->rules = $rules;
        return $this;
    }

    /**
     * Set custom error messages
     */
    public function messages(array $messages): self
    {
        $this->messages = array_merge($this->messages, $messages);
        return $this;
    }

    /**
     * Validate data against rules
     */
    public function validate(): bool
    {
        $this->errors = [];

        foreach ($this->rules as $field => $fieldRules) {
            $this->validateField($field, $fieldRules);
        }

        return empty($this->errors);
    }

    /**
     * Get validation errors
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Get first error message
     */
    public function getFirstError(): string
    {
        foreach ($this->errors as $fieldErrors) {
            return is_array($fieldErrors) ? $fieldErrors[0] : $fieldErrors;
        }

        return '';
    }

    /**
     * Throw validation exception if validation fails
     */
    public function validateOrFail(): void
    {
        if (!$this->validate()) {
            throw new ValidationException('Validation failed', $this->errors);
        }
    }

    /**
     * Validate a single field
     */
    private function validateField(string $field, $rules): void
    {
        if (is_string($rules)) {
            $rules = explode('|', $rules);
        }

        $value = $this->data[$field] ?? null;

        foreach ($rules as $rule) {
            $this->applyRule($field, $value, $rule);
        }
    }

    /**
     * Apply a single validation rule
     */
    private function applyRule(string $field, $value, string $rule): void
    {
        // Parse rule and parameters
        $ruleParts = explode(':', $rule);
        $ruleName = $ruleParts[0];
        $parameters = isset($ruleParts[1]) ? explode(',', $ruleParts[1]) : [];

        // Apply the rule
        $isValid = match ($ruleName) {
            'required' => $this->validateRequired($value),
            'string' => $this->validateString($value),
            'integer' => $this->validateInteger($value),
            'numeric' => $this->validateNumeric($value),
            'email' => $this->validateEmail($value),
            'url' => $this->validateUrl($value),
            'min' => $this->validateMin($value, $parameters[0] ?? 0),
            'max' => $this->validateMax($value, $parameters[0] ?? 0),
            'between' => $this->validateBetween($value, $parameters[0] ?? 0, $parameters[1] ?? 0),
            'in' => $this->validateIn($value, $parameters),
            'not_in' => $this->validateNotIn($value, $parameters),
            'regex' => $this->validateRegex($value, $parameters[0] ?? ''),
            'telegram_id' => $this->validateTelegramId($value),
            'phone' => $this->validatePhone($value),
            default => true
        };

        if (!$isValid) {
            $this->addError($field, $ruleName, $parameters);
        }
    }

    /**
     * Add validation error
     */
    private function addError(string $field, string $rule, array $parameters = []): void
    {
        $message = $this->getErrorMessage($field, $rule, $parameters);

        if (!isset($this->errors[$field])) {
            $this->errors[$field] = [];
        }

        $this->errors[$field][] = $message;
    }

    /**
     * Get error message for rule
     */
    private function getErrorMessage(string $field, string $rule, array $parameters = []): string
    {
        $key = "{$field}.{$rule}";

        if (isset($this->messages[$key])) {
            return $this->messages[$key];
        }

        if (isset($this->messages[$rule])) {
            return str_replace(':field', $field, $this->messages[$rule]);
        }

        return "The {$field} field is invalid.";
    }

    /**
     * Setup default error messages
     */
    private function setupDefaultMessages(): void
    {
        $this->messages = [
            'required' => 'فیلد :field الزامی است.',
            'string' => 'فیلد :field باید رشته باشد.',
            'integer' => 'فیلد :field باید عدد صحیح باشد.',
            'numeric' => 'فیلد :field باید عدد باشد.',
            'email' => 'فیلد :field باید ایمیل معتبر باشد.',
            'url' => 'فیلد :field باید URL معتبر باشد.',
            'min' => 'فیلد :field باید حداقل :min کاراکتر باشد.',
            'max' => 'فیلد :field باید حداکثر :max کاراکتر باشد.',
            'between' => 'فیلد :field باید بین :min و :max باشد.',
            'in' => 'فیلد :field انتخاب شده معتبر نیست.',
            'not_in' => 'فیلد :field انتخاب شده غیرمجاز است.',
            'regex' => 'فرمت فیلد :field صحیح نیست.',
            'telegram_id' => 'فیلد :field باید شناسه تلگرام معتبر باشد.',
            'phone' => 'فیلد :field باید شماره تلفن معتبر باشد.',
        ];
    }

    // Validation methods
    private function validateRequired($value): bool
    {
        return $value !== null && $value !== '' && $value !== [];
    }

    private function validateString($value): bool
    {
        return is_string($value);
    }

    private function validateInteger($value): bool
    {
        return filter_var($value, FILTER_VALIDATE_INT) !== false;
    }

    private function validateNumeric($value): bool
    {
        return is_numeric($value);
    }

    private function validateEmail($value): bool
    {
        return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
    }

    private function validateUrl($value): bool
    {
        return filter_var($value, FILTER_VALIDATE_URL) !== false;
    }

    private function validateMin($value, $min): bool
    {
        if (is_string($value)) {
            return mb_strlen($value) >= $min;
        }

        if (is_numeric($value)) {
            return $value >= $min;
        }

        return false;
    }

    private function validateMax($value, $max): bool
    {
        if (is_string($value)) {
            return mb_strlen($value) <= $max;
        }

        if (is_numeric($value)) {
            return $value <= $max;
        }

        return false;
    }

    private function validateBetween($value, $min, $max): bool
    {
        return $this->validateMin($value, $min) && $this->validateMax($value, $max);
    }

    private function validateIn($value, array $options): bool
    {
        return in_array($value, $options);
    }

    private function validateNotIn($value, array $options): bool
    {
        return !in_array($value, $options);
    }

    private function validateRegex($value, string $pattern): bool
    {
        return preg_match($pattern, $value) === 1;
    }

    private function validateTelegramId($value): bool
    {
        return Helper::isValidTelegramId($value);
    }

    private function validatePhone($value): bool
    {
        // Iranian phone number validation
        $pattern = '/^(\+98|0)?9\d{9}$/';
        return preg_match($pattern, $value) === 1;
    }

    /**
     * Static validation helper
     */
    public static function make(array $data, array $rules, array $messages = []): self
    {
        return (new self($data))
            ->rules($rules)
            ->messages($messages);
    }
}
