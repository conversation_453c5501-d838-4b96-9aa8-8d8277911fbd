<?php

declare(strict_types=1);

namespace WeBot\Microservices\Services;

use WeBot\Services\DatabaseService;
use WeBot\Core\CacheManager;
use WeBot\Utils\Logger;
use WeBot\Exceptions\WeBotException;
use WeBot\Models\Panel;
use WeBot\Models\Service;
use WeBot\Repositories\PanelRepository;
use WeBot\Repositories\ServiceRepository;

/**
 * Panel Management Microservice
 *
 * Handles all panel-related operations including panel management,
 * service creation, monitoring, and integration with VPN panels.
 *
 * @package WeBot\Microservices\Services
 * @version 2.0
 */
class PanelService
{
    private DatabaseService $database;
    private CacheManager $cache;
    private Logger $logger;
    private PanelRepository $panelRepository;
    private ServiceRepository $serviceRepository;
    private array $config;
    private array $adapters = [];

    public function __construct(
        DatabaseService $database,
        CacheManager $cache,
        PanelRepository $panelRepository,
        ServiceRepository $serviceRepository,
        array $config = []
    ) {
        $this->database = $database;
        $this->cache = $cache;
        $this->logger = Logger::getInstance();
        $this->panelRepository = $panelRepository;
        $this->serviceRepository = $serviceRepository;
        $this->config = array_merge($this->getDefaultConfig(), $config);

        $this->initializeAdapters();
    }

    /**
     * Handle service requests
     */
    public function handleRequest(array $request): array
    {
        $action = $request['action'] ?? '';
        $data = $request['data'] ?? [];

        try {
            return match ($action) {
                'create_panel' => $this->createPanel($data),
                'update_panel' => $this->updatePanel($data),
                'delete_panel' => $this->deletePanel($data),
                'get_panel' => $this->getPanel($data),
                'get_panels' => $this->getPanels($data),
                'test_panel_connection' => $this->testPanelConnection($data),
                'sync_panel' => $this->syncPanel($data),
                'create_service' => $this->createService($data),
                'update_service' => $this->updateService($data),
                'delete_service' => $this->deleteService($data),
                'get_service' => $this->getService($data),
                'get_user_services' => $this->getUserServices($data),
                'renew_service' => $this->renewService($data),
                'suspend_service' => $this->suspendService($data),
                'activate_service' => $this->activateService($data),
                'get_service_config' => $this->getServiceConfig($data),
                'get_service_usage' => $this->getServiceUsage($data),
                'get_panel_stats' => $this->getPanelStats($data),
                'health' => $this->healthCheck(),
                default => throw new WeBotException("Unknown action: {$action}")
            };
        } catch (\Exception $e) {
            $this->logger->error("Panel service error", [
                'action' => $action,
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            throw $e;
        }
    }

    /**
     * Create new panel
     */
    public function createPanel(array $data): array
    {
        $requiredFields = ['name', 'type', 'url', 'username', 'password'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                throw new WeBotException("Field {$field} is required");
            }
        }

        // Validate panel type
        if (!in_array($data['type'], ['marzban', 'marzneshin', 'xui'])) {
            throw new WeBotException("Invalid panel type");
        }

        // Test connection before creating
        $connectionTest = $this->testConnection($data);
        if (!$connectionTest['success']) {
            throw new WeBotException("Panel connection test failed: " . $connectionTest['error']);
        }

        $panelData = [
            'name' => $data['name'],
            'type' => $data['type'],
            'url' => rtrim($data['url'], '/'),
            'username' => $data['username'],
            'password' => $data['password'],
            'token' => null,
            'refresh_token' => null,
            'token_expires_at' => null,
            'status' => 'active',
            'version' => $connectionTest['version'] ?? 'unknown',
            'last_sync_at' => null,
            'last_health_check' => date('Y-m-d H:i:s'),
            'health_status' => 'healthy',
            'config_data' => json_encode($data['config_data'] ?? []),
            'statistics' => json_encode([]),
            'is_active' => true,
            'priority' => $data['priority'] ?? 1,
            'max_users' => $data['max_users'] ?? 1000,
            'current_users' => 0
        ];

        $panel = $this->panelRepository->create($panelData);

        $this->logger->info("Panel created", [
            'panel_id' => $panel->id,
            'name' => $data['name'],
            'type' => $data['type']
        ]);

        return [
            'success' => true,
            'panel' => $panel,
            'message' => 'Panel created successfully'
        ];
    }

    /**
     * Update panel
     */
    public function updatePanel(array $data): array
    {
        $panelId = $data['panel_id'] ?? null;
        $updateData = $data['update_data'] ?? [];

        if (!$panelId) {
            throw new WeBotException("Panel ID is required");
        }

        if (empty($updateData)) {
            throw new WeBotException("Update data is required");
        }

        $panel = $this->panelRepository->findById($panelId);
        if (!$panel) {
            throw new WeBotException("Panel not found", 404);
        }

        // If connection details changed, test connection
        if (isset($updateData['url']) || isset($updateData['username']) || isset($updateData['password'])) {
            $testData = array_merge((array)$panel, $updateData);
            $connectionTest = $this->testConnection($testData);
            if (!$connectionTest['success']) {
                throw new WeBotException("Panel connection test failed: " . $connectionTest['error']);
            }
        }

        $updated = $this->panelRepository->update($panelId, $updateData);

        if (!$updated) {
            throw new WeBotException("Failed to update panel");
        }

        // Clear cache
        $this->clearPanelCache($panelId);

        $updatedPanel = $this->panelRepository->findById($panelId);

        $this->logger->info("Panel updated", [
            'panel_id' => $panelId,
            'updated_fields' => array_keys($updateData)
        ]);

        return [
            'success' => true,
            'panel' => $updatedPanel,
            'message' => 'Panel updated successfully'
        ];
    }

    /**
     * Delete panel
     */
    public function deletePanel(array $data): array
    {
        $panelId = $data['panel_id'] ?? null;

        if (!$panelId) {
            throw new WeBotException("Panel ID is required");
        }

        $panel = $this->panelRepository->findById($panelId);
        if (!$panel) {
            throw new WeBotException("Panel not found", 404);
        }

        // Check if panel has active services
        $activeServices = $this->serviceRepository->countByPanelId($panelId, 'active');
        if ($activeServices > 0) {
            throw new WeBotException("Cannot delete panel with active services");
        }

        $deleted = $this->panelRepository->delete($panelId);

        if (!$deleted) {
            throw new WeBotException("Failed to delete panel");
        }

        // Clear cache
        $this->clearPanelCache($panelId);

        $this->logger->info("Panel deleted", [
            'panel_id' => $panelId,
            'name' => $panel->name
        ]);

        return [
            'success' => true,
            'message' => 'Panel deleted successfully'
        ];
    }

    /**
     * Get panel by ID
     */
    public function getPanel(array $data): array
    {
        $panelId = $data['panel_id'] ?? null;

        if (!$panelId) {
            throw new WeBotException("Panel ID is required");
        }

        $cacheKey = "panel_data:{$panelId}";
        $cached = $this->cache->get($cacheKey);

        if ($cached !== null) {
            return [
                'success' => true,
                'panel' => $cached,
                'from_cache' => true
            ];
        }

        $panel = $this->panelRepository->findById($panelId);

        if (!$panel) {
            throw new WeBotException("Panel not found", 404);
        }

        // Cache panel data
        $this->cache->set($cacheKey, $panel, $this->config['panel_cache_ttl']);

        return [
            'success' => true,
            'panel' => $panel,
            'from_cache' => false
        ];
    }

    /**
     * Get all panels
     */
    public function getPanels(array $data): array
    {
        $status = $data['status'] ?? null;
        $type = $data['type'] ?? null;
        $limit = $data['limit'] ?? 50;
        $offset = $data['offset'] ?? 0;

        $panels = $this->panelRepository->findAll($status, $type, $limit, $offset);

        return [
            'success' => true,
            'panels' => $panels,
            'filters' => [
                'status' => $status,
                'type' => $type,
                'limit' => $limit,
                'offset' => $offset
            ]
        ];
    }

    /**
     * Test panel connection
     */
    public function testPanelConnection(array $data): array
    {
        $panelId = $data['panel_id'] ?? null;

        if (!$panelId) {
            throw new WeBotException("Panel ID is required");
        }

        $panel = $this->panelRepository->findById($panelId);
        if (!$panel) {
            throw new WeBotException("Panel not found", 404);
        }

        $result = $this->testConnection((array)$panel);

        // Update panel health status
        $this->panelRepository->update($panelId, [
            'health_status' => $result['success'] ? 'healthy' : 'unhealthy',
            'last_health_check' => date('Y-m-d H:i:s')
        ]);

        return [
            'success' => $result['success'],
            'panel_id' => $panelId,
            'status' => $result['success'] ? 'healthy' : 'unhealthy',
            'response_time' => $result['response_time'] ?? null,
            'version' => $result['version'] ?? null,
            'error' => $result['error'] ?? null,
            'message' => $result['success'] ? 'Connection successful' : 'Connection failed'
        ];
    }

    /**
     * Sync panel data
     */
    public function syncPanel(array $data): array
    {
        $panelId = $data['panel_id'] ?? null;

        if (!$panelId) {
            throw new WeBotException("Panel ID is required");
        }

        $panel = $this->panelRepository->findById($panelId);
        if (!$panel) {
            throw new WeBotException("Panel not found", 404);
        }

        $adapter = $this->getAdapter($panel->type);

        try {
            // Sync users and services
            $syncResult = $adapter->syncPanel($panel);

            // Update panel statistics
            $this->panelRepository->update($panelId, [
                'statistics' => json_encode($syncResult['statistics']),
                'current_users' => $syncResult['user_count'],
                'last_sync_at' => date('Y-m-d H:i:s')
            ]);

            $this->logger->info("Panel synced", [
                'panel_id' => $panelId,
                'users_synced' => $syncResult['user_count'],
                'services_synced' => $syncResult['service_count']
            ]);

            return [
                'success' => true,
                'panel_id' => $panelId,
                'sync_result' => $syncResult,
                'message' => 'Panel synced successfully'
            ];
        } catch (\Exception $e) {
            $this->logger->error("Panel sync failed", [
                'panel_id' => $panelId,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Create new service
     */
    public function createService(array $data): array
    {
        $requiredFields = ['user_id', 'panel_id', 'plan_id', 'remark'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                throw new WeBotException("Field {$field} is required");
            }
        }

        $panel = $this->panelRepository->findById($data['panel_id']);
        if (!$panel) {
            throw new WeBotException("Panel not found", 404);
        }

        $adapter = $this->getAdapter($panel->type);

        try {
            // Create service on panel
            $panelResult = $adapter->createUser($panel, $data);

            // Create service record
            $serviceData = [
                'user_id' => $data['user_id'],
                'panel_id' => $data['panel_id'],
                'plan_id' => $data['plan_id'],
                'remark' => $data['remark'],
                'uuid' => $panelResult['uuid'],
                'volume' => $data['volume'] ?? 0,
                'used_volume' => 0,
                'days' => $data['days'] ?? 30,
                'status' => 'active',
                'expires_at' => date('Y-m-d H:i:s', strtotime('+' . ($data['days'] ?? 30) . ' days')),
                'panel_user_id' => $panelResult['panel_user_id'],
                'config_data' => json_encode($panelResult['config']),
                'last_sync_at' => date('Y-m-d H:i:s')
            ];

            $service = $this->serviceRepository->create($serviceData);

            $this->logger->info("Service created", [
                'service_id' => $service->id,
                'user_id' => $data['user_id'],
                'panel_id' => $data['panel_id'],
                'uuid' => $panelResult['uuid']
            ]);

            return [
                'success' => true,
                'service' => $service,
                'config' => $panelResult['config'],
                'message' => 'Service created successfully'
            ];
        } catch (\Exception $e) {
            $this->logger->error("Service creation failed", [
                'user_id' => $data['user_id'],
                'panel_id' => $data['panel_id'],
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Update an existing service
     */
    public function updateService(array $data): array
    {
        throw new WeBotException('Not implemented yet');
    }

    /**
     * Delete a service
     */
    public function deleteService(array $data): array
    {
        throw new WeBotException('Not implemented yet');
    }

    /**
     * Renew a service
     */
    public function renewService(array $data): array
    {
        throw new WeBotException('Not implemented yet');
    }

    /**
     * Suspend a service
     */
    public function suspendService(array $data): array
    {
        throw new WeBotException('Not implemented yet');
    }

    /**
     * Activate a service
     */
    public function activateService(array $data): array
    {
        throw new WeBotException('Not implemented yet');
    }

    /**
     * Get service by ID
     */
    public function getService(array $data): array
    {
        $serviceId = $data['service_id'] ?? null;

        if (!$serviceId) {
            throw new WeBotException("Service ID is required");
        }

        $service = $this->serviceRepository->findById($serviceId);

        if (!$service) {
            throw new WeBotException("Service not found", 404);
        }

        return [
            'success' => true,
            'service' => $service
        ];
    }

    /**
     * Get user services
     */
    public function getUserServices(array $data): array
    {
        $userId = $data['user_id'] ?? null;
        $status = $data['status'] ?? null;
        $limit = $data['limit'] ?? 50;
        $offset = $data['offset'] ?? 0;

        if (!$userId) {
            throw new WeBotException("User ID is required");
        }

        $services = $this->serviceRepository->findByUserIdWithFilters($userId, $status, $limit, $offset);

        return [
            'success' => true,
            'services' => $services,
            'user_id' => $userId
        ];
    }

    /**
     * Get service configuration
     */
    public function getServiceConfig(array $data): array
    {
        $serviceId = $data['service_id'] ?? null;

        if (!$serviceId) {
            throw new WeBotException("Service ID is required");
        }

        $service = $this->serviceRepository->findById($serviceId);
        if (!$service) {
            throw new WeBotException("Service not found", 404);
        }

        $panel = $this->panelRepository->findById($service->panel_id);
        if (!$panel) {
            throw new WeBotException("Panel not found", 404);
        }

        $adapter = $this->getAdapter($panel->type);

        try {
            $config = $adapter->getUserConfig($panel, $service);

            return [
                'success' => true,
                'service_id' => $serviceId,
                'config' => $config,
                'qr_code' => $this->generateQRCode($config['subscription_url'] ?? ''),
                'message' => 'Configuration retrieved successfully'
            ];
        } catch (\Exception $e) {
            throw new WeBotException("Failed to get service configuration: " . $e->getMessage());
        }
    }

    /**
     * Get service usage statistics
     */
    public function getServiceUsage(array $data): array
    {
        $serviceId = $data['service_id'] ?? null;

        if (!$serviceId) {
            throw new WeBotException("Service ID is required");
        }

        $service = $this->serviceRepository->findById($serviceId);
        if (!$service) {
            throw new WeBotException("Service not found", 404);
        }

        $panel = $this->panelRepository->findById($service->panel_id);
        if (!$panel) {
            throw new WeBotException("Panel not found", 404);
        }

        $adapter = $this->getAdapter($panel->type);

        try {
            $usage = $adapter->getUserUsage($panel, $service);

            // Update service usage
            $this->serviceRepository->update($serviceId, [
                'used_volume' => $usage['used_volume'],
                'last_sync_at' => date('Y-m-d H:i:s')
            ]);

            return [
                'success' => true,
                'service_id' => $serviceId,
                'usage' => $usage,
                'message' => 'Usage statistics retrieved successfully'
            ];
        } catch (\Exception $e) {
            throw new WeBotException("Failed to get service usage: " . $e->getMessage());
        }
    }

    /**
     * Get panel statistics
     */
    public function getPanelStats(array $data): array
    {
        $panelId = $data['panel_id'] ?? null;

        if (!$panelId) {
            throw new WeBotException("Panel ID is required");
        }

        $cacheKey = "panel_stats:{$panelId}";
        $cached = $this->cache->get($cacheKey);

        if ($cached !== null) {
            return [
                'success' => true,
                'stats' => $cached,
                'from_cache' => true
            ];
        }

        $stats = $this->panelRepository->getPanelStats($panelId);

        // Cache stats
        $this->cache->set($cacheKey, $stats, $this->config['stats_cache_ttl']);

        return [
            'success' => true,
            'stats' => $stats,
            'from_cache' => false
        ];
    }

    /**
     * Health check
     */
    public function healthCheck(): array
    {
        try {
            // Test database connection
            $this->database->query("SELECT 1");

            // Test cache connection
            $this->cache->set('health_check', time(), 10);
            $this->cache->get('health_check');

            return [
                'success' => true,
                'status' => 'healthy',
                'service' => 'panel-service',
                'timestamp' => time(),
                'version' => '1.0.0'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'status' => 'unhealthy',
                'service' => 'panel-service',
                'error' => $e->getMessage(),
                'timestamp' => time()
            ];
        }
    }

    /**
     * Initialize panel adapters
     */
    private function initializeAdapters(): void
    {
        // Mock adapters - replace with actual implementations
        $this->adapters = [
            'marzban' => new class {
                public function createUser($panel, $data)
                {
                    return [
                        'uuid' => uniqid('user_'),
                        'panel_user_id' => rand(1000, 9999),
                        'config' => ['subscription_url' => 'https://example.com/sub']
                    ];
                }

                public function getUserConfig($panel, $service)
                {
                    return ['subscription_url' => 'https://example.com/sub'];
                }

                public function getUserUsage($panel, $service)
                {
                    return ['used_volume' => rand(1000000, 10000000)];
                }

                public function syncPanel($panel)
                {
                    return [
                        'user_count' => rand(10, 100),
                        'service_count' => rand(5, 50),
                        'statistics' => ['total_traffic' => rand(1000000000, 10000000000)]
                    ];
                }
            },
            'marzneshin' => new class {
                public function createUser($panel, $data)
                {
                    return [
                        'uuid' => uniqid('user_'),
                        'panel_user_id' => rand(1000, 9999),
                        'config' => ['subscription_url' => 'https://example.com/sub']
                    ];
                }

                public function getUserConfig($panel, $service)
                {
                    return ['subscription_url' => 'https://example.com/sub'];
                }

                public function getUserUsage($panel, $service)
                {
                    return ['used_volume' => rand(1000000, 10000000)];
                }

                public function syncPanel($panel)
                {
                    return [
                        'user_count' => rand(10, 100),
                        'service_count' => rand(5, 50),
                        'statistics' => ['total_traffic' => rand(1000000000, 10000000000)]
                    ];
                }
            },
            'xui' => new class {
                public function createUser($panel, $data)
                {
                    return [
                        'uuid' => uniqid('user_'),
                        'panel_user_id' => rand(1000, 9999),
                        'config' => ['subscription_url' => 'https://example.com/sub']
                    ];
                }

                public function getUserConfig($panel, $service)
                {
                    return ['subscription_url' => 'https://example.com/sub'];
                }

                public function getUserUsage($panel, $service)
                {
                    return ['used_volume' => rand(1000000, 10000000)];
                }

                public function syncPanel($panel)
                {
                    return [
                        'user_count' => rand(10, 100),
                        'service_count' => rand(5, 50),
                        'statistics' => ['total_traffic' => rand(1000000000, 10000000000)]
                    ];
                }
            }
        ];
    }

    /**
     * Get adapter for panel type
     */
    private function getAdapter(string $panelType)
    {
        if (!isset($this->adapters[$panelType])) {
            throw new WeBotException("Unsupported panel type: {$panelType}");
        }

        return $this->adapters[$panelType];
    }

    /**
     * Test panel connection
     */
    private function testConnection(array $panel): array
    {
        $startTime = microtime(true);

        try {
            // Mock connection test - replace with actual implementation
            $responseTime = (microtime(true) - $startTime) * 1000;

            return [
                'success' => true,
                'response_time' => $responseTime,
                'version' => '1.0.0'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'response_time' => (microtime(true) - $startTime) * 1000
            ];
        }
    }

    /**
     * Generate QR code for configuration
     */
    private function generateQRCode(string $data): string
    {
        // Mock QR code generation - replace with actual implementation
        return base64_encode("QR_CODE_DATA_FOR: {$data}");
    }

    /**
     * Clear panel cache
     */
    private function clearPanelCache(int $panelId): void
    {
        $keys = [
            "panel_data:{$panelId}",
            "panel_stats:{$panelId}"
        ];

        foreach ($keys as $key) {
            $this->cache->delete($key);
        }
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'panel_cache_ttl' => 3600,     // 1 hour
            'stats_cache_ttl' => 1800,     // 30 minutes
            'connection_timeout' => 10,    // 10 seconds
            'max_retry_attempts' => 3
        ];
    }
}
