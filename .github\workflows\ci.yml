name: WeBot CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  PHP_VERSION: '8.1'
  COMPOSER_CACHE_DIR: ~/.composer/cache

jobs:
  # Code Quality and Static Analysis
  code-quality:
    name: Code Quality & Static Analysis
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ env.PHP_VERSION }}
        extensions: mbstring, pdo, pdo_mysql, redis, curl, json, gd, zip
        coverage: xdebug
        tools: composer:v2

    - name: Get composer cache directory
      id: composer-cache
      run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

    - name: Cache composer dependencies
      uses: actions/cache@v3
      with:
        path: ${{ steps.composer-cache.outputs.dir }}
        key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
        restore-keys: ${{ runner.os }}-composer-

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress --no-suggest --optimize-autoloader

    - name: Check PHP syntax
      run: find src tests -name "*.php" -exec php -l {} \;

    - name: Run PHP CodeSniffer
      run: vendor/bin/phpcs --standard=phpcs.xml --report=checkstyle --report-file=reports/checkstyle.xml

    - name: Run PHPStan
      run: vendor/bin/phpstan analyse --configuration=phpstan.neon --error-format=github --no-progress

    - name: Upload CodeSniffer results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: phpcs-report
        path: reports/checkstyle.xml

  # Unit Tests
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    needs: code-quality
    
    strategy:
      matrix:
        php-version: ['8.1', '8.2', '8.3']
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup PHP ${{ matrix.php-version }}
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php-version }}
        extensions: mbstring, pdo, pdo_mysql, redis, curl, json, gd, zip
        coverage: xdebug
        tools: composer:v2

    - name: Cache composer dependencies
      uses: actions/cache@v3
      with:
        path: ~/.composer/cache
        key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
        restore-keys: ${{ runner.os }}-composer-

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress --optimize-autoloader

    - name: Create storage directories
      run: |
        mkdir -p storage/logs
        mkdir -p storage/cache
        mkdir -p storage/sessions
        mkdir -p reports

    - name: Run Unit Tests
      run: vendor/bin/phpunit --testsuite=Unit --coverage-clover=reports/coverage.xml --log-junit=reports/junit.xml

    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results-php-${{ matrix.php-version }}
        path: reports/

  # Integration Tests
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: unit-tests
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: webot_test
          MYSQL_USER: webot
          MYSQL_PASSWORD: webot
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ env.PHP_VERSION }}
        extensions: mbstring, pdo, pdo_mysql, redis, curl, json, gd, zip
        coverage: xdebug
        tools: composer:v2

    - name: Cache composer dependencies
      uses: actions/cache@v3
      with:
        path: ~/.composer/cache
        key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
        restore-keys: ${{ runner.os }}-composer-

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress --optimize-autoloader

    - name: Setup test environment
      run: |
        cp .env.testing .env
        mkdir -p storage/logs storage/cache storage/sessions reports
        
    - name: Wait for services
      run: |
        sleep 10
        mysql --host=127.0.0.1 --port=3306 -uroot -proot -e "SHOW DATABASES;"
        redis-cli -h 127.0.0.1 -p 6379 ping

    - name: Run database migrations
      run: php scripts/run-migrations.php
      env:
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: webot_test
        DB_USERNAME: webot
        DB_PASSWORD: webot

    - name: Run Integration Tests
      run: vendor/bin/phpunit --testsuite=Integration --coverage-clover=reports/integration-coverage.xml
      env:
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: webot_test
        DB_USERNAME: webot
        DB_PASSWORD: webot
        REDIS_HOST: 127.0.0.1
        REDIS_PORT: 6379

    - name: Upload integration test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: integration-test-results
        path: reports/

  # Security Audit
  security:
    name: Security Audit
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ env.PHP_VERSION }}
        tools: composer:v2

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress

    - name: Run security audit
      run: composer audit

    - name: Check for known vulnerabilities
      run: |
        if [ -f "composer.lock" ]; then
          composer audit --format=json > security-report.json || true
          cat security-report.json
        fi

    - name: Upload security report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-report
        path: security-report.json

  # Coverage Report
  coverage:
    name: Coverage Report
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download test artifacts
      uses: actions/download-artifact@v3
      with:
        path: artifacts/

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ env.PHP_VERSION }}
        extensions: mbstring, pdo, pdo_mysql, redis, curl, json, gd, zip
        coverage: xdebug
        tools: composer:v2

    - name: Install dependencies
      run: composer install --prefer-dist --no-progress

    - name: Generate combined coverage report
      run: |
        mkdir -p reports
        vendor/bin/phpunit --coverage-html=reports/coverage --coverage-clover=reports/coverage.xml

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: reports/coverage.xml
        flags: unittests
        name: codecov-umbrella

    - name: Check coverage threshold
      run: |
        COVERAGE=$(php -r "
          \$xml = simplexml_load_file('reports/coverage.xml');
          \$metrics = \$xml->project->metrics;
          \$coverage = (\$metrics['coveredstatements'] / \$metrics['statements']) * 100;
          echo round(\$coverage, 2);
        ")
        echo "Coverage: $COVERAGE%"
        if (( $(echo "$COVERAGE < 80" | bc -l) )); then
          echo "Coverage $COVERAGE% is below 80% threshold"
          exit 1
        fi
