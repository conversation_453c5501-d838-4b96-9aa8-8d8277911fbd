# 🚀 WeBot 2.0 Installation Guide

Complete installation guide for WeBot 2.0 VPN Management Bot.

## 📋 Prerequisites

### System Requirements
- **PHP**: 8.1 or higher
- **MySQL**: 8.0 or higher
- **Redis**: 6.0 or higher (optional but recommended)
- **Web Server**: Nginx/Apache
- **SSL Certificate**: Required for production

### PHP Extensions
```bash
# Required extensions
php-cli php-mysql php-curl php-json php-mbstring php-xml php-zip
php-redis php-intl php-gd php-bcmath
```

## 🐳 Docker Installation (Recommended)

### Quick Start with Docker
```bash
# Clone the repository
git clone https://github.com/your-repo/webot.git
cd webot

# Copy environment file
cp .env.example .env

# Edit configuration
nano .env

# Start services
docker-compose up -d

# Run migrations
docker-compose exec app php migrate.php migrate

# Check status
docker-compose ps
```

### Docker Configuration
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "80:80"
      - "443:443"
    environment:
      - DB_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: your_password
      MYSQL_DATABASE: webot

  redis:
    image: redis:6-alpine
```

## 💻 Manual Installation

### 1. System Preparation

#### Ubuntu/Debian
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install PHP 8.1
sudo apt install software-properties-common
sudo add-apt-repository ppa:ondrej/php
sudo apt update
sudo apt install php8.1 php8.1-cli php8.1-fpm php8.1-mysql php8.1-curl \
    php8.1-json php8.1-mbstring php8.1-xml php8.1-zip php8.1-redis \
    php8.1-intl php8.1-gd php8.1-bcmath

# Install MySQL
sudo apt install mysql-server

# Install Redis
sudo apt install redis-server

# Install Nginx
sudo apt install nginx
```

#### CentOS/RHEL
```bash
# Install EPEL and Remi repositories
sudo yum install epel-release
sudo yum install https://rpms.remirepo.net/enterprise/remi-release-8.rpm

# Install PHP 8.1
sudo yum module enable php:remi-8.1
sudo yum install php php-cli php-fpm php-mysql php-curl php-json \
    php-mbstring php-xml php-zip php-redis php-intl php-gd php-bcmath

# Install MySQL
sudo yum install mysql-server

# Install Redis
sudo yum install redis

# Install Nginx
sudo yum install nginx
```

### 2. Download and Setup

```bash
# Create directory
sudo mkdir -p /var/www/webot
cd /var/www/webot

# Download WeBot
git clone https://github.com/your-repo/webot.git .

# Set permissions
sudo chown -R www-data:www-data /var/www/webot
sudo chmod -R 755 /var/www/webot
sudo chmod -R 777 storage/ public/uploads/

# Install dependencies
composer install --no-dev --optimize-autoloader
```

### 3. Database Setup

```bash
# Create database
mysql -u root -p
```

```sql
CREATE DATABASE webot CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'webot'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON webot.* TO 'webot'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 4. Configuration

```bash
# Copy environment file
cp .env.example .env

# Edit configuration
nano .env
```

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=webot
DB_USERNAME=webot
DB_PASSWORD=your_secure_password

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_WEBHOOK_URL=https://yourdomain.com/webhook.php

# Redis Configuration (optional)
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=

# Application Configuration
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com

# Security
APP_KEY=your_32_character_secret_key
JWT_SECRET=your_jwt_secret_key

# Panel Configurations
MARZBAN_URL=https://your-marzban-panel.com
MARZBAN_USERNAME=admin
MARZBAN_PASSWORD=admin_password

# Payment Gateway (example)
ZARINPAL_MERCHANT_ID=your_merchant_id
```

### 5. Run Migrations

```bash
# Run database migrations
php migrate.php migrate

# Verify installation
php scripts/validate-env.php
```

## 🌐 Web Server Configuration

### Nginx Configuration
```nginx
server {
    listen 80;
    listen 443 ssl http2;
    server_name yourdomain.com;
    root /var/www/webot/public;
    index index.php;

    # SSL Configuration
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # PHP handling
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Webhook endpoint
    location /webhook.php {
        try_files $uri =404;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Deny access to sensitive files
    location ~ /\. {
        deny all;
    }

    location ~ /(config|storage|vendor)/ {
        deny all;
    }
}
```

### Apache Configuration
```apache
<VirtualHost *:80>
    ServerName yourdomain.com
    DocumentRoot /var/www/webot/public
    
    <Directory /var/www/webot/public>
        AllowOverride All
        Require all granted
    </Directory>
    
    # Redirect to HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</VirtualHost>

<VirtualHost *:443>
    ServerName yourdomain.com
    DocumentRoot /var/www/webot/public
    
    SSLEngine on
    SSLCertificateFile /path/to/certificate.crt
    SSLCertificateKeyFile /path/to/private.key
    
    <Directory /var/www/webot/public>
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
```

## 🤖 Telegram Bot Setup

### 1. Create Bot
```bash
# Message @BotFather on Telegram
/newbot
# Follow instructions to get bot token
```

### 2. Set Webhook
```bash
# Set webhook URL
curl -X POST "https://api.telegram.org/bot{BOT_TOKEN}/setWebhook" \
     -H "Content-Type: application/json" \
     -d '{"url": "https://yourdomain.com/webhook.php"}'

# Verify webhook
curl "https://api.telegram.org/bot{BOT_TOKEN}/getWebhookInfo"
```

## ✅ Verification

### Health Check
```bash
# Check application status
curl https://yourdomain.com/health

# Check database connection
php scripts/validate-env.php

# Run tests
php vendor/bin/phpunit
```

### Test Bot
1. Send `/start` to your bot
2. Check logs: `tail -f storage/logs/webot.log`
3. Verify database entries

## 🔧 Troubleshooting

### Common Issues

**Permission Errors**
```bash
sudo chown -R www-data:www-data /var/www/webot
sudo chmod -R 755 /var/www/webot
sudo chmod -R 777 storage/ public/uploads/
```

**Database Connection**
```bash
# Test connection
php -r "
$pdo = new PDO('mysql:host=localhost;dbname=webot', 'webot', 'password');
echo 'Database connected successfully';
"
```

**Webhook Issues**
```bash
# Check webhook status
curl "https://api.telegram.org/bot{BOT_TOKEN}/getWebhookInfo"

# Delete webhook
curl -X POST "https://api.telegram.org/bot{BOT_TOKEN}/deleteWebhook"
```

## 📞 Support

- **Documentation**: [docs/](../README.md)
- **Issues**: GitHub Issues
- **Community**: Telegram Group

---

**Next Steps**: [Quick Start Guide](../getting-started/README.md)
