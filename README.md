# WeBot - Professional Telegram VPN Bot

[![PHP Version](https://img.shields.io/badge/PHP-8.1%2B-blue.svg)](https://php.net)
[![License](https://img.shields.io/badge/License-GPL--3.0-green.svg)](LICENSE)
[![Tests](https://github.com/webotdev/webot/workflows/WeBot%20CI%2FCD%20Pipeline/badge.svg)](https://github.com/webotdev/webot/actions)
[![Coverage](https://codecov.io/gh/webotdev/webot/branch/main/graph/badge.svg)](https://codecov.io/gh/webotdev/webot)
[![Quality Gate](https://img.shields.io/badge/Quality%20Gate-Passing-brightgreen.svg)](#code-quality)
[![PHPStan Level](https://img.shields.io/badge/PHPStan-Level%208-brightgreen.svg)](https://phpstan.org/)

A modern, professional Telegram VPN bot built with PHP 8.1+, featuring multi-panel support, advanced security, and comprehensive management capabilities.

## 🌟 Key Features

- **Multi-Panel Support**: Marzban, Marzneshin, X-UI
- **Advanced Security**: Rate limiting, input validation, SSL/TLS
- **Payment Integration**: Multiple payment gateways
- **Real-time Monitoring**: Performance tracking and analytics
- **Microservices Architecture**: Scalable and maintainable
- **RTL Support**: Full Persian/Farsi language support
- **Docker Ready**: Complete containerization support

## 🚀 Quick Start

### Prerequisites

- PHP 8.1 or higher
- MySQL 8.0+ or PostgreSQL 13+
- Redis 6.0+
- Composer 2.0+
- Docker & Docker Compose (optional)

### Installation

```bash
# Clone the repository
git clone https://github.com/webotdev/webot.git
cd webot

# Install dependencies
composer install --optimize-autoloader

# Set up environment
cp .env.example .env
# Edit .env with your configuration

# Run migrations
php scripts/run-migrations.php

# Start the application
php -S localhost:8000 -t public
```

### Docker Installation

```bash
# Clone and start with Docker
git clone https://github.com/webotdev/webot.git
cd webot

# Configure environment
cp .env.example .env
# Edit .env file

# Start services
docker-compose up -d

# Run migrations
docker-compose exec webot php scripts/run-migrations.php
```

## 📖 Documentation

### English Documentation
- [Installation Guide](INSTALLATION.md)
- [API Documentation](docs/api/)
- [Architecture Overview](docs/COMPLETE_PROJECT_DOCUMENTATION.md)
- [Performance Guide](docs/PERFORMANCE_OPTIMIZATION_SUMMARY.md)

### Persian Documentation (مستندات فارسی)
- [راهنمای نصب](README-fa.md)
- [مستندات کامل پروژه](docs/COMPLETE_PROJECT_DOCUMENTATION.md)
- [راهنمای بهبود عملکرد](docs/PERFORMANCE_OPTIMIZATION_SUMMARY.md)

## 🏗️ Architecture

WeBot follows Domain-Driven Design (DDD) principles with a feature-first architecture:

```
src/
├── Features/           # Feature-based modules
│   ├── Auth/          # Authentication & Authorization
│   ├── User/          # User management
│   ├── Payment/       # Payment processing
│   ├── VPNService/    # VPN service management
│   ├── Panel/         # VPN panel integration
│   └── Support/       # Support system
├── Core/              # Framework core
├── Services/          # Business services
├── Repositories/      # Data access layer
└── Utils/             # Utilities and helpers
```

## 🔧 Configuration

### Environment Variables

```env
# Application
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

# Database
DB_HOST=localhost
DB_USERNAME=your_db_user
DB_PASSWORD=your_db_password
DB_DATABASE=webot

# Telegram
TELEGRAM_BOT_TOKEN=your_bot_token
WEBHOOK_URL=https://your-domain.com/webhook

# Panel Configuration
PANEL_TYPE=marzban
PANEL_URL=https://your-panel.com
PANEL_USERNAME=admin
PANEL_PASSWORD=your_panel_password
```

## 🧪 Testing

```bash
# Run all tests
composer test

# Run with coverage
composer test -- --coverage-html reports/coverage

# Static analysis
composer analyze

# Code style check
composer cs-check

# Fix code style
composer cs-fix
```

## 🚀 Deployment

### Production Deployment

```bash
# Optimize for production
composer install --no-dev --optimize-autoloader

# Set production environment
export APP_ENV=production
export APP_DEBUG=false

# Run optimizations
php scripts/optimize-production.php

# Start with process manager
pm2 start ecosystem.config.js
```

### Docker Production

```bash
# Build production image
docker build -t webot:latest .

# Deploy with docker-compose
docker-compose -f docker-compose.prod.yml up -d
```

## 📊 Monitoring

WeBot includes comprehensive monitoring:

- **Performance Metrics**: Response times, memory usage
- **Error Tracking**: Automatic error reporting
- **Health Checks**: System health monitoring
- **Analytics**: User behavior and system usage

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

```bash
# Install development dependencies
composer install

# Set up pre-commit hooks
./scripts/setup-hooks.sh

# Run development server
php -S localhost:8000 -t public
```

## 📄 License

This project is licensed under the GPL-3.0 License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [Full Documentation](docs/)
- **Issues**: [GitHub Issues](https://github.com/webotdev/webot/issues)
- **Discussions**: [GitHub Discussions](https://github.com/webotdev/webot/discussions)
- **Telegram**: [@WeBot_Support](https://t.me/WeBot_Support)

## 🌟 Sponsors

Support this project by becoming a sponsor. Your logo will show up here with a link to your website.

---

**Made with ❤️ by the WeBot Team**

> For Persian documentation and support, please refer to [README-fa.md](README-fa.md)
