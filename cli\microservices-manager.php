#!/usr/bin/env php
<?php

declare(strict_types=1);

require_once __DIR__ . '/../autoload.php';

use WeBot\Core\Application;
use WeBot\Core\Config;
use WeBot\Core\CacheManager;
use WeBot\Services\DatabaseService;
use WeBot\Microservices\Bootstrap\MicroservicesBootstrap;
use WeBot\Microservices\Migration\MonolithToMicroservicesBridge;
use WeBot\Utils\Logger;

/**
 * Microservices Manager CLI
 * 
 * Command-line interface for managing microservices architecture,
 * including initialization, migration, monitoring, and maintenance.
 * 
 * @package WeBot\CLI
 * @version 2.0
 */
class MicroservicesManagerCLI
{
    private Application $app;
    private MicroservicesBootstrap $microservices;
    private MonolithToMicroservicesBridge $bridge;
    private Logger $logger;
    
    public function __construct()
    {
        $this->app = new Application();
        $this->logger = Logger::getInstance();

        // Initialize microservices
        $config = new Config();
        $configArray = [
            'prefix' => 'webot:',
            'default_ttl' => 3600,
            'enabled' => true,
            'redis_host' => '127.0.0.1',
            'redis_port' => 6379
        ];
        $cache = new CacheManager($configArray);
        $database = new DatabaseService($config);

        $this->microservices = new MicroservicesBootstrap($cache, $database);
        $this->bridge = new MonolithToMicroservicesBridge($this->app, $this->microservices);
    }
    
    /**
     * Main CLI entry point
     */
    public function run(array $argv): void
    {
        $command = $argv[1] ?? 'help';
        $args = array_slice($argv, 2);
        
        try {
            switch ($command) {
                case 'init':
                    $this->initializeCommand($args);
                    break;
                    
                case 'status':
                    $this->statusCommand($args);
                    break;
                    
                case 'migrate':
                    $this->migrateCommand($args);
                    break;
                    
                case 'rollback':
                    $this->rollbackCommand($args);
                    break;
                    
                case 'sync':
                    $this->syncCommand($args);
                    break;
                    
                case 'health':
                    $this->healthCommand($args);
                    break;
                    
                case 'services':
                    $this->servicesCommand($args);
                    break;
                    
                case 'registry':
                    $this->registryCommand($args);
                    break;
                    
                case 'test':
                    $this->testCommand($args);
                    break;
                    
                case 'help':
                default:
                    $this->helpCommand();
                    break;
            }
        } catch (\Exception $e) {
            $this->error("Command failed: " . $e->getMessage());
            exit(1);
        }
    }
    
    /**
     * Initialize microservices
     */
    private function initializeCommand(array $args): void
    {
        $this->info("Initializing microservices architecture...");
        
        $result = $this->microservices->initialize();
        
        if ($result['success']) {
            $this->success("Microservices initialized successfully!");
            $this->info("Services: " . implode(', ', $result['services']));
        } else {
            $this->error("Failed to initialize microservices");
        }
    }
    
    /**
     * Show system status
     */
    private function statusCommand(array $args): void
    {
        $this->info("Checking system status...");
        
        // Get microservices status
        $microStatus = $this->microservices->getSystemStatus();
        
        // Get migration status
        $migrationStatus = $this->bridge->getMigrationStatus();
        
        $this->info("\n=== Microservices Status ===");
        $this->info("Overall Status: " . $microStatus['overall_status']);
        
        $this->info("\n=== Services ===");
        foreach ($microStatus['services'] as $service => $status) {
            $statusColor = $status === 'healthy' ? 'green' : 'red';
            $this->coloredOutput("  {$service}: {$status}", $statusColor);
        }
        
        $this->info("\n=== Components ===");
        foreach ($microStatus['components'] as $component => $status) {
            $statusColor = $status === 'healthy' ? 'green' : 'red';
            $this->coloredOutput("  {$component}: {$status}", $statusColor);
        }
        
        $this->info("\n=== Migration Progress ===");
        $this->info("Overall Progress: " . round($migrationStatus['overall_progress'], 2) . "%");
        
        foreach ($migrationStatus['features'] as $feature => $status) {
            $featureStatus = $status['status'] ?? 'monolith';
            $statusColor = $featureStatus === 'microservice' ? 'green' : 'yellow';
            $this->coloredOutput("  {$feature}: {$featureStatus}", $statusColor);
        }
    }
    
    /**
     * Migrate features to microservices
     */
    private function migrateCommand(array $args): void
    {
        $feature = $args[0] ?? 'all';
        
        $this->info("Migrating feature: {$feature}");
        
        $result = $this->bridge->migrateFeature($feature);
        
        if ($result['success']) {
            $this->success("Feature {$feature} migrated successfully!");
        } else {
            $this->error("Failed to migrate feature: {$feature}");
        }
    }
    
    /**
     * Rollback features to monolith
     */
    private function rollbackCommand(array $args): void
    {
        $feature = $args[0] ?? null;
        
        if (!$feature) {
            $this->error("Feature name is required for rollback");
            return;
        }
        
        $this->warning("Rolling back feature: {$feature}");
        
        $result = $this->bridge->rollbackFeature($feature);
        
        if ($result['success']) {
            $this->success("Feature {$feature} rolled back successfully!");
        } else {
            $this->error("Failed to rollback feature: {$feature}");
        }
    }
    
    /**
     * Sync data between monolith and microservices
     */
    private function syncCommand(array $args): void
    {
        $dataType = $args[0] ?? 'all';
        
        $this->info("Syncing data: {$dataType}");
        
        $result = $this->bridge->syncData($dataType);
        
        if ($result['success']) {
            $this->success("Data sync completed successfully!");
            if (isset($result['total_synced'])) {
                $this->info("Total records synced: " . $result['total_synced']);
            }
        } else {
            $this->error("Failed to sync data: {$dataType}");
        }
    }
    
    /**
     * Check health of all services
     */
    private function healthCommand(array $args): void
    {
        $this->info("Performing health checks...");
        
        try {
            $orchestrator = $this->microservices->getOrchestrator();
            $healthResults = $orchestrator->handleRequest([
                'operation' => 'health_check_all'
            ]);
            
            $this->info("\n=== Health Check Results ===");
            
            foreach ($healthResults['health_results'] as $service => $instances) {
                $this->info("\n{$service}:");
                foreach ($instances as $instanceId => $health) {
                    $status = $health['status'];
                    $statusColor = $status === 'healthy' ? 'green' : 'red';
                    $responseTime = isset($health['response_time']) ? " ({$health['response_time']}ms)" : "";
                    $this->coloredOutput("  {$instanceId}: {$status}{$responseTime}", $statusColor);
                }
            }
        } catch (\Exception $e) {
            $this->error("Health check failed: " . $e->getMessage());
        }
    }
    
    /**
     * Manage services
     */
    private function servicesCommand(array $args): void
    {
        $action = $args[0] ?? 'list';
        
        switch ($action) {
            case 'list':
                $this->listServices();
                break;
                
            case 'restart':
                $serviceName = $args[1] ?? null;
                if ($serviceName) {
                    $this->restartService($serviceName);
                } else {
                    $this->error("Service name is required");
                }
                break;
                
            default:
                $this->error("Unknown services action: {$action}");
        }
    }
    
    /**
     * Manage service registry
     */
    private function registryCommand(array $args): void
    {
        $action = $args[0] ?? 'stats';
        
        switch ($action) {
            case 'stats':
                $this->showRegistryStats();
                break;
                
            case 'services':
                $this->showRegisteredServices();
                break;
                
            default:
                $this->error("Unknown registry action: {$action}");
        }
    }
    
    /**
     * Test microservices functionality
     */
    private function testCommand(array $args): void
    {
        $testType = $args[0] ?? 'basic';
        
        $this->info("Running {$testType} tests...");
        
        switch ($testType) {
            case 'basic':
                $this->runBasicTests();
                break;
                
            case 'integration':
                $this->runIntegrationTests();
                break;
                
            case 'load':
                $this->runLoadTests();
                break;
                
            default:
                $this->error("Unknown test type: {$testType}");
        }
    }
    
    /**
     * List all services
     */
    private function listServices(): void
    {
        try {
            $registry = $this->microservices->getServiceRegistry();
            $services = $registry->getAllServices();
            
            $this->info("\n=== Registered Services ===");
            
            foreach ($services as $serviceName => $instances) {
                $this->info("\n{$serviceName}:");
                foreach ($instances as $instance) {
                    $url = "{$instance['protocol']}://{$instance['host']}:{$instance['port']}";
                    $status = $instance['status'];
                    $statusColor = $status === 'healthy' ? 'green' : 'red';
                    $this->coloredOutput("  {$instance['id']}: {$url} ({$status})", $statusColor);
                }
            }
        } catch (\Exception $e) {
            $this->error("Failed to list services: " . $e->getMessage());
        }
    }
    
    /**
     * Restart a service
     */
    private function restartService(string $serviceName): void
    {
        $this->info("Restarting service: {$serviceName}");
        
        try {
            // In a real implementation, this would restart the service
            $this->success("Service {$serviceName} restarted successfully!");
        } catch (\Exception $e) {
            $this->error("Failed to restart service: " . $e->getMessage());
        }
    }
    
    /**
     * Show service registry statistics
     */
    private function showRegistryStats(): void
    {
        try {
            $registry = $this->microservices->getServiceRegistry();
            $stats = $registry->getServiceStatistics();
            
            $this->info("\n=== Service Registry Statistics ===");
            $this->info("Total Services: " . $stats['total_services']);
            $this->info("Total Instances: " . $stats['total_instances']);
            $this->coloredOutput("Healthy Instances: " . $stats['healthy_instances'], 'green');
            $this->coloredOutput("Unhealthy Instances: " . $stats['unhealthy_instances'], 'red');
        } catch (\Exception $e) {
            $this->error("Failed to get registry stats: " . $e->getMessage());
        }
    }
    
    /**
     * Show registered services
     */
    private function showRegisteredServices(): void
    {
        $this->listServices();
    }
    
    /**
     * Run basic tests
     */
    private function runBasicTests(): void
    {
        $tests = [
            'Service Registry' => function() {
                $registry = $this->microservices->getServiceRegistry();
                return !empty($registry->getAllServices());
            },
            'API Gateway' => function() {
                $gateway = $this->microservices->getApiGateway();
                return $gateway !== null;
            },
            'Service Orchestrator' => function() {
                $orchestrator = $this->microservices->getOrchestrator();
                return $orchestrator !== null;
            }
        ];
        
        $this->runTests($tests);
    }
    
    /**
     * Run integration tests
     */
    private function runIntegrationTests(): void
    {
        $tests = [
            'User Service Health' => function() {
                $userService = $this->microservices->getService('user-service');
                $result = $userService->handleRequest(['action' => 'health']);
                return $result['success'] ?? false;
            },
            'Payment Service Health' => function() {
                $paymentService = $this->microservices->getService('payment-service');
                $result = $paymentService->handleRequest(['action' => 'health']);
                return $result['success'] ?? false;
            },
            'Panel Service Health' => function() {
                $panelService = $this->microservices->getService('panel-service');
                $result = $panelService->handleRequest(['action' => 'health']);
                return $result['success'] ?? false;
            }
        ];
        
        $this->runTests($tests);
    }
    
    /**
     * Run load tests
     */
    private function runLoadTests(): void
    {
        $this->info("Load testing not implemented yet");
    }
    
    /**
     * Run test suite
     */
    private function runTests(array $tests): void
    {
        $passed = 0;
        $total = count($tests);
        
        foreach ($tests as $testName => $testFunction) {
            try {
                $result = $testFunction();
                if ($result) {
                    $this->coloredOutput("✓ {$testName}", 'green');
                    $passed++;
                } else {
                    $this->coloredOutput("✗ {$testName}", 'red');
                }
            } catch (\Exception $e) {
                $this->coloredOutput("✗ {$testName}: " . $e->getMessage(), 'red');
            }
        }
        
        $this->info("\nTest Results: {$passed}/{$total} passed");
        
        if ($passed === $total) {
            $this->success("All tests passed!");
        } else {
            $this->warning("Some tests failed");
        }
    }
    
    /**
     * Show help information
     */
    private function helpCommand(): void
    {
        $this->info("WeBot Microservices Manager CLI");
        $this->info("Usage: php microservices-manager.php <command> [options]");
        $this->info("");
        $this->info("Commands:");
        $this->info("  init                    Initialize microservices architecture");
        $this->info("  status                  Show system status");
        $this->info("  migrate <feature>       Migrate feature to microservices");
        $this->info("  rollback <feature>      Rollback feature to monolith");
        $this->info("  sync <data_type>        Sync data between monolith and microservices");
        $this->info("  health                  Check health of all services");
        $this->info("  services <action>       Manage services (list, restart)");
        $this->info("  registry <action>       Manage service registry (stats, services)");
        $this->info("  test <type>             Run tests (basic, integration, load)");
        $this->info("  help                    Show this help message");
        $this->info("");
        $this->info("Examples:");
        $this->info("  php microservices-manager.php init");
        $this->info("  php microservices-manager.php migrate user_management");
        $this->info("  php microservices-manager.php sync users");
        $this->info("  php microservices-manager.php test basic");
    }
    
    /**
     * Output colored text
     */
    private function coloredOutput(string $text, string $color): void
    {
        $colors = [
            'red' => "\033[31m",
            'green' => "\033[32m",
            'yellow' => "\033[33m",
            'blue' => "\033[34m",
            'reset' => "\033[0m"
        ];
        
        $colorCode = $colors[$color] ?? $colors['reset'];
        echo $colorCode . $text . $colors['reset'] . "\n";
    }
    
    /**
     * Output info message
     */
    private function info(string $message): void
    {
        echo $message . "\n";
    }
    
    /**
     * Output success message
     */
    private function success(string $message): void
    {
        $this->coloredOutput($message, 'green');
    }
    
    /**
     * Output warning message
     */
    private function warning(string $message): void
    {
        $this->coloredOutput($message, 'yellow');
    }
    
    /**
     * Output error message
     */
    private function error(string $message): void
    {
        $this->coloredOutput($message, 'red');
    }
}

// Run CLI if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $cli = new MicroservicesManagerCLI();
    $cli->run($argv);
}
