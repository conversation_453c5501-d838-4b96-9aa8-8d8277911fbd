<?php

declare(strict_types=1);

namespace WeBot\Middleware;

use WeBot\Core\CacheManager;
use WeBot\Exceptions\SecurityException;

/**
 * Security Middleware
 *
 * Handles security headers, CSRF protection,
 * input validation, and security policies.
 *
 * @package WeBot\Middleware
 * @version 2.0
 */
class SecurityMiddleware
{
    private CacheManager $cache;
    private array $config;

    public function __construct(CacheManager $cache, array $config = [])
    {
        $this->cache = $cache;
        $this->config = array_merge([
            'csrf_enabled' => true,
            'csrf_token_lifetime' => 3600,
            'security_headers' => [
                'X-Content-Type-Options' => 'nosniff',
                'X-Frame-Options' => 'DENY',
                'X-XSS-Protection' => '1; mode=block',
                'Strict-Transport-Security' => 'max-age=31536000; includeSubDomains',
                'Content-Security-Policy' => "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none';",
                'Referrer-Policy' => 'strict-origin-when-cross-origin',
                'Permissions-Policy' => 'geolocation=(), microphone=(), camera=()'
            ],
            'input_validation' => true,
            'max_request_size' => 10 * 1024 * 1024, // 10MB
            'blocked_user_agents' => [],
            'blocked_ips' => []
        ], $config);
    }

    /**
     * Handle security middleware
     */
    public function handle(array $request, callable $next): array
    {
        try {
            // Security checks
            $this->performSecurityChecks($request);

            // CSRF protection for state-changing requests
            if ($this->config['csrf_enabled'] && $this->isStateChangingRequest($request)) {
                $this->validateCSRFToken($request);
            }

            // Input validation
            if ($this->config['input_validation']) {
                $this->validateInput($request);
            }

            // Process request
            $response = $next($request);

            // Add security headers
            return $this->addSecurityHeaders($response);
        } catch (SecurityException $e) {
            return [
                'success' => false,
                'error' => 'Security violation',
                'message' => $e->getMessage(),
                'status_code' => 403
            ];
        }
    }

    /**
     * Perform basic security checks
     */
    private function performSecurityChecks(array $request): void
    {
        // Check request size
        $contentLength = $request['headers']['Content-Length'] ?? 0;
        if ($contentLength > $this->config['max_request_size']) {
            throw new SecurityException('Request too large');
        }

        // Check blocked IPs
        $clientIP = $this->getClientIP($request);
        if (in_array($clientIP, $this->config['blocked_ips'])) {
            throw new SecurityException('IP address blocked');
        }

        // Check blocked user agents
        $userAgent = $request['headers']['User-Agent'] ?? '';
        foreach ($this->config['blocked_user_agents'] as $blockedUA) {
            if (stripos($userAgent, $blockedUA) !== false) {
                throw new SecurityException('User agent blocked');
            }
        }

        // Check for suspicious patterns
        $this->detectSuspiciousPatterns($request);
    }

    /**
     * Validate CSRF token
     */
    private function validateCSRFToken(array $request): void
    {
        $token = $request['headers']['X-CSRF-Token'] ??
                 $request['body']['_token'] ??
                 $request['query']['_token'] ?? null;

        if (!$token) {
            throw new SecurityException('CSRF token missing');
        }

        if (!$this->isValidCSRFToken($token)) {
            throw new SecurityException('Invalid CSRF token');
        }
    }

    /**
     * Generate CSRF token
     */
    public function generateCSRFToken(): string
    {
        $token = bin2hex(random_bytes(32));
        $cacheKey = "csrf_token:{$token}";

        $this->cache->set($cacheKey, time(), $this->config['csrf_token_lifetime']);

        return $token;
    }

    /**
     * Validate CSRF token
     */
    private function isValidCSRFToken(string $token): bool
    {
        $cacheKey = "csrf_token:{$token}";
        $timestamp = $this->cache->get($cacheKey);

        if (!$timestamp) {
            return false;
        }

        // Check if token is expired
        if (time() - $timestamp > $this->config['csrf_token_lifetime']) {
            $this->cache->delete($cacheKey);
            return false;
        }

        return true;
    }

    /**
     * Validate input data
     */
    private function validateInput(array $request): void
    {
        // Check for common injection patterns
        $dangerousPatterns = [
            '/(<script[^>]*>.*?<\/script>)/is', // Script tags
            '/(javascript:)/i', // JavaScript protocol
            '/(on\w+\s*=)/i', // Event handlers
            '/(\bUNION\b.*\bSELECT\b)/i', // SQL injection
            '/(\bDROP\b.*\bTABLE\b)/i', // SQL injection
            '/(\bINSERT\b.*\bINTO\b)/i', // SQL injection
            '/(\.\.\/)/i', // Path traversal
            '/(\${.*})/i', // Template injection
        ];

        $inputData = json_encode($request['body'] ?? []);

        foreach ($dangerousPatterns as $pattern) {
            if (preg_match($pattern, $inputData)) {
                throw new SecurityException('Potentially malicious input detected');
            }
        }
    }

    /**
     * Detect suspicious patterns
     */
    private function detectSuspiciousPatterns(array $request): void
    {
        $userAgent = $request['headers']['User-Agent'] ?? '';
        $uri = $request['uri'] ?? '';

        // Check for common attack patterns
        $suspiciousPatterns = [
            '/\b(sqlmap|nikto|nmap|masscan)\b/i',
            '/\b(union.*select|drop.*table|insert.*into)\b/i',
            '/\b(eval\(|base64_decode|exec\()\b/i',
            '/\.\.\//i',
            '/<script/i'
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $userAgent . ' ' . $uri)) {
                // Log suspicious activity
                $this->logSuspiciousActivity($request, $pattern);

                // Optionally block the request
                if ($this->config['block_suspicious_requests'] ?? false) {
                    throw new SecurityException('Suspicious activity detected');
                }
            }
        }
    }

    /**
     * Add security headers to response
     */
    private function addSecurityHeaders(array $response): array
    {
        $securityHeaders = $this->config['security_headers'];

        // Add nonce to CSP if needed
        if (isset($securityHeaders['Content-Security-Policy'])) {
            $nonce = base64_encode(random_bytes(16));
            $securityHeaders['Content-Security-Policy'] = str_replace(
                "'unsafe-inline'",
                "'nonce-{$nonce}'",
                $securityHeaders['Content-Security-Policy']
            );
            $response['csp_nonce'] = $nonce;
        }

        // Merge security headers with existing response headers
        $response['headers'] = array_merge($response['headers'] ?? [], $securityHeaders);

        return $response;
    }

    /**
     * Check if request is state-changing
     */
    private function isStateChangingRequest(array $request): bool
    {
        $method = strtoupper($request['method'] ?? 'GET');
        return in_array($method, ['POST', 'PUT', 'PATCH', 'DELETE']);
    }

    /**
     * Get client IP address
     */
    private function getClientIP(array $request): string
    {
        $headers = $request['headers'] ?? [];

        // Check for forwarded IP headers
        $ipHeaders = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Standard
        ];

        foreach ($ipHeaders as $header) {
            if (!empty($headers[$header])) {
                $ips = explode(',', $headers[$header]);
                $ip = trim($ips[0]);

                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $request['client_ip'] ?? '127.0.0.1';
    }

    /**
     * Log suspicious activity
     */
    private function logSuspiciousActivity(array $request, string $pattern): void
    {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $this->getClientIP($request),
            'user_agent' => $request['headers']['User-Agent'] ?? '',
            'uri' => $request['uri'] ?? '',
            'method' => $request['method'] ?? '',
            'pattern' => $pattern,
            'headers' => $request['headers'] ?? []
        ];

        // Log to file or database
        error_log('SECURITY_ALERT: ' . json_encode($logData));

        // Store in cache for rate limiting
        $cacheKey = "suspicious_activity:" . $this->getClientIP($request);
        $count = $this->cache->get($cacheKey) ?? 0;
        $this->cache->set($cacheKey, $count + 1, 3600); // 1 hour
    }

    /**
     * Rate limiting for suspicious IPs
     */
    public function checkSuspiciousActivity(array $request): bool
    {
        $clientIP = $this->getClientIP($request);
        $cacheKey = "suspicious_activity:{$clientIP}";
        $count = $this->cache->get($cacheKey) ?? 0;

        return $count > ($this->config['max_suspicious_requests'] ?? 10);
    }

    /**
     * Content Security Policy violation handler
     */
    public function handleCSPViolation(array $violationReport): void
    {
        // Log CSP violation
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'type' => 'csp_violation',
            'report' => $violationReport
        ];

        error_log('CSP_VIOLATION: ' . json_encode($logData));
    }

    /**
     * Security audit middleware
     */
    public function auditRequest(array $request, callable $next): array
    {
        $startTime = microtime(true);

        // Process request
        $response = $next($request);

        $endTime = microtime(true);

        // Log security audit
        $auditData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $this->getClientIP($request),
            'method' => $request['method'] ?? '',
            'uri' => $request['uri'] ?? '',
            'user_agent' => $request['headers']['User-Agent'] ?? '',
            'response_time' => round(($endTime - $startTime) * 1000, 2),
            'status_code' => $response['status_code'] ?? 200,
            'user_id' => $request['user']['id'] ?? null
        ];

        // Store audit log
        $this->storeAuditLog($auditData);

        return $response;
    }

    /**
     * Store audit log
     */
    private function storeAuditLog(array $auditData): void
    {
        // In production, store in database or dedicated logging service
        error_log('AUDIT: ' . json_encode($auditData));
    }
}
