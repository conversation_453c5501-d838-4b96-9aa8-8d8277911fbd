<?php

declare(strict_types=1);

namespace WeBot\Tests;

/**
 * WeBot Test Runner
 * 
 * Central test runner for all WeBot tests including
 * integration tests, unit tests, and quality gates.
 * 
 * @package WeBot\Tests
 * @version 2.0
 */
class TestRunner
{
    private array $testSuites = [];
    private array $results = [];
    private int $totalTests = 0;
    private int $passedTests = 0;
    private int $failedTests = 0;

    public function __construct()
    {
        $this->registerTestSuites();
    }

    /**
     * Register all test suites
     */
    private function registerTestSuites(): void
    {
        // Integration Tests
        $this->testSuites['integration'] = [
            'name' => 'Integration Tests',
            'description' => 'Controller-Service integration and data flow tests',
            'class' => 'WeBot\\Tests\\Integration\\ControllerServiceIntegrationTest',
            'file' => __DIR__ . '/Integration/ControllerServiceIntegrationTest.php'
        ];

        // Quality Gate Tests
        $this->testSuites['quality'] = [
            'name' => 'Quality Gate Tests',
            'description' => 'Code quality, syntax, and architecture validation',
            'class' => 'WeBot\\Tests\\QualityGateTest',
            'file' => __DIR__ . '/QualityGateTest.php'
        ];
    }

    /**
     * Run all test suites
     */
    public function runAllTests(): array
    {
        echo "🧪 WeBot Test Runner v2.0\n";
        echo "========================\n\n";

        foreach (array_keys($this->testSuites) as $suiteKey) {
            try {
                $this->runTestSuite($suiteKey);
            } catch (\Throwable $e) {
                echo "❌ Test suite failed: " . $e->getMessage() . "\n";
                $this->results[$suiteKey] = [
                    'status' => 'FAILED',
                    'error' => $e->getMessage(),
                    'tests' => 0,
                    'passed' => 0,
                    'failed' => 1
                ];
                $this->failedTests++;
            }

            echo "\n";
        }

        return $this->generateFinalReport();
    }

    /**
     * Run specific test suite
     */
    public function runTestSuite(string $suiteKey): void
    {
        if (!isset($this->testSuites[$suiteKey])) {
            throw new \Exception("Test suite '{$suiteKey}' not found");
        }
        $suite = $this->testSuites[$suiteKey];

        echo "📋 Running {$suite['name']}...\n";
        echo "Description: {$suite['description']}\n";
        echo str_repeat('-', 50) . "\n";

        if (!file_exists($suite['file'])) {
            throw new \Exception("Test file not found: {$suite['file']}");
        }

        require_once $suite['file'];

        if (!class_exists($suite['class'])) {
            throw new \Exception("Test class not found: {$suite['class']}");
        }

        $testInstance = new $suite['class']();

        if (method_exists($testInstance, 'runAllTests')) {
            $results = $testInstance->runAllTests();
            
            if (method_exists($testInstance, 'printSummary')) {
                $testInstance->printSummary();
            }

            $this->results[$suiteKey] = [
                'status' => $results['failed_tests'] > 0 ? 'FAILED' : 'PASSED',
                'tests' => $results['total_tests'] ?? 0,
                'passed' => $results['passed_tests'] ?? 0,
                'failed' => $results['failed_tests'] ?? 0,
                'success_rate' => $results['success_rate'] ?? 0,
                'details' => $results['results'] ?? []
            ];

            $this->totalTests += $results['total_tests'] ?? 0;
            $this->passedTests += $results['passed_tests'] ?? 0;
            $this->failedTests += $results['failed_tests'] ?? 0;

        } else {
            throw new \Exception("Test class {$suite['class']} does not have runAllTests method");
        }
    }

    /**
     * Generate final report
     */
    private function generateFinalReport(): array
    {
        $overallSuccessRate = $this->totalTests > 0 ? 
            round(($this->passedTests / $this->totalTests) * 100, 2) : 0;

        $report = [
            'timestamp' => date('Y-m-d H:i:s'),
            'total_suites' => count($this->testSuites),
            'total_tests' => $this->totalTests,
            'passed_tests' => $this->passedTests,
            'failed_tests' => $this->failedTests,
            'success_rate' => $overallSuccessRate,
            'suites' => $this->results
        ];

        $this->printFinalReport($report);
        $this->saveReportToFile($report);

        return $report;
    }

    /**
     * Print final report
     */
    private function printFinalReport(array $report): void
    {
        echo "🏆 Final Test Report\n";
        echo "===================\n";
        echo "Timestamp: {$report['timestamp']}\n";
        echo "Total Test Suites: {$report['total_suites']}\n";
        echo "Total Tests: {$report['total_tests']}\n";
        echo "Passed Tests: {$report['passed_tests']}\n";
        echo "Failed Tests: {$report['failed_tests']}\n";
        echo "Overall Success Rate: {$report['success_rate']}%\n\n";

        echo "📊 Suite Breakdown:\n";
        foreach ($report['suites'] as $suiteKey => $suite) {
            $status = $suite['status'] === 'PASSED' ? '✅' : '❌';
            $suiteName = $this->testSuites[$suiteKey]['name'];
            echo "{$status} {$suiteName}: {$suite['passed']}/{$suite['tests']} ({$suite['success_rate']}%)\n";
        }

        echo "\n";

        if ($report['failed_tests'] > 0) {
            echo "❌ OVERALL RESULT: FAILED\n";
            echo "🔧 Please fix the failing tests before proceeding.\n";
        } else {
            echo "✅ OVERALL RESULT: PASSED\n";
            echo "🎉 All tests passed! Ready for next phase.\n";
        }
    }

    /**
     * Save report to file
     */
    private function saveReportToFile(array $report): void
    {
        $reportFile = __DIR__ . '/reports/test_report_' . date('Y-m-d_H-i-s') . '.json';
        
        // Create reports directory if it doesn't exist
        $reportsDir = dirname($reportFile);
        if (!is_dir($reportsDir)) {
            mkdir($reportsDir, 0755, true);
        }

        file_put_contents($reportFile, json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "📄 Report saved to: {$reportFile}\n";
    }



    /**
     * List available test suites
     */
    public function listTestSuites(): void
    {
        echo "📋 Available Test Suites:\n";
        echo "========================\n";
        
        foreach ($this->testSuites as $key => $suite) {
            echo "🔹 {$key}: {$suite['name']}\n";
            echo "   Description: {$suite['description']}\n";
            echo "   Class: {$suite['class']}\n\n";
        }
    }

    /**
     * Check test environment
     */
    public function checkEnvironment(): bool
    {
        echo "🔍 Checking Test Environment...\n";
        echo "==============================\n";

        $checks = [
            'PHP Version' => version_compare(PHP_VERSION, '8.0.0', '>='),
            'Required Extensions' => extension_loaded('json') && extension_loaded('mbstring'),
            'Test Directory' => is_dir(__DIR__),
            'Write Permissions' => is_writable(__DIR__)
        ];

        $allPassed = true;
        foreach ($checks as $check => $result) {
            $status = $result ? '✅' : '❌';
            echo "{$status} {$check}\n";
            if (!$result) {
                $allPassed = false;
            }
        }

        echo "\n";
        if ($allPassed) {
            echo "🟢 Environment check passed!\n";
        } else {
            echo "🔴 Environment check failed!\n";
        }

        return $allPassed;
    }
}

// CLI interface
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'] ?? '')) {
    $runner = new TestRunner();
    
    $command = $argv[1] ?? 'all';
    
    switch ($command) {
        case 'all':
            $runner->checkEnvironment();
            $runner->runAllTests();
            break;
            
        case 'list':
            $runner->listTestSuites();
            break;
            
        case 'env':
            $runner->checkEnvironment();
            break;
            
        case 'integration':
            $runner->runTestSuite('integration');
            break;

        case 'quality':
            $runner->runTestSuite('quality');
            break;
            
        default:
            echo "Usage: php TestRunner.php [all|list|env|integration|quality]\n";
            echo "  all         - Run all test suites\n";
            echo "  list        - List available test suites\n";
            echo "  env         - Check test environment\n";
            echo "  integration - Run integration tests only\n";
            echo "  quality     - Run quality gate tests only\n";
            break;
    }
}
