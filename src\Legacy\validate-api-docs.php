<?php

declare(strict_types=1);

/**
 * API Documentation Validation Script
 * 
 * Validates that all documentation classes work correctly
 * with proper type safety and no errors.
 */

// Include bootstrap
require_once __DIR__ . '/src/Core/bootstrap.php';

use WeBot\Documentation\OpenApiGenerator;
use WeBot\Documentation\ApiDocumentationGenerator;
use WeBot\Core\Config;

echo "🔍 Validating WeBot API Documentation System\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    // Test 1: Check if classes exist
    echo "📋 Testing class loading...\n";
    
    if (!class_exists(OpenApiGenerator::class)) {
        throw new Exception("OpenApiGenerator class not found");
    }
    echo "  ✅ OpenApiGenerator class loaded\n";
    
    if (!class_exists(ApiDocumentationGenerator::class)) {
        throw new Exception("ApiDocumentationGenerator class not found");
    }
    echo "  ✅ ApiDocumentationGenerator class loaded\n";
    
    if (!class_exists(Config::class)) {
        throw new Exception("Config class not found");
    }
    echo "  ✅ Config class loaded\n\n";
    
    // Test 2: Try to instantiate classes
    echo "🏗️ Testing class instantiation...\n";
    
    // Test OpenApiGenerator
    $openApiGenerator = new OpenApiGenerator('2.0.0-test', 'https://test.webot.com');
    echo "  ✅ OpenApiGenerator instantiated successfully\n";
    
    // Test Config
    $config = new Config();
    echo "  ✅ Config instantiated successfully\n";
    
    // Test ApiDocumentationGenerator with real Config
    $testOutputDir = sys_get_temp_dir() . '/webot_validation_test';
    if (!is_dir($testOutputDir)) {
        mkdir($testOutputDir, 0755, true);
    }
    
    $docGenerator = new ApiDocumentationGenerator($config, $testOutputDir);
    echo "  ✅ ApiDocumentationGenerator instantiated successfully\n";
    
    // Clean up
    if (is_dir($testOutputDir)) {
        rmdir($testOutputDir);
    }
    echo "\n";
    
    // Test 3: Check required methods exist
    echo "🔧 Testing required methods...\n";
    
    $requiredMethods = [
        'generateAll',
        'generateOpenApiJson',
        'generateOpenApiYaml',
        'generateMarkdownDocs',
        'generatePostmanCollection',
        'generateCodeExamples',
        'generateAuthenticationGuide',
        'generateErrorCodesDoc',
        'generateRateLimitingDoc'
    ];
    
    foreach ($requiredMethods as $method) {
        if (!method_exists($docGenerator, $method)) {
            throw new Exception("ApiDocumentationGenerator missing method: {$method}");
        }
        echo "  ✅ {$method} exists\n";
    }
    echo "\n";
    
    // Test 4: Test OpenAPI generation
    echo "📋 Testing OpenAPI generation...\n";
    
    $spec = $openApiGenerator->generate();
    if (!is_array($spec)) {
        throw new Exception("OpenAPI generate() should return array");
    }
    
    if (!isset($spec['openapi']) || !isset($spec['info']) || !isset($spec['paths'])) {
        throw new Exception("OpenAPI specification missing required sections");
    }
    
    echo "  ✅ OpenAPI specification generated successfully\n";
    echo "  📊 Found " . count($spec['paths']) . " API endpoints\n";
    echo "  📋 Found " . count($spec['components']['schemas']) . " data schemas\n\n";
    
    // Test 5: Test Config interface compliance
    echo "🔧 Testing Config interface compliance...\n";
    
    if (!($config instanceof \WeBot\Core\ConfigInterface)) {
        throw new Exception("Config does not implement ConfigInterface");
    }
    echo "  ✅ Config implements ConfigInterface\n";
    
    // Test required methods
    $configMethods = ['get', 'set', 'has', 'all'];
    foreach ($configMethods as $method) {
        if (!method_exists($config, $method)) {
            throw new Exception("Config missing method: {$method}");
        }
        echo "  ✅ Config::{$method} exists\n";
    }
    echo "\n";
    
    // Test 6: Test file structure
    echo "📁 Testing file structure...\n";
    
    $expectedFiles = [
        'src/Documentation/OpenApiGenerator.php',
        'src/Documentation/ApiDocumentationGenerator.php',
        'src/Core/Config.php',
        'src/Core/ConfigInterface.php',
        'scripts/generate-docs.php',
        'docs/api/README.md'
    ];
    
    foreach ($expectedFiles as $file) {
        if (file_exists($file)) {
            echo "  ✅ {$file} exists\n";
        } else {
            echo "  ❌ {$file} missing\n";
        }
    }
    echo "\n";
    
    echo "🎉 All validation tests completed successfully!\n\n";
    
    // Summary
    echo "📊 Validation Summary:\n";
    echo "  ✅ All required classes exist and load correctly\n";
    echo "  ✅ All classes can be instantiated without errors\n";
    echo "  ✅ All required methods exist\n";
    echo "  ✅ OpenAPI generation works correctly\n";
    echo "  ✅ Config interface compliance verified\n";
    echo "  ✅ File structure is complete\n\n";
    
    echo "🚀 API Documentation system is fully operational!\n";
    echo "💡 You can now run: php scripts/generate-docs.php --format=all\n";
    
} catch (Exception $e) {
    echo "\n❌ Validation failed: " . $e->getMessage() . "\n";
    echo "📍 Error in: " . $e->getFile() . " on line " . $e->getLine() . "\n";
    exit(1);
} catch (Error $e) {
    echo "\n💥 Fatal error: " . $e->getMessage() . "\n";
    echo "📍 Error in: " . $e->getFile() . " on line " . $e->getLine() . "\n";
    exit(1);
}
