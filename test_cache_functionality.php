<?php
/**
 * Cache Functionality Test for WeBot
 * 
 * This script tests the cache system functionality and verifies
 * that caching operations work properly.
 */

declare(strict_types=1);

echo "=== WeBot Cache Functionality Test ===\n\n";

// Load the application
try {
    require_once __DIR__ . '/autoload.php';
    echo "✅ Application loaded successfully\n\n";
} catch (Exception $e) {
    echo "❌ Failed to load application: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 1: Cache manager class availability
echo "1. Cache Manager Class Availability Test:\n";
$cacheManagerOk = false;

if (class_exists('WeBot\Core\CacheManager')) {
    echo "   ✅ CacheManager class exists\n";
    $cacheManagerOk = true;
    
    try {
        $cacheManager = new WeBot\Core\CacheManager();
        echo "   ✅ CacheManager can be instantiated\n";
        
        $methods = ['get', 'set', 'delete', 'clear'];
        foreach ($methods as $method) {
            if (method_exists($cacheManager, $method)) {
                echo "   ✅ CacheManager has {$method}() method\n";
            } else {
                echo "   ❌ CacheManager missing {$method}() method\n";
                $cacheManagerOk = false;
            }
        }
    } catch (Exception $e) {
        echo "   ❌ Failed to instantiate CacheManager: " . $e->getMessage() . "\n";
        $cacheManagerOk = false;
    }
} else {
    echo "   ❌ CacheManager class does not exist\n";
}

// Test 2: File-based cache operations
echo "\n2. File-based Cache Operations Test:\n";
$fileCacheOk = true;

$cacheDir = 'storage/cache';
if (is_dir($cacheDir) && is_writable($cacheDir)) {
    echo "   ✅ Cache directory is accessible\n";
    
    // Test cache set operation
    $testKey = 'test_cache_key_' . time();
    $testValue = ['data' => 'test value', 'timestamp' => time()];
    $cacheFile = $cacheDir . '/' . md5($testKey) . '.cache';
    
    try {
        $serializedValue = serialize($testValue);
        if (file_put_contents($cacheFile, $serializedValue)) {
            echo "   ✅ Cache set operation works\n";
            
            // Test cache get operation
            $retrievedValue = file_get_contents($cacheFile);
            if ($retrievedValue !== false) {
                $unserializedValue = unserialize($retrievedValue);
                if ($unserializedValue === $testValue) {
                    echo "   ✅ Cache get operation works\n";
                } else {
                    echo "   ❌ Cache get operation failed (data mismatch)\n";
                    $fileCacheOk = false;
                }
            } else {
                echo "   ❌ Cache get operation failed (cannot read)\n";
                $fileCacheOk = false;
            }
            
            // Test cache delete operation
            if (unlink($cacheFile)) {
                echo "   ✅ Cache delete operation works\n";
            } else {
                echo "   ❌ Cache delete operation failed\n";
                $fileCacheOk = false;
            }
        } else {
            echo "   ❌ Cache set operation failed\n";
            $fileCacheOk = false;
        }
    } catch (Exception $e) {
        echo "   ❌ File cache operations failed: " . $e->getMessage() . "\n";
        $fileCacheOk = false;
    }
} else {
    echo "   ❌ Cache directory not accessible\n";
    $fileCacheOk = false;
}

// Test 3: Cache expiration handling
echo "\n3. Cache Expiration Handling Test:\n";
$expirationOk = true;

try {
    $expiredCacheFile = $cacheDir . '/expired_test.cache';
    $expiredData = [
        'data' => 'expired test data',
        'expires_at' => time() - 3600 // Expired 1 hour ago
    ];
    
    file_put_contents($expiredCacheFile, serialize($expiredData));
    echo "   ✅ Created expired cache file\n";
    
    // Check if we can detect expiration
    $retrievedData = unserialize(file_get_contents($expiredCacheFile));
    if (isset($retrievedData['expires_at']) && $retrievedData['expires_at'] < time()) {
        echo "   ✅ Can detect expired cache entries\n";
    } else {
        echo "   ❌ Cannot detect expired cache entries\n";
        $expirationOk = false;
    }
    
    // Clean up
    unlink($expiredCacheFile);
} catch (Exception $e) {
    echo "   ❌ Cache expiration test failed: " . $e->getMessage() . "\n";
    $expirationOk = false;
}

// Test 4: Cache key generation
echo "\n4. Cache Key Generation Test:\n";
$keyGenerationOk = true;

try {
    $testKeys = [
        'simple_key',
        'key.with.dots',
        'key:with:colons',
        'key/with/slashes',
        'key with spaces'
    ];
    
    foreach ($testKeys as $key) {
        $hashedKey = md5($key);
        if (strlen($hashedKey) === 32 && ctype_xdigit($hashedKey)) {
            echo "   ✅ Key '{$key}' -> {$hashedKey}\n";
        } else {
            echo "   ❌ Invalid hash for key '{$key}'\n";
            $keyGenerationOk = false;
        }
    }
} catch (Exception $e) {
    echo "   ❌ Key generation test failed: " . $e->getMessage() . "\n";
    $keyGenerationOk = false;
}

// Test 5: Cache size management
echo "\n5. Cache Size Management Test:\n";
$sizeManagementOk = true;

try {
    // Create multiple cache files to test size management
    $testFiles = [];
    for ($i = 0; $i < 5; $i++) {
        $filename = $cacheDir . "/size_test_{$i}.cache";
        $data = str_repeat('x', 1024); // 1KB of data
        file_put_contents($filename, $data);
        $testFiles[] = $filename;
    }
    
    echo "   ✅ Created test cache files\n";
    
    // Calculate total cache size
    $totalSize = 0;
    foreach ($testFiles as $file) {
        $totalSize += filesize($file);
    }
    
    echo "   ✅ Total test cache size: " . round($totalSize / 1024, 2) . " KB\n";
    
    // Clean up test files
    foreach ($testFiles as $file) {
        unlink($file);
    }
    
    echo "   ✅ Cache cleanup successful\n";
} catch (Exception $e) {
    echo "   ❌ Cache size management test failed: " . $e->getMessage() . "\n";
    $sizeManagementOk = false;
}

// Test 6: Concurrent cache access simulation
echo "\n6. Concurrent Cache Access Simulation Test:\n";
$concurrencyOk = true;

try {
    $concurrentFile = $cacheDir . '/concurrent_test.cache';
    
    // Simulate multiple writes
    for ($i = 0; $i < 3; $i++) {
        $data = ['iteration' => $i, 'timestamp' => microtime(true)];
        file_put_contents($concurrentFile, serialize($data));
        usleep(10000); // 10ms delay
    }
    
    echo "   ✅ Concurrent write simulation completed\n";
    
    // Verify final state
    if (file_exists($concurrentFile)) {
        $finalData = unserialize(file_get_contents($concurrentFile));
        if (isset($finalData['iteration']) && $finalData['iteration'] === 2) {
            echo "   ✅ Final cache state is correct\n";
        } else {
            echo "   ❌ Final cache state is incorrect\n";
            $concurrencyOk = false;
        }
        
        unlink($concurrentFile);
    } else {
        echo "   ❌ Concurrent test file not found\n";
        $concurrencyOk = false;
    }
} catch (Exception $e) {
    echo "   ❌ Concurrent access test failed: " . $e->getMessage() . "\n";
    $concurrencyOk = false;
}

echo "\n=== Overall Status ===\n";
if ($fileCacheOk && $expirationOk && $keyGenerationOk && $sizeManagementOk && $concurrencyOk) {
    echo "✅ Cache functionality is working properly!\n";
    echo "ℹ️  File-based caching is operational\n";
    
    if (!$cacheManagerOk) {
        echo "⚠️  CacheManager class needs attention but basic caching works\n";
    }
    
    exit(0);
} else {
    echo "❌ Cache functionality has issues.\n";
    echo "\n🔧 To fix cache issues:\n";
    echo "   1. Ensure cache directory is writable\n";
    echo "   2. Check CacheManager class implementation\n";
    echo "   3. Verify cache configuration\n";
    echo "   4. Test cache operations manually\n";
    exit(1);
}
