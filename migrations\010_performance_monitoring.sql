-- WeBot Performance Monitoring Migration
-- Version: 2.0
-- Date: 2025-01-27
-- Task: TASK-003 Database Schema Optimization - Performance Monitoring

-- ==============================================
-- PERFORMANCE MONITORING TABLES
-- ==============================================

-- Query performance log
CREATE TABLE IF NOT EXISTS query_performance_log (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    query_hash VARCHAR(64) NOT NULL,
    query_type ENUM('SELECT', 'INSERT', 'UPDATE', 'DELETE', 'OTHER') NOT NULL,
    table_name VARCHAR(64) NULL,
    execution_time_ms DECIMAL(10,3) NOT NULL,
    rows_examined BIGINT UNSIGNED DEFAULT 0,
    rows_sent BIGINT UNSIGNED DEFAULT 0,
    query_text TEXT NULL,
    user_id BIGINT UNSIGNED NULL,
    ip_address VARCHAR(45) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_query_hash (query_hash),
    INDEX idx_execution_time (execution_time_ms),
    INDEX idx_created_at (created_at),
    INDEX idx_table_name (table_name),
    INDEX idx_query_type (query_type),
    INDEX idx_slow_queries (execution_time_ms, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Database health metrics
CREATE TABLE IF NOT EXISTS database_health_metrics (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,4) NOT NULL,
    metric_unit VARCHAR(20) NULL,
    table_name VARCHAR(64) NULL,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_metric_name (metric_name),
    INDEX idx_recorded_at (recorded_at),
    INDEX idx_table_metric (table_name, metric_name),
    INDEX idx_metric_time (metric_name, recorded_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Index usage statistics
CREATE TABLE IF NOT EXISTS index_usage_stats (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    table_name VARCHAR(64) NOT NULL,
    index_name VARCHAR(64) NOT NULL,
    usage_count BIGINT UNSIGNED DEFAULT 0,
    last_used_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_table_index (table_name, index_name),
    INDEX idx_usage_count (usage_count),
    INDEX idx_last_used (last_used_at),
    INDEX idx_table_name (table_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ==============================================
-- PERFORMANCE ANALYSIS VIEWS
-- ==============================================

-- View for slow queries analysis
CREATE OR REPLACE VIEW slow_queries_analysis AS
SELECT 
    query_hash,
    query_type,
    table_name,
    COUNT(*) as execution_count,
    AVG(execution_time_ms) as avg_execution_time,
    MAX(execution_time_ms) as max_execution_time,
    MIN(execution_time_ms) as min_execution_time,
    SUM(execution_time_ms) as total_execution_time,
    AVG(rows_examined) as avg_rows_examined,
    AVG(rows_sent) as avg_rows_sent,
    DATE(created_at) as date
FROM query_performance_log 
WHERE execution_time_ms > 100 -- Queries slower than 100ms
GROUP BY query_hash, DATE(created_at)
ORDER BY avg_execution_time DESC;

-- View for table performance summary
CREATE OR REPLACE VIEW table_performance_summary AS
SELECT 
    table_name,
    COUNT(*) as total_queries,
    AVG(execution_time_ms) as avg_execution_time,
    SUM(CASE WHEN execution_time_ms > 1000 THEN 1 ELSE 0 END) as slow_queries,
    SUM(CASE WHEN execution_time_ms > 5000 THEN 1 ELSE 0 END) as very_slow_queries,
    AVG(rows_examined) as avg_rows_examined,
    MAX(execution_time_ms) as max_execution_time,
    DATE(created_at) as date
FROM query_performance_log 
WHERE table_name IS NOT NULL
GROUP BY table_name, DATE(created_at)
ORDER BY avg_execution_time DESC;

-- View for database health overview
CREATE OR REPLACE VIEW database_health_overview AS
SELECT 
    metric_name,
    AVG(metric_value) as avg_value,
    MAX(metric_value) as max_value,
    MIN(metric_value) as min_value,
    metric_unit,
    DATE(recorded_at) as date
FROM database_health_metrics
GROUP BY metric_name, metric_unit, DATE(recorded_at)
ORDER BY date DESC, metric_name;

-- ==============================================
-- STORED PROCEDURES FOR MONITORING
-- ==============================================

DELIMITER $$

-- Procedure to collect database health metrics
CREATE PROCEDURE CollectDatabaseHealthMetrics()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Collect table sizes
    INSERT INTO database_health_metrics (metric_name, metric_value, metric_unit, table_name)
    SELECT 
        'table_size_mb',
        ROUND(((data_length + index_length) / 1024 / 1024), 2),
        'MB',
        table_name
    FROM information_schema.tables 
    WHERE table_schema = DATABASE()
    AND table_type = 'BASE TABLE';
    
    -- Collect index sizes
    INSERT INTO database_health_metrics (metric_name, metric_value, metric_unit, table_name)
    SELECT 
        'index_size_mb',
        ROUND((index_length / 1024 / 1024), 2),
        'MB',
        table_name
    FROM information_schema.tables 
    WHERE table_schema = DATABASE()
    AND table_type = 'BASE TABLE';
    
    -- Collect row counts
    INSERT INTO database_health_metrics (metric_name, metric_value, metric_unit, table_name)
    SELECT 
        'row_count',
        table_rows,
        'rows',
        table_name
    FROM information_schema.tables 
    WHERE table_schema = DATABASE()
    AND table_type = 'BASE TABLE';
    
    -- Collect connection count
    INSERT INTO database_health_metrics (metric_name, metric_value, metric_unit)
    SELECT 
        'active_connections',
        COUNT(*),
        'connections'
    FROM information_schema.processlist;
    
    -- Collect slow query count (last hour)
    INSERT INTO database_health_metrics (metric_name, metric_value, metric_unit)
    SELECT 
        'slow_queries_last_hour',
        COUNT(*),
        'queries'
    FROM query_performance_log 
    WHERE execution_time_ms > 1000 
    AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR);
    
    COMMIT;
END$$

-- Procedure to analyze query performance
CREATE PROCEDURE AnalyzeQueryPerformance(IN days_back INT)
BEGIN
    SELECT 
        'Top 10 Slowest Queries' as analysis_type,
        query_hash,
        query_type,
        table_name,
        COUNT(*) as execution_count,
        ROUND(AVG(execution_time_ms), 2) as avg_execution_time_ms,
        ROUND(MAX(execution_time_ms), 2) as max_execution_time_ms,
        ROUND(SUM(execution_time_ms), 2) as total_execution_time_ms
    FROM query_performance_log 
    WHERE created_at > DATE_SUB(NOW(), INTERVAL days_back DAY)
    GROUP BY query_hash, query_type, table_name
    ORDER BY avg_execution_time_ms DESC
    LIMIT 10;
    
    SELECT 
        'Tables with Most Slow Queries' as analysis_type,
        table_name,
        COUNT(*) as total_queries,
        SUM(CASE WHEN execution_time_ms > 1000 THEN 1 ELSE 0 END) as slow_queries,
        ROUND(AVG(execution_time_ms), 2) as avg_execution_time_ms,
        ROUND((SUM(CASE WHEN execution_time_ms > 1000 THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as slow_query_percentage
    FROM query_performance_log 
    WHERE created_at > DATE_SUB(NOW(), INTERVAL days_back DAY)
    AND table_name IS NOT NULL
    GROUP BY table_name
    HAVING slow_queries > 0
    ORDER BY slow_query_percentage DESC;
END$$

-- Procedure to optimize table maintenance
CREATE PROCEDURE OptimizeTableMaintenance()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE table_name VARCHAR(64);
    DECLARE table_cursor CURSOR FOR 
        SELECT t.table_name 
        FROM information_schema.tables t
        WHERE t.table_schema = DATABASE()
        AND t.table_type = 'BASE TABLE';
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN table_cursor;
    read_loop: LOOP
        FETCH table_cursor INTO table_name;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- Analyze table
        SET @sql = CONCAT('ANALYZE TABLE ', table_name);
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
        -- Optimize table if fragmentation is high
        SET @sql = CONCAT('OPTIMIZE TABLE ', table_name);
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
    END LOOP;
    CLOSE table_cursor;
END$$

-- Procedure to cleanup old performance data
CREATE PROCEDURE CleanupPerformanceData()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Keep only last 30 days of query performance logs
    DELETE FROM query_performance_log 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- Keep only last 90 days of health metrics
    DELETE FROM database_health_metrics 
    WHERE recorded_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
    
    -- Reset index usage stats older than 7 days
    UPDATE index_usage_stats 
    SET usage_count = 0 
    WHERE updated_at < DATE_SUB(NOW(), INTERVAL 7 DAY);
    
    COMMIT;
END$$

DELIMITER ;

-- ==============================================
-- TRIGGERS FOR AUTOMATIC MONITORING
-- ==============================================

-- Note: These triggers would be implemented in application code
-- for better performance, but here's the structure:

/*
-- Trigger to log slow queries (would be implemented in application)
DELIMITER $$
CREATE TRIGGER log_slow_query
    AFTER INSERT ON query_performance_log
    FOR EACH ROW
BEGIN
    IF NEW.execution_time_ms > 1000 THEN
        INSERT INTO slow_query_alerts (
            query_hash, 
            execution_time_ms, 
            table_name, 
            created_at
        ) VALUES (
            NEW.query_hash, 
            NEW.execution_time_ms, 
            NEW.table_name, 
            NEW.created_at
        );
    END IF;
END$$
DELIMITER ;
*/

-- ==============================================
-- SCHEDULED EVENTS FOR MONITORING
-- ==============================================

-- Event to collect health metrics every hour
CREATE EVENT IF NOT EXISTS hourly_health_metrics
ON SCHEDULE EVERY 1 HOUR
STARTS CURRENT_TIMESTAMP
DO CALL CollectDatabaseHealthMetrics();

-- Event to cleanup old performance data daily
CREATE EVENT IF NOT EXISTS daily_performance_cleanup
ON SCHEDULE EVERY 1 DAY
STARTS TIMESTAMP(CURDATE() + INTERVAL 1 DAY, '02:00:00')
DO CALL CleanupPerformanceData();

-- Event to optimize tables weekly
CREATE EVENT IF NOT EXISTS weekly_table_optimization
ON SCHEDULE EVERY 1 WEEK
STARTS TIMESTAMP(CURDATE() + INTERVAL 1 DAY, '01:00:00')
DO CALL OptimizeTableMaintenance();

-- ==============================================
-- PERFORMANCE MONITORING CONFIGURATION
-- ==============================================

-- Enable slow query log
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 1; -- Log queries slower than 1 second
SET GLOBAL log_queries_not_using_indexes = 'ON';

-- Enable performance schema
SET GLOBAL performance_schema = 'ON';

-- Configure InnoDB settings for better performance
SET GLOBAL innodb_buffer_pool_size = **********; -- 1GB (adjust based on available RAM)
SET GLOBAL innodb_log_file_size = 268435456; -- 256MB
SET GLOBAL innodb_flush_log_at_trx_commit = 2; -- Better performance, slight durability trade-off
SET GLOBAL innodb_file_per_table = 'ON';

-- Configure query cache (if using MySQL < 8.0)
-- SET GLOBAL query_cache_type = 'ON';
-- SET GLOBAL query_cache_size = 67108864; -- 64MB

-- ==============================================
-- MONITORING HELPER FUNCTIONS
-- ==============================================

DELIMITER $$

-- Function to get table fragmentation percentage
CREATE FUNCTION GetTableFragmentation(table_name VARCHAR(64))
RETURNS DECIMAL(5,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE fragmentation DECIMAL(5,2) DEFAULT 0;
    
    SELECT 
        ROUND(((data_length + index_length - data_free) / (data_length + index_length)) * 100, 2)
    INTO fragmentation
    FROM information_schema.tables 
    WHERE table_schema = DATABASE()
    AND table_name = table_name;
    
    RETURN COALESCE(fragmentation, 0);
END$$

-- Function to check if index is being used
CREATE FUNCTION IsIndexUsed(table_name VARCHAR(64), index_name VARCHAR(64))
RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE usage_count BIGINT DEFAULT 0;
    
    SELECT COALESCE(usage_count, 0)
    INTO usage_count
    FROM index_usage_stats 
    WHERE table_name = table_name 
    AND index_name = index_name;
    
    RETURN usage_count > 0;
END$$

DELIMITER ;
