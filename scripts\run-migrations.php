#!/usr/bin/env php
<?php
/**
 * WeBot Database Migration Runner
 * 
 * This script runs database migrations in the correct order
 * and provides detailed feedback about the migration process.
 * 
 * Usage:
 *   php scripts/run-migrations.php [--dry-run] [--specific=migration_file]
 * 
 * @package WeBot
 * @version 2.0
 */

declare(strict_types=1);

// Load CLI bootstrap
require_once __DIR__ . '/bootstrap.php';

use PDO;
use PDOException;

/**
 * Parse command line arguments
 */
function parseArguments(array $argv): array
{
    $options = [
        'dry_run' => false,
        'specific' => null,
        'help' => false,
        'verbose' => false,
        'force' => false,
    ];
    
    for ($i = 1; $i < count($argv); $i++) {
        $arg = $argv[$i];
        
        if ($arg === '--help' || $arg === '-h') {
            $options['help'] = true;
        } elseif ($arg === '--dry-run') {
            $options['dry_run'] = true;
        } elseif ($arg === '--verbose' || $arg === '-v') {
            $options['verbose'] = true;
        } elseif ($arg === '--force') {
            $options['force'] = true;
        } elseif (strpos($arg, '--specific=') === 0) {
            $options['specific'] = substr($arg, 11);
        }
    }
    
    return $options;
}

/**
 * Show help message
 */
function showHelp(): void
{
    echo "WeBot Database Migration Runner\n";
    echo "==============================\n\n";
    echo "Usage: php scripts/run-migrations.php [options]\n\n";
    echo "Options:\n";
    echo "  --dry-run              Show what would be executed without running\n";
    echo "  --specific=FILE        Run only specific migration file\n";
    echo "  --verbose, -v          Show verbose output\n";
    echo "  --force                Force run even if already executed\n";
    echo "  --help, -h             Show this help message\n\n";
    echo "Examples:\n";
    echo "  php scripts/run-migrations.php\n";
    echo "  php scripts/run-migrations.php --dry-run\n";
    echo "  php scripts/run-migrations.php --specific=009_advanced_database_optimization.sql\n";
    echo "  php scripts/run-migrations.php --verbose\n\n";
}

/**
 * Get database connection
 */
function getDatabaseConnection(): PDO
{
    $host = $_ENV['DB_HOST'] ?? 'localhost';
    $port = $_ENV['DB_PORT'] ?? '3306';
    $database = $_ENV['DB_DATABASE'] ?? 'webot';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";
    
    try {
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]);
        
        return $pdo;
    } catch (PDOException $e) {
        throw new Exception("Database connection failed: " . $e->getMessage());
    }
}

/**
 * Create migrations table if not exists
 */
function createMigrationsTable(PDO $pdo): void
{
    $sql = "
        CREATE TABLE IF NOT EXISTS migrations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            migration VARCHAR(255) NOT NULL UNIQUE,
            executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            execution_time_ms INT DEFAULT 0,
            checksum VARCHAR(64) NULL,
            INDEX idx_migration (migration),
            INDEX idx_executed_at (executed_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($sql);
}

/**
 * Get list of migration files
 */
function getMigrationFiles(string $migrationsDir, ?string $specific = null): array
{
    if ($specific) {
        $specificPath = $migrationsDir . '/' . $specific;
        if (!file_exists($specificPath)) {
            throw new Exception("Specific migration file not found: {$specific}");
        }
        return [$specific];
    }
    
    $files = glob($migrationsDir . '/*.sql');
    $migrations = [];
    
    foreach ($files as $file) {
        $migrations[] = basename($file);
    }
    
    sort($migrations);
    return $migrations;
}

/**
 * Check if migration was already executed
 */
function isMigrationExecuted(PDO $pdo, string $migration): bool
{
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM migrations WHERE migration = ?");
    $stmt->execute([$migration]);
    return $stmt->fetchColumn() > 0;
}

/**
 * Calculate file checksum
 */
function calculateChecksum(string $filePath): string
{
    return hash('sha256', file_get_contents($filePath));
}

/**
 * Execute migration file
 */
function executeMigration(PDO $pdo, string $filePath, string $migration, bool $dryRun = false, bool $verbose = false): array
{
    $content = file_get_contents($filePath);
    $checksum = calculateChecksum($filePath);
    
    if ($dryRun) {
        echo "Would execute migration: {$migration}\n";
        if ($verbose) {
            echo "File: {$filePath}\n";
            echo "Checksum: {$checksum}\n";
            echo "Content preview:\n" . substr($content, 0, 200) . "...\n\n";
        }
        return ['success' => true, 'time' => 0, 'dry_run' => true];
    }
    
    $startTime = microtime(true);
    
    try {
        $pdo->beginTransaction();
        
        // Split content by delimiter for stored procedures
        $statements = preg_split('/;\s*$/m', $content);
        $executedStatements = 0;
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (empty($statement) || strpos($statement, '--') === 0) {
                continue;
            }
            
            // Handle DELIMITER statements
            if (stripos($statement, 'DELIMITER') === 0) {
                continue;
            }
            
            $pdo->exec($statement);
            $executedStatements++;
        }
        
        $executionTime = round((microtime(true) - $startTime) * 1000);
        
        // Record migration
        $stmt = $pdo->prepare("
            INSERT INTO migrations (migration, execution_time_ms, checksum) 
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE 
                executed_at = CURRENT_TIMESTAMP,
                execution_time_ms = VALUES(execution_time_ms),
                checksum = VALUES(checksum)
        ");
        $stmt->execute([$migration, $executionTime, $checksum]);
        
        $pdo->commit();
        
        return [
            'success' => true, 
            'time' => $executionTime, 
            'statements' => $executedStatements
        ];
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw new Exception("Migration failed: " . $e->getMessage());
    }
}

/**
 * Main migration process
 */
function main(array $argv): int
{
    $options = parseArguments($argv);
    
    if ($options['help']) {
        showHelp();
        return 0;
    }
    
    try {
        echo "🚀 WeBot Database Migration Runner\n";
        echo "==================================\n\n";
        
        // Get database connection
        $pdo = getDatabaseConnection();
        echo "✅ Database connection established\n";
        
        // Create migrations table
        createMigrationsTable($pdo);
        echo "✅ Migrations table ready\n\n";
        
        // Get migration files
        $migrationsDir = __DIR__ . '/../migrations';
        $migrations = getMigrationFiles($migrationsDir, $options['specific']);
        
        if (empty($migrations)) {
            echo "ℹ️  No migration files found\n";
            return 0;
        }
        
        echo "📁 Found " . count($migrations) . " migration file(s)\n\n";
        
        $executed = 0;
        $skipped = 0;
        $totalTime = 0;
        
        foreach ($migrations as $migration) {
            $filePath = $migrationsDir . '/' . $migration;
            
            // Check if already executed
            if (!$options['force'] && isMigrationExecuted($pdo, $migration)) {
                echo "⏭️  Skipping {$migration} (already executed)\n";
                $skipped++;
                continue;
            }
            
            echo "🔄 Executing {$migration}...";
            
            try {
                $result = executeMigration(
                    $pdo, 
                    $filePath, 
                    $migration, 
                    $options['dry_run'], 
                    $options['verbose']
                );
                
                if ($result['dry_run'] ?? false) {
                    echo " [DRY RUN]\n";
                } else {
                    echo " ✅ ({$result['time']}ms";
                    if (isset($result['statements'])) {
                        echo ", {$result['statements']} statements";
                    }
                    echo ")\n";
                    $totalTime += $result['time'];
                }
                
                $executed++;
                
            } catch (Exception $e) {
                echo " ❌ FAILED\n";
                echo "Error: " . $e->getMessage() . "\n";
                return 1;
            }
        }
        
        echo "\n📊 Migration Summary:\n";
        echo "   Executed: {$executed}\n";
        echo "   Skipped: {$skipped}\n";
        echo "   Total time: {$totalTime}ms\n";
        
        if ($options['dry_run']) {
            echo "\n⚠️  This was a dry run. No changes were made to the database.\n";
        } else {
            echo "\n🎉 All migrations completed successfully!\n";
        }
        
        return 0;
        
    } catch (Exception $e) {
        echo "❌ Migration failed: " . $e->getMessage() . "\n";
        return 1;
    }
}

/**
 * Show migration status
 */
function showMigrationStatus(PDO $pdo): void
{
    echo "📋 Migration Status:\n";
    echo "===================\n";

    $stmt = $pdo->query("
        SELECT
            migration,
            executed_at,
            execution_time_ms,
            checksum
        FROM migrations
        ORDER BY executed_at DESC
        LIMIT 10
    ");

    $migrations = $stmt->fetchAll();

    if (empty($migrations)) {
        echo "No migrations executed yet.\n";
        return;
    }

    foreach ($migrations as $migration) {
        echo sprintf(
            "✅ %s (%s, %dms)\n",
            $migration['migration'],
            $migration['executed_at'],
            $migration['execution_time_ms']
        );
    }
}

// Run the migration
try {
    $exitCode = main($argv);
    exit($exitCode);
} catch (Exception $e) {
    echo "❌ Fatal error: " . $e->getMessage() . "\n";
    exit(1);
}
