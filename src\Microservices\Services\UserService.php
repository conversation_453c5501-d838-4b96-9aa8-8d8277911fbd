<?php

declare(strict_types=1);

namespace WeBot\Microservices\Services;

use WeBot\Services\DatabaseService;
use WeBot\Core\CacheManager;
use WeBot\Utils\Logger;
use WeBot\Exceptions\WeBotException;
use WeBot\Models\User;
use WeBot\Repositories\UserRepository;

/**
 * User Microservice
 *
 * Handles all user-related operations including authentication,
 * registration, profile management, and user data operations.
 *
 * @package WeBot\Microservices\Services
 * @version 2.0
 */
class UserService
{
    private DatabaseService $database;
    private CacheManager $cache;
    private Logger $logger;
    private UserRepository $userRepository;
    private array $config;

    public function __construct(
        DatabaseService $database,
        CacheManager $cache,
        UserRepository $userRepository,
        array $config = []
    ) {
        $this->database = $database;
        $this->cache = $cache;
        $this->logger = Logger::getInstance();
        $this->userRepository = $userRepository;
        $this->config = array_merge($this->getDefaultConfig(), $config);
    }

    /**
     * Handle service requests
     */
    public function handleRequest(array $request): array
    {
        $action = $request['action'] ?? '';
        $data = $request['data'] ?? [];

        try {
            return match ($action) {
                'authenticate' => $this->authenticate($data),
                'register' => $this->registerUser($data),
                'get_user' => $this->getUser($data),
                'update_user' => $this->updateUser($data),
                'delete_user' => $this->deleteUser($data),
                'get_user_stats' => $this->getUserStats($data),
                'search_users' => $this->searchUsers($data),
                'ban_user' => $this->banUser($data),
                'unban_user' => $this->unbanUser($data),
                'update_wallet' => $this->updateWallet($data),
                'get_user_activity' => $this->getUserActivity($data),
                'health' => $this->healthCheck(),
                default => throw new WeBotException("Unknown action: {$action}")
            };
        } catch (\Exception $e) {
            $this->logger->error("User service error", [
                'action' => $action,
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            throw $e;
        }
    }

    /**
     * Authenticate user
     */
    public function authenticate(array $data): array
    {
        $userId = $data['user_id'] ?? null;
        $token = $data['token'] ?? null;

        if (!$userId) {
            throw new WeBotException("User ID is required");
        }

        // Check cache first
        $cacheKey = "user_auth:{$userId}";
        $cached = $this->cache->get($cacheKey);

        if ($cached !== null) {
            return [
                'success' => true,
                'user' => $cached,
                'from_cache' => true
            ];
        }

        // Get user from database
        $user = $this->userRepository->findById($userId);

        if (!$user) {
            throw new WeBotException("User not found", 404);
        }

        // Check if user is banned
        if ($user->banned) {
            throw new WeBotException("User is banned", 403);
        }

        // Update last activity
        $this->userRepository->updateLastActivity($userId);

        // Cache user data
        $this->cache->set($cacheKey, $user, $this->config['user_cache_ttl']);

        return [
            'success' => true,
            'user' => $user,
            'from_cache' => false
        ];
    }

    /**
     * Register new user
     */
    public function registerUser(array $data): array
    {
        $requiredFields = ['userid', 'first_name'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                throw new WeBotException("Field {$field} is required");
            }
        }

        // Check if user already exists
        $existingUser = $this->userRepository->findById($data['userid']);
        if ($existingUser) {
            throw new WeBotException("User already exists", 409);
        }

        // Prepare user data
        $userData = [
            'userid' => $data['userid'],
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'] ?? '',
            'username' => $data['username'] ?? null,
            'phone' => $data['phone'] ?? null,
            'step' => 'main',
            'temp' => '',
            'wallet' => 0,
            'isAdmin' => false,
            'banned' => false,
            'referrer_id' => $data['referrer_id'] ?? null,
            'total_spent' => 0,
            'last_activity' => date('Y-m-d H:i:s')
        ];

        // Create user
        $createdUser = $this->userRepository->create($userData);

        // Get created user
        $user = $this->userRepository->findById($createdUser->id);

        // Cache user data
        $cacheKey = "user_auth:{$createdUser->id}";
        $this->cache->set($cacheKey, $user, $this->config['user_cache_ttl']);

        $this->logger->info("User registered", [
            'user_id' => $createdUser->id,
            'username' => $userData['username']
        ]);

        return [
            'success' => true,
            'user' => $user,
            'message' => 'User registered successfully'
        ];
    }

    /**
     * Get user by ID
     */
    public function getUser(array $data): array
    {
        $userId = $data['user_id'] ?? null;

        if (!$userId) {
            throw new WeBotException("User ID is required");
        }

        // Check cache first
        $cacheKey = "user_data:{$userId}";
        $cached = $this->cache->get($cacheKey);

        if ($cached !== null) {
            return [
                'success' => true,
                'user' => $cached,
                'from_cache' => true
            ];
        }

        $user = $this->userRepository->findById($userId);

        if (!$user) {
            throw new WeBotException("User not found", 404);
        }

        // Cache user data
        $this->cache->set($cacheKey, $user, $this->config['user_cache_ttl']);

        return [
            'success' => true,
            'user' => $user,
            'from_cache' => false
        ];
    }

    /**
     * Update user
     */
    public function updateUser(array $data): array
    {
        $userId = $data['user_id'] ?? null;
        $updateData = $data['update_data'] ?? [];

        if (!$userId) {
            throw new WeBotException("User ID is required");
        }

        if (empty($updateData)) {
            throw new WeBotException("Update data is required");
        }

        // Check if user exists
        $user = $this->userRepository->findById($userId);
        if (!$user) {
            throw new WeBotException("User not found", 404);
        }

        // Update user
        $updated = $this->userRepository->update($userId, $updateData);

        if (!$updated) {
            throw new WeBotException("Failed to update user");
        }

        // Clear cache
        $this->clearUserCache($userId);

        // Get updated user
        $updatedUser = $this->userRepository->findById($userId);

        $this->logger->info("User updated", [
            'user_id' => $userId,
            'updated_fields' => array_keys($updateData)
        ]);

        return [
            'success' => true,
            'user' => $updatedUser,
            'message' => 'User updated successfully'
        ];
    }

    /**
     * Delete user
     */
    public function deleteUser(array $data): array
    {
        $userId = $data['user_id'] ?? null;

        if (!$userId) {
            throw new WeBotException("User ID is required");
        }

        // Check if user exists
        $user = $this->userRepository->findById($userId);
        if (!$user) {
            throw new WeBotException("User not found", 404);
        }

        // Delete user
        $deleted = $this->userRepository->delete($userId);

        if (!$deleted) {
            throw new WeBotException("Failed to delete user");
        }

        // Clear cache
        $this->clearUserCache($userId);

        $this->logger->info("User deleted", [
            'user_id' => $userId
        ]);

        return [
            'success' => true,
            'message' => 'User deleted successfully'
        ];
    }

    /**
     * Get user statistics
     */
    public function getUserStats(array $data): array
    {
        $userId = $data['user_id'] ?? null;

        if (!$userId) {
            throw new WeBotException("User ID is required");
        }

        $cacheKey = "user_stats:{$userId}";
        $cached = $this->cache->get($cacheKey);

        if ($cached !== null) {
            return [
                'success' => true,
                'stats' => $cached,
                'from_cache' => true
            ];
        }

        $stats = $this->userRepository->getUserStats($userId);

        // Cache stats
        $this->cache->set($cacheKey, $stats, $this->config['stats_cache_ttl']);

        return [
            'success' => true,
            'stats' => $stats,
            'from_cache' => false
        ];
    }

    /**
     * Search users
     */
    public function searchUsers(array $data): array
    {
        $query = $data['query'] ?? '';
        $limit = $data['limit'] ?? 50;
        $offset = $data['offset'] ?? 0;

        if (strlen($query) < 2) {
            throw new WeBotException("Query must be at least 2 characters");
        }

        $users = $this->userRepository->search($query, $limit, $offset);

        return [
            'success' => true,
            'users' => $users,
            'query' => $query,
            'limit' => $limit,
            'offset' => $offset
        ];
    }

    /**
     * Ban user
     */
    public function banUser(array $data): array
    {
        $userId = $data['user_id'] ?? null;
        $reason = $data['reason'] ?? 'No reason provided';

        if (!$userId) {
            throw new WeBotException("User ID is required");
        }

        $updated = $this->userRepository->update($userId, ['banned' => true]);

        if (!$updated) {
            throw new WeBotException("Failed to ban user");
        }

        // Clear cache
        $this->clearUserCache($userId);

        $this->logger->warning("User banned", [
            'user_id' => $userId,
            'reason' => $reason
        ]);

        return [
            'success' => true,
            'message' => 'User banned successfully'
        ];
    }

    /**
     * Unban user
     */
    public function unbanUser(array $data): array
    {
        $userId = $data['user_id'] ?? null;

        if (!$userId) {
            throw new WeBotException("User ID is required");
        }

        $updated = $this->userRepository->update($userId, ['banned' => false]);

        if (!$updated) {
            throw new WeBotException("Failed to unban user");
        }

        // Clear cache
        $this->clearUserCache($userId);

        $this->logger->info("User unbanned", [
            'user_id' => $userId
        ]);

        return [
            'success' => true,
            'message' => 'User unbanned successfully'
        ];
    }

    /**
     * Update user wallet
     */
    public function updateWallet(array $data): array
    {
        $userId = $data['user_id'] ?? null;
        $amount = $data['amount'] ?? null;
        $operation = $data['operation'] ?? 'add'; // add, subtract, set

        if (!$userId || $amount === null) {
            throw new WeBotException("User ID and amount are required");
        }

        $user = $this->userRepository->findById($userId);
        if (!$user) {
            throw new WeBotException("User not found", 404);
        }

        $currentWallet = $user->balance ?? 0;

        $newWallet = match ($operation) {
            'add' => $currentWallet + $amount,
            'subtract' => $currentWallet - $amount,
            'set' => $amount,
            default => throw new WeBotException("Invalid operation")
        };

        // Ensure wallet doesn't go negative
        if ($newWallet < 0) {
            throw new WeBotException("Insufficient wallet balance");
        }

        $updated = $this->userRepository->update($userId, ['balance' => $newWallet]);

        if (!$updated) {
            throw new WeBotException("Failed to update wallet");
        }

        // Clear cache
        $this->clearUserCache($userId);

        $this->logger->info("Wallet updated", [
            'user_id' => $userId,
            'operation' => $operation,
            'amount' => $amount,
            'old_balance' => $currentWallet,
            'new_balance' => $newWallet
        ]);

        return [
            'success' => true,
            'old_balance' => $currentWallet,
            'new_balance' => $newWallet,
            'message' => 'Wallet updated successfully'
        ];
    }

    /**
     * Get user activity
     */
    public function getUserActivity(array $data): array
    {
        $userId = $data['user_id'] ?? null;
        $limit = $data['limit'] ?? 30;

        if (!$userId) {
            throw new WeBotException("User ID is required");
        }

        $activity = $this->userRepository->getUserActivity($userId, $limit);

        return [
            'success' => true,
            'activity' => $activity,
            'user_id' => $userId
        ];
    }

    /**
     * Health check
     */
    public function healthCheck(): array
    {
        try {
            // Test database connection
            $this->database->query("SELECT 1");

            // Test cache connection
            $this->cache->set('health_check', time(), 10);
            $this->cache->get('health_check');

            return [
                'success' => true,
                'status' => 'healthy',
                'service' => 'user-service',
                'timestamp' => time(),
                'version' => '1.0.0'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'status' => 'unhealthy',
                'service' => 'user-service',
                'error' => $e->getMessage(),
                'timestamp' => time()
            ];
        }
    }

    /**
     * Clear user cache
     */
    private function clearUserCache(int $userId): void
    {
        $keys = [
            "user_auth:{$userId}",
            "user_data:{$userId}",
            "user_stats:{$userId}"
        ];

        foreach ($keys as $key) {
            $this->cache->delete($key);
        }
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'user_cache_ttl' => 3600,      // 1 hour
            'stats_cache_ttl' => 1800,     // 30 minutes
            'max_search_results' => 100,
            'min_query_length' => 2
        ];
    }
}
