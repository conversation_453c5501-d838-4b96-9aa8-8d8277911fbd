<?php

declare(strict_types=1);

namespace WeBot\Core;

/**
 * Cache Manager
 *
 * Comprehensive caching solution with Redis backend,
 * supporting multiple cache strategies and automatic invalidation.
 *
 * @package WeBot\Core
 * @version 2.0
 */
class CacheManager
{
    private $redis;
    private string $prefix;
    private int $defaultTtl;
    private array $tags = [];
    private bool $enabled;

    public function __construct(array $config = [])
    {
        $this->prefix = $config['prefix'] ?? 'webot:';
        $this->defaultTtl = $config['default_ttl'] ?? 3600;
        $this->enabled = $config['enabled'] ?? true;

        $this->initializeRedis($config);
    }

    /**
     * Get cached value
     */
    public function get(string $key, $default = null)
    {
        if (!$this->enabled) {
            return $default;
        }

        try {
            $value = $this->redis->get($this->makeKey($key));

            if ($value === false) {
                return $default;
            }

            return $this->unserialize($value);
        } catch (\Exception $e) {
            $this->handleError('get', $e);
            return $default;
        }
    }

    /**
     * Set cached value
     */
    public function set(string $key, $value, ?int $ttl = null): bool
    {
        if (!$this->enabled) {
            return false;
        }

        try {
            $ttl ??= $this->defaultTtl;
            $serialized = $this->serialize($value);

            $result = $this->redis->setex($this->makeKey($key), $ttl, $serialized);

            // Store tags if any
            if (!empty($this->tags)) {
                $this->storeTags($key, $this->tags);
                $this->tags = [];
            }

            return $result;
        } catch (\Exception $e) {
            $this->handleError('set', $e);
            return false;
        }
    }

    /**
     * Remember cached value (get or set)
     */
    public function remember(string $key, callable $callback, ?int $ttl = null)
    {
        $value = $this->get($key);

        if ($value !== null) {
            return $value;
        }

        $value = $callback();
        $this->set($key, $value, $ttl);

        return $value;
    }

    /**
     * Delete cached value
     */
    public function delete(string $key): bool
    {
        if (!$this->enabled) {
            return false;
        }

        try {
            $result = $this->redis->del($this->makeKey($key));
            $this->deleteTags($key);

            return $result > 0;
        } catch (\Exception $e) {
            $this->handleError('delete', $e);
            return false;
        }
    }

    /**
     * Check if key exists
     */
    public function exists(string $key): bool
    {
        if (!$this->enabled) {
            return false;
        }

        try {
            return $this->redis->exists($this->makeKey($key)) > 0;
        } catch (\Exception $e) {
            $this->handleError('exists', $e);
            return false;
        }
    }

    /**
     * Increment value
     */
    public function increment(string $key, int $value = 1): int
    {
        if (!$this->enabled) {
            return 0;
        }

        try {
            return $this->redis->incrBy($this->makeKey($key), $value);
        } catch (\Exception $e) {
            $this->handleError('increment', $e);
            return 0;
        }
    }

    /**
     * Decrement value
     */
    public function decrement(string $key, int $value = 1): int
    {
        if (!$this->enabled) {
            return 0;
        }

        try {
            return $this->redis->decrBy($this->makeKey($key), $value);
        } catch (\Exception $e) {
            $this->handleError('decrement', $e);
            return 0;
        }
    }

    /**
     * Set multiple values
     */
    public function setMultiple(array $values, ?int $ttl = null): bool
    {
        if (!$this->enabled) {
            return false;
        }

        try {
            $pipeline = $this->redis->multi();

            foreach ($values as $key => $value) {
                $serialized = $this->serialize($value);
                $pipeline->setex($this->makeKey($key), $ttl ?? $this->defaultTtl, $serialized);
            }

            $results = $pipeline->exec();
            return !in_array(false, $results, true);
        } catch (\Exception $e) {
            $this->handleError('setMultiple', $e);
            return false;
        }
    }

    /**
     * Get multiple values
     */
    public function getMultiple(array $keys, $default = null): array
    {
        if (!$this->enabled) {
            return array_fill_keys($keys, $default);
        }

        try {
            $prefixedKeys = array_map([$this, 'makeKey'], $keys);
            $values = $this->redis->mget($prefixedKeys);

            $result = [];
            foreach ($keys as $index => $key) {
                $value = $values[$index];
                $result[$key] = $value !== false ? $this->unserialize($value) : $default;
            }

            return $result;
        } catch (\Exception $e) {
            $this->handleError('getMultiple', $e);
            return array_fill_keys($keys, $default);
        }
    }

    /**
     * Tag cache entries
     */
    public function tags(array $tags): self
    {
        $this->tags = $tags;
        return $this;
    }

    /**
     * Flush cache by tags
     */
    public function flushTags(array $tags): bool
    {
        if (!$this->enabled) {
            return false;
        }

        try {
            $keys = [];

            foreach ($tags as $tag) {
                $tagKeys = $this->redis->sMembers($this->makeTagKey($tag));
                $keys = array_merge($keys, $tagKeys);
            }

            if (!empty($keys)) {
                $this->redis->del($keys);

                // Remove tag sets
                foreach ($tags as $tag) {
                    $this->redis->del($this->makeTagKey($tag));
                }
            }

            return true;
        } catch (\Exception $e) {
            $this->handleError('flushTags', $e);
            return false;
        }
    }

    /**
     * Clear all cache
     */
    public function flush(): bool
    {
        if (!$this->enabled) {
            return false;
        }

        try {
            $keys = $this->redis->keys("{$this->prefix}*");

            if (!empty($keys)) {
                return $this->redis->del($keys) > 0;
            }

            return true;
        } catch (\Exception $e) {
            $this->handleError('flush', $e);
            return false;
        }
    }

    /**
     * Get keys matching pattern
     */
    public function getKeysByPattern(string $pattern): array
    {
        if (!$this->enabled) {
            return [];
        }

        try {
            $keys = $this->redis->keys("{$this->prefix}{$pattern}");

            // Remove prefix from keys
            return array_map(function ($key) {
                return str_replace($this->prefix, '', $key);
            }, $keys ?: []);
        } catch (\Exception $e) {
            $this->handleError('getKeysByPattern', $e);
            return [];
        }
    }

    /**
     * Delete keys matching pattern
     */
    public function deletePattern(string $pattern): bool
    {
        if (!$this->enabled) {
            return false;
        }

        try {
            $keys = $this->redis->keys("{$this->prefix}{$pattern}");

            if (!empty($keys)) {
                return $this->redis->del($keys) > 0;
            }

            return true;
        } catch (\Exception $e) {
            $this->handleError('deletePattern', $e);
            return false;
        }
    }

    /**
     * Set expiration time for a key
     */
    public function expire(string $key, int $seconds): bool
    {
        if (!$this->enabled) {
            return false;
        }

        try {
            return $this->redis->expire($this->makeKey($key), $seconds);
        } catch (\Exception $e) {
            $this->handleError('expire', $e);
            return false;
        }
    }



    /**
     * Cache user data
     */
    public function cacheUser(int $userId, array $userData, int $ttl = 1800): bool
    {
        return $this->tags(['users', "user:{$userId}"])
                   ->set("user:{$userId}", $userData, $ttl);
    }

    /**
     * Get cached user data
     */
    public function getCachedUser(int $userId): ?array
    {
        return $this->get("user:{$userId}");
    }

    /**
     * Cache API response
     */
    public function cacheApiResponse(string $endpoint, array $params, $response, int $ttl = 300): bool
    {
        $key = $this->makeApiKey($endpoint, $params);
        return $this->tags(['api', 'responses'])
                   ->set($key, $response, $ttl);
    }

    /**
     * Get cached API response
     */
    public function getCachedApiResponse(string $endpoint, array $params)
    {
        $key = $this->makeApiKey($endpoint, $params);
        return $this->get($key);
    }

    /**
     * Cache database query result
     */
    public function cacheQuery(string $sql, array $params, $result, int $ttl = 300): bool
    {
        $key = $this->makeQueryKey($sql, $params);
        return $this->tags(['queries', 'database'])
                   ->set($key, $result, $ttl);
    }

    /**
     * Get cached query result
     */
    public function getCachedQuery(string $sql, array $params)
    {
        $key = $this->makeQueryKey($sql, $params);
        return $this->get($key);
    }

    /**
     * Cache session data
     */
    public function cacheSession(string $sessionId, array $data, int $ttl = 1800): bool
    {
        return $this->tags(['sessions'])
                   ->set("session:{$sessionId}", $data, $ttl);
    }

    /**
     * Get cached session data
     */
    public function getCachedSession(string $sessionId): ?array
    {
        return $this->get("session:{$sessionId}");
    }

    /**
     * Cache configuration data
     */
    public function cacheConfig(string $key, $value, int $ttl = 3600): bool
    {
        return $this->tags(['config'])
                   ->set("config:{$key}", $value, $ttl);
    }

    /**
     * Get cached configuration
     */
    public function getCachedConfig(string $key)
    {
        return $this->get("config:{$key}");
    }

    /**
     * Warm up cache with frequently accessed data
     */
    public function warmUp(): array
    {
        $results = [];

        try {
            // Warm up user data for active users
            $results['users'] = $this->warmUpUsers();

            // Warm up configuration data
            $results['config'] = $this->warmUpConfig();

            // Warm up frequently accessed queries
            $results['queries'] = $this->warmUpQueries();
        } catch (\Exception $e) {
            $results['error'] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Get cache statistics
     */
    public function getStats(): array
    {
        if (!$this->enabled || !$this->redis) {
            return ['enabled' => false];
        }

        try {
            $info = $this->redis->info();

            return [
                'enabled' => true,
                'connected' => $this->redis->ping() === '+PONG',
                'memory_usage' => $info['used_memory_human'] ?? 'unknown',
                'total_keys' => $this->redis->dbSize(),
                'hit_ratio' => $this->calculateHitRatio($info),
                'uptime' => $info['uptime_in_seconds'] ?? 0,
                'version' => $info['redis_version'] ?? 'unknown'
            ];
        } catch (\Exception $e) {
            return [
                'enabled' => true,
                'connected' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Optimize cache performance
     */
    public function optimize(): array
    {
        $results = [];

        try {
            // Clean expired keys
            $results['cleanup'] = $this->cleanupExpiredKeys();

            // Optimize memory usage
            $results['memory'] = $this->optimizeMemory();

            // Update cache statistics
            $results['stats'] = $this->getStats();
        } catch (\Exception $e) {
            $results['error'] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Cache panel data
     */
    public function cachePanelData(int $serverId, string $type, $data, int $ttl = 600): bool
    {
        $key = "panel:{$serverId}:{$type}";
        return $this->tags(['panels', "server:{$serverId}"])
                   ->set($key, $data, $ttl);
    }

    /**
     * Get cached panel data
     */
    public function getCachedPanelData(int $serverId, string $type)
    {
        return $this->get("panel:{$serverId}:{$type}");
    }

    /**
     * Rate limiting
     */
    public function rateLimit(string $key, int $maxAttempts, int $windowSeconds): bool
    {
        $rateLimitKey = "rate_limit:{$key}";
        $current = $this->increment($rateLimitKey);

        if ($current === 1) {
            $this->redis->expire($this->makeKey($rateLimitKey), $windowSeconds);
        }

        return $current <= $maxAttempts;
    }

    /**
     * Session management
     */
    public function setSession(string $sessionId, array $data, int $ttl = 7200): bool
    {
        return $this->set("session:{$sessionId}", $data, $ttl);
    }

    /**
     * Get session data
     */
    public function getSession(string $sessionId): ?array
    {
        return $this->get("session:{$sessionId}");
    }

    /**
     * Delete session
     */
    public function deleteSession(string $sessionId): bool
    {
        return $this->delete("session:{$sessionId}");
    }

    /**
     * Initialize Redis connection
     */
    private function initializeRedis(array $config): void
    {
        if (!$this->enabled) {
            return;
        }

        try {
            $this->redis = new \Redis();

            $host = $config['host'] ?? 'redis';
            $port = $config['port'] ?? 6379;
            $password = $config['password'] ?? null;
            $database = $config['database'] ?? 0;

            $this->redis->connect($host, $port);

            if ($password) {
                $this->redis->auth($password);
            }

            $this->redis->select($database);

            // Set serialization options
            if (class_exists('Redis')) {
                $this->redis->setOption(\Redis::OPT_SERIALIZER, \Redis::SERIALIZER_JSON);
                $this->redis->setOption(\Redis::OPT_PREFIX, $this->prefix);
            }
        } catch (\Exception $e) {
            $this->enabled = false;
            error_log("Redis connection failed: " . $e->getMessage());
        }
    }

    /**
     * Make cache key
     */
    private function makeKey(string $key): string
    {
        return "{$this->prefix}{$key}";
    }

    /**
     * Make tag key
     */
    private function makeTagKey(string $tag): string
    {
        return $this->prefix . "tag:{$tag}";
    }

    /**
     * Make API cache key
     */
    private function makeApiKey(string $endpoint, array $params): string
    {
        ksort($params);
        $paramString = http_build_query($params);
        return "api:" . md5("{$endpoint}{$paramString}");
    }

    /**
     * Make query cache key
     */
    private function makeQueryKey(string $sql, array $params): string
    {
        $normalizedSql = preg_replace('/\s+/', ' ', trim($sql));
        $paramString = serialize($params);
        return "query:" . md5($normalizedSql . $paramString);
    }

    /**
     * Warm up user cache
     */
    private function warmUpUsers(): array
    {
        // This would typically fetch active users from database
        // For now, return placeholder
        return ['warmed_users' => 0, 'status' => 'placeholder'];
    }

    /**
     * Warm up config cache
     */
    private function warmUpConfig(): array
    {
        // This would typically fetch configuration from database
        // For now, return placeholder
        return ['warmed_configs' => 0, 'status' => 'placeholder'];
    }

    /**
     * Warm up query cache
     */
    private function warmUpQueries(): array
    {
        // This would typically execute frequently used queries
        // For now, return placeholder
        return ['warmed_queries' => 0, 'status' => 'placeholder'];
    }

    /**
     * Calculate cache hit ratio
     */
    private function calculateHitRatio(array $info): float
    {
        $hits = (int)($info['keyspace_hits'] ?? 0);
        $misses = (int)($info['keyspace_misses'] ?? 0);
        $total = $hits + $misses;

        return $total > 0 ? ($hits / $total) * 100 : 0;
    }

    /**
     * Clean up expired keys
     */
    private function cleanupExpiredKeys(): array
    {
        try {
            $cleaned = 0;
            $cursor = 0;
            do {
                $result = $this->redis->scan($cursor, $this->prefix . '*', 100);
                $cursor = $result[0];
                $keys = $result[1];

                foreach ($keys as $key) {
                    $ttl = $this->redis->ttl($key);
                    if ($ttl === -1) { // No expiration set
                        // Set default expiration for keys without TTL
                        $this->redis->expire($key, $this->defaultTtl);
                        $cleaned++;
                    }
                }
            } while ($cursor !== 0);

            return [
                'keys_processed' => $cleaned,
                'status' => 'completed'
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Optimize memory usage
     */
    private function optimizeMemory(): array
    {
        try {
            // Get memory info before optimization
            $info = $this->redis->info('memory');
            $memoryBefore = $info['used_memory'] ?? 0;

            // Run memory optimization if available
            try {
                $this->redis->eval("return redis.call('MEMORY', 'PURGE')", []);
            } catch (\Exception $e) {
                // MEMORY PURGE might not be available in all Redis versions
            }

            // Get memory info after optimization
            $info = $this->redis->info('memory');
            $memoryAfter = $info['used_memory'] ?? 0;

            return [
                'memory_before' => $memoryBefore,
                'memory_after' => $memoryAfter,
                'memory_saved' => $memoryBefore - $memoryAfter,
                'fragmentation_ratio' => $info['mem_fragmentation_ratio'] ?? 1.0
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Store tags for a key
     */
    private function storeTags(string $key, array $tags): void
    {
        $fullKey = $this->makeKey($key);

        foreach ($tags as $tag) {
            $this->redis->sAdd($this->makeTagKey($tag), $fullKey);
        }
    }

    /**
     * Delete tags for a key
     */
    private function deleteTags(string $key): void
    {
        $fullKey = $this->makeKey($key);
        $tagKeys = $this->redis->keys($this->makeTagKey('*'));

        foreach ($tagKeys as $tagKey) {
            $this->redis->sRem($tagKey, $fullKey);
        }
    }

    /**
     * Serialize value
     */
    private function serialize($value): string
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    /**
     * Unserialize value
     */
    private function unserialize(string $value)
    {
        return json_decode($value, true);
    }

    /**
     * Calculate hit rate
     */
    private function calculateHitRate(array $info): float
    {
        $hits = $info['keyspace_hits'] ?? 0;
        $misses = $info['keyspace_misses'] ?? 0;
        $total = $hits + $misses;

        return $total > 0 ? round(($hits / $total) * 100, 2) : 0.0;
    }

    /**
     * Handle cache errors
     */
    private function handleError(string $operation, \Exception $e): void
    {
        error_log("Cache {$operation} error: " . $e->getMessage());

        // In production, you might want to disable cache temporarily
        if (Environment::getInstance()->isProduction()) {
            $this->enabled = false;
        }
    }

    /**
     * Reconnect to cache server
     */
    public function reconnect(): bool
    {
        try {
            // Close existing connection
            if ($this->redis) {
                $this->redis->close();
            }

            // Reinitialize Redis connection
            $config = [
                'host' => $_ENV['REDIS_HOST'] ?? 'localhost',
                'port' => (int)($_ENV['REDIS_PORT'] ?? 6379),
                'password' => $_ENV['REDIS_PASSWORD'] ?? null,
                'database' => (int)($_ENV['REDIS_DATABASE'] ?? 0)
            ];

            $this->initializeRedis($config);

            // Test connection
            if ($this->redis && $this->redis->ping()) {
                $this->enabled = true;
                error_log('Cache reconnection successful');
                return true;
            }

            return false;
        } catch (\Exception $e) {
            error_log('Cache reconnection failed: ' . $e->getMessage());
            $this->enabled = false;
            return false;
        }
    }

    /**
     * Disable cache temporarily
     */
    public function disable(): bool
    {
        try {
            $this->enabled = false;
            error_log('Cache disabled temporarily');
            return true;
        } catch (\Exception $e) {
            error_log('Failed to disable cache: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Enable cache
     */
    public function enable(): bool
    {
        try {
            // Test connection first
            if ($this->redis && $this->redis->ping()) {
                $this->enabled = true;
                error_log('Cache enabled');
                return true;
            } else {
                // Try to reconnect
                return $this->reconnect();
            }
        } catch (\Exception $e) {
            error_log('Failed to enable cache: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if cache is enabled and connected
     */
    public function isConnected(): bool
    {
        try {
            return $this->enabled && $this->redis && $this->redis->ping();
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get cache connection status
     */
    public function getConnectionStatus(): array
    {
        return [
            'enabled' => $this->enabled,
            'connected' => $this->isConnected(),
            'redis_available' => $this->redis !== null,
            'last_error' => $this->redis ? null : 'Redis not initialized'
        ];
    }
}
