# WeBot Source Code
## کد اصلی WeBot

این پوشه شامل تمام کدهای PHP اصلی WeBot است که بر اساس استاندارد PSR-4 سازماندهی شده‌اند.

---

## 📁 ساختار پوشه‌ها

### 🎮 Controllers/
**مسئولیت**: مدیریت درخواست‌ها و پاسخ‌ها

- **AuthController.php** - احراز هویت، ban، spam check
- **UserController.php** - ثبت‌نام، پروفایل، منوی اصلی
- **AdminController.php** - پنل ادمین، مدیریت کاربران
- **PaymentController.php** - پردازش پرداخت‌ها، کیف پول
- **ServiceController.php** - خرید، تمدید، مدیریت سرویس‌ها
- **PanelController.php** - اتصال به پنل‌ها، مانیتورینگ
- **TicketController.php** - سیستم پشتیبانی

### 🔧 Services/
**مسئولیت**: منطق تجاری و عملیات پیچیده

- **TelegramService.php** - API تلگرام، ارسال پیام
- **DatabaseService.php** - عملیات دیتابیس، ORM ساده
- **PaymentService.php** - منطق پرداخت، درگاه‌ها
- **PanelService.php** - اتصال به Marzban/Marzneshin/X-UI
- **QRCodeService.php** - تولید QR Code
- **MessageService.php** - قالب‌بندی و ارسال پیام‌ها

### 📊 Models/
**مسئولیت**: نمایش داده‌ها و روابط

- **User.php** - مدل کاربر
- **Payment.php** - مدل پرداخت
- **Service.php** - مدل سرویس VPN
- **Panel.php** - مدل پنل
- **Ticket.php** - مدل تیکت پشتیبانی

### 🗄 Repositories/
**مسئولیت**: دسترسی به داده‌ها

- **UserRepository.php** - CRUD کاربران
- **PaymentRepository.php** - CRUD پرداخت‌ها
- **ServiceRepository.php** - CRUD سرویس‌ها

### 🛡 Middleware/
**مسئولیت**: پردازش میانی درخواست‌ها

- **AuthMiddleware.php** - بررسی احراز هویت
- **AdminMiddleware.php** - بررسی دسترسی ادمین
- **RateLimitMiddleware.php** - محدودیت نرخ درخواست
- **ValidationMiddleware.php** - اعتبارسنجی ورودی‌ها

### 🔨 Utils/
**مسئولیت**: ابزارهای کمکی

- **Logger.php** - سیستم لاگ
- **Validator.php** - اعتبارسنجی داده‌ها
- **Helper.php** - توابع کمکی عمومی
- **DateHelper.php** - کار با تاریخ شمسی

### ⚠️ Exceptions/
**مسئولیت**: مدیریت خطاها

- **WeBotException.php** - کلاس پایه خطاها
- **ValidationException.php** - خطاهای اعتبارسنجی
- **PaymentException.php** - خطاهای پرداخت
- **PanelException.php** - خطاهای اتصال به پنل

---

## 🎯 اصول طراحی

### Single Responsibility Principle
هر کلاس فقط یک مسئولیت دارد

### Dependency Injection
وابستگی‌ها از طریق constructor تزریق می‌شوند

### Interface Segregation
استفاده از interface برای انعطاف‌پذیری

### Open/Closed Principle
کلاس‌ها برای توسعه باز و برای تغییر بسته هستند

---

## 📝 نحوه استفاده

### ایجاد Controller جدید:
```php
<?php
namespace WeBot\Controllers;

class NewController extends BaseController
{
    public function handle($data)
    {
        // Implementation
    }
}
```

### ایجاد Service جدید:
```php
<?php
namespace WeBot\Services;

class NewService
{
    private $repository;
    
    public function __construct(RepositoryInterface $repository)
    {
        $this->repository = $repository;
    }
}
```

---

## 🔄 Namespace Convention

```php
WeBot\Controllers\*     // کنترلرها
WeBot\Services\*        // سرویس‌ها  
WeBot\Models\*          // مدل‌ها
WeBot\Repositories\*    // ریپازیتوری‌ها
WeBot\Middleware\*      // میدل‌ویرها
WeBot\Utils\*           // ابزارها
WeBot\Exceptions\*      // خطاها
```

---

*این ساختار برای توسعه مدرن و قابل نگهداری WeBot طراحی شده است.*
