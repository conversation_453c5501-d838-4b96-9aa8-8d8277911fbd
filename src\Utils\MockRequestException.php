<?php

declare(strict_types=1);

namespace WeBot\Utils;

use Exception;

/**
 * Mock RequestException for GuzzleHttp\Exception\RequestException
 * Used when GuzzleHttp is not available
 */
class MockRequestException extends Exception
{
    private ?MockResponse $response;

    public function __construct(string $message = '', int $code = 0, ?Exception $previous = null, ?MockResponse $response = null)
    {
        parent::__construct($message, $code, $previous);
        $this->response = $response;
    }

    public function getResponse(): ?MockResponse
    {
        return $this->response;
    }

    public function hasResponse(): bool
    {
        return $this->response !== null;
    }
}
