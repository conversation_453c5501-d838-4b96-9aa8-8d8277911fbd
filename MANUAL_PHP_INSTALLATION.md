﻿# Manual PHP Installation Guide

Since automatic installation failed, please follow these steps:

## Option 1: Download PHP manually
1. Go to: https://windows.php.net/download/
2. Download PHP 8.2+ (Thread Safe, x64)
3. Extract to: C:\Users\<USER>\OneDrive\Documents\GitHub Rep\We-Bot\php-runtime\
4. Run this script again

## Option 2: Use XAMPP
1. Download XAMPP from: https://www.apachefriends.org/
2. Install XAMPP
3. Add C:\xampp\php to your PATH
4. Run: php --version to verify

## Option 3: Use Chocolatey (as Administrator)
1. Open PowerShell as Administrator
2. Run: choco install php --version 8.2.26 -y
3. Run: php --version to verify

After PHP is installed, run:
php composer.phar install
